<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.2</version>
        <relativePath/>
    </parent>

    <groupId>com.cleevio</groupId>
    <artifactId>fatbot-api</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>fatbot-api</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <kotlin.code.style>official</kotlin.code.style>
        <java.version>21</java.version>
        <kotlin.compiler.jvmTarget>21</kotlin.compiler.jvmTarget>
        <kotlin.version>1.9.24</kotlin.version>
        <org.springdoc.version>2.5.0</org.springdoc.version>
        <sentry.version>7.14.0</sentry.version>
        <testcontainers.version>1.19.8</testcontainers.version>
        <kotest.version>5.9.0</kotest.version>
        <jooq.version>3.19.9</jooq.version>
        <sonar.qualitygate.wait>true</sonar.qualitygate.wait>
        <sonar.core.codeCoveragePlugin>jacoco</sonar.core.codeCoveragePlugin>
        <sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
        <sonar.coverage.jacoco.xmlReportPaths>
            ${project.basedir}/target/site/jacoco/jacoco.xml
        </sonar.coverage.jacoco.xmlReportPaths>
    </properties>

    <repositories>
        <repository>
            <id>mavenCentral</id>
            <url>https://repo1.maven.org/maven2/</url>
        </repository>
        <repository>
            <id>cleevio-gitlab-maven</id>
            <url>https://gitlab.cleevio.cz/api/v4/groups/147/-/packages/maven</url>
        </repository>
        <repository>
            <id>cleeviox-gitlab-maven</id>
            <url>https://gitlab.cleevio.cz/api/v4/groups/1141/-/packages/maven</url>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <id>cleevio-gitlab-maven</id>
            <url>https://gitlab.cleevio.cz/api/v4/groups/147/-/packages/maven</url>
        </repository>
        <snapshotRepository>
            <id>cleevio-gitlab-maven</id>
            <url>https://gitlab.cleevio.cz/api/v4/groups/147/-/packages/maven</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <sourceDirectory>${project.basedir}/src/main/kotlin</sourceDirectory>
        <testSourceDirectory>${project.basedir}/src/test/kotlin</testSourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.github.gantsign.maven</groupId>
                <artifactId>ktlint-maven-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <id>check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
                <version>8.0.2</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>initialize</phase>
                    </execution>
                </executions>
                <configuration>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <generateGitPropertiesFilename>
                        ${project.build.outputDirectory}/git.properties
                    </generateGitPropertiesFilename>
                    <includeOnlyProperties>
                        <includeOnlyProperty>^git.build.(time|version)$</includeOnlyProperty>
                        <includeOnlyProperty>^git.commit.id.(abbrev|full)$</includeOnlyProperty>
                    </includeOnlyProperties>
                    <commitIdGenerationMode>full</commitIdGenerationMode>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <configuration>
                    <jvmTarget>${java.version}</jvmTarget>
                    <args>
                        <arg>-Xjsr305=strict</arg>
                        <arg>-Xemit-jvm-type-annotations</arg>
                    </args>
                    <compilerPlugins>
                        <plugin>spring</plugin>
                        <plugin>jpa</plugin>
                    </compilerPlugins>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-noarg</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.5</version>
                <configuration>
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*IT.java</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.12</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.gmaven</groupId>
                <artifactId>groovy-maven-plugin</artifactId>
                <version>2.1.1</version>
                <executions>
                    <execution>
                        <!-- Start the container in any phase before the actual code
                             generation is required, i.e. at the latest in
                             generate-sources -->
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>execute</goal>
                        </goals>
                        <configuration>
                            <source>
                                import org.testcontainers.containers.PostgreSQLContainer

                                container = new PostgreSQLContainer("postgres:15")
                                        .withUsername("postgres")
                                        .withDatabaseName("postgres")
                                        .withPassword("postgres")

                                container.start()

                                project.properties.setProperty('db.url', container.getJdbcUrl())
                                project.properties.setProperty('db.username', container.getUsername())
                                project.properties.setProperty('db.password', container.getPassword())
                            </source>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.testcontainers</groupId>
                        <artifactId>postgresql</artifactId>
                        <version>${testcontainers.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-maven-plugin</artifactId>
                <configuration>
                    <driver>org.postgresql.Driver</driver>
                    <url>${db.url}</url>
                    <username>${db.username}</username>
                    <password>${db.password}</password>
                    <promptOnNonLocalDatabase>false</promptOnNonLocalDatabase>
                    <databaseChangeLogTableName>DATABASECHANGELOG</databaseChangeLogTableName>
                    <databaseChangeLogLockTableName>DATABASECHANGELOGLOCK</databaseChangeLogLockTableName>
                    <changelogCatalogName>public</changelogCatalogName>
                    <changeLogFile>db/changelog/db.changelog.xml</changeLogFile>
                    <changeLogDirectory>src/main/resources/</changeLogDirectory>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>jakarta.xml.bind</groupId>
                        <artifactId>jakarta.xml.bind-api</artifactId>
                        <version>4.0.2</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>update</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.jooq</groupId>
                <artifactId>jooq-codegen-maven</artifactId>
                <version>${jooq.version}</version>
                <executions>
                    <execution>
                        <id>generate-psql</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <jdbc>
                                <driver>org.postgresql.Driver</driver>
                                <url>${db.url}</url>
                                <user>${db.username}</user>
                                <password>${db.password}</password>
                            </jdbc>
                            <generator>
                                <name>org.jooq.codegen.KotlinGenerator</name>
                                <database>
                                    <name>org.jooq.meta.postgres.PostgresDatabase</name>
                                    <includes>.*</includes>
                                    <excludes/>
                                    <inputSchema>public</inputSchema>
                                    <forcedTypes>
                                        <forcedType>
                                            <name>INSTANT</name>
                                            <includeTypes>(?i:TIMESTAMP)</includeTypes>
                                        </forcedType>
                                        <forcedType>
                                            <name>INSTANT</name>
                                            <includeTypes>(?i:TIMESTAMPTZ)</includeTypes>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.application.common.wrapper.AddressWrapper</userType>
                                            <converter>com.cleevio.fatbot.application.common.converter.AddressWrapperJooqConverter</converter>
                                            <includeExpression>
                                                .*\.WALLET.ADDRESS|
                                                .*\.BOT_WALLET.ADDRESS|
                                                .*\.TRANSACTION.TOKEN_ADDRESS|
                                                .*\.EVM_TOKEN_INFO.ADDRESS|
                                                .*\.MARKET_POSITION.TOKEN_ADDRESS|
                                                .*\.MARKET_POSITION_SNAPSHOT.TOKEN_ADDRESS|
                                                .*\.TOKEN_PRICE_SNAPSHOT.TOKEN_ADDRESS|
                                                .*\.HOT_TOKEN.TOKEN_ADDRESS|
                                                .*\.BOT_TRANSACTION.TOKEN_ADDRESS|
                                                .*\.TOKEN_PAIR_INFO.TOKEN_ADDRESS|
                                                .*\.TOKEN_PAIR_INFO.PAIR_ADDRESS|
                                                .*\.BOT_MARKET_POSITION.TOKEN_ADDRESS|
                                                .*\.BOT_TOKEN_INFO.ADDRESS|
                                                .*\.BOT_TOKEN_INFO.CREATOR_ADDRESS|
                                                .*\.TOKEN_AUDIT.TOKEN_ADDRESS|
                                                .*\.TOKEN_PAIR_INFO.CREATOR_ADDRESS|
                                                .*\.LIMIT_ORDER.TOKEN_ADDRESS|
                                            </includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                com.cleevio.fatbot.application.module.wallet.constant.Chain
                                            </userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>
                                                .*\.WALLET.CHAIN|
                                                .*\.MARKET_POSITION.CHAIN|
                                                .*\.REFERRAL_REWARD.CHAIN|
                                                .*\.TRANSACTION.CHAIN|
                                                .*\.EVM_TOKEN_INFO.CHAIN|
                                                .*\.MARKET_POSITION_SNAPSHOT.CHAIN|
                                                .*\.HOT_TOKEN.CHAIN|
                                                .*\.TOKEN_PRICE_SNAPSHOT.CHAIN|
                                                .*\.TOKEN_PAIR_INFO.CHAIN|
                                                .*\.BOT_MARKET_POSITION.CHAIN|
                                                .*\.BOT_TOKEN_INFO.CHAIN|
                                                .*\.BOT_WALLET.CHAIN|
                                                .*\.BOT_PORTFOLIO_VALUE_SNAPSHOT.CHAIN|
                                                .*\.TOKEN_AUDIT.CHAIN|
                                                .*\.LIMIT_ORDER.CHAIN|
                                            </includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>
                                                kotlin.Array&lt;com.cleevio.fatbot.application.module.wallet.constant.Chain&gt;
                                            </userType>
                                            <includeExpression>.*\.FIREBASE_USER.SELECTED_CHAINS</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.TRANSACTION.STATUS</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.application.module.transaction.constant.TransactionType</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.TRANSACTION.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.EXCHANGE_RATE_SNAPSHOT.CURRENCY</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.BOT_TRANSACTION.STATUS</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.BOT_TRANSACTION.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.application.module.market.port.out.GetDex.Dex</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.TOKEN_PAIR_INFO.DEX_TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.BOT_MARKET_POSITION.STATE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionSellReason</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.BOT_MARKET_POSITION.SELL_REASON</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.application.module.market.port.out.GetDex.PoolType</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.TOKEN_PAIR_INFO.RAYDIUM_POOL_TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateType</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.BOT_MARKET_POSITION_TRADE_STATE.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.application.module.transaction.constant.TransactionFailureReason</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.TRANSACTION.FAIL_REASON</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.domain.limitorder.LimitOrderType</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.LIMIT_ORDER.TYPE</includeExpression>
                                        </forcedType>
                                        <forcedType>
                                            <userType>com.cleevio.fatbot.domain.heartbeat.HeartBeatType</userType>
                                            <enumConverter>true</enumConverter>
                                            <includeExpression>.*\.HEART_BEAT.TYPE</includeExpression>
                                        </forcedType>
                                    </forcedTypes>
                                </database>
                                <generate>
                                    <pojos>false</pojos>
                                    <daos>false</daos>
                                    <records>false</records>
                                    <javaTimeTypes>true</javaTimeTypes>
                                </generate>
                                <target>
                                    <packageName>com.cleevio.fatbot</packageName>
                                    <directory>target/generated-sources/jooq</directory>
                                </target>
                            </generator>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.github.aoudiamoncef</groupId>
                <artifactId>apollo-client-maven-plugin</artifactId>
                <version>7.0.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <services>
                                <bitquery-v2>
                                    <enabled>true</enabled>
                                    <sourceFolder>${project.basedir}/src/main/resources/graphql/bitquery/v2</sourceFolder>
                                    <compilationUnit>
                                        <name>bitquery-v2</name>
                                        <compilerParams>
                                            <schemaPackageName>com.cleevio.fatbot.graphql.bitquery.v2</schemaPackageName>
                                            <generateKotlinModels>true</generateKotlinModels>
                                            <targetLanguage>KOTLIN_1_5</targetLanguage>
                                            <scalarsMapping>
                                                <DateTime>
                                                    <targetName>java.time.Instant</targetName>
                                                    <expression>com.cleevio.fatbot.infrastructure.config.graphql.InstantGraphQLAdapter()</expression>
                                                </DateTime>
                                            </scalarsMapping>
                                        </compilerParams>
                                    </compilationUnit>
                                    <introspection>
                                        <enabled>true</enabled>
                                        <endpointUrl>https://streaming.bitquery.io/graphql</endpointUrl>
                                        <headers>
                                            <Authorization>Bearer ${BITQUERY_ACCESS_TOKEN}</Authorization>
                                        </headers>
                                        <schemaFile>${project.basedir}/src/main/resources/graphql/bitquery/v2/schema.json</schemaFile>
                                    </introspection>
                                </bitquery-v2>
                                <!-- Due to fact that Solana and EVM queries are on different GQL endpoints -->
                                <!-- we generate data for each schema separately, to avoid potential conflicts in types -->
                                <bitquery-eap>
                                    <enabled>true</enabled>
                                    <sourceFolder>${project.basedir}/src/main/resources/graphql/bitquery/eap</sourceFolder>
                                    <compilationUnit>
                                        <name>bitquery-eap</name>
                                        <compilerParams>
                                            <schemaPackageName>com.cleevio.fatbot.graphql.bitquery.eap</schemaPackageName>
                                            <generateKotlinModels>true</generateKotlinModels>
                                            <targetLanguage>KOTLIN_1_5</targetLanguage>
                                            <scalarsMapping>
                                                <DateTime>
                                                    <targetName>java.time.Instant</targetName>
                                                    <expression>com.cleevio.fatbot.infrastructure.config.graphql.InstantGraphQLAdapter()</expression>
                                                </DateTime>
                                            </scalarsMapping>
                                        </compilerParams>
                                    </compilationUnit>
                                    <introspection>
                                        <enabled>true</enabled>
                                        <endpointUrl>https://streaming.bitquery.io/eap</endpointUrl>
                                        <headers>
                                            <Authorization>Bearer ory_at_yAunowuI9fPhjPEb7E3y-elc-1shRTfUzdddcx6FEJI.u7fHindd0aFYGzz72KzmuL6akQwFz-hiDIFYm77kj-o</Authorization>
                                        </headers>
                                        <schemaFile>${project.basedir}/src/main/resources/graphql/bitquery/eap/schema.json</schemaFile>
                                    </introspection>
                                </bitquery-eap>
                            </services>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-core-jvm</artifactId>
            <version>1.9.0</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-slf4j</artifactId>
            <version>1.9.0</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-reactor</artifactId>
            <version>1.9.0</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.victools</groupId>
            <artifactId>jsonschema-generator</artifactId>
            <version>4.35.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.victools</groupId>
            <artifactId>jsonschema-module-jackson</artifactId>
            <version>4.35.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.uuid</groupId>
            <artifactId>java-uuid-generator</artifactId>
            <version>5.1.0</version>
        </dependency>

        <dependency>
            <groupId>io.hypersistence</groupId>
            <artifactId>hypersistence-utils-hibernate-63</artifactId>
            <version>3.8.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
            <version>2.9.2</version>
        </dependency>

        <dependency>
            <groupId>com.cleevio.library</groupId>
            <artifactId>spring-boot-starter-locking-handler</artifactId>
            <version>1.0.9</version>
        </dependency>
        <dependency>
            <groupId>com.cleevio.library</groupId>
            <artifactId>spring-boot-starter-exception-handler</artifactId>
            <version>1.0.3</version>
        </dependency>

        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-spring</artifactId>
            <version>5.16.0</version>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-jdbc-template</artifactId>
            <version>5.16.0</version>
        </dependency>

        <!-- crypto -->
        <dependency>
            <groupId>org.web3j</groupId>
            <artifactId>core</artifactId>
            <version>4.10.3</version>
        </dependency>

        <dependency>
            <groupId>org.web3j</groupId>
            <artifactId>contracts</artifactId>
            <version>4.10.3</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.77</version>
        </dependency>

        <!-- Custom fatbot fork of solanaj repository until PR 41 is merged -->
        <!-- https://gitlab.cleevio.cz/cleeviox/backend/fatbot-solanaj -->
        <dependency>
            <groupId>com.mmorrell</groupId>
            <artifactId>solanaj</artifactId>
            <version>1.20.16-FORK</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.bitcoinj/bitcoinj-core -->
        <dependency>
            <groupId>org.bitcoinj</groupId>
            <artifactId>bitcoinj-core</artifactId>
            <version>0.16.5</version>
            <scope>compile</scope>
        </dependency>

        <!-- logging -->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>7.4</version>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-spring-boot-starter-jakarta</artifactId>
            <version>${sentry.version}</version>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-logback</artifactId>
            <version>${sentry.version}</version>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-jdbc</artifactId>
            <version>${sentry.version}</version>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-kotlin-extensions</artifactId>
            <version>${sentry.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.loki4j</groupId>
            <artifactId>loki-logback-appender</artifactId>
            <version>1.5.1</version>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.grafana</groupId>
            <artifactId>grafana-opentelemetry-starter</artifactId>
            <version>1.4.0</version>
        </dependency>

        <!-- openapi -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>${org.springdoc.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-common</artifactId>
            <version>${org.springdoc.version}</version>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>2.2.21</version>
        </dependency>

        <!-- security -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.firebase</groupId>
            <artifactId>firebase-admin</artifactId>
            <version>9.2.0</version>
        </dependency>

        <!-- jooq -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jooq</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>jooq</artifactId>
            <version>${jooq.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>jooq-codegen</artifactId>
            <version>${jooq.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>jooq-kotlin</artifactId>
            <version>${jooq.version}</version>
        </dependency>

        <!-- GraphQL -->
        <dependency>
            <groupId>com.apollographql.apollo3</groupId>
            <artifactId>apollo-runtime</artifactId>
            <version>3.8.5</version>
        </dependency>
        <dependency>
            <groupId>com.apollographql.apollo3</groupId>
            <artifactId>apollo-api-jvm</artifactId>
            <version>3.8.5</version>
        </dependency>
        <dependency>
            <groupId>com.apollographql.apollo3</groupId>
            <artifactId>apollo-runtime-jvm</artifactId>
            <version>3.8.5</version>
        </dependency>

        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit5</artifactId>
            <version>${kotlin.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-testcontainers</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.mockk</groupId>
            <artifactId>mockk-jvm</artifactId>
            <version>1.13.11</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-assertions-core-jvm</artifactId>
            <version>5.9.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ninja-squad</groupId>
            <artifactId>springmockk</artifactId>
            <version>4.0.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>mockwebserver</artifactId>
            <version>4.12.0</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>