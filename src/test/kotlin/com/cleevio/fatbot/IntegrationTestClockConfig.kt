package com.cleevio.fatbot

import com.cleevio.fatbot.application.common.util.plusMonths
import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import java.time.Clock
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalUnit

@TestConfiguration
class IntegrationTestClockConfig {
	@Bean
	@Primary
	fun testClock(): Clock = IntegrationTestClock(defaultStartTimeOfEachTest = Instant.parse("2024-08-30T10:00:00Z"))
}

class IntegrationTestClock(
	private val defaultStartTimeOfEachTest: Instant,
) : Clock() {

	private val logger = logger()

	private var currentTime = defaultStartTimeOfEachTest

	fun advanceBy(amountToAdd: Long, temporal: TemporalUnit) {
		currentTime = if (temporal == ChronoUnit.MONTHS) {
			currentTime.plusMonths(amountToAdd)
		} else {
			currentTime.plus(amountToAdd, temporal)
		}

		logger.info("Clock in test advanced by $amountToAdd $temporal. Current time: $currentTime")
	}

	fun setTo(instant: Instant) {
		require(instant.isAfter(currentTime)) { "New time have to be in future!" }
		logger.info("Clock in test set to $instant")
		this.currentTime = instant
	}

	fun currentTime() = currentTime

	fun reset() {
		logger.info("Clock in test set to $currentTime")
		currentTime = defaultStartTimeOfEachTest
	}

	override fun instant() = currentTime

	override fun withZone(zone: ZoneId?): Clock {
		TODO("Not implemented")
	}

	override fun getZone(): ZoneId = ZoneOffset.UTC

	fun getCurrentDate(): LocalDate = LocalDate.ofInstant(currentTime(), zone)
}
