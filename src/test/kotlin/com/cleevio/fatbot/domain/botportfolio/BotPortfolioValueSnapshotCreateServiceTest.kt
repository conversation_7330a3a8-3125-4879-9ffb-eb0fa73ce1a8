package com.cleevio.fatbot.domain.botportfolio

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.Test

class BotPortfolioValueSnapshotCreateServiceTest(
	@Autowired private val botPortfolioValueSnapshotRepository: BotPortfolioValueSnapshotRepository,
	@Autowired private val botPortfolioValueSnapshotCreateService: BotPortfolioValueSnapshotCreateService,
	@Autowired private val clock: IntegrationTestClock,
) : IntegrationTest() {

	@Test
	fun `should create snapshot`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 2.toUUID(),
		)

		botPortfolioValueSnapshotCreateService.create(
			botId = 3.toUUID(),
			snapshotMadeAt = Instant.now(clock).minus(1, ChronoUnit.DAYS),
			portfolioValueUsd = BigDecimal("300.0"),
			acquisitionValueUsd = BigDecimal("150.0"),
		)

		val snapshots = botPortfolioValueSnapshotRepository.findAll()
		snapshots.size shouldBe 1
		snapshots[0].botId shouldBe 3.toUUID()
		snapshots[0].snapshotMadeAt shouldBe Instant.now(clock).minus(1, ChronoUnit.DAYS)
		snapshots[0].portfolioValueUsd shouldBeEqualComparingTo BigDecimal("300.0")
		snapshots[0].acquisitionValueUsd shouldBeEqualComparingTo BigDecimal("150.0")
	}
}
