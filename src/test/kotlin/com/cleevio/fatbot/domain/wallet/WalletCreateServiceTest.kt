package com.cleevio.fatbot.domain.wallet

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import jodd.util.Base64
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class WalletCreateServiceTest(
	@Autowired private val underTest: WalletCreateService,
	@Autowired private val walletRepository: WalletRepository,
) : IntegrationTest() {

	@Test
	fun `should encrypt and decrypt EVM private key, stripping 0x and making it lowercase`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		// when
		underTest.create(
			userId = 1.toUUID(),
			addressWrapper = AddressWrapper(addressString = "******************************************"),
			privateKey = "0x547D11A39047CB34981A9BFD2EC26BADCDE36015726FC2663F688CC9CBF83AA6",
			chain = Chain.EVM_MAINNET,
			walletCount = 0,
		)

		// then in DB private key should be encrypted
		val privateKeyInDb = walletRepository.findAll()[0].privateKey

		Base64.decodeToString(
			privateKeyInDb,
		).lowercase() shouldNotBe "547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6"

		// and when fetched by entity it should be decrypted
		walletRepository.findAll().first().run {
			privateKey shouldBe "547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6"
		}
	}

	@Test
	fun `should encrypt and decrypt SOLANA private key`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		// when
		underTest.create(
			userId = 1.toUUID(),
			addressWrapper = AddressWrapper(addressString = "HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH"),
			privateKey = "npLNkAEgTjed78Q97G7t5PiwtVuGqKCYgpYjtsuoKrefWAbxocZBxK4mAtexP7ECgJVA9Fbba57UM1jMX7pmXYY",
			chain = Chain.SOLANA,
			walletCount = 0,
		)

		// then in DB private key should be encrypted
		val privateKeyInDb = walletRepository.findAll()[0].privateKey

		Base64.decodeToString(
			privateKeyInDb,
		) shouldNotBe "npLNkAEgTjed78Q97G7t5PiwtVuGqKCYgpYjtsuoKrefWAbxocZBxK4mAtexP7ECgJVA9Fbba57UM1jMX7pmXYY"

		// and when fetched by entity it should be decrypted
		walletRepository.findAll().first().run {
			privateKey shouldBe "npLNkAEgTjed78Q97G7t5PiwtVuGqKCYgpYjtsuoKrefWAbxocZBxK4mAtexP7ECgJVA9Fbba57UM1jMX7pmXYY"
		}
	}
}
