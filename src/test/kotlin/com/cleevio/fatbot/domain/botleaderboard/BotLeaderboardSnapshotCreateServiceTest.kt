package com.cleevio.fatbot.domain.botleaderboard

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.Test

class BotLeaderboardSnapshotCreateServiceTest(
	@Autowired private val botLeaderboardSnapshotCreateService: BotLeaderboardSnapshotCreateService,
	@Autowired private val botLeaderboardSnapshotRepository: BotLeaderboardSnapshotRepository,
	@Autowired private val clock: IntegrationTestClock,
) : IntegrationTest() {

	@Test
	fun `should create bot leaderboard snapshot with positive values`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 31.toUUID())
		integrationTestHelper.getBot(
			id = 100.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 31.toUUID(),
			name = "TestBot1",
		)

		val botId = 100.toUUID()
		val profitValueUsd = BigDecimal("1000.0")
		val profitValueFraction = BigDecimal("0.5")
		val balanceUsd = BigDecimal("500.0")
		val snapshotMadeAt = Instant.now(clock).minus(1, ChronoUnit.HOURS)

		// when
		val snapshot = botLeaderboardSnapshotCreateService.create(
			botId = botId,
			profitValueUsd = profitValueUsd,
			profitValueFraction = profitValueFraction,
			balanceUsd = balanceUsd,
			snapshotMadeAt = snapshotMadeAt,
		)

		// then
		snapshot.botId shouldBe botId
		snapshot.profitValueUsd shouldBe profitValueUsd
		snapshot.profitValueFraction shouldBe profitValueFraction
		snapshot.balanceUsd shouldBe balanceUsd
		snapshot.snapshotMadeAt shouldBe snapshotMadeAt
	}

	@Test
	fun `should create bot leaderboard snapshot with negative values`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 32.toUUID())
		integrationTestHelper.getBot(
			id = 101.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 32.toUUID(),
			name = "TestBot2",
		)

		val botId = 101.toUUID()
		val profitValueUsd = BigDecimal("-500.0")
		val profitValueFraction = BigDecimal("-0.25")
		val balanceUsd = BigDecimal("1000.0")
		val snapshotMadeAt = Instant.now(clock).minus(2, ChronoUnit.HOURS)

		// when
		val snapshot = botLeaderboardSnapshotCreateService.create(
			botId = botId,
			profitValueUsd = profitValueUsd,
			profitValueFraction = profitValueFraction,
			balanceUsd = balanceUsd,
			snapshotMadeAt = snapshotMadeAt,
		)

		// then
		snapshot.botId shouldBe botId
		snapshot.profitValueUsd shouldBe profitValueUsd
		snapshot.profitValueFraction shouldBe profitValueFraction
		snapshot.balanceUsd shouldBe balanceUsd
		snapshot.snapshotMadeAt shouldBe snapshotMadeAt
	}

	@Test
	fun `should create bot leaderboard snapshot with zero values`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 33.toUUID())
		integrationTestHelper.getBot(
			id = 102.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 33.toUUID(),
			name = "TestBot3",
		)

		val botId = 102.toUUID()
		val profitValueUsd = BigDecimal.ZERO
		val profitValueFraction = BigDecimal.ZERO
		val balanceUsd = BigDecimal.ZERO
		val snapshotMadeAt = Instant.now(clock).minus(3, ChronoUnit.HOURS)

		// when
		val snapshot = botLeaderboardSnapshotCreateService.create(
			botId = botId,
			profitValueUsd = profitValueUsd,
			profitValueFraction = profitValueFraction,
			balanceUsd = balanceUsd,
			snapshotMadeAt = snapshotMadeAt,
		)

		// then
		snapshot.botId shouldBe botId
		snapshot.profitValueUsd shouldBe profitValueUsd
		snapshot.profitValueFraction shouldBe profitValueFraction
		snapshot.balanceUsd shouldBe balanceUsd
		snapshot.snapshotMadeAt shouldBe snapshotMadeAt
	}

	@Test
	fun `should create multiple snapshots for the same bot`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 34.toUUID())
		integrationTestHelper.getBot(
			id = 103.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 34.toUUID(),
			name = "TestBot4",
		)

		val botId = 103.toUUID()
		val now = Instant.now(clock)

		// when
		val snapshot1 = botLeaderboardSnapshotCreateService.create(
			botId = botId,
			profitValueUsd = BigDecimal("1000.0"),
			profitValueFraction = BigDecimal("0.5"),
			balanceUsd = BigDecimal("500.0"),
			snapshotMadeAt = now.minus(2, ChronoUnit.HOURS),
		)

		val snapshot2 = botLeaderboardSnapshotCreateService.create(
			botId = botId,
			profitValueUsd = BigDecimal("1500.0"),
			profitValueFraction = BigDecimal("0.75"),
			balanceUsd = BigDecimal("750.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		// then
		val snapshots = botLeaderboardSnapshotRepository.findAll()
			.filter { it.botId == botId }
			.sortedBy { it.snapshotMadeAt }

		snapshots.size shouldBe 2
		snapshots[0].id shouldBe snapshot1.id
		snapshots[1].id shouldBe snapshot2.id
	}
}
