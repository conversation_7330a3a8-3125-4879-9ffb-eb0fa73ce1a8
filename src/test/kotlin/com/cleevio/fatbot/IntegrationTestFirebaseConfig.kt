package com.cleevio.fatbot

import com.cleevio.fatbot.application.module.access.port.out.GetEnabledChains
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary

@TestConfiguration
class IntegrationTestFirebaseConfig {

	/**
	 * We need to create this bean as a @Primary because GetEnabledChainIds is
	 * called when Spring context is being created (to filter out EVM chain properties) and
	 * in that step, mocks would not yet be initialized.
	 */
	@Bean
	@Primary
	fun getEnabledChainIds(): GetEnabledChains {
		return GetEnabledChains {
			return@GetEnabledChains setOf(
				Chain.EVM_MAINNET,
				Chain.EVM_BASE,
				Chain.EVM_BSC,
				Chain.SOLANA,
				Chain.EVM_ARBITRUM_ONE,
			)
		}
	}
}
