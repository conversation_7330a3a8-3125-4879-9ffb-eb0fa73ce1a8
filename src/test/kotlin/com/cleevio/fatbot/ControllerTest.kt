package com.cleevio.fatbot

import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.access.port.out.ParseToken
import com.cleevio.fatbot.application.module.access.port.out.UserAuthDetail
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.user.FirebaseUser
import com.cleevio.fatbot.infrastructure.config.SecurityConfig
import com.cleevio.fatbot.infrastructure.config.properties.FatbotApiProperties
import com.ninjasquad.springmockk.MockkBean
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ActiveProfiles
import java.util.UUID

@ActiveProfiles("test")
@ExtendWith(MockKExtension::class)
@Import(SecurityConfig::class, FatbotApiTestPropertiesConfig::class)
abstract class ControllerTest {

	val user = User(
		accessToken = "userAccessToken",
		email = "<EMAIL>",
	)

	@MockkBean
	lateinit var commandBus: CommandBus

	@MockkBean
	lateinit var queryBus: QueryBus

	@MockkBean
	lateinit var parseToken: ParseToken

	@MockkBean
	lateinit var firebaseUserFinderService: FirebaseUserFinderService

	@BeforeEach
	fun beforeEach() {
		every { parseToken(any()) } returns user.toUserAuthDetail()
		every { firebaseUserFinderService.getByEmailIgnoringCase(any()) } returns user.toFirebaseUser()
	}

	@AfterEach
	fun afterEach() {
		verify {
			parseToken(token = user.accessToken)
			firebaseUserFinderService.getByEmailIgnoringCase(email = user.email)
		}

		confirmVerified(
			commandBus,
			queryBus,
			parseToken,
			firebaseUserFinderService,
		)
	}
}

class User(
	val accessToken: String,
	val email: String,
) {
	fun toUserAuthDetail() = UserAuthDetail(email = email)
	fun toFirebaseUser() = FirebaseUser(
		id = 1.toUUID(),
		email = email,
		referralCode = null,
		referredByUserId = null,
		selectedChains = Chain.entries.mapToSet { it },
	)
}

@TestConfiguration
class FatbotApiTestPropertiesConfig {

	@Bean
	@Primary
	fun fatbotApiProperties(): FatbotApiProperties = FatbotApiProperties(
		apiKey = UUID.fromString("00000000-0000-0000-0000-000000000000"),
	)
}
