package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.toAddress
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigInteger
import java.time.temporal.ChronoUnit

@Suppress("ktlint:standard:max-line-length")
class GetTokenPricesEVMTest(
	@Autowired private val getTokenPrices: GetTokenPrices,
	@Autowired private val integrationTestClock: IntegrationTestClock,
) : IntegrationTest() {

	@Test
	fun `on multiple invocations should call chain only once`() {
		every { web3jWrapper.getWeb3j() } returns web3j

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************","******************************************"]
				"0xe016d3ab00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000002000000000000000000000000aaa0000000000000000000000000000000000000000000000000000000000000bbb0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"00000000000000000000000000000000000000000000000000000000000000a0" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"000000000000000000000000bbb0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000000000000000000000000000000000000000000f" +
			"000000000000000000000000000000000000000000000000000000000000000e"

		getTokenPrices(
			tokens = setOf(
				"aaa".toAddress().toChainAddress(Chain.EVM_MAINNET),
				"bbb".toAddress().toChainAddress(Chain.EVM_MAINNET),
			),
		)

		integrationTestClock.advanceBy(40, ChronoUnit.SECONDS)

		getTokenPrices(
			tokens = setOf(
				"aaa".toAddress().toChainAddress(Chain.EVM_MAINNET),
				"bbb".toAddress().toChainAddress(Chain.EVM_MAINNET),
			),
		)

		verify(exactly = 1) { readOnlyTransactionManager.sendCall(any(), any(), any()) }
	}

	@Test
	fun `when only part of request is not in cache, only that part is being requested from chain`() {
		every { web3jWrapper.getWeb3j() } returns web3j

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************","******************************************"]
				"0xe016d3ab00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000002000000000000000000000000aaa0000000000000000000000000000000000000000000000000000000000000bbb0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"00000000000000000000000000000000000000000000000000000000000000a0" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"000000000000000000000000bbb0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000000000000000000000000000000000000000000f" +
			"000000000000000000000000000000000000000000000000000000000000000e"

		getTokenPrices(
			tokens = setOf(
				"aaa".toAddress().toChainAddress(Chain.EVM_MAINNET),
				"bbb".toAddress().toChainAddress(Chain.EVM_MAINNET),
			),
		)
		integrationTestClock.advanceBy(40, ChronoUnit.SECONDS)

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// ["0xccc0000000000000000000000000000000000000"]
				"0xe016d3ab00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000001000000000000000000000000ccc0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000ccc0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000000000000000000000000000000000000000000f"

		getTokenPrices(
			tokens = setOf(
				"aaa".toAddress().toChainAddress(Chain.EVM_MAINNET),
				"ccc".toAddress().toChainAddress(Chain.EVM_MAINNET),
			),
		)

		getTokenPrices(
			tokens = setOf(
				"aaa".toAddress().toChainAddress(Chain.EVM_MAINNET),
				"bbb".toAddress().toChainAddress(Chain.EVM_MAINNET),
				"ccc".toAddress().toChainAddress(Chain.EVM_MAINNET),
			),
		)
		verify(exactly = 2) { readOnlyTransactionManager.sendCall(any(), any(), any()) }
	}

	@Test
	fun `the second after values become stale, cache should return old values still but start fetching new`() {
		every { web3jWrapper.getWeb3j() } returns web3j

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************","******************************************"]
				"0xe016d3ab00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000002000000000000000000000000aaa0000000000000000000000000000000000000000000000000000000000000bbb0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"00000000000000000000000000000000000000000000000000000000000000a0" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"000000000000000000000000bbb0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000000000000000000000000000000000000000000f" +
			"000000000000000000000000000000000000000000000000000000000000000e" andThen
			"0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"00000000000000000000000000000000000000000000000000000000000000a0" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"000000000000000000000000bbb0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"0000000000000000000000000000000000000000000000000000000000000005" +
			"0000000000000000000000000000000000000000000000000000000000000004"

		val freshPrices = getTokenPrices(
			tokens = setOf(
				"aaa".toAddress().toChainAddress(Chain.EVM_MAINNET),
				"bbb".toAddress().toChainAddress(Chain.EVM_MAINNET),
			),
		)

		freshPrices["aaa".toAddress().toChainAddress(Chain.EVM_MAINNET)] shouldBe BigInteger("15").asBaseAmount()
		freshPrices["bbb".toAddress().toChainAddress(Chain.EVM_MAINNET)] shouldBe BigInteger("14").asBaseAmount()

		integrationTestClock.advanceBy(51, ChronoUnit.SECONDS)

		val stalePrices = getTokenPrices(
			tokens = setOf(
				"aaa".toAddress().toChainAddress(Chain.EVM_MAINNET),
				"bbb".toAddress().toChainAddress(Chain.EVM_MAINNET),
			),
		)

		stalePrices["aaa".toAddress().toChainAddress(Chain.EVM_MAINNET)] shouldBe BigInteger("15").asBaseAmount()
		stalePrices["bbb".toAddress().toChainAddress(Chain.EVM_MAINNET)] shouldBe BigInteger("14").asBaseAmount()

		// At this point we fetched the already stale token prices
		// the cache responded with old values, while in async it started to refresh the values
		// this wait is here to let async have some time to trigger next .sendCall() and update the cache with
		// fresh values (in our case return of 'andThen')
		// Note: If this test becomes flaky, increase this value
		Thread.sleep(100)

		verify(exactly = 2) { readOnlyTransactionManager.sendCall(any(), any(), any()) }

		val refreshedPrices = getTokenPrices(
			tokens = setOf(
				"aaa".toAddress().toChainAddress(Chain.EVM_MAINNET),
				"bbb".toAddress().toChainAddress(Chain.EVM_MAINNET),
			),
		)

		refreshedPrices["aaa".toAddress().toChainAddress(Chain.EVM_MAINNET)] shouldBe BigInteger("5").asBaseAmount()
		refreshedPrices["bbb".toAddress().toChainAddress(Chain.EVM_MAINNET)] shouldBe BigInteger("4").asBaseAmount()
	}

	@Test
	fun `when fetching values that became expired, newly fetched values will be provided`() {
		every { web3jWrapper.getWeb3j() } returns web3j

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************","******************************************"]
				"0xe016d3ab00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000002000000000000000000000000aaa0000000000000000000000000000000000000000000000000000000000000bbb0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"00000000000000000000000000000000000000000000000000000000000000a0" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"000000000000000000000000bbb0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000000000000000000000000000000000000000000f" +
			"000000000000000000000000000000000000000000000000000000000000000e" andThen
			"0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"00000000000000000000000000000000000000000000000000000000000000a0" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"000000000000000000000000bbb0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"0000000000000000000000000000000000000000000000000000000000000005" +
			"0000000000000000000000000000000000000000000000000000000000000004"

		val freshPrices = getTokenPrices(
			tokens = setOf(
				"aaa".toAddress().toChainAddress(Chain.EVM_MAINNET),
				"bbb".toAddress().toChainAddress(Chain.EVM_MAINNET),
			),
		)

		freshPrices["aaa".toAddress().toChainAddress(Chain.EVM_MAINNET)] shouldBe BigInteger("15").asBaseAmount()
		freshPrices["bbb".toAddress().toChainAddress(Chain.EVM_MAINNET)] shouldBe BigInteger("14").asBaseAmount()

		integrationTestClock.advanceBy(91, ChronoUnit.SECONDS)

		val newPrices = getTokenPrices(
			tokens = setOf(
				"aaa".toAddress().toChainAddress(Chain.EVM_MAINNET),
				"bbb".toAddress().toChainAddress(Chain.EVM_MAINNET),
			),
		)

		newPrices["aaa".toAddress().toChainAddress(Chain.EVM_MAINNET)] shouldBe BigInteger("5").asBaseAmount()
		newPrices["bbb".toAddress().toChainAddress(Chain.EVM_MAINNET)] shouldBe BigInteger("4").asBaseAmount()
	}
}
