package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.util.toUUID
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.platform.commons.annotation.Testable
import org.p2p.solanaj.rpc.types.ConfirmedTransaction
import java.math.BigInteger

@Testable
class TokensSuccessfullySwappedEventTest {

	@Test
	fun test() {
		@Suppress("ktlint:standard:max-line-length")
		val log = "Program data: kOvr5lzT9wYGm4hX/quBhPtof2NGGMA12sQ53BrrO1WYoPAAAAAAAR2Mz4esAUe651brljou9iRMlpFWmo7AjwAgouuPvbWhAOmkNQAAAAAjVd2FAwAAAADh9QUAAAAAgJaYAAAAAAAkAAAANjkyYWE0ZjItNzQxYS00N2NjLWI1ZjktOGFkNDkxOTliMTZi"

		val confirmedTransaction = mockk<ConfirmedTransaction> {
			every { meta } returns mockk {
				every { preBalances } returns listOf(0, 15)
				every { postBalances } returns listOf(10, 100)
				every { logMessages } returns listOf(log)
			}
		}

		GetTransactionResultsSVM.TokensSuccessfullySwappedEvent.containedInTransaction(confirmedTransaction) shouldBe true

		val decoded = GetTransactionResultsSVM.TokensSuccessfullySwappedEvent.fromTransaction(confirmedTransaction)

		decoded.run {
			tokenIn shouldBe AddressWrapper("So11111111111111111111111111111111111111112")
			tokenOut shouldBe AddressWrapper("2zMMhcVQEXDtdE6vsFS7S7D5oUodfJHE8vd1gnBouauv")
			amountIn shouldBe 9e8.toLong()
			amountOut shouldBe 15130776867L
			fee shouldBe 1e8.toLong()
			referralFee shouldBe 1e7.toLong()
			referralUUID shouldBe "692aa4f2-741a-47cc-b5f9-8ad49199b16b".toUUID()
			balanceChange shouldBe BaseAmount(BigInteger.valueOf(10))
		}
	}
}
