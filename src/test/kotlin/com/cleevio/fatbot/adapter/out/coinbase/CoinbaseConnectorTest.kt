package com.cleevio.fatbot.adapter.out.coinbase

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.urlEncode
import io.kotest.matchers.shouldBe
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import java.math.BigDecimal

class CoinbaseConnectorTest(
	@Autowired private val underTest: CoinbaseConnector,
) : IntegrationTest() {

	companion object {
		@JvmStatic
		@DynamicPropertySource
		fun properties(registry: DynamicPropertyRegistry) {
			registry.add("integration.coinbase.base-url") {
				"http://localhost:${MOCK_WEB_SERVER.port}"
			}
		}

		@AfterAll
		@JvmStatic
		fun tearDown() {
			MOCK_WEB_SERVER.shutdown()
		}
	}

	@Test
	@Disabled("Connector is mocked")
	fun `test getUsdExchangeRate for ETH, should deserialize correctly`() {
		MOCK_WEB_SERVER.enqueue(
			MockResponse()
				.setBody(COINBASE_ETH_EXCHANGE_RATES_RESPONSE_JSON)
				.addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON),
		)

		val exchangeRate = underTest.getUsdExchangeRate(CryptoCurrency.ETH)
		exchangeRate shouldBe BigDecimal("2468.775")

		val recordedRequest = MOCK_WEB_SERVER.takeRequest()
		recordedRequest.method shouldBe HttpMethod.GET.name()
		recordedRequest.path shouldBe "/v2/exchange-rates?currency=ETH".urlEncode()
	}

	@Test
	@Disabled("Connector is mocked")
	fun `test getUsdExchangeRate for SOL, should deserialize correctly`() {
		MOCK_WEB_SERVER.enqueue(
			MockResponse()
				.setBody(COINBASE_SOL_EXCHANGE_RATES_RESPONSE_JSON)
				.addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON),
		)

		val exchangeRate = underTest.getUsdExchangeRate(CryptoCurrency.SOL)
		exchangeRate shouldBe BigDecimal("146.64")

		val recordedRequest = MOCK_WEB_SERVER.takeRequest()
		recordedRequest.method shouldBe HttpMethod.GET.name()
		recordedRequest.path shouldBe "/v2/exchange-rates?currency=SOL".urlEncode()
	}
}

private val MOCK_WEB_SERVER = MockWebServer()

// ~500 other exchange rates omitted for brevity, this is sufficient for test use case
private const val COINBASE_SOL_EXCHANGE_RATES_RESPONSE_JSON = """
{
	"data": {
		"currency": "SOL",
		"rates": {
			"UPI": "637643.39523232909209012",
			"USD": "146.64",
			"USDC": "146.64"
		}
	}
}
"""
private const val COINBASE_ETH_EXCHANGE_RATES_RESPONSE_JSON = """
{
	"data": {
		"currency": "ETH",
		"rates": {
			"UPI": "10677438.205657621745365125",
			"USD": "2468.775",
			"USDC": "2468.775"
		}
	}
}
"""
