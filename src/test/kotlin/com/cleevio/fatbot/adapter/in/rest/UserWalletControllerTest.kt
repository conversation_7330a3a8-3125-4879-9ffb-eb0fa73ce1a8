package com.cleevio.fatbot.adapter.`in`.rest

import com.cleevio.fatbot.ControllerTest
import com.cleevio.fatbot.adapter.`in`.ApiVersion
import com.cleevio.fatbot.adapter.`in`.rest.v2.UserWalletControllerV2
import com.cleevio.fatbot.adapter.`in`.rest.v3.UserWalletControllerV3
import com.cleevio.fatbot.addAcceptHeader
import com.cleevio.fatbot.addBearerAuthHeader
import com.cleevio.fatbot.addJsonContent
import com.cleevio.fatbot.addParams
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.command.CreateNewUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.command.DeleteUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.command.ImportUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.command.PatchUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.query.ExportUserWalletQuery
import com.cleevio.fatbot.application.module.wallet.query.GetAllUserWalletsQuery
import com.cleevio.fatbot.jsonContent
import com.cleevio.fatbot.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.delete
import org.springframework.test.web.servlet.get
import org.springframework.test.web.servlet.patch
import org.springframework.test.web.servlet.post
import org.springframework.util.LinkedMultiValueMap
import java.math.BigDecimal
import java.net.URI

@WebMvcTest(UserWalletController::class, UserWalletControllerV2::class, UserWalletControllerV3::class)
class UserWalletControllerTest(
	@Autowired private val mockMvc: MockMvc,
) : ControllerTest() {

	@Test
	fun `test getAllUserWallets, should serialize and deserialize correctly`() {
		every { queryBus(any<GetAllUserWalletsQuery>()) } returns listOf(
			GetAllUserWalletsQuery.Result(
				walletId = 5.toUUID(),
				isDefault = true,
				walletAddress = AddressWrapper(addressString = "******************************************"),
				walletDetailUrl = URI("https://ethscan.io/address/******************************************"),
				walletBalance = NativeAmount.ZERO,
				walletBalanceUsd = BigDecimal.ZERO,
				customName = "My wallet",
				chain = Chain.EVM_MAINNET,
				currentPortfolioValueUsd = BigDecimal.ONE,
				currentPortfolioValueChangeUsd = BigDecimal.ZERO,
				currentPortfolioValueChangeFraction = BigDecimal.ZERO,
			),
		)

		mockMvc.get("/v3/users/me/wallets") {
			addAcceptHeader(ApiVersion.VERSION_1_JSON)
			addBearerAuthHeader(user.accessToken)
			addParams(LinkedMultiValueMap(mapOf("useSelectedChains" to listOf("true"))))
		}.andExpect {
			status { isOk() }
			jsonContent(
				"""
				[
					{
						"walletId": "00000000-0000-0000-0000-000000000005",
						"walletDetailUrl": "https://ethscan.io/address/******************************************",
						"isDefault": true,
						"walletAddress": "******************************************",
						"customName": "My wallet"
					}
				]
				""".trimIndent(),
			)
		}

		verify {
			queryBus(
				GetAllUserWalletsQuery(userId = 1.toUUID(), useSelectedChains = true),
			)
		}
	}

	@Test
	fun `test createNewUserWallet, should serialize and deserialize correctly`() {
		every { commandBus(any<CreateNewUserWalletCommand>()) } returns CreateNewUserWalletCommand.Result(
			walletId = 10.toUUID(),
			walletAddress = AddressWrapper(addressString = "******************************************"),
			privateKey = "547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
		)

		mockMvc.post("/v2/users/me/wallets") {
			addAcceptHeader(ApiVersion.VERSION_1_JSON)
			addBearerAuthHeader(user.accessToken)
			addJsonContent(
				"""
				{
					"userId": "00000000-0000-0000-0000-000000000001",
					"chain": "SOLANA"
				}
				""".trimIndent(),
			)
		}.andExpect {
			status { isOk() }
			jsonContent(
				"""
				{
					"walletId": "00000000-0000-0000-0000-000000000010",
					"walletAddress": "******************************************",
					"privateKey": "547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6"
				}
				""".trimIndent(),
			)
		}

		verify {
			commandBus(
				CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.SOLANA),
			)
		}
	}

	@Test
	fun `test importUserWallet, should serialize and deserialize correctly`() {
		every { commandBus(any<ImportUserWalletCommand>()) } returns ImportUserWalletCommand.Result(
			walletId = 10.toUUID(),
			walletAddress = AddressWrapper(addressString = "******************************************"),
			privateKey = "547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
		)

		mockMvc.post("/v2/users/me/wallets/import") {
			addAcceptHeader(ApiVersion.VERSION_1_JSON)
			addBearerAuthHeader(user.accessToken)
			addJsonContent(
				"""
				{
					"privateKey": "0x547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
					"chain": "EVM_MAINNET"
				}
				""".trimIndent(),
			)
		}.andExpect {
			status { isOk() }
			jsonContent(
				"""
				{
					"walletId": "00000000-0000-0000-0000-000000000010",
					"walletAddress": "******************************************",
					"privateKey": "547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6"
				}
				""".trimIndent(),
			)
		}

		verify {
			commandBus(
				ImportUserWalletCommand(
					userId = 1.toUUID(),
					privateKey = "0x547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
					chain = Chain.EVM_MAINNET,
				),
			)
		}
	}

	@Test
	fun `test exportWallet, should serialize and deserialize correctly`() {
		every { queryBus(any<ExportUserWalletQuery>()) } returns ExportUserWalletQuery.Result(
			privateKey = "0x547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
		)

		mockMvc.post("/users/me/wallets/00000000-0000-0000-0000-000000000005/export") {
			addAcceptHeader(ApiVersion.VERSION_1_JSON)
			addBearerAuthHeader(user.accessToken)
			addJsonContent(
				"""
				{
					"password": "password"
				}
				""".trimIndent(),
			)
		}.andExpect {
			status { isOk() }
			jsonContent(
				"""
				{
					"privateKey": "0x547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6"
				}
				""".trimIndent(),
			)
		}

		verify {
			queryBus(
				ExportUserWalletQuery(
					userId = 1.toUUID(),
					walletId = 5.toUUID(),
					password = "password",
				),
			)
		}
	}

	@Test
	fun `test deleteWallet, should serialize and deserialize correctly`() {
		every { commandBus(any<DeleteUserWalletCommand>()) } just Runs

		mockMvc.delete("/users/me/wallets/00000000-0000-0000-0000-000000000005") {
			addAcceptHeader(ApiVersion.VERSION_1_JSON)
			addBearerAuthHeader(user.accessToken)
		}.andExpect {
			status { isNoContent() }
		}

		verify {
			commandBus(
				DeleteUserWalletCommand(
					userId = 1.toUUID(),
					walletId = 5.toUUID(),
				),
			)
		}
	}

	@Test
	fun `test patchUserWallet, should serialize and deserialize correctly`() {
		every { commandBus(any<PatchUserWalletCommand>()) } just Runs

		mockMvc.patch("/users/me/wallets/00000000-0000-0000-0000-000000000005") {
			addAcceptHeader(ApiVersion.VERSION_1_JSON)
			addBearerAuthHeader(user.accessToken)
			addJsonContent(
				"""
				{
					"customName": "My WTH wallet",
					"buyAntiMevProtection": true,
					"sellAntiMevProtection": true,
					"antiRug": true
				}
				""".trimIndent(),
			)
		}.andExpect {
			status { isNoContent() }
		}

		verify {
			commandBus(
				PatchUserWalletCommand(
					userId = 1.toUUID(),
					walletId = 5.toUUID(),
					customName = "My WTH wallet",
					buyAntiMevProtection = true,
					sellAntiMevProtection = true,
				),
			)
		}
	}
}
