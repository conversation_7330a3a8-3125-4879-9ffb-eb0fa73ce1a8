package com.cleevio.fatbot.adapter.`in`.rest

import com.cleevio.fatbot.ControllerTest
import com.cleevio.fatbot.adapter.`in`.ApiVersion
import com.cleevio.fatbot.adapter.`in`.rest.dto.PatchBotSettingsRequest
import com.cleevio.fatbot.addAcceptHeader
import com.cleevio.fatbot.addBearerAuthHeader
import com.cleevio.fatbot.addJsonContent
import com.cleevio.fatbot.application.common.util.toUUID
import com.cleevio.fatbot.application.module.bot.command.PatchBotCommand
import com.cleevio.fatbot.toUUID
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.patch
import java.math.BigDecimal
import java.util.Optional

@WebMvcTest(BotController::class)
class BotControllerTest(
	@Autowired private val mockMvc: MockMvc,
) : ControllerTest() {

	@Test
	fun `should correctly deserialize patchBot request`() {
		every { commandBus(any<PatchBotCommand>()) } returns Unit

		mockMvc.patch("/bots/019445cc-7c7a-7de9-a886-61be720faec0") {
			addAcceptHeader(ApiVersion.VERSION_1_JSON)
			addBearerAuthHeader(user.accessToken)
			addJsonContent(
				"""
				{
					"isActive": false,
				    "name" : "testName",
					"marketCapFromUsd": "100.123",
					"marketCapToUsd": null
				}
				""".trimIndent(),
			)
		}.andExpect { status { isNoContent() } }

		verify {
			commandBus(
				PatchBotCommand(
					userId = 1.toUUID(),
					botId = "019445cc-7c7a-7de9-a886-61be720faec0".toUUID(),
					patchRequest = PatchBotSettingsRequest(
						name = "testName",
						marketCapFromUsd = Optional.of(BigDecimal("100.123")),
						marketCapToUsd = Optional.empty(),

						// Defaults
						avatarFileId = null,
						tradeAmount = null,
						buyFrequency = null,
						stopLossFraction = null,
						profitTargetFraction = null,
						liquidityFromUsd = null,
						liquidityToUsd = null,
						buyVolume = null,
						sellVolume = null,
						dailyVolumeFromUsd = null,
						dailyVolumeToUsd = null,
						numberOfHoldersFrom = null,
						numberOfHoldersTo = null,
					),
				),
			)
		}
	}
}
