package com.cleevio.fatbot.adapter.`in`.rest

import com.cleevio.fatbot.ControllerTest
import com.cleevio.fatbot.adapter.`in`.ApiVersion
import com.cleevio.fatbot.adapter.`in`.anonymous.rest.PublicTokenControllerV2
import com.cleevio.fatbot.addAcceptHeader
import com.cleevio.fatbot.addBearerAuthHeader
import com.cleevio.fatbot.addJsonContent
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.token.query.GetContinuousTokenPriceChartQuery
import com.cleevio.fatbot.application.module.token.query.TokenPriceTimeIntervalItem
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.jsonContent
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import java.time.Instant

@WebMvcTest(PublicTokenControllerV2::class)
class TokenControllerTest(
	@Autowired private val mockMvc: MockMvc,
) : ControllerTest() {

	@Test
	fun `test getTokenPriceChart, should serialize and deserialize correctly`() {
		every { queryBus(any<GetContinuousTokenPriceChartQuery>()) } returns GetContinuousTokenPriceChartQuery.Result(
			listOf(
				TokenPriceTimeIntervalItem(
					timestamp = Instant.parse("2024-11-01T10:00:00Z"),
					close = 4.00.toBigDecimal(),
					volume = 5.00.toBigDecimal(),
					closeNative = 6.00.toBigDecimal(),
				),
			),
		)

		mockMvc.post("/public/v2/tokens/0xA43fe16908251ee70EF74718545e4FE6C5cCEc9f/continuous-price-chart") {
			addAcceptHeader(ApiVersion.VERSION_1_JSON)
			addBearerAuthHeader(user.accessToken)
			addJsonContent(
				"""
				{
				    "chain" : "EVM_MAINNET",
					"timeRange": "DAY"
				}
				""".trimIndent(),
			)
		}.andExpect {
			status { isOk() }
			jsonContent(
				"""
				{
					"data": [
						{
							"timestamp": "2024-11-01T10:00:00Z",
							"close": 4.00,
							"volume": 5.00,
							"closeNative": 6.00
						}
					]
				}
				""".trimIndent(),
			)
		}

		verify {
			queryBus(
				GetContinuousTokenPriceChartQuery.withNowItem(
					chain = Chain.EVM_MAINNET,
					pairAddress = AddressWrapper("0xA43fe16908251ee70EF74718545e4FE6C5cCEc9f"),
					timeRange = TimeRange.DAY,
				),
			)
		}
	}
}
