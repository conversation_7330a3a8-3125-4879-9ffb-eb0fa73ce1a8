package com.cleevio.fatbot.application.common

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.platform.commons.annotation.Testable

@Testable
class AddressWrapperTest {

	@Test
	fun `should convert ETH address to byte array and back`() {
		val addressWrapper = AddressWrapper(addressString = "******************************************")
		addressWrapper.getAddressString() shouldBe "******************************************"

		val afterConversion = AddressWrapper(addressBytes = addressWrapper.getAddressBytes())
		afterConversion.getAddressString() shouldBe "******************************************"
	}

	@Test
	fun `should convert ETH address to byte array and back, when whitespace before and after`() {
		val addressWrapper = AddressWrapper(addressString = "   ******************************************     ")
		addressWrapper.getAddressString() shouldBe "******************************************"

		val afterConversion = AddressWrapper(addressBytes = addressWrapper.getAddressBytes())
		afterConversion.getAddressString() shouldBe "******************************************"
	}

	@Test
	fun `should convert SOL address to byte array and back`() {
		val addressWrapper = AddressWrapper(addressString = "HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH")
		addressWrapper.getAddressString() shouldBe "HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH"

		val afterConversion = AddressWrapper(addressBytes = addressWrapper.getAddressBytes())
		afterConversion.getAddressString() shouldBe "HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH"
	}

	@Test
	fun `should convert SOL address to byte array and back, when whitespace before and after`() {
		val addressWrapper = AddressWrapper(addressString = "   HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH     ")
		addressWrapper.getAddressString() shouldBe "HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH"

		val afterConversion = AddressWrapper(addressBytes = addressWrapper.getAddressBytes())
		afterConversion.getAddressString() shouldBe "HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH"
	}

	@Test
	fun `should throw when does not start with leading 0x`() {
		shouldThrow<IllegalArgumentException> {
			AddressWrapper(addressString = "4838B106FCe9647Bdf1E7877BF73cE8B0BAD5f97")
		}
	}

	@Test
	fun `should throw when byte array of incorrect size`() {
		val illegalAddress = "4838B106FCe9647Bdf1E7877BF73cE8B0BAD5f97".encodeToByteArray()
		shouldThrow<IllegalArgumentException> {
			AddressWrapper(addressBytes = illegalAddress)
		}
	}
}
