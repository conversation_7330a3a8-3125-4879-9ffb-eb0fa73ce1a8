package com.cleevio.fatbot.application.common

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.platform.commons.annotation.Testable
import java.math.BigDecimal
import java.math.BigInteger

@Testable
class CryptoMathTest {

	@Test
	fun `test base to native math`() {
		val baseAmount = BaseAmount(BigInteger("1"))

		val solana = Chain.SOLANA
		val eth = Chain.EVM_MAINNET
		val base = Chain.EVM_BASE
		val bsc = Chain.EVM_BSC
		val tokenDecimals = 6

		baseAmount.toNative(solana) shouldBe BigDecimal("0.000000001").asNativeAmount()
		baseAmount.toNative(eth) shouldBe BigDecimal("0.000000000000000001").asNativeAmount()
		baseAmount.toNative(base) shouldBe BigDecimal("0.000000000000000001").asNativeAmount()
		baseAmount.toNative(bsc) shouldBe BigDecimal("0.000000000000000001").asNativeAmount()

		baseAmount.toNative(tokenDecimals) shouldBe BigDecimal("0.000001").asNativeAmount()
	}

	@Test
	fun `test native to base math`() {
		val nativeAmount = NativeAmount(BigDecimal("1.1"))

		val solana = Chain.SOLANA
		val eth = Chain.EVM_MAINNET
		val base = Chain.EVM_BASE
		val bsc = Chain.EVM_BSC
		val tokenDecimals = 6

		nativeAmount.toBase(solana) shouldBe BigInteger("1100000000").asBaseAmount()
		nativeAmount.toBase(eth) shouldBe BigInteger("1100000000000000000").asBaseAmount()
		nativeAmount.toBase(base) shouldBe BigInteger("1100000000000000000").asBaseAmount()
		nativeAmount.toBase(bsc) shouldBe BigInteger("1100000000000000000").asBaseAmount()

		nativeAmount.toBase(tokenDecimals) shouldBe BigInteger("1100000").asBaseAmount()
	}

	@Test
	fun `should cut-off excessive decimals when native amount has exceeding decimals`() {
		val solanaNativeAmount = NativeAmount(BigDecimal("1.0000000001"))
		solanaNativeAmount.toBase(Chain.SOLANA) shouldBe BigInteger("1000000000").asBaseAmount()

		val ethNativeAmount = NativeAmount(BigDecimal("1.0000000000000000001"))
		ethNativeAmount.toBase(Chain.EVM_MAINNET) shouldBe BigInteger("1000000000000000000").asBaseAmount()

		val baseNativeAmount = NativeAmount(BigDecimal("1.0000000000000000001"))
		baseNativeAmount.toBase(Chain.EVM_BASE) shouldBe BigInteger("1000000000000000000").asBaseAmount()

		val bscNativeAmount = NativeAmount(BigDecimal("1.0000000000000000001"))
		bscNativeAmount.toBase(Chain.EVM_BSC) shouldBe BigInteger("1000000000000000000").asBaseAmount()

		val tokenNativeAmount = NativeAmount(BigDecimal("1.0000001"))
		tokenNativeAmount.toBase(decimals = 6) shouldBe BigInteger("1000000").asBaseAmount()

		val tokenNativeAmount2 = NativeAmount(BigDecimal("1.1234567890000"))
		tokenNativeAmount2.toBase(decimals = 6) shouldBe BigInteger("1123456").asBaseAmount()
	}
}
