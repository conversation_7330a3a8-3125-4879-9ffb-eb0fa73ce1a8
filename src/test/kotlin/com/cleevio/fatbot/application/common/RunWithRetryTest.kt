package com.cleevio.fatbot.application.common

import com.cleevio.fatbot.application.common.util.runWithRetry
import io.kotest.matchers.result.shouldBeFailure
import io.kotest.matchers.result.shouldBeSuccess
import io.kotest.matchers.throwable.shouldHaveMessage
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.platform.commons.annotation.Testable
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.toJavaDuration

@Testable
class RunWithRetryTest {

	@Test
	fun `should retry until successful result`() {
		val blockMock = mockk<() -> Int>()
		every { blockMock.invoke() } throwsMany listOf(
			IllegalStateException("Error 1"),
			IllegalStateException("Error 2"),
			IllegalStateException("Error 3"),
		) andThen 42

		val result = runWithRetry(
			retries = 5,
			retryDelay = 1.milliseconds.toJavaDuration(),
			block = { runCatching { blockMock() } },
		)

		verify(exactly = 4) { blockMock() }

		result shouldBeSuccess 42
	}

	@Test
	fun `should retry max amount of times`() {
		val blockMock = mockk<() -> Int>()
		every { blockMock.invoke() } throwsMany listOf(
			IllegalStateException("Error 1"),
			IllegalStateException("Error 2"),
			IllegalStateException("Error 3"),
		) andThen 42

		val result = runWithRetry(
			retries = 1,
			retryDelay = 1.milliseconds.toJavaDuration(),
			block = { runCatching { blockMock() } },
		)

		verify(exactly = 2) { blockMock() }

		result shouldBeFailure { it shouldHaveMessage "Ran out of retries" }
	}

	@Test
	fun `should not retry if successful on first call`() {
		val blockMock = mockk<() -> Int>()
		every { blockMock.invoke() } returns 42

		val result = runWithRetry(
			retries = 5,
			retryDelay = 1.milliseconds.toJavaDuration(),
			block = { runCatching { blockMock() } },
		)

		verify(exactly = 1) { blockMock() }

		result shouldBeSuccess 42
	}
}
