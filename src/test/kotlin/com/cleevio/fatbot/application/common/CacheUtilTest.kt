package com.cleevio.fatbot.application.common

import com.cleevio.fatbot.application.common.util.calculateNumberOfNanosUntilEndOfMinute
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.junit.platform.commons.annotation.Testable
import java.util.stream.Stream
import kotlin.test.assertEquals

@Testable
class CacheUtilTest {

	@ParameterizedTest
	@MethodSource("currentTimeLongAndExpectedNanosProvider")
	fun `test calculateNumberOfNanosUntilEndOfMinute, should calculate correctly`(
		currentTime: Long,
		expectedNanos: Long,
	) {
		assertEquals(expectedNanos, calculateNumberOfNanosUntilEndOfMinute(currentTime))
	}

	companion object {
		@JvmStatic
		fun currentTimeLongAndExpectedNanosProvider(): Stream<Arguments> {
			return Stream.of(
				Arguments.of(
					1736257560000000000, // 2025-01-07T13:46:00.000Z
					60000000000, // 60s
				),
				Arguments.of(
					1736257570000000000, // 2025-01-07T13:46:10.000Z
					50000000000, // 50s
				),
				Arguments.of(
					1736257580000000000, // 2025-01-07T13:46:20.000Z
					40000000000, // 40s
				),
				Arguments.of(
					1736257590000000000, // 2025-01-07T13:46:30.000Z
					30000000000, // 30s
				),
				Arguments.of(
					1736257619000000000, // 2025-01-07T13:46:59.000Z
					**********, // 1s
				),
			)
		}
	}
}
