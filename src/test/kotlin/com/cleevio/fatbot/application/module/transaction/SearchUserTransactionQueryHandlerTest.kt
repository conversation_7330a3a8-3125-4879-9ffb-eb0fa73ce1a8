package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollAsc
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.referral.ReferralRewardService.Companion.ZERO_UUID
import com.cleevio.fatbot.application.module.transaction.constant.BuySellTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailureReason
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.constant.TransferCurrencyTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransferTokenTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.query.SearchUserTransactionQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldNotContain
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import java.net.URI

class SearchUserTransactionQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns BigDecimal("2000.001")
	}

	@Test
	fun `should search all user EVM Mainnet transactions`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = 10.toAddress(),
			beforeSave = {
				patchSettings(
					customName = "rich",
					buyAntiMevProtection = null,
					sellAntiMevProtection = null,
				)
			},
		)
		integrationTestHelper.getWallet(id = 20.toUUID(), userId = 1.toUUID(), address = 20.toAddress())
		integrationTestHelper.getWallet(id = 30.toUUID(), userId = 1.toUUID(), address = 30.toAddress())

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = 666.toAddress(),
			name = "Hot token",
			symbol = "HOTT",
			tokenDecimals = BigInteger.valueOf(18),
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = 999.toAddress(),
			name = "Cold token",
			symbol = "BRRR",
			tokenDecimals = BigInteger.valueOf(9),
		)

		val tx0 = integrationTestHelper.getTransaction(
			id = 0.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000000",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(100),
						amountOut = BigInteger.valueOf(100_000),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("2000.001"),
				)
			},
		)

		val tx1 = integrationTestHelper.getTransaction(
			id = 1.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000100",
			type = TransactionType.BUY,
			entityModifier = { it.markAsFailed(failReason = TransactionFailureReason.UNDEFINED) },
		)

		val tx2 = integrationTestHelper.getTransaction(
			id = 2.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000200",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(200),
						amountOut = BigInteger.valueOf(200_000),
						fee = BigInteger.valueOf(2),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("3000.001"),
				)
			},
		)

		val tx3 = integrationTestHelper.getTransaction(
			id = 3.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000300",
			type = TransactionType.BUY,
		)

		val tx4 = integrationTestHelper.getTransaction(
			id = 4.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000400",
			type = TransactionType.APPROVE,
			entityModifier = { it.markAsSuccess() },
		)

		val tx5 = integrationTestHelper.getTransaction(
			id = 5.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000500",
			type = TransactionType.SELL,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(50_000),
						amountOut = BigInteger.valueOf(50),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("1000.001"),
				)
			},
		)

		val tx6 = integrationTestHelper.getTransaction(
			id = 6.toUUID(),
			walletId = 30.toUUID(),
			tokenAddress = 999.toAddress(),
			signedTx = "0x000000000600",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(800),
						amountOut = BigInteger.valueOf(800_000),
						fee = BigInteger.valueOf(8),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("2500.001"),
				)
			},
		)

		val resultFirstSlice = queryBus(
			SearchUserTransactionQuery.ofAllTokens(
				userId = 1.toUUID(),
				useSelectedChains = true,
				walletId = null,
				searchString = null,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 2, lastId = null),
				allowedStatuses = setOf(TransactionStatus.PENDING, TransactionStatus.SUCCESS, TransactionStatus.FAILED),
			),
		)

		val resultNextSlice = queryBus(
			SearchUserTransactionQuery.ofAllTokens(
				userId = 1.toUUID(),
				useSelectedChains = true,
				walletId = null,
				searchString = null,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = resultFirstSlice.lastId),
				allowedStatuses = setOf(TransactionStatus.PENDING, TransactionStatus.SUCCESS, TransactionStatus.FAILED),
			),
		)

		val resultLastSlice = queryBus(
			SearchUserTransactionQuery.ofAllTokens(
				userId = 1.toUUID(),
				useSelectedChains = true,
				walletId = null,
				searchString = null,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = resultNextSlice.lastId),
				allowedStatuses = setOf(TransactionStatus.PENDING, TransactionStatus.SUCCESS, TransactionStatus.FAILED),
			),
		)

		resultFirstSlice.content.size shouldBe 2
		resultFirstSlice.lastId shouldBe tx5.id
		resultFirstSlice.hasMore shouldBe true

		with(resultFirstSlice.content[0]) {
			txId shouldBe tx6.id
			txHash shouldBe tx6.txHash
			txDetailUrl shouldBe URI.create("https://etherscan.io/tx/${tx6.txHash!!.txHash}")
			transactionType shouldBe TransactionType.BUY
			transactionStatus shouldBe TransactionStatus.SUCCESS
			walletId shouldBe 30.toUUID()
			walletCustomName shouldBe "ETH wallet 1"
			createdAt shouldBe tx6.createdAt
			tokenAddress shouldBe 999.toAddress()
			tokenName shouldBe "Cold token"
			tokenSymbol shouldBe "BRRR"
			tokenNativeAmount shouldBe NativeAmount(BigDecimal("0.000800000"))
			currencyNativeAmount shouldBe NativeAmount(BigDecimal("0.000000000000000800"))
			currencyAmountUsd shouldBe BigDecimal("0.000000000002000000800")
			transactionChain shouldBe Chain.EVM_MAINNET
		}

		with(resultFirstSlice.content[1]) {
			txId shouldBe tx5.id
			txHash shouldBe tx5.txHash
			txDetailUrl shouldBe URI.create("https://etherscan.io/tx/${tx5.txHash!!.txHash}")
			transactionType shouldBe TransactionType.SELL
			transactionStatus shouldBe TransactionStatus.SUCCESS
			walletId shouldBe 10.toUUID()
			walletCustomName shouldBe "rich"
			createdAt shouldBe tx5.createdAt
			tokenAddress shouldBe 666.toAddress()
			tokenName shouldBe "Hot token"
			tokenSymbol shouldBe "HOTT"
			tokenNativeAmount shouldBe NativeAmount(BigDecimal("0.000000000000050000"))
			currencyNativeAmount shouldBe NativeAmount(BigDecimal("0.000000000000000050"))
			currencyAmountUsd shouldBe BigDecimal("0.000000000000050000050")
			transactionChain shouldBe Chain.EVM_MAINNET
		}

		resultNextSlice.content.size shouldBe 4
		resultNextSlice.lastId shouldBe tx0.id
		resultNextSlice.hasMore shouldBe false

		// tx4 is of type approve, that should be filtered out
		resultNextSlice.content.map { it.txId } shouldNotContain tx4.id

		resultNextSlice.content[0].txId shouldBe tx3.id
		resultNextSlice.content[1].txId shouldBe tx2.id
		resultNextSlice.content[2].txId shouldBe tx1.id
		resultNextSlice.content[3].txId shouldBe tx0.id

		resultLastSlice.content.size shouldBe 0
		resultLastSlice.lastId shouldBe null
		resultLastSlice.hasMore shouldBe false
	}

	@Test
	fun `should search user EVM mainnet transfer token transaction`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 10.toUUID(), userId = 1.toUUID(), address = 10.toAddress())
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = 666.toAddress(),
			name = "Hot token",
			symbol = "HOTT",
			tokenDecimals = BigInteger.valueOf(18),
		)
		val tx1 = integrationTestHelper.getTransaction(
			id = 1.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000001",
			type = TransactionType.TRANSFER_TOKEN,
			entityModifier = {
				it.markAsSuccess(
					result = TransferTokenTransactionSuccess(
						amountIn = BigInteger.valueOf(100_000_000),
						amountOut = BigInteger.valueOf(100_000_000_000_000),
					),
					exchangeRateUsd = BigDecimal("2000.001"),
				)
			},
		)

		val result = queryBus(
			SearchUserTransactionQuery.ofAllTokens(
				userId = 1.toUUID(),
				useSelectedChains = true,
				walletId = null,
				searchString = null,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				allowedStatuses = setOf(TransactionStatus.PENDING, TransactionStatus.SUCCESS, TransactionStatus.FAILED),
			),
		)

		result.content.size shouldBe 1
		result.lastId shouldBe tx1.id
		result.hasMore shouldBe false

		with(result.content[0]) {
			txId shouldBe tx1.id
			txHash shouldBe tx1.txHash
			txDetailUrl shouldBe URI.create("https://etherscan.io/tx/${tx1.txHash!!.txHash}")
			transactionType shouldBe TransactionType.TRANSFER_TOKEN
			transactionStatus shouldBe TransactionStatus.SUCCESS
			walletId shouldBe 10.toUUID()
			walletCustomName shouldBe "ETH wallet 1"
			createdAt shouldBe tx1.createdAt
			tokenAddress shouldBe 666.toAddress()
			tokenName shouldBe "Hot token"
			tokenSymbol shouldBe "HOTT"
			tokenNativeAmount shouldBe NativeAmount(BigDecimal("0.000100000000000000"))
			currencyNativeAmount shouldBe NativeAmount(BigDecimal("0.000000000100000000"))
			currencyAmountUsd!!.shouldBeEqualComparingTo(BigDecimal("0.000000200000100000"))
			transactionChain shouldBe Chain.EVM_MAINNET
		}
	}

	@Test
	fun `should search user EVM Mainnet transfer ETH transaction`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 10.toUUID(), userId = 1.toUUID(), address = 10.toAddress())

		val tx1 = integrationTestHelper.getTransaction(
			id = 1.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = null,
			signedTx = "0x000000000001",
			type = TransactionType.TRANSFER_CURRENCY,
			entityModifier = {
				it.markAsSuccess(
					TransferCurrencyTransactionSuccess(
						value = BigInteger.valueOf(100_000_000),
						fee = BigInteger.valueOf(5_000),
					),
					exchangeRateUsd = BigDecimal("2000.001"),
				)
			},
		)

		val result = queryBus(
			SearchUserTransactionQuery.ofAllTokens(
				userId = 1.toUUID(),
				useSelectedChains = true,
				walletId = null,
				searchString = null,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				allowedStatuses = setOf(TransactionStatus.PENDING, TransactionStatus.SUCCESS, TransactionStatus.FAILED),
			),
		)

		result.content.size shouldBe 1
		result.lastId shouldBe tx1.id
		result.hasMore shouldBe false

		with(result.content[0]) {
			txId shouldBe tx1.id
			txHash shouldBe tx1.txHash
			txDetailUrl shouldBe URI.create("https://etherscan.io/tx/${tx1.txHash!!.txHash}")
			transactionType shouldBe TransactionType.TRANSFER_CURRENCY
			transactionStatus shouldBe TransactionStatus.SUCCESS
			walletId shouldBe 10.toUUID()
			walletCustomName shouldBe "ETH wallet 1"
			createdAt shouldBe tx1.createdAt
			tokenAddress shouldBe null
			tokenName shouldBe null
			tokenSymbol shouldBe null
			tokenNativeAmount shouldBe null
			currencyNativeAmount shouldBe NativeAmount(BigDecimal("0.000000000100000000"))
			currencyAmountUsd!!.shouldBeEqualComparingTo(BigDecimal("0.000000200000100000"))
			transactionChain shouldBe Chain.EVM_MAINNET
		}
	}

	@Test
	fun `should search all user EVM Mainnet transactions of specific token`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = 10.toAddress(),
			beforeSave = {
				patchSettings(
					customName = "rich",
					buyAntiMevProtection = null,
					sellAntiMevProtection = null,
				)
			},
		)
		integrationTestHelper.getWallet(id = 20.toUUID(), userId = 1.toUUID(), address = 20.toAddress())
		integrationTestHelper.getWallet(id = 30.toUUID(), userId = 1.toUUID(), address = 30.toAddress())

		integrationTestHelper.getEvmTokenInfo(tokenAddress = 666.toAddress())
		integrationTestHelper.getEvmTokenInfo(tokenAddress = 999.toAddress())

		val tx0 = integrationTestHelper.getTransaction(
			id = 0.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000000",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(100),
						amountOut = BigInteger.valueOf(100_000),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("2000.001"),
				)
			},
		)

		val tx1 = integrationTestHelper.getTransaction(
			id = 1.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000100",
			type = TransactionType.BUY,
			entityModifier = { it.markAsFailed(failReason = TransactionFailureReason.UNDEFINED) },
		)

		val tx2 = integrationTestHelper.getTransaction(
			id = 2.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000200",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(200),
						amountOut = BigInteger.valueOf(200_000),
						fee = BigInteger.valueOf(2),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("3000.001"),
				)
			},
		)

		val tx3 = integrationTestHelper.getTransaction(
			id = 3.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000300",
			type = TransactionType.BUY,
		)

		val tx4 = integrationTestHelper.getTransaction(
			id = 4.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000400",
			type = TransactionType.APPROVE,
			entityModifier = { it.markAsSuccess() },
		)

		val tx5 = integrationTestHelper.getTransaction(
			id = 5.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000500",
			type = TransactionType.SELL,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(50_000),
						amountOut = BigInteger.valueOf(50),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("1000.001"),
				)
			},
		)

		val tx6 = integrationTestHelper.getTransaction(
			id = 6.toUUID(),
			walletId = 30.toUUID(),
			tokenAddress = 999.toAddress(),
			signedTx = "0x000000000600",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(800),
						amountOut = BigInteger.valueOf(800_000),
						fee = BigInteger.valueOf(8),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("2500.001"),
				)
			},
		)

		val resultDesc = queryBus(
			SearchUserTransactionQuery.ofSpecificToken(
				chain = Chain.EVM_MAINNET,
				userId = 1.toUUID(),
				walletId = null,
				tokenAddress = 666.toAddress(),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				allowedStatuses = setOf(TransactionStatus.PENDING, TransactionStatus.SUCCESS, TransactionStatus.FAILED),
			),
		)

		val resultAsc = queryBus(
			SearchUserTransactionQuery.ofSpecificToken(
				chain = Chain.EVM_MAINNET,
				userId = 1.toUUID(),
				walletId = null,
				tokenAddress = 666.toAddress(),
				infiniteScroll = InfiniteScrollAsc.UUID(size = 10, lastId = null),
				allowedStatuses = setOf(TransactionStatus.PENDING, TransactionStatus.SUCCESS, TransactionStatus.FAILED),
			),
		)

		resultDesc.content.size shouldBe 5
		resultDesc.lastId shouldBe tx0.id
		resultDesc.hasMore shouldBe false

		resultDesc.content[0].txId shouldBe tx5.id
		resultDesc.content[1].txId shouldBe tx3.id
		resultDesc.content[2].txId shouldBe tx2.id
		resultDesc.content[3].txId shouldBe tx1.id
		resultDesc.content[4].txId shouldBe tx0.id

		resultAsc.content.size shouldBe 5
		resultAsc.lastId shouldBe tx5.id
		resultAsc.hasMore shouldBe false

		resultAsc.content[0].txId shouldBe tx0.id
		resultAsc.content[1].txId shouldBe tx1.id
		resultAsc.content[2].txId shouldBe tx2.id
		resultAsc.content[3].txId shouldBe tx3.id
		resultAsc.content[4].txId shouldBe tx5.id
	}

	@Test
	fun `should search last user EVM Mainnet transactions of specific token`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = 10.toAddress(),
			beforeSave = {
				patchSettings(
					customName = "rich",
					buyAntiMevProtection = null,
					sellAntiMevProtection = null,
				)
			},
		)
		integrationTestHelper.getWallet(id = 20.toUUID(), userId = 1.toUUID(), address = 20.toAddress())
		integrationTestHelper.getWallet(id = 30.toUUID(), userId = 1.toUUID(), address = 30.toAddress())

		integrationTestHelper.getEvmTokenInfo(chain = Chain.EVM_MAINNET, tokenAddress = 666.toAddress())

		integrationTestHelper.getEvmTokenInfo(tokenAddress = 999.toAddress())

		val tx0 = integrationTestHelper.getTransaction(
			id = 0.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000000",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(100),
						amountOut = BigInteger.valueOf(100_000),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("2000.001"),
				)
			},
		)

		val tx1 = integrationTestHelper.getTransaction(
			id = 1.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000100",
			type = TransactionType.BUY,
			entityModifier = { it.markAsFailed(failReason = TransactionFailureReason.UNDEFINED) },
		)

		val tx2 = integrationTestHelper.getTransaction(
			id = 2.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000200",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(200),
						amountOut = BigInteger.valueOf(200_000),
						fee = BigInteger.valueOf(2),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("3000.001"),
				)
			},
		)

		val tx3 = integrationTestHelper.getTransaction(
			id = 3.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000300",
			type = TransactionType.BUY,
		)

		val tx4 = integrationTestHelper.getTransaction(
			id = 4.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000400",
			type = TransactionType.APPROVE,
			entityModifier = { it.markAsSuccess() },
		)

		val tx5 = integrationTestHelper.getTransaction(
			id = 5.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000500",
			type = TransactionType.SELL,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(50_000),
						amountOut = BigInteger.valueOf(50),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("1000.001"),
				)
			},
		)

		val tx6 = integrationTestHelper.getTransaction(
			id = 6.toUUID(),
			walletId = 30.toUUID(),
			tokenAddress = 999.toAddress(),
			signedTx = "0x000000000600",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(800),
						amountOut = BigInteger.valueOf(800_000),
						fee = BigInteger.valueOf(8),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("2500.001"),
				)
			},
		)

		val result = queryBus(
			SearchUserTransactionQuery.ofSpecificTokenLastFive(
				chain = Chain.EVM_MAINNET,
				userId = 1.toUUID(),
				tokenAddress = 666.toAddress(),
			),
		)

		result.content.size shouldBe 5
		result.lastId shouldBe tx0.id
		result.hasMore shouldBe true

		// tx4 is of type approve, that should be filtered out
		result.content.map { it.txId } shouldNotContain tx4.id

		result.content[0].txId shouldBe tx5.id
		result.content[1].txId shouldBe tx3.id
		result.content[2].txId shouldBe tx2.id
		result.content[3].txId shouldBe tx1.id
		result.content[4].txId shouldBe tx0.id
	}

	@Test
	fun `should search all user EVM Mainnet transactions with status pending`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = 10.toAddress(),
			beforeSave = {
				patchSettings(
					customName = "rich",
					buyAntiMevProtection = null,
					sellAntiMevProtection = null,
				)
			},
		)
		integrationTestHelper.getWallet(id = 20.toUUID(), userId = 1.toUUID(), address = 20.toAddress())
		integrationTestHelper.getWallet(id = 30.toUUID(), userId = 1.toUUID(), address = 30.toAddress())

		integrationTestHelper.getEvmTokenInfo(tokenAddress = 666.toAddress())
		integrationTestHelper.getEvmTokenInfo(tokenAddress = 999.toAddress())

		val tx0 = integrationTestHelper.getTransaction(
			id = 1000.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000000",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(100),
						amountOut = BigInteger.valueOf(100_000),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("2000.001"),
				)
			},
		)

		val tx1 = integrationTestHelper.getTransaction(
			id = 1001.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000100",
			type = TransactionType.BUY,
			entityModifier = { it.markAsFailed(failReason = TransactionFailureReason.UNDEFINED) },
		)

		val tx2 = integrationTestHelper.getTransaction(
			id = 1002.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000200",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(200),
						amountOut = BigInteger.valueOf(200_000),
						fee = BigInteger.valueOf(2),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("3000.001"),
				)
			},
		)

		val tx3 = integrationTestHelper.getTransaction(
			id = 1003.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000300",
			type = TransactionType.BUY,
			entityModifier = { it.markAsSuccess() },
		)

		val tx4 = integrationTestHelper.getTransaction(
			id = 1004.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000400",
			type = TransactionType.APPROVE,
			status = TransactionStatus.PENDING,
		)

		val tx5 = integrationTestHelper.getTransaction(
			id = 1005.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000500",
			type = TransactionType.SELL,
			status = TransactionStatus.PENDING,
		)

		val tx6 = integrationTestHelper.getTransaction(
			id = 1006.toUUID(),
			walletId = 30.toUUID(),
			tokenAddress = 999.toAddress(),
			signedTx = "0x000000000600",
			type = TransactionType.BUY,
			entityModifier = { it.markAsFailed(failReason = TransactionFailureReason.UNDEFINED) },
		)

		val result = queryBus(
			SearchUserTransactionQuery.ofAllTokens(
				userId = 1.toUUID(),
				useSelectedChains = true,
				walletId = null,
				searchString = null,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				allowedStatuses = setOf(TransactionStatus.PENDING),
			),
		)

		result.content.size shouldBe 1
		result.lastId shouldBe tx5.id
		result.hasMore shouldBe false

		result.content[0].txId shouldBe tx5.id
	}

	@Test
	fun `should search all user EVM Mainnet transactions based on search string`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = 10.toAddress(),
			beforeSave = {
				patchSettings(
					customName = "rich",
					buyAntiMevProtection = null,
					sellAntiMevProtection = null,
				)
			},
		)
		integrationTestHelper.getWallet(id = 20.toUUID(), userId = 1.toUUID(), address = 20.toAddress())
		integrationTestHelper.getWallet(id = 30.toUUID(), userId = 1.toUUID(), address = 30.toAddress())

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = 666.toAddress(),
			name = "Hot token",
			symbol = "HOTT",
			tokenDecimals = BigInteger.valueOf(18),
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = 999.toAddress(),
			name = "Cold token",
			symbol = "BRRR",
			tokenDecimals = BigInteger.valueOf(9),
		)

		val tx0 = integrationTestHelper.getTransaction(
			id = 1000.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000000",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(100),
						amountOut = BigInteger.valueOf(100_000),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("2000.001"),
				)
			},
		)

		val tx1 = integrationTestHelper.getTransaction(
			id = 1001.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000100",
			type = TransactionType.BUY,
			entityModifier = { it.markAsFailed(failReason = TransactionFailureReason.UNDEFINED) },
		)

		val tx2 = integrationTestHelper.getTransaction(
			id = 1002.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000200",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(200),
						amountOut = BigInteger.valueOf(200_000),
						fee = BigInteger.valueOf(2),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("3000.001"),
				)
			},
		)

		val tx3 = integrationTestHelper.getTransaction(
			id = 1003.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000300",
			type = TransactionType.BUY,
		)

		val tx4 = integrationTestHelper.getTransaction(
			id = 1004.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000400",
			type = TransactionType.APPROVE,
			entityModifier = { it.markAsSuccess() },
		)

		val tx5 = integrationTestHelper.getTransaction(
			id = 1005.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 666.toAddress(),
			signedTx = "0x000000000500",
			type = TransactionType.SELL,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(50_000),
						amountOut = BigInteger.valueOf(50),
						fee = BigInteger.valueOf(1),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("1000.001"),
				)
			},
		)

		val tx6 = integrationTestHelper.getTransaction(
			id = 1006.toUUID(),
			walletId = 30.toUUID(),
			tokenAddress = 999.toAddress(),
			signedTx = "0x000000000600",
			type = TransactionType.BUY,
			entityModifier = {
				it.markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(800),
						amountOut = BigInteger.valueOf(800_000),
						fee = BigInteger.valueOf(8),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("2500.001"),
				)
			},
		)

		val result = queryBus(
			SearchUserTransactionQuery.ofAllTokens(
				userId = 1.toUUID(),
				useSelectedChains = true,
				walletId = null,
				searchString = "oT", // searching for HOTT
				infiniteScroll = InfiniteScrollDesc.UUID(size = 2, lastId = null),
				allowedStatuses = setOf(TransactionStatus.SUCCESS),
			),
		)

		result.content.size shouldBe 2
		result.lastId shouldBe tx2.id
		result.hasMore shouldBe true

		result.content[0].txId shouldBe tx5.id
		result.content[1].txId shouldBe tx2.id

		val nextSliceResult = queryBus(
			SearchUserTransactionQuery.ofAllTokens(
				userId = 1.toUUID(),
				useSelectedChains = true,
				walletId = null,
				searchString = "oT", // searching for HOTT
				infiniteScroll = InfiniteScrollDesc.UUID(size = 2, lastId = result.lastId),
				allowedStatuses = setOf(TransactionStatus.SUCCESS),
			),
		)

		nextSliceResult.content.size shouldBe 1
		nextSliceResult.lastId shouldBe tx0.id
		nextSliceResult.hasMore shouldBe false

		nextSliceResult.content[0].txId shouldBe tx0.id
	}

	@Test
	fun `should return tx only once if token on multiple chains`() {
		// given
		integrationTestHelper.getFirebaseUser(
			id = 1.toUUID(),
			selectedChains = setOf(Chain.EVM_MAINNET, Chain.EVM_BSC),
		)

		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = 10.toAddress(),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getWallet(
			id = 20.toUUID(),
			userId = 1.toUUID(),
			address = 20.toAddress(),
			chain = Chain.EVM_BSC,
		)
		integrationTestHelper.getWallet(
			id = 30.toUUID(),
			userId = 1.toUUID(),
			address = 30.toAddress(),
			chain = Chain.EVM_ARBITRUM_ONE,
		)

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = 111.toAddress(),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = 111.toAddress(),
			chain = Chain.EVM_BSC,
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = 111.toAddress(),
			chain = Chain.EVM_ARBITRUM_ONE,
		)

		integrationTestHelper.getTransaction(
			id = 1000.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = 111.toAddress(),
			chainId = Chain.EVM_MAINNET.evmId,
		)

		// when
		val result = queryBus(
			SearchUserTransactionQuery.ofAllTokens(
				userId = 1.toUUID(),
				useSelectedChains = true,
				walletId = null,
				searchString = null,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				allowedStatuses = setOf(TransactionStatus.PENDING),
			),
		)

		// then
		result.content.size shouldBe 1
		with(result.content.first()) {
			tokenAddress shouldBe 111.toAddress()
			transactionChain shouldBe Chain.EVM_MAINNET
		}
	}

	private fun setupSelectedChains(selectedChains: Set<Chain>) {
		integrationTestHelper.getFirebaseUser(
			id = 1.toUUID(),
			selectedChains = selectedChains,
		)

		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = 10.toAddress(),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getWallet(
			id = 20.toUUID(),
			userId = 1.toUUID(),
			address = 20.toAddress(),
			chain = Chain.EVM_BSC,
		)
		integrationTestHelper.getWallet(
			id = 30.toUUID(),
			userId = 1.toUUID(),
			address = 30.toAddress(),
			chain = Chain.EVM_ARBITRUM_ONE,
		)

		integrationTestHelper.getEvmTokenInfo(
			id = 100.toUUID(),
			tokenAddress = "aaa".toAddress(),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getEvmTokenInfo(
			id = 101.toUUID(),
			tokenAddress = "bbb".toAddress(),
			chain = Chain.EVM_BSC,
		)
		integrationTestHelper.getEvmTokenInfo(
			id = 102.toUUID(),
			tokenAddress = "ccc".toAddress(),
			chain = Chain.EVM_ARBITRUM_ONE,
		)

		integrationTestHelper.getTransaction(
			id = 1000.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = "aaa".toAddress(),
			chainId = Chain.EVM_MAINNET.evmId,
		)

		integrationTestHelper.getTransaction(
			id = 1001.toUUID(),
			walletId = 20.toUUID(),
			tokenAddress = "bbb".toAddress(),
			chainId = Chain.EVM_BSC.evmId,
			signedTx = "signedTx1",
		)

		integrationTestHelper.getTransaction(
			id = 1002.toUUID(),
			walletId = 30.toUUID(),
			tokenAddress = "ccc".toAddress(),
			chainId = Chain.EVM_ARBITRUM_ONE.evmId,
			signedTx = "signedTx2",
		)
	}

	@Test
	fun `should search on multiple user selected chains`() {
		setupSelectedChains(selectedChains = setOf(Chain.EVM_MAINNET, Chain.EVM_BSC))

		val result = queryBus(
			SearchUserTransactionQuery.ofAllTokens(
				userId = 1.toUUID(),
				useSelectedChains = true,
				walletId = null,
				searchString = null,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				allowedStatuses = TransactionStatus.entries.toSet(),
			),
		)

		result.content.size shouldBe 2

		with(result.content[0]) {
			tokenAddress shouldBe "bbb".toAddress()
			transactionChain shouldBe Chain.EVM_BSC
		}

		with(result.content[1]) {
			tokenAddress shouldBe "aaa".toAddress()
			transactionChain shouldBe Chain.EVM_MAINNET
		}
	}

	@Test
	fun `should search on one user selected chain`() {
		setupSelectedChains(selectedChains = setOf(Chain.EVM_ARBITRUM_ONE))

		val result = queryBus(
			SearchUserTransactionQuery.ofAllTokens(
				userId = 1.toUUID(),
				useSelectedChains = true,
				walletId = null,
				searchString = null,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				allowedStatuses = TransactionStatus.entries.toSet(),
			),
		)

		result.content.size shouldBe 1

		with(result.content[0]) {
			tokenAddress shouldBe "ccc".toAddress()
			transactionChain shouldBe Chain.EVM_ARBITRUM_ONE
		}
	}

	@Test
	fun `should search on all chains regardless of user selected chains`() {
		setupSelectedChains(selectedChains = setOf(Chain.EVM_MAINNET))

		val result = queryBus(
			SearchUserTransactionQuery.ofAllTokens(
				userId = 1.toUUID(),
				useSelectedChains = false,
				walletId = null,
				searchString = null,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				allowedStatuses = TransactionStatus.entries.toSet(),
			),
		)

		result.content.size shouldBe 3

		with(result.content[0]) {
			tokenAddress shouldBe "ccc".toAddress()
			transactionChain shouldBe Chain.EVM_ARBITRUM_ONE
		}

		with(result.content[1]) {
			tokenAddress shouldBe "bbb".toAddress()
			transactionChain shouldBe Chain.EVM_BSC
		}

		with(result.content[2]) {
			tokenAddress shouldBe "aaa".toAddress()
			transactionChain shouldBe Chain.EVM_MAINNET
		}
	}
}
