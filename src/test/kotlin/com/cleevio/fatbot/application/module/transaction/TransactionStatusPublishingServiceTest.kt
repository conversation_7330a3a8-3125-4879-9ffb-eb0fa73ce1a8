package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.transaction.port.out.model.TransactionStatusModel
import com.cleevio.fatbot.domain.transaction.Transaction
import com.cleevio.fatbot.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class TransactionStatusPublishingServiceTest(
	@Autowired private val underTest: TransactionStatusPublishingService,
) : IntegrationTest() {

	@Test
	fun `should publish transaction statuses to correct users`() {
		val user1 = integrationTestHelper.getFirebaseUser(id = 1.toUUID(), email = "<EMAIL>")
		val user2 = integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")
		val user3 = integrationTestHelper.getFirebaseUser(id = 3.toUUID(), email = "<EMAIL>")
		val wallet1 = integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = user1.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		val wallet2 = integrationTestHelper.getWallet(
			id = 11.toUUID(),
			userId = user2.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		val wallet3 = integrationTestHelper.getWallet(
			id = 12.toUUID(),
			userId = user3.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		val wallet4 = integrationTestHelper.getWallet(
			id = 13.toUUID(),
			userId = user3.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		val wallet5 = integrationTestHelper.getWallet(
			id = 14.toUUID(),
			userId = user3.id,
			address = AddressWrapper(addressString = "******************************************"),
		)

		val transaction1 = integrationTestHelper.getTransaction(
			id = 20.toUUID(),
			walletId = wallet1.id,
			signedTx = "dummySignedTx1",
		)
		val transaction2 = integrationTestHelper.getTransaction(
			id = 21.toUUID(),
			walletId = wallet2.id,
			signedTx = "dummySignedTx2",
		)
		val transaction3 = integrationTestHelper.getTransaction(
			id = 22.toUUID(),
			walletId = wallet2.id,
			signedTx = "dummySignedTx3",
		)
		val transaction4 = integrationTestHelper.getTransaction(
			id = 23.toUUID(),
			walletId = wallet2.id,
			signedTx = "dummySignedTx4",
		)
		val transaction5 = integrationTestHelper.getTransaction(
			id = 24.toUUID(),
			walletId = wallet3.id,
			signedTx = "dummySignedTx5",
		)
		val transaction6 = integrationTestHelper.getTransaction(
			id = 25.toUUID(),
			walletId = wallet3.id,
			signedTx = "dummySignedTx6",
		)
		val transaction7 = integrationTestHelper.getTransaction(
			id = 26.toUUID(),
			walletId = wallet3.id,
			signedTx = "dummySignedTx7",
		)
		val transaction8 = integrationTestHelper.getTransaction(
			id = 27.toUUID(),
			walletId = wallet4.id,
			signedTx = "dummySignedTx8",
		)
		val transaction9 = integrationTestHelper.getTransaction(
			id = 28.toUUID(),
			walletId = wallet5.id,
			signedTx = "dummySignedTx9",
		)

		every { publishUserTransactionStatusChanged(any(), any()) } just Runs

		underTest.publishTransactionsToUsers(
			listOf(
				transaction1.toTransactionStatusModel(),
				transaction2.toTransactionStatusModel(),
				transaction3.toTransactionStatusModel(),
				transaction4.toTransactionStatusModel(),
				transaction5.toTransactionStatusModel(),
				transaction6.toTransactionStatusModel(),
				transaction7.toTransactionStatusModel(),
				transaction8.toTransactionStatusModel(),
			),
		)

		verify {
			publishUserTransactionStatusChanged(
				userId = user1.id,
				transactions = listOf(
					transaction1.toTransactionStatusModel(),
				),
			)
			publishUserTransactionStatusChanged(
				userId = user2.id,
				transactions = listOf(
					transaction2.toTransactionStatusModel(),
					transaction3.toTransactionStatusModel(),
					transaction4.toTransactionStatusModel(),
				),
			)
			publishUserTransactionStatusChanged(
				userId = user3.id,
				transactions = listOf(
					transaction5.toTransactionStatusModel(),
					transaction6.toTransactionStatusModel(),
					transaction7.toTransactionStatusModel(),
					transaction8.toTransactionStatusModel(),
				),
			)
		}
	}
}

private fun Transaction.toTransactionStatusModel() = TransactionStatusModel(
	transactionId = this.id,
	transactionStatus = this.status,
	txHash = this.txHash!!,
	walletId = this.walletId,
)
