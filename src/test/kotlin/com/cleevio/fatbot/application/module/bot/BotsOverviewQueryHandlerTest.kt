package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.out.evm.GetTokenPricesEVM
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.bot.query.BotsOverviewQuery
import com.cleevio.fatbot.application.module.bot.query.CompareBotsQuery
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.getPrivatePropertyOfProxy
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION
import com.cleevio.fatbot.toUUID
import com.github.benmanes.caffeine.cache.AsyncCache
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.jooq.DSLContext
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.Test

class BotsOverviewQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val clock: IntegrationTestClock,
	@Autowired private val dslContext: DSLContext,
) : IntegrationTest() {

	@Test
	fun `should calculate profitable and loss transactions only from closed positions`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getFile(id = 31.toUUID())

		integrationTestHelper.getBot(
			id = 100.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 31.toUUID(),
		)

		integrationTestHelper.getBotWallet(
			id = 200.toUUID(),
			botId = 100.toUUID(),
		)

		integrationTestHelper.getFile(id = 21.toUUID())
		integrationTestHelper.getFile(id = 22.toUUID())
		integrationTestHelper.getFile(id = 23.toUUID())

		val tokenAddress1 = AddressWrapper("FYxc7ntfTjV2c86oF4dDp8yYiyNLAR5rdhFhNGSMpump")
		val tokenAddress2 = AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump")
		val tokenAddress3 = AddressWrapper("HTYLJ5QTnq28HFUWQuBPKdc4RJu6JGb5ucesqAt3xTt8")

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress1,
			name = "ADHD",
			symbol = "ADHD",
			imageFileId = 21.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress2,
			name = "yolo",
			symbol = "yolo",
			imageFileId = 22.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress3,
			name = "PumpFunAss",
			symbol = "PumpFunAss",
			imageFileId = 23.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 400.toUUID(),
			botWalletId = 200.toUUID(),
			tokenAddress = tokenAddress1,
			profitTargetFraction = BigDecimal("20.0"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234567,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "50".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 401.toUUID(),
			botWalletId = 200.toUUID(),
			tokenAddress = tokenAddress2,
			profitTargetFraction = BigDecimal("31.83"),
			stopLossFraction = BigDecimal("21.22"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234568,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "15".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
				setSellDataFromChain(
					amountOfTokensSold = "15".toBigInteger(),
					amountOfBaseCurrencyReceived = "20".toBigInteger(),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 402.toUUID(),
			botWalletId = 200.toUUID(),
			tokenAddress = tokenAddress3,
			profitTargetFraction = BigDecimal("23.3"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234569,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "25".toBigInteger(),
					amountIn = "20".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
				setSellDataFromChain(
					amountOfTokensSold = "25".toBigInteger(),
					amountOfBaseCurrencyReceived = "15".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
					fee = BigInteger("1"),
				)
			},
		)

		// Quick-fix for clock not being used for createdAt/updatedAt timestamp on JPA entities
		dslContext.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.CREATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))
			.set(BOT_MARKET_POSITION.UPDATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))
			.execute()

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress1,
			chain = Chain.SOLANA,
			priceWei = BigInteger("16"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress2,
			chain = Chain.SOLANA,
			priceWei = BigInteger("26"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress3,
			chain = Chain.SOLANA,
			priceWei = BigInteger("36"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("3.5")

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(tokenAddress1.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("100")))
					put(tokenAddress2.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("200")))
					put(tokenAddress3.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("300")))
				},
			)

		// when
		val result = queryBus(
			BotsOverviewQuery(
				userId = 1.toUUID(),
				filter = BotsOverviewQuery.Filter(
					timeRange = TimeRange.WEEK,
				),
			),
		)

		// then
		result.run {
			botsProfitableTransactionsCount shouldBe 1
			botsLossTransactionsCount shouldBe 1
			botsTransactionsCount shouldBe 5
			botsBuyTransactionsCount shouldBe 3
		}
	}

	@Test
	fun `should return portfolio overview only for user's bots`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		integrationTestHelper.getFile(id = 31.toUUID())
		integrationTestHelper.getFile(id = 32.toUUID())
		integrationTestHelper.getFile(id = 33.toUUID())

		integrationTestHelper.getBot(
			id = 100.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 31.toUUID(),
		)
		integrationTestHelper.getBot(
			id = 101.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 32.toUUID(),
		)
		integrationTestHelper.getBot(
			id = 102.toUUID(),
			userId = 2.toUUID(),
			avatarFileId = 33.toUUID(),
		)

		integrationTestHelper.getBot(
			id = 103.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 33.toUUID(),
		)

		integrationTestHelper.getBotWallet(
			id = 200.toUUID(),
			botId = 100.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("100000000")) // 0.1 SOL
				increaseAcquisitionValue(BigDecimal("10.0")) // at 100$/SOL
			},
		)
		integrationTestHelper.getBotWallet(
			id = 201.toUUID(),
			botId = 101.toUUID(),
		)
		integrationTestHelper.getBotWallet(
			id = 202.toUUID(),
			botId = 102.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("200000000")) // 0.2 SOL
				increaseAcquisitionValue(BigDecimal("40.0")) // at 200$/SOL
			},
		)

		integrationTestHelper.getBotWallet(
			id = 203.toUUID(),
			botId = 103.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("100000000")) // 0.1 SOL
				increaseAcquisitionValue(BigDecimal("30.0")) // at 300$/SOL
			},
		)

		val now = Instant.now(clock)

		integrationTestHelper.getBotPortfolioValueSnapshot(
			botId = 100.toUUID(),
			portfolioValueUsd = BigDecimal("10.0"),
			snapshotMadeAt = now.minus(7, ChronoUnit.DAYS),
			acquisitionValueUsd = BigDecimal("10.0"),
		)
		integrationTestHelper.getBotPortfolioValueSnapshot(
			botId = 100.toUUID(),
			portfolioValueUsd = BigDecimal("15.0"),
			snapshotMadeAt = now.minus(4, ChronoUnit.DAYS),
			acquisitionValueUsd = BigDecimal("10.0"),
		)
		integrationTestHelper.getBotPortfolioValueSnapshot(
			botId = 100.toUUID(),
			portfolioValueUsd = BigDecimal("5.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.DAYS),
			acquisitionValueUsd = BigDecimal("10.0"),
		)

		integrationTestHelper.getBotPortfolioValueSnapshot(
			botId = 101.toUUID(),
			portfolioValueUsd = BigDecimal.ZERO,
			snapshotMadeAt = now.minus(7, ChronoUnit.DAYS),
			acquisitionValueUsd = BigDecimal.ZERO,
		)
		integrationTestHelper.getBotPortfolioValueSnapshot(
			botId = 101.toUUID(),
			portfolioValueUsd = BigDecimal.ZERO,
			snapshotMadeAt = now.minus(4, ChronoUnit.DAYS),
			acquisitionValueUsd = BigDecimal.ZERO,
		)
		integrationTestHelper.getBotPortfolioValueSnapshot(
			botId = 101.toUUID(),
			portfolioValueUsd = BigDecimal.ZERO,
			snapshotMadeAt = now.minus(1, ChronoUnit.DAYS),
			acquisitionValueUsd = BigDecimal.ZERO,
		)

		integrationTestHelper.getBotPortfolioValueSnapshot(
			botId = 102.toUUID(),
			portfolioValueUsd = BigDecimal("40.0"),
			snapshotMadeAt = now.minus(7, ChronoUnit.DAYS),
			acquisitionValueUsd = BigDecimal("40.0"),
		)

		integrationTestHelper.getBotPortfolioValueSnapshot(
			botId = 103.toUUID(),
			portfolioValueUsd = BigDecimal("45.0"),
			snapshotMadeAt = now.minus(4, ChronoUnit.DAYS),
			acquisitionValueUsd = BigDecimal("30.0"),
		)

		integrationTestHelper.getFile(id = 21.toUUID())
		integrationTestHelper.getFile(id = 22.toUUID())
		integrationTestHelper.getFile(id = 23.toUUID())

		val tokenAddress1 = AddressWrapper("FYxc7ntfTjV2c86oF4dDp8yYiyNLAR5rdhFhNGSMpump")
		val tokenAddress2 = AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump")
		val tokenAddress3 = AddressWrapper("HTYLJ5QTnq28HFUWQuBPKdc4RJu6JGb5ucesqAt3xTt8")

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress1,
			name = "ADHD",
			symbol = "ADHD",
			imageFileId = 21.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress2,
			name = "yolo",
			symbol = "yolo",
			imageFileId = 22.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress3,
			name = "PumpFunAss",
			symbol = "PumpFunAss",
			imageFileId = 23.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 400.toUUID(),
			botWalletId = 200.toUUID(),
			tokenAddress = tokenAddress1,
			profitTargetFraction = BigDecimal("20.0"), // 20% higher than entry
			stopLossFraction = BigDecimal("8.0"), // 20% lower than entry
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234567,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "50".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 401.toUUID(),
			botWalletId = 201.toUUID(),
			tokenAddress = tokenAddress2,
			profitTargetFraction = BigDecimal("31.83"), // 20% higher than entry
			stopLossFraction = BigDecimal("21.22"), // 20% lower than entry
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234568,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "15".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
				setSellDataFromChain(
					amountOfTokensSold = "15".toBigInteger(),
					amountOfBaseCurrencyReceived = "20".toBigInteger(),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 402.toUUID(),
			botWalletId = 201.toUUID(),
			tokenAddress = tokenAddress3,
			profitTargetFraction = BigDecimal("23.3"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234569,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "25".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
				setSellDataFromChain(
					amountOfTokensSold = "15".toBigInteger(),
					amountOfBaseCurrencyReceived = "20".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
					fee = BigInteger("1"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 403.toUUID(),
			botWalletId = 202.toUUID(),
			tokenAddress = tokenAddress3,
			profitTargetFraction = BigDecimal("25.3"),
			stopLossFraction = BigDecimal("12.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234459,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "35".toBigInteger(),
					amountIn = "15".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
				setSellDataFromChain(
					amountOfTokensSold = "45".toBigInteger(),
					amountOfBaseCurrencyReceived = "30".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
					fee = BigInteger("1"),
				)
			},
		)

		// Quick-fix for clock not being used for createdAt/updatedAt timestamp on JPA entities
		dslContext.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.CREATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))
			.set(BOT_MARKET_POSITION.UPDATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))

		// Set up token price snapshots
		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress1,
			chain = Chain.SOLANA,
			priceWei = BigInteger("16"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress2,
			chain = Chain.SOLANA,
			priceWei = BigInteger("26"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress3,
			chain = Chain.SOLANA,
			priceWei = BigInteger("36"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("100.0")

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(tokenAddress1.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("100")))
					put(tokenAddress2.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("200")))
					put(tokenAddress3.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("300")))
				},
			)

		// when
		val result = queryBus(
			BotsOverviewQuery(
				userId = 1.toUUID(),
				filter = BotsOverviewQuery.Filter(
					timeRange = TimeRange.WEEK,
				),
			),
		)

		// then
		result.run {
			userId shouldBe 1.toUUID()
			botsTotalValueAmountUsd shouldBeEqualComparingTo BigDecimal("20.2039280005000000")

			botsTotalPnlAmountUsd shouldBeEqualComparingTo BigDecimal("-19.7960719995000000")
			botsTotalPnlAmountFraction shouldBeEqualComparingTo BigDecimal("-0.494901799987500")

			botsOneDayChangeAmountUsd shouldBeEqualComparingTo BigDecimal("10.2039280005000000")
			botsOneDayChangeFraction shouldBeEqualComparingTo BigDecimal("1.020392800050000")

			botsPortfolioValueSumPastValues shouldContainExactlyInAnyOrder listOf(
				CompareBotsQuery.BotPortfolioPastValue(
					portfolioValueUsd = BigDecimal("10.0"),
					createdAt = Instant.now(clock).minus(7, ChronoUnit.DAYS),
				),
				CompareBotsQuery.BotPortfolioPastValue(
					portfolioValueUsd = BigDecimal("60.0"),
					createdAt = Instant.now(clock).minus(4, ChronoUnit.DAYS),
				),
				CompareBotsQuery.BotPortfolioPastValue(
					portfolioValueUsd = BigDecimal("5.0"),
					createdAt = Instant.now(clock).minus(1, ChronoUnit.DAYS),
				),
				CompareBotsQuery.BotPortfolioPastValue(
					portfolioValueUsd = BigDecimal("20.2039280005000000"),
					createdAt = Instant.now(clock),
				),
			)
		}
	}
}
