package com.cleevio.fatbot.application.module.transaction.fee

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class FeeDetailsProviderTest(
	@Autowired private val feeDetailsProviderEVM: FeeDetailsProvider,
) : IntegrationTest() {

	@Test
	fun `should provide correct platform fee for non-referred users`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		val result = feeDetailsProviderEVM.getFeeDetails(1.toUUID(), 111.toAddress().toChainAddress(Chain.EVM_BSC))

		result.platformFeeBps.value shouldBe 100
	}

	@Test
	fun `should provide correct platform fee for referred users`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>", referredByUserId = 1.toUUID())

		val result = feeDetailsProviderEVM.getFeeDetails(2.toUUID(), 111.toAddress().toChainAddress(Chain.EVM_BSC))

		result.platformFeeBps.value shouldBe 90
	}

	@Test
	fun `should provide correct referral fee for non-referred users`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		val result = feeDetailsProviderEVM.getFeeDetails(1.toUUID(), 111.toAddress().toChainAddress(Chain.EVM_BSC))

		result.referrerFeeBps.value shouldBe 0
	}

	@Test
	fun `should provide correct referral fee for referred users`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>", referredByUserId = 1.toUUID())

		val result = feeDetailsProviderEVM.getFeeDetails(2.toUUID(), 111.toAddress().toChainAddress(Chain.EVM_BSC))

		result.referrerFeeBps.value shouldBe 3000
	}

	@Test
	fun `should provide no referrerId for non-referred users`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		val result = feeDetailsProviderEVM.getFeeDetails(1.toUUID(), 111.toAddress().toChainAddress(Chain.EVM_BSC))

		result.referrerId shouldBe null
	}

	@Test
	fun `should provide correct referrerId for referred users`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>", referredByUserId = 1.toUUID())

		val result = feeDetailsProviderEVM.getFeeDetails(2.toUUID(), 111.toAddress().toChainAddress(Chain.EVM_BSC))

		result.referrerId shouldBe 1.toUUID()
	}

	@Test
	fun `should provide zero fees for stable coin even for referred users`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>", referredByUserId = 1.toUUID())

		val result = feeDetailsProviderEVM.getFeeDetails(
			1.toUUID(),
			AddressWrapper("0xA0b86991c6218b36c1d19D4a2e9Eb0cE3606eB48").toChainAddress(Chain.EVM_MAINNET),
		)

		result.referrerId shouldBe null
		result.platformFeeBps shouldBe BasisPoint.ZERO
		result.referrerFeeBps shouldBe BasisPoint.ZERO
	}
}
