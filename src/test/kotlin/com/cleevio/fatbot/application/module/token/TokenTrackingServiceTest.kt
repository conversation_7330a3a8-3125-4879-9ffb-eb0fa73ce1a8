package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.service.TokenTrackingService
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.market.MarketPositionRepository
import com.cleevio.fatbot.domain.transaction.TransactionRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger

@Suppress("ktlint:standard:max-line-length")
class TokenTrackingServiceTest(
	@Autowired private val transactionRepository: TransactionRepository,
	@Autowired private val marketPositionRepository: MarketPositionRepository,
	@Autowired private val underTest: TokenTrackingService,
) : IntegrationTest() {

	@Test
	fun `should track token when the token has lower on chain balance`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		integrationTestHelper.getMarketPosition(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 100000.toBigInteger(),
					amountOfBaseCurrencyPaid = 100000.toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			tokenDecimals = 6.toBigInteger(),
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j

		// GetTokenPricesEVM call - token price is 50000 wei
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// ["******************************************"]
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000000000000000000000000000000000000000c350"

		// GetErc20TokenBalancesEVM call - on chain balance is 80000, platform balance is 100000
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f4700000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000000000000013880"

		transactionRepository.count() shouldBe 0

		underTest.trackTokensForUser(
			userId = user.id,
		)

		transactionRepository.count() shouldBe 1
		transactionRepository.findAll().first().run {
			walletId shouldBe wallet.id
			type shouldBe TransactionType.SELL
			status shouldBe TransactionStatus.SUCCESS
			nonce shouldBe null
			baseValue shouldBe 1000.toBigInteger()
			amountIn shouldBe 20000.toBigInteger()
			amountOut shouldBe 1000.toBigInteger()
			fatbotFee shouldBe null
			signedTx shouldBe null
			tokenAddress shouldBe AddressWrapper("******************************************")
			chain shouldBe Chain.EVM_MAINNET
		}

		marketPositionRepository.findAll().first().run {
			totalTokenAmountBought shouldBe 100000.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 100000.toBigInteger()
			totalTokenAmountSold shouldBe 20000.toBigInteger()
		}
	}

	@Test
	fun `shouldn't track token when the token has higher on chain balance`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		integrationTestHelper.getMarketPosition(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 100000.toBigInteger(),
					amountOfBaseCurrencyPaid = 100000.toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j

		// GetTokenPricesEVM call - token price is 50000 wei
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// ["******************************************"]
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000000000000000000000000000000000000000c350"

		// GetErc20TokenBalancesEVM call - on chain balance is 120000, platform balance is 100000
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f4700000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000000000000000000000000000000000000001d4c0"

		transactionRepository.count() shouldBe 0

		underTest.trackTokensForUser(
			userId = user.id,
		)

		transactionRepository.count() shouldBe 0

		marketPositionRepository.findAll().first().run {
			totalTokenAmountBought shouldBe 100000.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 100000.toBigInteger()
			totalTokenAmountSold shouldBe 0.toBigInteger()
		}
	}

	@Test
	fun `shouldn't track token when the token on chain balance and platform balance are equal`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		integrationTestHelper.getMarketPosition(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 100000.toBigInteger(),
					amountOfBaseCurrencyPaid = 100000.toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j

		// GetTokenPricesEVM call - token price is 50000 wei
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// ["******************************************"]
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000000000000000000000000000000000000000c350"

		// GetErc20TokenBalancesEVM call - on chain balance is 100000, platform balance is 100000
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f4700000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"00000000000000000000000000000000000000000000000000000000000186a0"

		transactionRepository.count() shouldBe 0

		underTest.trackTokensForUser(
			userId = user.id,
		)

		transactionRepository.count() shouldBe 0

		marketPositionRepository.findAll().first().run {
			totalTokenAmountBought shouldBe 100000.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 100000.toBigInteger()
			totalTokenAmountSold shouldBe 0.toBigInteger()
		}
	}

	@Test
	fun `should track multiple tokens from multiple wallets with lower platform balance`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet1 = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		val wallet2 = integrationTestHelper.getWallet(
			id = 6.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		val tokenAddress1 = "******************************************" // platform 100000, on-chain 90000, price 50000
		val tokenAddress2 = "******************************************" // platform 100000, on-chain 100000, price 60000
		val tokenAddress3 = "******************************************" // platform 100000, on-chain 25000, price 70000
		val tokenAddress4 = "******************************************" // platform 50000, on-chain 30000, price 80000
		val tokenAddress5 = "******************************************" // platform 50000, on-chain 40000, price 90000
		val tokenAddress6 = "******************************************" // platform 50000, on-chain 60000, price 100000
		val marketPosition1 = integrationTestHelper.getMarketPosition(
			walletId = wallet1.id,
			tokenAddress = AddressWrapper(addressString = tokenAddress1),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 120000.toBigInteger(),
					amountOfBaseCurrencyPaid = 120000.toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
				newSell(
					amountOfTokensSold = 20000.toBigInteger(),
					amountOfBaseCurrencyReceived = 20000.toBigInteger(),
				)
			},
		)
		val marketPosition2 = integrationTestHelper.getMarketPosition(
			walletId = wallet1.id,
			tokenAddress = AddressWrapper(addressString = tokenAddress2),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 150000.toBigInteger(),
					amountOfBaseCurrencyPaid = 150000.toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
				newSell(
					amountOfTokensSold = 50000.toBigInteger(),
					amountOfBaseCurrencyReceived = 50000.toBigInteger(),
				)
			},
		)
		val marketPosition3 = integrationTestHelper.getMarketPosition(
			walletId = wallet1.id,
			tokenAddress = AddressWrapper(addressString = tokenAddress3),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 100000.toBigInteger(),
					amountOfBaseCurrencyPaid = 100000.toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)
		val marketPosition4 = integrationTestHelper.getMarketPosition(
			walletId = wallet2.id,
			tokenAddress = AddressWrapper(addressString = tokenAddress4),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 150000.toBigInteger(),
					amountOfBaseCurrencyPaid = 150000.toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
				newSell(
					amountOfTokensSold = 100000.toBigInteger(),
					amountOfBaseCurrencyReceived = 100000.toBigInteger(),
				)
			},
		)
		val marketPosition5 = integrationTestHelper.getMarketPosition(
			walletId = wallet2.id,
			tokenAddress = AddressWrapper(addressString = tokenAddress5),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 75000.toBigInteger(),
					amountOfBaseCurrencyPaid = 75000.toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
				newSell(
					amountOfTokensSold = 25000.toBigInteger(),
					amountOfBaseCurrencyReceived = 25000.toBigInteger(),
				)
			},
		)
		val marketPosition6 = integrationTestHelper.getMarketPosition(
			walletId = wallet2.id,
			tokenAddress = AddressWrapper(addressString = tokenAddress6),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 50000.toBigInteger(),
					amountOfBaseCurrencyPaid = 50000.toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = tokenAddress1),
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = tokenAddress2),
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = tokenAddress3),
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = tokenAddress4),
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = tokenAddress5),
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = tokenAddress6),
			tokenDecimals = 6.toBigInteger(),
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j

		// GetTokenPricesEVM call
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************","******************************************","******************************************","******************************************","******************************************","******************************************"]
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000050000000000000000000000006982508145454ce325ddbe47a25d4ec3d23119330000000000000000000000006982508145454ce325ddbe47a25d4ec3d23119350000000000000000000000006982508145454ce325ddbe47a25d4ec3d23119360000000000000000000000006982508145454ce325ddbe47a25d4ec3d23119370000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311938",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000120" +
			"0000000000000000000000000000000000000000000000000000000000000006" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311934" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311935" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311936" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311937" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311938" +
			"0000000000000000000000000000000000000000000000000000000000000006" +
			"000000000000000000000000000000000000000000000000000000000000c350" +
			"000000000000000000000000000000000000000000000000000000000000ea60" +
			"0000000000000000000000000000000000000000000000000000000000011170" +
			"0000000000000000000000000000000000000000000000000000000000013880" +
			"0000000000000000000000000000000000000000000000000000000000015f90" +
			"00000000000000000000000000000000000000000000000000000000000186a0"

		// GetErc20TokenBalancesEVM call for wallet1
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f4700000000000000000000000000000000000000000000000000000000000000000030000000000000000000000006982508145454ce325ddbe47a25d4ec3d23119330000000000000000000000006982508145454ce325ddbe47a25d4ec3d23119340000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311935",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000003" +
			"0000000000000000000000000000000000000000000000000000000000015f90" +
			"00000000000000000000000000000000000000000000000000000000000186a0" +
			"00000000000000000000000000000000000000000000000000000000000061a8"

		// GetErc20TokenBalancesEVM call for wallet2
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f4700100000000000000000000000000000000000000000000000000000000000000030000000000000000000000006982508145454ce325ddbe47a25d4ec3d23119360000000000000000000000006982508145454ce325ddbe47a25d4ec3d23119370000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311938",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000003" +
			"0000000000000000000000000000000000000000000000000000000000007530" +
			"0000000000000000000000000000000000000000000000000000000000009c40" +
			"000000000000000000000000000000000000000000000000000000000000ea60"

		transactionRepository.count() shouldBe 0

		underTest.trackTokensForUser(
			userId = user.id,
		)

		transactionRepository.count() shouldBe 4
		val transactions = transactionRepository.findAll().sortedBy { it.createdAt }
		transactions[0].run {
			walletId shouldBe wallet1.id
			type shouldBe TransactionType.SELL
			status shouldBe TransactionStatus.SUCCESS
			baseValue shouldBe 500.toBigInteger()
			amountIn shouldBe 10000.toBigInteger()
			amountOut shouldBe 500.toBigInteger()
			tokenAddress shouldBe AddressWrapper("******************************************")
			chain shouldBe Chain.EVM_MAINNET
		}
		transactions[1].run {
			walletId shouldBe wallet1.id
			type shouldBe TransactionType.SELL
			status shouldBe TransactionStatus.SUCCESS
			baseValue shouldBe 5250.toBigInteger()
			amountIn shouldBe 75000.toBigInteger()
			amountOut shouldBe 5250.toBigInteger()
			tokenAddress shouldBe AddressWrapper("******************************************")
			chain shouldBe Chain.EVM_MAINNET
		}
		transactions[2].run {
			walletId shouldBe wallet2.id
			type shouldBe TransactionType.SELL
			status shouldBe TransactionStatus.SUCCESS
			baseValue shouldBe 1600.toBigInteger()
			amountIn shouldBe 20000.toBigInteger()
			amountOut shouldBe 1600.toBigInteger()
			tokenAddress shouldBe AddressWrapper("******************************************")
			chain shouldBe Chain.EVM_MAINNET
		}
		transactions[3].run {
			walletId shouldBe wallet2.id
			type shouldBe TransactionType.SELL
			status shouldBe TransactionStatus.SUCCESS
			baseValue shouldBe 900.toBigInteger()
			amountIn shouldBe 10000.toBigInteger()
			amountOut shouldBe 900.toBigInteger()
			tokenAddress shouldBe AddressWrapper("******************************************")
			chain shouldBe Chain.EVM_MAINNET
		}

		val marketPositionsById = marketPositionRepository.findAll().associateBy { it.id }

		marketPositionsById[marketPosition1.id]!!.run {
			totalTokenAmountBought shouldBe 120000.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 120000.toBigInteger()
			totalTokenAmountSold shouldBe 30000.toBigInteger()
		}
		marketPositionsById[marketPosition2.id]!!.run {
			totalTokenAmountBought shouldBe 150000.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 150000.toBigInteger()
			totalTokenAmountSold shouldBe 50000.toBigInteger()
		}
		marketPositionsById[marketPosition3.id]!!.run {
			totalTokenAmountBought shouldBe 100000.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 100000.toBigInteger()
			totalTokenAmountSold shouldBe 75000.toBigInteger()
		}
		marketPositionsById[marketPosition4.id]!!.run {
			totalTokenAmountBought shouldBe 150000.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 150000.toBigInteger()
			totalTokenAmountSold shouldBe 120000.toBigInteger()
		}
		marketPositionsById[marketPosition5.id]!!.run {
			totalTokenAmountBought shouldBe 75000.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 75000.toBigInteger()
			totalTokenAmountSold shouldBe 35000.toBigInteger()
		}
		marketPositionsById[marketPosition6.id]!!.run {
			totalTokenAmountBought shouldBe 50000.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 50000.toBigInteger()
			totalTokenAmountSold shouldBe 0.toBigInteger()
		}
	}

	@Test
	fun `should close market position when chain balance is zero`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		integrationTestHelper.getMarketPosition(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 100000.toBigInteger(),
					amountOfBaseCurrencyPaid = 100000.toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			tokenDecimals = 6.toBigInteger(),
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j

		// GetTokenPricesEVM call - token price is 50000 wei
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// ["******************************************"]
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000000000000000000000000000000000000000c350"

		// GetErc20TokenBalancesEVM call - on chain balance is 0, platform balance is 100000
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f4700000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000000000000000000"

		transactionRepository.count() shouldBe 0

		underTest.trackTokensForUser(
			userId = user.id,
		)

		transactionRepository.count() shouldBe 1
		transactionRepository.findAll().first().run {
			walletId shouldBe wallet.id
			type shouldBe TransactionType.SELL
			status shouldBe TransactionStatus.SUCCESS
			nonce shouldBe null
			baseValue shouldBe 5000.toBigInteger()
			amountIn shouldBe 100000.toBigInteger()
			amountOut shouldBe 5000.toBigInteger()
			fatbotFee shouldBe null
			signedTx shouldBe null
			tokenAddress shouldBe AddressWrapper("******************************************")
			chain shouldBe Chain.EVM_MAINNET
		}

		marketPositionRepository.count() shouldBe 0
	}
}
