package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.crypto.asSignedTx
import com.cleevio.fatbot.application.common.crypto.asWei
import com.cleevio.fatbot.application.common.crypto.hashToTxHash
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.transaction.TransactionRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@Disabled
class TransactionServiceTest(
	@Autowired private val transactionRepository: TransactionRepository,
	@Autowired private val underTest: TransactionService,
) : IntegrationTest() {

	@Test
	fun `should save single legacy BUY transaction`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		@Suppress("ktlint:standard:max-line-length")
		val signedTxInput = "f86f808504a817c80082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc10000808401546d71a06f217d459b2288b716a27f478c9b267148c46ebc3f341c1e0eda9c8e92bd388fa00c27f7c3486f3ed278796516240fbc014d9e4584d76d79aee82f268bdcfe0c75".asSignedTx()

		underTest.saveTransactions(
			walletId = wallet.id,
			signedTxList = listOf(signedTxInput),
			transactionType = TransactionType.BUY,
			tokenAddress = AddressWrapper("******************************************"),
		)

		transactionRepository.count() shouldBe 1
		transactionRepository.findAll().first().run {
			walletId shouldBe 5.toUUID()
			type shouldBe TransactionType.BUY
			nonce shouldBe 0
			baseValue shouldBe 10000000000000000L.asWei()
			signedTx shouldBe signedTxInput
			txHash shouldBe signedTxInput.hashToTxHash()
			tokenAddress shouldBe AddressWrapper("******************************************")
			status shouldBe TransactionStatus.PENDING
			chain shouldBe Chain.EVM_BASE
			verificationCount shouldBe 0
		}
	}

	@Test
	fun `should save single EIP1559 BUY transaction`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		@Suppress("ktlint:standard:max-line-length")
		val signedTxInput = "02f87583aa36a78084773594008506fc23ac0082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c080a0698aa6103964f9cb951138833513da76d62c87f9fa8a88bfb0ce22a5b3f3cf6ea072fc1fbbd6a56f8947ff142cf40aded549a4e42790ed8851c9d74809bcaea862".asSignedTx()

		underTest.saveTransactions(
			walletId = wallet.id,
			signedTxList = listOf(signedTxInput),
			transactionType = TransactionType.BUY,
			tokenAddress = AddressWrapper("******************************************"),
		)

		transactionRepository.count() shouldBe 1
		transactionRepository.findAll().first().run {
			walletId shouldBe 5.toUUID()
			type shouldBe TransactionType.BUY
			nonce shouldBe 0
			baseValue shouldBe 10000000000000000L.asWei()
			signedTx shouldBe signedTxInput
			txHash shouldBe signedTxInput.hashToTxHash()
			tokenAddress shouldBe AddressWrapper("******************************************")
			status shouldBe TransactionStatus.PENDING
			chain shouldBe Chain.EVM_BASE
			verificationCount shouldBe 0
		}
	}

	@Test
	fun `should save legacy SELL and APPROVE transaction`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		@Suppress("ktlint:standard:max-line-length")
		val signedApproveTxInput = "f8ad808504a817c80082c35094046bbabe36c18422303ab83adb74bcdaa4e8ee1b80b844095ea7b3000000000000000000000000b934d7659c7168635a881fcc3efafb3af64a0c070000000000000000000000000000000000000000000000000de0b6b3a76400008401546d71a0c97712dcc7a66b09aa1666af01815190e90e9eecb171000f46f5f3a17b6fef86a016174885587620ec09da5e76b062866cf15d7e58b16f33d54c2fae1a75d08431".asSignedTx()

		@Suppress("ktlint:standard:max-line-length")
		val signedSellTxInput = "f8ae018504a817c80082ea6094046bbabe36c18422303ab83adb74bcdaa4e8ee1b80b845a9059cbb2a000000000000000000000000b934d7659c7168635a881fcc3efafb3af64a0c070000000000000000000000000000000000000000000000000de0b6b3a76400008401546d71a01b636985ed2317463803379b592fb5876f9828d71f7464f924a4fb33a12890f2a079709d00a36277bca72bdc27ae04ae85d7456ef7b25c985bb384a6e61c220052".asSignedTx()

		underTest.saveTransactions(
			walletId = wallet.id,
			signedTxList = listOf(signedApproveTxInput, signedSellTxInput),
			transactionType = TransactionType.SELL,
			tokenAddress = AddressWrapper("******************************************"),
		)

		transactionRepository.count() shouldBe 2
		val transactions = transactionRepository.findAll().sortedBy { it.createdAt }
		transactions[0].run {
			walletId shouldBe 5.toUUID()
			type shouldBe TransactionType.APPROVE
			nonce shouldBe 0
			baseValue shouldBe 0L.asWei()
			signedTx shouldBe signedApproveTxInput
			txHash shouldBe signedApproveTxInput.hashToTxHash()
			tokenAddress shouldBe AddressWrapper("******************************************")
			status shouldBe TransactionStatus.PENDING
			chain shouldBe Chain.EVM_BASE
			verificationCount shouldBe 0
		}
		transactions[1].run {
			walletId shouldBe 5.toUUID()
			type shouldBe TransactionType.SELL
			nonce shouldBe 1
			baseValue shouldBe 0L.asWei()
			signedTx shouldBe signedSellTxInput
			txHash shouldBe signedSellTxInput.hashToTxHash()
			tokenAddress shouldBe AddressWrapper("******************************************")
			status shouldBe TransactionStatus.PENDING
			chain shouldBe Chain.EVM_BASE
			verificationCount shouldBe 0
		}
	}

	@Test
	fun `should save EIP1559 SELL and APPROVE transaction`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		@Suppress("ktlint:standard:max-line-length")
		val signedApproveTxInput = "02f8b383aa36a78084773594008506fc23ac0082c35094046bbabe36c18422303ab83adb74bcdaa4e8ee1b80b844095ea7b3000000000000000000000000b934d7659c7168635a881fcc3efafb3af64a0c070000000000000000000000000000000000000000000000000de0b6b3a7640000c080a00a9fabaaabf88b77ff6f7b3f9ce347a079c04135cc4c41e107faafd9d248c745a058eee327224f2cdf7035a3fb778cedf15a82fd7e8bfa619fb443780e4ab5ddad".asSignedTx()

		@Suppress("ktlint:standard:max-line-length")
		val signedSellTxInput = "02f8b483aa36a70184773594008506fc23ac0082ea6094046bbabe36c18422303ab83adb74bcdaa4e8ee1b80b845a9059cbb2a000000000000000000000000b934d7659c7168635a881fcc3efafb3af64a0c070000000000000000000000000000000000000000000000000de0b6b3a7640000c001a01312abc7d915cc62cdc80d38f9b800c12e950221dc677fbfd72f5fde2530c467a0566442abe31c69fd02b3ec566cecc9377313b09266d6125176198bca79e0172b".asSignedTx()

		underTest.saveTransactions(
			walletId = wallet.id,
			signedTxList = listOf(signedApproveTxInput, signedSellTxInput),
			transactionType = TransactionType.SELL,
			tokenAddress = AddressWrapper("******************************************"),
		)

		transactionRepository.count() shouldBe 2
		val transactions = transactionRepository.findAll().sortedBy { it.createdAt }
		transactions[0].run {
			walletId shouldBe 5.toUUID()
			type shouldBe TransactionType.APPROVE
			nonce shouldBe 0
			baseValue shouldBe 0L.asWei()
			signedTx shouldBe signedApproveTxInput
			txHash shouldBe signedApproveTxInput.hashToTxHash()
			tokenAddress shouldBe AddressWrapper("******************************************")
			status shouldBe TransactionStatus.PENDING
			chain shouldBe Chain.EVM_BASE
			verificationCount shouldBe 0
		}
		transactions[1].run {
			walletId shouldBe 5.toUUID()
			type shouldBe TransactionType.SELL
			nonce shouldBe 1
			baseValue shouldBe 0L.asWei()
			signedTx shouldBe signedSellTxInput
			txHash shouldBe signedSellTxInput.hashToTxHash()
			tokenAddress shouldBe AddressWrapper("******************************************")
			status shouldBe TransactionStatus.PENDING
			chain shouldBe Chain.EVM_BASE
			verificationCount shouldBe 0
		}
	}

	@Test
	fun `should save TRANSFER_TOKEN transaction`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		@Suppress("ktlint:standard:max-line-length")
		val signedTxInput = "02f8b483aa36a70184773594008506fc23ac0082ea6094046bbabe36c18422303ab83adb74bcdaa4e8ee1b80b845a9059cbb2a000000000000000000000000b934d7659c7168635a881fcc3efafb3af64a0c070000000000000000000000000000000000000000000000008ac7230489e80000c080a0a14300d192da50c479ff339fb988506fbe5ed94a3e1d0dbb5035890f2c5e52aaa04ac9a8e9de12a05dddaa0ad3a4cff294455a1ceb010287064cab4279179002f4".asSignedTx()

		underTest.saveTransactions(
			walletId = wallet.id,
			signedTxList = listOf(signedTxInput),
			transactionType = TransactionType.TRANSFER_TOKEN,
			tokenAddress = AddressWrapper("******************************************"),
		)

		transactionRepository.count() shouldBe 1
		transactionRepository.findAll().first().run {
			walletId shouldBe 5.toUUID()
			type shouldBe TransactionType.TRANSFER_TOKEN
			nonce shouldBe 1
			baseValue shouldBe 0L.asWei()
			signedTx shouldBe signedTxInput
			txHash shouldBe signedTxInput.hashToTxHash()
			tokenAddress shouldBe AddressWrapper("******************************************")
			status shouldBe TransactionStatus.PENDING
			chain shouldBe Chain.EVM_BASE
			verificationCount shouldBe 0
		}
	}

	@Test
	fun `should save TRANSFER_ETH transaction`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		@Suppress("ktlint:standard:max-line-length")
		val signedTxInput = "02f87583aa36a78084773594008506fc23ac0082520894b934d7659c7168635a881fcc3efafb3af64a0c0787b1a2bc2ec5000080c001a0f3b54eed19f834bffeb9e0731e530cd27d665e2a20fa4b68233bb74a82383be1a0179651b8fffeb0787bc0b895eeb34d8544c8cd968d6e99cfd7d64da1833bb433".asSignedTx()

		underTest.saveTransactions(
			walletId = wallet.id,
			signedTxList = listOf(signedTxInput),
			transactionType = TransactionType.TRANSFER_CURRENCY,
			tokenAddress = null,
		)

		transactionRepository.count() shouldBe 1
		transactionRepository.findAll().first().run {
			walletId shouldBe 5.toUUID()
			type shouldBe TransactionType.TRANSFER_CURRENCY
			nonce shouldBe 0
			baseValue shouldBe 50000000000000000L.asWei()
			signedTx shouldBe signedTxInput
			txHash shouldBe signedTxInput.hashToTxHash()
			tokenAddress shouldBe null
			status shouldBe TransactionStatus.PENDING
			chain shouldBe Chain.EVM_BASE
			verificationCount shouldBe 0
		}
	}
}
