package com.cleevio.fatbot.application.module.userfattycard

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.userfattycard.query.SearchUserFattyCardsQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class SearchUserFattyCardsQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should search all user fatty cards`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 1.toUUID())
		integrationTestHelper.getFattyCard(
			id = 1.toUUID(),
			avatarFileId = 1.toUUID(),
			rarity = "RARE",
			probability = BigDecimal.valueOf(10),
			donutReward = BigDecimal.valueOf(100),
		)

		// Create multiple user fatty cards
		integrationTestHelper.getUserFattyCard(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 1.toUUID(),
		)
		integrationTestHelper.getUserFattyCard(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 1.toUUID(),
		)

		val result = queryBus(
			SearchUserFattyCardsQuery(
				userId = 1.toUUID(),
				claimed = null,
				displayed = null,
			),
		)

		result.size shouldBe 2
		result.forEach { card ->
			card.avatarFileId shouldBe 1.toUUID()
			card.rarity shouldBe "RARE"
			card.probability shouldBe BigDecimal.valueOf(10)
			card.donutReward shouldBe BigDecimal.valueOf(100)
		}
	}

	@Test
	fun `should filter by claimed status`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 1.toUUID())
		integrationTestHelper.getFattyCard(
			id = 1.toUUID(),
			avatarFileId = 1.toUUID(),
		)

		// Create claimed and unclaimed cards
		integrationTestHelper.getUserFattyCard(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 1.toUUID(),
			claimed = true,
		)
		integrationTestHelper.getUserFattyCard(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 1.toUUID(),
			claimed = false,
		)

		// When
		val claimedResult = queryBus(
			SearchUserFattyCardsQuery(
				userId = 1.toUUID(),
				claimed = true,
				displayed = null,
			),
		)
		val unclaimedResult = queryBus(
			SearchUserFattyCardsQuery(
				userId = 1.toUUID(),
				claimed = false,
				displayed = null,
			),
		)

		// Then
		claimedResult.size shouldBe 1
		unclaimedResult.size shouldBe 1
		claimedResult.first().userFattyCardId shouldBe 1.toUUID()
		unclaimedResult.first().userFattyCardId shouldBe 2.toUUID()
	}

	@Test
	fun `should filter by displayed status`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 1.toUUID())
		integrationTestHelper.getFattyCard(
			id = 1.toUUID(),
			avatarFileId = 1.toUUID(),
		)

		// Create displayed and hidden cards
		integrationTestHelper.getUserFattyCard(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 1.toUUID(),
			displayed = true,
		)
		integrationTestHelper.getUserFattyCard(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 1.toUUID(),
		)

		// When
		val displayedResult = queryBus(
			SearchUserFattyCardsQuery(
				userId = 1.toUUID(),
				claimed = null,
				displayed = true,
			),
		)
		val hiddenResult = queryBus(
			SearchUserFattyCardsQuery(
				userId = 1.toUUID(),
				claimed = null,
				displayed = false,
			),
		)

		// Then
		displayedResult.run {
			size shouldBe 1
			first().userFattyCardId shouldBe 1.toUUID()
		}
		hiddenResult.run {
			size shouldBe 1
			first().userFattyCardId shouldBe 2.toUUID()
		}
	}

	@Test
	fun `should combine claimed and displayed filters`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 1.toUUID())
		integrationTestHelper.getFattyCard(
			id = 1.toUUID(),
			avatarFileId = 1.toUUID(),
		)

		// Create cards with different combinations of claimed and displayed status
		integrationTestHelper.getUserFattyCard(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 1.toUUID(),
			claimed = true,
			displayed = true,
		)
		integrationTestHelper.getUserFattyCard(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 1.toUUID(),
			claimed = true,
			displayed = false,
		)
		integrationTestHelper.getUserFattyCard(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 1.toUUID(),
			claimed = false,
			displayed = true,
		)

		// When
		val result = queryBus(
			SearchUserFattyCardsQuery(
				userId = 1.toUUID(),
				claimed = true,
				displayed = true,
			),
		)

		// Then
		result.run {
			size shouldBe 1
			first().userFattyCardId shouldBe 1.toUUID()
		}
	}
}
