package com.cleevio.fatbot.application.module.analytics

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.module.analytics.port.out.AggregateDailyFees
import com.cleevio.fatbot.application.module.analytics.port.out.DailyFeeType
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.analytics.PlatformDailyFeeRepository
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDate

class PlatformDailyFeeServiceTest(
	@Autowired private val underTest: PlatformDailyFeeService,
	@Autowired private val platformDailyFeeRepository: PlatformDailyFeeRepository,
) : IntegrationTest() {

	@Test
	fun `should save list of daily fees`() {
		val dailyFees = listOf(
			AggregateDailyFees.Result(
				chain = Chain.EVM_MAINNET,
				date = LocalDate.of(2024, 12, 1),
				type = DailyFeeType.REVENUE,
				baseAmount = 123456789.toBigInteger().asBaseAmount(),
			),
			AggregateDailyFees.Result(
				chain = Chain.EVM_MAINNET,
				date = LocalDate.of(2024, 12, 2),
				type = DailyFeeType.REFERRAL,
				baseAmount = 23456789.toBigInteger().asBaseAmount(),
			),
		)

		underTest.saveAll(dailyFees)

		platformDailyFeeRepository.findAll().size shouldBe 2
	}

	@Test
	fun `should update daily fees with existing dates`() {
		val dailyFees = listOf(
			AggregateDailyFees.Result(
				chain = Chain.EVM_MAINNET,
				date = LocalDate.of(2024, 12, 1),
				type = DailyFeeType.REVENUE,
				baseAmount = 123456789.toBigInteger().asBaseAmount(),
			),
			AggregateDailyFees.Result(
				chain = Chain.EVM_MAINNET,
				date = LocalDate.of(2024, 12, 2),
				type = DailyFeeType.REFERRAL,
				baseAmount = 23456789.toBigInteger().asBaseAmount(),
			),
		)

		underTest.saveAll(dailyFees)

		val updatedDailyFees = listOf(
			AggregateDailyFees.Result(
				chain = Chain.EVM_MAINNET,
				date = LocalDate.of(2024, 12, 1),
				type = DailyFeeType.REVENUE,
				baseAmount = 1234.toBigInteger().asBaseAmount(),
			),
			AggregateDailyFees.Result(
				chain = Chain.EVM_MAINNET,
				date = LocalDate.of(2024, 12, 2),
				type = DailyFeeType.REFERRAL,
				baseAmount = 2345.toBigInteger().asBaseAmount(),
			),
		)

		underTest.saveAll(updatedDailyFees)

		val result = platformDailyFeeRepository.findAll()
		result.size shouldBe 2
		with(result[0]) {
			date shouldBe LocalDate.of(2024, 12, 1)
			baseAmount shouldBe 1234.toBigInteger()
		}

		with(result[1]) {
			date shouldBe LocalDate.of(2024, 12, 2)
			baseAmount shouldBe 2345.toBigInteger()
		}
	}
}
