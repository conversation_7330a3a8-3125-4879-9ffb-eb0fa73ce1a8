package com.cleevio.fatbot.application.module.analytics.port.out

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.module.referral.ReferralRewardService.Companion.ZERO_UUID
import com.cleevio.fatbot.application.module.transaction.constant.BuySellTransactionSuccess
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.REFERRAL_REWARD
import com.cleevio.fatbot.tables.references.TRANSACTION
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import org.jooq.DSLContext
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import java.util.UUID

class AggregateDailyFeesTest(
	@Autowired private val underTest: AggregateDailyFees,
	@Autowired private val dslContext: DSLContext,
	@Autowired private val clock: IntegrationTestClock,
) : IntegrationTest() {

	@Test
	fun `should aggregate daily fees when no data`() {
		val result = underTest()

		result.size shouldBe 0
	}

	@Test
	fun `should aggregate daily fees for all days`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)

		// day 1
		saveSuccessTransaction(1, wallet.id, 123456)
		saveSuccessTransaction(2, wallet.id, 234567)
		saveReferral(1, user.id, 12345)

		clock.advanceBy(1, ChronoUnit.DAYS)

		// day 2
		saveSuccessTransaction(3, wallet.id, 345678)

		val dailyFees = underTest()

		val today = clock.getDate()
		val yesterday = today.minusDays(1)

		dailyFees.size shouldBe 3
		dailyFees shouldContainExactlyInAnyOrder listOf(
			AggregateDailyFees.Result(
				chain = Chain.EVM_MAINNET,
				date = yesterday,
				type = DailyFeeType.REVENUE,
				baseAmount = 358023.toBigInteger().asBaseAmount(),
			),
			AggregateDailyFees.Result(
				chain = Chain.EVM_MAINNET,
				date = today,
				type = DailyFeeType.REVENUE,
				baseAmount = 345678.toBigInteger().asBaseAmount(),
			),
			AggregateDailyFees.Result(
				chain = Chain.EVM_MAINNET,
				date = yesterday,
				type = DailyFeeType.REFERRAL,
				baseAmount = 12345.toBigInteger().asBaseAmount(),
			),
		)
	}

	@Test
	fun `should aggregate daily fees in date range`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)

		// day 1
		saveSuccessTransaction(1, wallet.id, 123456)
		saveSuccessTransaction(2, wallet.id, 234567)
		saveReferral(1, user.id, 12345)

		clock.advanceBy(1, ChronoUnit.DAYS)

		// day 2
		val from = clock.getDate() // inclusive
		saveSuccessTransaction(3, wallet.id, 345678)
		saveReferral(2, user.id, 23456)

		clock.advanceBy(1, ChronoUnit.DAYS)

		// day 3
		val to = clock.getDate() // exclusive
		saveSuccessTransaction(4, wallet.id, 98765)
		saveReferral(3, user.id, 345678)

		val result = underTest(from, to)

		result.size shouldBe 2
		result shouldContainExactlyInAnyOrder listOf(
			AggregateDailyFees.Result(
				chain = Chain.EVM_MAINNET,
				date = from,
				type = DailyFeeType.REVENUE,
				baseAmount = 345678.toBigInteger().asBaseAmount(),
			),
			AggregateDailyFees.Result(
				chain = Chain.EVM_MAINNET,
				date = from,
				type = DailyFeeType.REFERRAL,
				baseAmount = 23456.toBigInteger().asBaseAmount(),
			),
		)
	}

	private fun saveReferral(id: Int, userId: UUID, baseAmount: Long, claim: Boolean = false) {
		integrationTestHelper.getReferralReward(
			id = id.toUUID(),
			userId = userId,
			txHash = TxHash("dummyHash$id"),
			baseAmount = BigInteger.valueOf(baseAmount),
			beforeSave = { if (claim) claim() },
		)
		setReferralTimestamp(id)
	}

	private fun saveSuccessTransaction(id: Int, walletId: UUID, fee: Long) {
		integrationTestHelper.getTransaction(
			id = id.toUUID(),
			walletId = walletId,
			signedTx = "dummySignedTx$id",
			beforeSave = {
				markAsSuccess(
					result = BuySellTransactionSuccess(
						amountIn = BigInteger.valueOf(100000),
						amountOut = BigInteger.ZERO,
						fee = BigInteger.valueOf(fee),
						referralFee = BigInteger.ZERO,
						referralRewardRecipient = ZERO_UUID,
						balanceChange = BaseAmount.ZERO,
					),
					exchangeRateUsd = BigDecimal("2400.001"),
				)
			},
		)
		setTransactionTimestamp(id)
	}

	private fun setReferralTimestamp(id: Int) {
		dslContext.update(REFERRAL_REWARD)
			.set(
				mapOf(
					REFERRAL_REWARD.CREATED_AT to clock.currentTime(),
					REFERRAL_REWARD.UPDATED_AT to clock.currentTime(),
				),
			)
			.where(REFERRAL_REWARD.ID.eq(id.toUUID()))
			.execute()
	}

	private fun setTransactionTimestamp(id: Int) {
		dslContext.update(TRANSACTION)
			.set(
				mapOf(
					TRANSACTION.CREATED_AT to clock.currentTime(),
					TRANSACTION.UPDATED_AT to clock.currentTime(),
				),
			)
			.where(TRANSACTION.ID.eq(id.toUUID()))
			.execute()
	}

	private fun IntegrationTestClock.getDate() = LocalDate.ofInstant(this.currentTime(), ZoneOffset.UTC)
}
