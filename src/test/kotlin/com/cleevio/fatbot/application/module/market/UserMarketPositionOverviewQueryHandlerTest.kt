package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.query.UserMarketPositionOverviewQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.tokenprice.TokenPriceRepository
import com.cleevio.fatbot.domain.tokenprice.TokenPriceSnapshot
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.p2p.solanaj.core.PublicKey
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger
import java.time.temporal.ChronoUnit

class UserMarketPositionOverviewQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val tokenPriceRepository: TokenPriceRepository,
	@Autowired private val clock: IntegrationTestClock,
) : IntegrationTest() {

	@Test
	fun `should get empty result when no market positions`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2300.001")

		val result =
			queryBus(UserMarketPositionOverviewQuery.ofWallet(userId = user.id, walletId = wallet.id))

		with(result) {
			totalValueAmountUsd shouldBeEqualComparingTo BigDecimal.ZERO
			totalPnlAmountUsd shouldBeEqualComparingTo BigDecimal.ZERO
			totalPnlAmountFraction shouldBeEqualComparingTo BigDecimal.ZERO
			oneDayChangeAmountUsd shouldBeEqualComparingTo BigDecimal.ZERO
			oneDayChangeFraction shouldBeEqualComparingTo BigDecimal.ZERO
		}
	}

	@Test
	fun `should get user market position overview of specific wallet`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = "******************************************"),
		)

		tokenPriceRepository.save(
			TokenPriceSnapshot(
				tokenAddress = AddressWrapper(addressString = "******************************************"),
				chain = Chain.EVM_MAINNET,
				priceWei = 550000000000000000.toBigInteger(), // 0.55 ETH
				exchangeRateUsd = "2300.001".toBigDecimal(),
				validAt = clock.currentTime(),
			),
		)

		integrationTestHelper.getExchangeRateSnapshot(
			currency = CryptoCurrency.ETH,
			exchangeRateUsd = BigDecimal("2300.001"),
			validAt = clock.currentTime(),
		)

		integrationTestHelper.getMarketPositionSnapshot(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			totalTokenAmountBought = 1000000000000000000.toBigInteger(), // 1 full token if 18 decimals
			totalTokenAmountSold = 0.toBigInteger(),
			totalTokenAcquisitionCostInWei = 1000000000000000000.toBigInteger(), // for 1 ETH
			snapshotMadeAt = clock.currentTime(),
			totalTokenDispositionCostInWei = BigInteger.ZERO,
			totalTokenAcquisitionCostUsd = BigDecimal("300"),
		)

		clock.advanceBy(1, ChronoUnit.DAYS)

		integrationTestHelper.getMarketPosition(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 1000000000000000000.toBigInteger(), // 1 full token if 18 decimals
					amountOfBaseCurrencyPaid = 1000000000000000000.toBigInteger(), // for 1 ETH
					exchangeRate = BigDecimal("250"),
				)
			},
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns "250".toBigDecimal()
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getBaseFeePerGas() } returns 1000.toBigInteger()

		// GetTokenPricesEVM call - token price is 1.1 ETH
		every {
			@Suppress("ktlint:standard:max-line-length")
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000F43FC2C04EE0000"

		// GetErc20TokenBalancesEVM call - on chain balance is 80000, platform balance is 100000
		every {
			@Suppress("ktlint:standard:max-line-length")
			readOnlyTransactionManager.sendCall(
				"0x12796FD562ff10E21762E57b7489ca115120D962",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000000000000013880"

		val result =
			queryBus(UserMarketPositionOverviewQuery.ofWallet(userId = user.id, walletId = wallet.id))

		with(result) {
			// 10^18 base tokens bought, 1.1 ETH price per (e18) token =>
			totalValueAmountUsd shouldBeEqualComparingTo "275.000000000000000000000000000000000".toBigDecimal()
			totalPnlAmountUsd shouldBeEqualComparingTo "25.000000000000000000000000000000000000".toBigDecimal()
			totalPnlAmountFraction shouldBeEqualComparingTo "0.1".toBigDecimal()
			oneDayChangeAmountUsd shouldBeEqualComparingTo "-990.00055".toBigDecimal()
			oneDayChangeFraction shouldBeEqualComparingTo BigDecimal("-0.7826087901700912303951172195142524")
		}
	}

	@Test
	fun `should get user market position and wallet position with missing 24 ago position overview of specific wallet`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = "******************************************"),
		)

		tokenPriceRepository.save(
			TokenPriceSnapshot(
				tokenAddress = AddressWrapper(addressString = "******************************************"),
				chain = Chain.EVM_MAINNET,
				priceWei = 550000000000000000.toBigInteger(), // 0.55 ETH
				exchangeRateUsd = "2300.001".toBigDecimal(),
				validAt = clock.currentTime(),
			),
		)

		integrationTestHelper.getExchangeRateSnapshot(
			currency = CryptoCurrency.ETH,
			exchangeRateUsd = BigDecimal("2300.001"),
			validAt = clock.currentTime(),
		)

		integrationTestHelper.getMarketPositionSnapshot(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			totalTokenAmountBought = 1000000000000000000.toBigInteger(), // 1 full token if 18 decimals
			totalTokenAmountSold = 0.toBigInteger(),
			totalTokenAcquisitionCostInWei = 1000000000000000000.toBigInteger(), // for 1 ETH
			snapshotMadeAt = clock.currentTime(),
			totalTokenDispositionCostInWei = BigInteger.ZERO,
			totalTokenAcquisitionCostUsd = BigDecimal("3"),
		)

		clock.advanceBy(1, ChronoUnit.DAYS)

		integrationTestHelper.getMarketPosition(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 1000000000000000000.toBigInteger(), // 1 full token if 18 decimals
					amountOfBaseCurrencyPaid = 1000000000000000000.toBigInteger(), // for 1 ETH
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)

		integrationTestHelper.getWalletCurrencyPosition(
			walletId = wallet.id,
			totalBought = 1000000000000000000.toBigInteger(),
			totalSold = 0.toBigInteger(),
			totalAcquisitionCostUsd = "2300.001".toBigDecimal(),
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns "2300.001".toBigDecimal()
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getBaseFeePerGas() } returns 1000.toBigInteger()

		// GetTokenPricesEVM call - token price is 1.1 ETH
		every {
			@Suppress("ktlint:standard:max-line-length")
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000F43FC2C04EE0000"

		// GetErc20TokenBalancesEVM call - on chain balance is 80000, platform balance is 100000
		every {
			@Suppress("ktlint:standard:max-line-length")
			readOnlyTransactionManager.sendCall(
				"0x12796FD562ff10E21762E57b7489ca115120D962",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000000000000013880"

		val result =
			queryBus(UserMarketPositionOverviewQuery.ofWallet(userId = user.id, walletId = wallet.id))

		with(result) {
			// 10^18 base tokens bought, 1.1 ETH price per (e18) token =>
			totalValueAmountUsd shouldBeEqualComparingTo "4830.0021".toBigDecimal()
			totalPnlAmountUsd shouldBeEqualComparingTo "2528.5011000000000000000000000000000000000".toBigDecimal()
			totalPnlAmountFraction shouldBeEqualComparingTo "1.098631327989864006142078582629336".toBigDecimal()
			// TODO: These are so high, because wallets don't have a 24h ago position, so they count as a pure profit
			// TODO: Should we omit this result, when one of the 24h ago positions are missing?
			oneDayChangeAmountUsd shouldBeEqualComparingTo "3565.00155".toBigDecimal()
			oneDayChangeFraction shouldBeEqualComparingTo "2.818181818181818181818181818181818".toBigDecimal()
		}
	}

	@Test
	fun `should get user market position and wallet position overview of specific wallet`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = "******************************************"),
		)

		tokenPriceRepository.save(
			TokenPriceSnapshot(
				tokenAddress = AddressWrapper(addressString = "******************************************"),
				chain = Chain.EVM_MAINNET,
				priceWei = 550000000000000000.toBigInteger(), // 0.55 ETH
				exchangeRateUsd = "2300.001".toBigDecimal(),
				validAt = clock.currentTime(),
			),
		)

		integrationTestHelper.getExchangeRateSnapshot(
			currency = CryptoCurrency.ETH,
			exchangeRateUsd = BigDecimal("2300.001"),
			validAt = clock.currentTime(),
		)

		integrationTestHelper.getMarketPositionSnapshot(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			totalTokenAmountBought = 1000000000000000000.toBigInteger(), // 1 full token if 18 decimals
			totalTokenAmountSold = 0.toBigInteger(),
			totalTokenAcquisitionCostInWei = 1000000000000000000.toBigInteger(), // for 1 ETH
			snapshotMadeAt = clock.currentTime(),
			totalTokenDispositionCostInWei = BigInteger.ZERO,
			totalTokenAcquisitionCostUsd = BigDecimal("3"),
		)

		integrationTestHelper.getWalletCurrencyPosition(
			walletId = wallet.id,
			totalBought = 1000000000000000000.toBigInteger(),
			totalSold = 0.toBigInteger(),
			totalAcquisitionCostUsd = "2000.001".toBigDecimal(),
		) {
			createdAt = clock.currentTime()
		}

		clock.advanceBy(1, ChronoUnit.DAYS)

		integrationTestHelper.getMarketPosition(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 1000000000000000000.toBigInteger(), // 1 full token if 18 decimals
					amountOfBaseCurrencyPaid = 1000000000000000000.toBigInteger(), // for 1 ETH
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns "2300.001".toBigDecimal()
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getBaseFeePerGas() } returns 1000.toBigInteger()

		// GetTokenPricesEVM call - token price is 1.1 ETH
		every {
			@Suppress("ktlint:standard:max-line-length")
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000F43FC2C04EE0000"

		// GetErc20TokenBalancesEVM call - on chain balance is 80000, platform balance is 100000
		every {
			@Suppress("ktlint:standard:max-line-length")
			readOnlyTransactionManager.sendCall(
				"0x12796FD562ff10E21762E57b7489ca115120D962",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000000000000013880"

		val result = queryBus(UserMarketPositionOverviewQuery.ofWallet(userId = user.id, walletId = wallet.id))

		with(result) {
			// 10^18 base tokens bought, 1.1 ETH price per (e18) token =>
			totalValueAmountUsd shouldBeEqualComparingTo "4830.0021".toBigDecimal()
			totalPnlAmountUsd shouldBeEqualComparingTo "2828.5011000000000000000000000000000000000".toBigDecimal()
			totalPnlAmountFraction shouldBeEqualComparingTo "1.413189950941818165466817153726129".toBigDecimal()
			oneDayChangeAmountUsd shouldBeEqualComparingTo "1265.00055".toBigDecimal()
			oneDayChangeFraction shouldBeEqualComparingTo "0.354838709677419354838709677419355".toBigDecimal()
		}
	}

	@Test
	fun `should get user market position with solana wallet`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper("EjwYJMj7wyP9w29WttYBSfh4dfFSuCFiSpAmjhiU72yp"),
			chain = Chain.SOLANA,
		)
		val solanaToken = integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			name = "VINE VINE",
			symbol = "VINE",
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getTokenPairInfo(
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			pairAddress = AddressWrapper("DRWyFG6KtxDwv41qjfSFF6aA1VHsWqUtwCkGhT4Ur7fe"),
			dexType = GetDex.Dex.PUMP_FUN,
			tokenDecimals = 6.toBigInteger(),
			uniswapV3Fee = null,
		)

		integrationTestHelper.getMarketPosition(
			walletId = 10.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = "1000000".toBigInteger(),
					amountOfBaseCurrencyPaid = "48".toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = solanaToken.address,
			chain = Chain.SOLANA,
			priceWei = BigInteger("24"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3500.0"),
		)
		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = solanaToken.address,
			chain = Chain.SOLANA,
			priceWei = BigInteger("96"),
			validAt = clock.currentTime().minus(24, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3500.0"),
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns "3500.0".toBigDecimal()

		every {
			rpcApi.getMultipleAccounts(listOf(PublicKey("DRWyFG6KtxDwv41qjfSFF6aA1VHsWqUtwCkGhT4Ur7fe")), any())
		} returns listOf(
			mockk {
				every { data } returns listOf(
					"F7f4N2DYrGDEtoNK7+cCAOjXqikJAAAAxB5x/l3pAQDoK4ctAgAAAACAxqR+jQMAAA==",
					"base64",
				)
			},
		)

		every { rpcApi.getMultipleAccountsOptional(any(), any()) } returns emptyList()

		val result = queryBus(UserMarketPositionOverviewQuery.ofWallet(userId = 1.toUUID(), walletId = 10.toUUID()))

		with(result) {
			totalValueAmountUsd shouldBeEqualComparingTo "0.000168".toBigDecimal()
			totalPnlAmountUsd shouldBeEqualComparingTo BigDecimal("0.****************")
			totalPnlAmountFraction shouldBeEqualComparingTo BigDecimal("2332.******************************")
			oneDayChangeAmountUsd shouldBeEqualComparingTo "0.000168".toBigDecimal()
			oneDayChangeFraction shouldBeEqualComparingTo BigDecimal.ZERO
		}
	}
}
