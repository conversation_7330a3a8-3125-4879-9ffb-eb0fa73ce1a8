package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.module.bot.command.UpdateBotStatusCommand
import com.cleevio.fatbot.application.module.bot.exception.InvalidBotStateTransitionException
import com.cleevio.fatbot.application.module.bot.exception.MaxActiveBotsExceeded
import com.cleevio.fatbot.domain.bot.BotRepository
import com.cleevio.fatbot.domain.bot.MAX_ACTIVE_BOTS_PER_USER
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal

class UpdateBotStateCommandHandlerTest(
	@Autowired private val underTest: UpdateBotStateCommandHandler,
	@Autowired private val botRepository: BotRepository,
) : IntegrationTest() {

	@Test
	fun `should activate inactive bot`() {
		// given
		val userId = 1.toUUID()
		integrationTestHelper.getFirebaseUser(id = userId)
		integrationTestHelper.getFile(id = 20.toUUID())
		val bot = integrationTestHelper.getBot(
			id = 2.toUUID(),
			userId = userId,
			isActive = false,
			name = "testBot",
			avatarFileId = 20.toUUID(),
			profitTargetFraction = BigDecimal("0.30"),
			stopLossFraction = BigDecimal("0.15"),
			tradeAmount = BigDecimal("500.4"),
			buyFrequency = 15,
		)

		// when
		underTest.handle(UpdateBotStatusCommand(userId = userId, botId = bot.id, active = true))

		// then
		botRepository.findByIdOrNull(bot.id)!!.isActive shouldBe true
	}

	@Test
	fun `should deactivate active bot`() {
		// given
		val userId = 1.toUUID()
		integrationTestHelper.getFirebaseUser(id = userId)
		integrationTestHelper.getFile(id = 20.toUUID())
		val bot = integrationTestHelper.getBot(
			id = 2.toUUID(),
			userId = userId,
			isActive = true,
			name = "testBot",
			avatarFileId = 20.toUUID(),
			profitTargetFraction = BigDecimal("0.30"),
			stopLossFraction = BigDecimal("0.15"),
			tradeAmount = BigDecimal("500.4"),
			buyFrequency = 15,
		)

		// when
		underTest.handle(UpdateBotStatusCommand(userId = userId, botId = bot.id, active = false))

		// then
		botRepository.findByIdOrNull(bot.id)!!.isActive shouldBe false
	}

	@Test
	fun `should throw when activating already active bot`() {
		// given
		val userId = 1.toUUID()
		integrationTestHelper.getFirebaseUser(id = userId)
		integrationTestHelper.getFile(id = 20.toUUID())
		val bot = integrationTestHelper.getBot(
			id = 2.toUUID(),
			userId = userId,
			isActive = true,
			name = "testBot",
			avatarFileId = 20.toUUID(),
			profitTargetFraction = BigDecimal("0.30"),
			stopLossFraction = BigDecimal("0.15"),
			tradeAmount = BigDecimal("500.4"),
			buyFrequency = 15,
		)

		// when & then
		shouldThrow<InvalidBotStateTransitionException> {
			underTest.handle(UpdateBotStatusCommand(userId = userId, botId = bot.id, active = true))
		}
	}

	@Test
	fun `should throw when deactivating already inactive bot`() {
		// given
		val userId = 1.toUUID()
		integrationTestHelper.getFirebaseUser(id = userId)
		integrationTestHelper.getFile(id = 20.toUUID())
		val bot = integrationTestHelper.getBot(
			id = 2.toUUID(),
			userId = userId,
			isActive = false,
			name = "testBot",
			avatarFileId = 20.toUUID(),
			profitTargetFraction = BigDecimal("0.30"),
			stopLossFraction = BigDecimal("0.15"),
			tradeAmount = BigDecimal("500.4"),
			buyFrequency = 15,
		)

		// when & then
		shouldThrow<InvalidBotStateTransitionException> {
			underTest.handle(UpdateBotStatusCommand(userId = userId, botId = bot.id, active = false))
		}
	}

	@Test
	fun `should throw when maximum active bots limit exceeded`() {
		// given
		val userId = 1.toUUID()
		integrationTestHelper.getFirebaseUser(id = userId)
		integrationTestHelper.getFile(id = 20.toUUID())

		// Create MAX_ACTIVE_BOTS_PER_USER active bots
		repeat(MAX_ACTIVE_BOTS_PER_USER) { index ->
			integrationTestHelper.getBot(
				id = (index + 1).toUUID(),
				userId = userId,
				isActive = true,
				name = "testBot$index",
				avatarFileId = 20.toUUID(),
				profitTargetFraction = BigDecimal("0.30"),
				stopLossFraction = BigDecimal("0.15"),
				tradeAmount = BigDecimal("500.4"),
				buyFrequency = 15,
			)
		}

		// Create one inactive bot
		val inactiveBot = integrationTestHelper.getBot(
			id = 99.toUUID(),
			userId = userId,
			isActive = false,
			name = "inactiveBot",
			avatarFileId = 20.toUUID(),
			profitTargetFraction = BigDecimal("0.30"),
			stopLossFraction = BigDecimal("0.15"),
			tradeAmount = BigDecimal("500.4"),
			buyFrequency = 15,
		)

		// when & then
		shouldThrow<MaxActiveBotsExceeded> {
			underTest.handle(UpdateBotStatusCommand(userId = userId, botId = inactiveBot.id, active = true))
		}
	}
}
