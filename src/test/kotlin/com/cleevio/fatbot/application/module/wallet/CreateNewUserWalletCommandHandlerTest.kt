package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.command.CreateNewUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.exception.MaxWalletCountExceededException
import com.cleevio.fatbot.domain.wallet.WalletRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldNotThrowAny
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class CreateNewUserWalletCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val walletRepository: WalletRepository,
) : IntegrationTest() {

	@Test
	fun `should create user EVM wallet`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		commandBus(CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.EVM_MAINNET))

		walletRepository.findAll().first().run {
			userId shouldBe 1.toUUID()
			chain shouldBe Chain.EVM_MAINNET
			customName shouldBe "ETH wallet 1"
			buyAntiMevProtection shouldBe true
			sellAntiMevProtection shouldBe true
			isDefault shouldBe true
		}
	}

	@Test
	fun `should create user SOLANA wallet`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		commandBus(CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.SOLANA))

		walletRepository.findAll().first().run {
			userId shouldBe 1.toUUID()
			chain shouldBe Chain.SOLANA
			customName shouldBe "SOL wallet 1"
			buyAntiMevProtection shouldBe false
			sellAntiMevProtection shouldBe false
			isDefault shouldBe true
		}
	}

	@Test
	fun `should throw when creating wallet would exceed limit`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		shouldThrow<MaxWalletCountExceededException> {
			commandBus(CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.EVM_MAINNET))
		}
	}

	@Test
	fun `shouldn't throw when creating wallet just under the limit`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "GYCiYAtstSznrYaTi2hMkKJzqRhPBcfeRUqWJWtabULJ"),
			chain = Chain.SOLANA,
		)
		integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "GYCiYAtstSznrYaTi2hMkKJzqRhPBcfeRUqWJWtabULK"),
			chain = Chain.SOLANA,
		)

		shouldNotThrowAny {
			commandBus(CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.SOLANA))
		}
	}

	@Test
	fun `should create wallets with correctly assigned default wallet settings`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		commandBus(CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.EVM_MAINNET))
		commandBus(CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.EVM_MAINNET))
		commandBus(CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.EVM_MAINNET))
		commandBus(CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.SOLANA))

		val wallets = walletRepository.findAll().sortedBy { it.createdAt }
		wallets[0].customName shouldBe "ETH wallet 1"
		wallets[0].buyAntiMevProtection shouldBe true
		wallets[0].sellAntiMevProtection shouldBe true
		wallets[0].isDefault shouldBe true

		wallets[1].customName shouldBe "ETH wallet 2"
		wallets[1].buyAntiMevProtection shouldBe true
		wallets[1].sellAntiMevProtection shouldBe true
		wallets[1].isDefault shouldBe false

		wallets[2].customName shouldBe "ETH wallet 3"
		wallets[2].buyAntiMevProtection shouldBe true
		wallets[2].sellAntiMevProtection shouldBe true
		wallets[2].isDefault shouldBe false

		wallets[3].customName shouldBe "SOL wallet 1"
		wallets[3].buyAntiMevProtection shouldBe false
		wallets[3].sellAntiMevProtection shouldBe false
		wallets[3].isDefault shouldBe true
	}
}
