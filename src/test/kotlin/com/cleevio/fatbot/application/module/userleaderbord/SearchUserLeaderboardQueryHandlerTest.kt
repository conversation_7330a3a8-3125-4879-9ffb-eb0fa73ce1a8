package com.cleevio.fatbot.application.module.userleaderbord

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollAsc
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.userleaderbord.query.SearchUserLeaderboardQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class SearchUserLeaderboardQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should return all users on leaderboard sorted by rank`() {
		// Create three users with different rankings
		val user1 = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val user2 = integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")
		val user3 = integrationTestHelper.getFirebaseUser(id = 3.toUUID(), email = "<EMAIL>")

		// Create statistics for the users
		integrationTestHelper.getUserStatistics(
			userId = user1.id,
			beforeSave = { updateDonuts(1000.toBigDecimal()) },
		)
		integrationTestHelper.getUserStatistics(
			userId = user2.id,
			beforeSave = { updateDonuts(500.toBigDecimal()) },
		)
		integrationTestHelper.getUserStatistics(
			userId = user3.id,
			beforeSave = { updateDonuts(250.toBigDecimal()) },
		)

		// Create leaderboard entries
		integrationTestHelper.getUserLeaderboard(
			userId = user1.id,
			rank = 1,
			donutMultiplier = 2.0.toBigDecimal(),
			donutGainedSnapshot = 1000.toBigDecimal(),
		)
		integrationTestHelper.getUserLeaderboard(
			userId = user2.id,
			rank = 2,
			donutMultiplier = 1.5.toBigDecimal(),
			donutGainedSnapshot = 500.toBigDecimal(),
		)
		integrationTestHelper.getUserLeaderboard(
			userId = user3.id,
			rank = 3,
			donutMultiplier = 1.2.toBigDecimal(),
			donutGainedSnapshot = 250.toBigDecimal(),
		)

		val result = queryBus(SearchUserLeaderboardQuery(InfiniteScrollAsc.Integer(size = 10, lastId = null)))

		result.content.size shouldBe 3
		result.content[0].run {
			userId shouldBe user1.id
			rank shouldBe 1
			multiplier shouldBeEqualComparingTo 2.0.toBigDecimal()
			donutsGained shouldBeEqualComparingTo 1000.toBigDecimal()
		}
		result.content[1].run {
			userId shouldBe user2.id
			rank shouldBe 2
			multiplier shouldBeEqualComparingTo 1.5.toBigDecimal()
			donutsGained shouldBeEqualComparingTo 500.toBigDecimal()
		}
		result.content[2].run {
			userId shouldBe user3.id
			rank shouldBe 3
			multiplier shouldBeEqualComparingTo 1.2.toBigDecimal()
			donutsGained shouldBeEqualComparingTo 250.toBigDecimal()
		}
	}

	@Test
	fun `should support pagination`() {
		// Create multiple users with rankings
		(1..5).forEach { i ->
			val user = integrationTestHelper.getFirebaseUser(id = i.toUUID(), email = "test@test$i.com")
			integrationTestHelper.getUserStatistics(
				userId = user.id,
				beforeSave = { updateTradedAmount((1000 - (i * 100)).toBigDecimal()) },
			)
			integrationTestHelper.getUserLeaderboard(
				userId = user.id,
				rank = i,
				donutMultiplier = (2.0 - (i * 0.2)).toBigDecimal(),
				donutGainedSnapshot = (1000 - (i * 100)).toBigDecimal(),
			)
		}

		// when - request only the first page with 2 items
		val result = queryBus(
			SearchUserLeaderboardQuery(InfiniteScrollAsc.Integer(size = 2, lastId = null)),
		)

		// then
		result.content.size shouldBe 2

		// Check that we got the first two users by rank
		result.content[0].rank shouldBe 1
		result.content[1].rank shouldBe 2

		// when - request the second page with 2 items
		val secondPageResult = queryBus(
			SearchUserLeaderboardQuery(InfiniteScrollAsc.Integer(size = 2, lastId = 2)),
		)

		// then
		secondPageResult.content.size shouldBe 2

		// Check that we got the third and fourth users by rank
		secondPageResult.content[0].rank shouldBe 3
		secondPageResult.content[1].rank shouldBe 4

		// when - request the third page (which should have only one item)
		val thirdPageResult = queryBus(SearchUserLeaderboardQuery(InfiniteScrollAsc.Integer(size = 10, lastId = 4)))

		// then
		thirdPageResult.content.size shouldBe 1
		thirdPageResult.content[0].rank shouldBe 5
	}
}
