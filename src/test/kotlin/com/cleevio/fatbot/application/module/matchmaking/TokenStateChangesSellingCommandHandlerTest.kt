package com.cleevio.fatbot.application.module.matchmaking

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.solana.pumpfun.model.PumpFunCurveState
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.matchmaking.command.TokenStateChangesSellingCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionRepository
import com.cleevio.fatbot.domain.botwallet.BotWalletRepository
import com.cleevio.fatbot.setAndReturnPrivateProperty
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import kotlin.test.Test

class TokenStateChangesSellingCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val botWalletRepository: BotWalletRepository,
	@Autowired private val botMarketPositionRepository: BotMarketPositionRepository,
) : IntegrationTest() {

	@Test
	fun `should match selling bot - for token state changes - new token price is higher then sell profit price`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(***********)) },
		)

		integrationTestHelper.getBotMarketPosition(
			id = 5.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			assumedBuyPrice = BigDecimal(10),
			blockSlot = 4,
		)

		// when
		commandBus(
			TokenStateChangesSellingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesSellingCommand.NewState(
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						marketCapUsd = BigDecimal.valueOf(1840.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(13.1),
						fractionOfSellTransactions = BigDecimal.valueOf(0.3),
					),
				),
			),
		)

		// then
		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(***********)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
		botMarketPositions.first().state shouldBe BotMarketPositionState.PENDING_CLOSED
	}

	@Test
	fun `should match selling bot - for token state changes - new token sell volume is lower than bot sell volume`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			buyVolume = BigDecimal(0.5),
			sellVolume = BigDecimal(0.5),
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(***********)) },
		)

		integrationTestHelper.getBotMarketPosition(
			id = 5.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			assumedBuyPrice = BigDecimal(10),
			blockSlot = 4,
		)

		// when
		commandBus(
			TokenStateChangesSellingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesSellingCommand.NewState(
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						marketCapUsd = BigDecimal.valueOf(1840.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.2),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal(8.3),
						fractionOfSellTransactions = BigDecimal.valueOf(0.3),
					),
				),
			),
		)

		// then
		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(***********)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
		botMarketPositions.first().state shouldBe BotMarketPositionState.PENDING_CLOSED
	}

	@Test
	fun `should match selling bot - for token state changes - new token price is lower then stop loss price`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(***********)) },
		)

		integrationTestHelper.getBotMarketPosition(
			id = 5.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			assumedBuyPrice = BigDecimal(10),
			blockSlot = 4,
		)

		// when
		commandBus(
			TokenStateChangesSellingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesSellingCommand.NewState(
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						marketCapUsd = BigDecimal.valueOf(1840.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal(8.3),
						fractionOfSellTransactions = BigDecimal.valueOf(0.3),
					),
				),
			),
		)

		// then
		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(***********)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
		botMarketPositions.first().state shouldBe BotMarketPositionState.PENDING_CLOSED
	}

	@Test
	fun `should not match selling bots - where the new token state change was not bought`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(***********)) },
		)

		integrationTestHelper.getBotMarketPosition(
			id = 5.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			assumedBuyPrice = BigDecimal(10),
			blockSlot = 4,
		)

		// when
		commandBus(
			TokenStateChangesSellingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesSellingCommand.NewState(
						tokenAddress = AddressWrapper("2sCUCJdVkmyXp4dT8sFaA9LKgSMK4yDPi9zLHiwXpump"),
						marketCapUsd = BigDecimal.valueOf(1290.0),
						liquidityUsd = BigDecimal.valueOf(906.0),
						volumeUsd = BigDecimal.valueOf(8.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal(33.0),
						fractionOfSellTransactions = BigDecimal.valueOf(0.3),
					),
				),
			),
		)

		// then
		val notUpdatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		notUpdatedBotWallet shouldNotBe null

		notUpdatedBotWallet!!.id shouldBe 4.toUUID()
		notUpdatedBotWallet.balance shouldBe BigInteger.valueOf(***********)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
		botMarketPositions.first().state shouldBe BotMarketPositionState.OPENED
	}

	@Test
	fun `should not match selling bots - where market position for same token address is not opened`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(***********)) },
		)

		integrationTestHelper.getBotMarketPosition(
			id = 5.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			assumedBuyPrice = BigDecimal(10),
			blockSlot = 4,
			beforeSave = { setAndReturnPrivateProperty("state", BotMarketPositionState.PENDING_CLOSED) },
		)

		// when
		commandBus(
			TokenStateChangesSellingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesSellingCommand.NewState(
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						marketCapUsd = BigDecimal.valueOf(1840.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal(8.3),
						fractionOfSellTransactions = BigDecimal.valueOf(0.3),
					),
				),
			),
		)

		// then
		val notUpdatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		notUpdatedBotWallet shouldNotBe null

		notUpdatedBotWallet!!.id shouldBe 4.toUUID()
		notUpdatedBotWallet.balance shouldBe BigInteger.valueOf(***********)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
		botMarketPositions.first().state shouldBe BotMarketPositionState.PENDING_CLOSED
	}

	@Test
	fun `should not match selling bots - where new token price is in market position range`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(***********)) },
		)

		integrationTestHelper.getBotMarketPosition(
			id = 5.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			assumedBuyPrice = BigDecimal(10),
			blockSlot = 4,
		)

		// when
		commandBus(
			TokenStateChangesSellingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesSellingCommand.NewState(
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						marketCapUsd = BigDecimal.valueOf(1290.0),
						liquidityUsd = BigDecimal.valueOf(906.0),
						volumeUsd = BigDecimal.valueOf(8.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal(8.5),
						fractionOfSellTransactions = BigDecimal.valueOf(0.3),
					),
				),
			),
		)

		// then
		val notUpdatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		notUpdatedBotWallet shouldNotBe null

		notUpdatedBotWallet!!.id shouldBe 4.toUUID()
		notUpdatedBotWallet.balance shouldBe BigInteger.valueOf(***********)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
		botMarketPositions.first().state shouldBe BotMarketPositionState.OPENED
	}

	@Test
	fun `should not match selling bots - when new token selling volume is higher than sell volume of bot`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			buyVolume = BigDecimal(0.8),
			sellVolume = BigDecimal(0.2),
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(***********)) },
		)

		integrationTestHelper.getBotMarketPosition(
			id = 5.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			assumedBuyPrice = BigDecimal(10),
			blockSlot = 4,
		)

		// when
		commandBus(
			TokenStateChangesSellingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesSellingCommand.NewState(
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						marketCapUsd = BigDecimal.valueOf(1290.0),
						liquidityUsd = BigDecimal.valueOf(906.0),
						volumeUsd = BigDecimal.valueOf(8.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal(12.0),
						fractionOfSellTransactions = BigDecimal.valueOf(0.3),
					),
				),
			),
		)

		// then
		val notUpdatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		notUpdatedBotWallet shouldNotBe null

		notUpdatedBotWallet!!.id shouldBe 4.toUUID()
		notUpdatedBotWallet.balance shouldBe BigInteger.valueOf(***********)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
		botMarketPositions.first().state shouldBe BotMarketPositionState.OPENED
	}
}
