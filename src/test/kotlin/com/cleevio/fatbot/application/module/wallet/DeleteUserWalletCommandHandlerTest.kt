package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.service.MarketPositionSnapshotService
import com.cleevio.fatbot.application.module.wallet.command.CreateNewUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.command.DeleteUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.exception.LastWalletDeletionException
import com.cleevio.fatbot.application.module.wallet.exception.WalletNotFoundException
import com.cleevio.fatbot.domain.wallet.WalletRepository
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger

class DeleteUserWalletCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val walletRepository: WalletRepository,
	@Autowired private val marketPositionSnapshotService: MarketPositionSnapshotService,
) : IntegrationTest() {

	@Test
	fun `should not allow deletion of last wallet on chain`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 10.toUUID(), userId = 1.toUUID(), chain = Chain.EVM_BASE)
		integrationTestHelper.getWallet(id = 20.toUUID(), userId = 1.toUUID(), chain = Chain.EVM_MAINNET)

		walletRepository.count() shouldBe 2

		shouldThrow<LastWalletDeletionException> {
			commandBus(
				DeleteUserWalletCommand(
					userId = 1.toUUID(),
					walletId = 10.toUUID(),
				),
			)
		}

		walletRepository.count() shouldBe 2
	}

	@Test
	fun `should delete user wallet`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 10.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getWallet(id = 20.toUUID(), userId = 1.toUUID(), address = 20.toAddress())

		walletRepository.count() shouldBe 2

		commandBus(
			DeleteUserWalletCommand(
				userId = 1.toUUID(),
				walletId = 10.toUUID(),
			),
		)

		walletRepository.count() shouldBe 1
	}

	@Test
	fun `should delete user wallet with transactions and market position`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 10.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getWallet(id = 20.toUUID(), userId = 1.toUUID(), address = 20.toAddress())
		integrationTestHelper.getTransaction(id = 20.toUUID(), walletId = 10.toUUID(), signedTx = "0x000000000100")
		integrationTestHelper.getTransaction(id = 21.toUUID(), walletId = 10.toUUID(), signedTx = "0x000000000200")
		integrationTestHelper.getMarketPosition(
			id = 30.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = AddressWrapper("******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = BigInteger("1000000000000000000"),
					amountOfBaseCurrencyPaid = BigInteger("1000000000000000000"),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)
		integrationTestHelper.getMarketPosition(
			id = 31.toUUID(),
			walletId = 10.toUUID(),
			tokenAddress = AddressWrapper("******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = BigInteger("1000000000000000000"),
					amountOfBaseCurrencyPaid = BigInteger("1000000000000000000"),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)
		// manually also create market position snapshots
		marketPositionSnapshotService.createNewSnapshotsOfMarketPositionTableAndDeleteOldSnapshots()

		walletRepository.count() shouldBe 2

		commandBus(
			DeleteUserWalletCommand(
				userId = 1.toUUID(),
				walletId = 10.toUUID(),
			),
		)

		walletRepository.count() shouldBe 1
	}

	@Test
	fun `should not delete user wallet when does not belong to user`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 10.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		shouldThrow<WalletNotFoundException> {
			commandBus(
				DeleteUserWalletCommand(
					userId = 2.toUUID(),
					walletId = 10.toUUID(),
				),
			)
		}
	}

	@Test
	fun `should delete user default wallet and mark oldest one as new default`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		commandBus(CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.EVM_MAINNET))
		commandBus(CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.EVM_MAINNET))
		commandBus(CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.EVM_MAINNET))

		commandBus(CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.EVM_BASE))
		commandBus(CreateNewUserWalletCommand(userId = 1.toUUID(), chain = Chain.SOLANA))

		val walletsBeforeDelete = walletRepository.findAll()
		walletsBeforeDelete.size shouldBe 5

		val beforeDeleteOrdered = walletsBeforeDelete.sortedBy { it.createdAt }
		beforeDeleteOrdered[0].isDefault shouldBe true
		beforeDeleteOrdered[1].isDefault shouldBe false
		beforeDeleteOrdered[2].isDefault shouldBe false

		beforeDeleteOrdered[3].isDefault shouldBe true
		beforeDeleteOrdered[4].isDefault shouldBe true

		commandBus(
			DeleteUserWalletCommand(
				userId = 1.toUUID(),
				walletId = beforeDeleteOrdered[0].id,
			),
		)

		val walletsAfterDelete = walletRepository.findAll()
		walletsAfterDelete.size shouldBe 4

		val afterDeleteOrdered = walletsAfterDelete.sortedBy { it.createdAt }

		afterDeleteOrdered[0].isDefault shouldBe true
		afterDeleteOrdered[1].isDefault shouldBe false

		afterDeleteOrdered[2].isDefault shouldBe true
		afterDeleteOrdered[3].isDefault shouldBe true
	}
}
