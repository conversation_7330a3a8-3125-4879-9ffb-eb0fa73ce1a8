package com.cleevio.fatbot.application.module.bot.service

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.application.module.bot.BotFinderService
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ActiveBotsDaysServiceTest(
	@Autowired private val activeBotsDaysService: ActiveBotsDaysService,
	@Autowired private val integrationTestClock: IntegrationTestClock,
	@Autowired private val botFinderService: BotFinderService,
) : IntegrationTest() {

	@Test
	fun `should increase number of active days for only active bots`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 2.toUUID(),
		)

		integrationTestHelper.getFile(id = 4.toUUID())

		integrationTestHelper.getBot(
			id = 5.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 4.toUUID(),
			isActive = false,
		)

		// when
		activeBotsDaysService.processBotActiveDays()

		// then
		val allBots = botFinderService.findAll()
		allBots.size shouldBe 2

		val activeBot = allBots.first { it.isActive && it.id == 3.toUUID() }
		activeBot.id shouldBe 3.toUUID()
		activeBot.isActive shouldBe true
		activeBot.numberOfActiveDays shouldBe 2

		val deactivatedBot = allBots.first { !it.isActive }
		deactivatedBot.id shouldBe 5.toUUID()
		deactivatedBot.isActive shouldBe false
		deactivatedBot.numberOfActiveDays shouldBe 1
	}
}
