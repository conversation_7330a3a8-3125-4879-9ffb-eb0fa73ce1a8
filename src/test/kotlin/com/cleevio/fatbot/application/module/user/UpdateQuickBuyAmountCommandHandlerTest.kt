package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.user.command.UpdateQuickBuyAmountCommand
import com.cleevio.fatbot.application.module.user.exception.IllegalQuickBuyAmount
import com.cleevio.fatbot.domain.user.FirebaseUserRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class UpdateQuickBuyAmountCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val firebaseUserRepository: FirebaseUserRepository,
) : IntegrationTest() {

	@Test
	fun `should set default value for quick buy amount to 10`() {
		val user = integrationTestHelper.getFirebaseUser(
			id = 1.toUUID(),
		)

		user.quickBuyAmountUsd shouldBe 10.toBigDecimal()
	}

	@Test
	fun `should save quick buy amount`() {
		firebaseUserRepository.findAll().size shouldBe 0

		val user = integrationTestHelper.getFirebaseUser(
			id = 1.toUUID(),
		)

		commandBus(UpdateQuickBuyAmountCommand(userId = user.id, quickBuyAmountUsd = 20.5.toBigDecimal()))

		val updatedUser = firebaseUserRepository.findAll().first()

		updatedUser.quickBuyAmountUsd shouldBe 20.5.toBigDecimal()
	}

	@Test
	fun `should not be able to set quick buy amount to zero`() {
		firebaseUserRepository.findAll().size shouldBe 0

		val user = integrationTestHelper.getFirebaseUser(
			id = 1.toUUID(),
		)

		shouldThrowExactly<IllegalQuickBuyAmount> {
			commandBus(UpdateQuickBuyAmountCommand(userId = user.id, quickBuyAmountUsd = 0.toBigDecimal()))
		}

		val savedUser = firebaseUserRepository.findAll().first()
		savedUser.quickBuyAmountUsd shouldBe BigDecimal("10")
	}

	@Test
	fun `should not be able to set quick buy amount to negative`() {
		firebaseUserRepository.findAll().size shouldBe 0

		val user = integrationTestHelper.getFirebaseUser(
			id = 1.toUUID(),
		)

		shouldThrowExactly<IllegalQuickBuyAmount> {
			commandBus(UpdateQuickBuyAmountCommand(userId = user.id, quickBuyAmountUsd = BigDecimal("-3.14")))
		}

		val savedUser = firebaseUserRepository.findAll().first()
		savedUser.quickBuyAmountUsd shouldBe BigDecimal("10")
	}
}
