package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.query.GetTokenBalanceQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger

@Suppress("ktlint:standard:max-line-length")
class GetTokenBalanceQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should get correct platform and on chain balance for token`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		integrationTestHelper.getMarketPosition(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 120000.toBigInteger(),
					amountOfBaseCurrencyPaid = 120000.toBigInteger(),
					exchangeRate = BigDecimal("3"),
				)
				newSell(
					amountOfTokensSold = 20000.toBigInteger(),
					amountOfBaseCurrencyReceived = 20000.toBigInteger(),
				)
			},
		)

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			tokenDecimals = 9.toBigInteger(),
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j

		// GetErc20TokenBalancesEVM call - on chain balance is 80000, platform balance is 100000
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f4700000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000000000000013880"

		val result = queryBus(
			GetTokenBalanceQuery(
				userId = user.id,
				walletId = wallet.id,
				tokenAddress = AddressWrapper("******************************************"),
			),
		)

		result.onChainBalance.amount shouldBeEqualComparingTo 0.00008.toBigDecimal()
		result.platformBalance.amount shouldBeEqualComparingTo 0.0001.toBigDecimal()
	}

	@Test
	fun `should return 0 on platform balance for non-existing token position`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
		)

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			tokenDecimals = 6.toBigInteger(),
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j

		// GetErc20TokenBalancesEVM call - on chain balance is 80000, platform balance is 100000
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f4700000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000000000000013880"

		val result = queryBus(
			GetTokenBalanceQuery(
				userId = user.id,
				walletId = wallet.id,
				tokenAddress = AddressWrapper("******************************************"),
			),
		)

		result.onChainBalance.amount shouldBeEqualComparingTo 0.08.toBigDecimal()
		result.platformBalance.amount shouldBeEqualComparingTo 0.toBigDecimal()
	}
}
