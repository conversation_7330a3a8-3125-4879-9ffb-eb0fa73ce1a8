package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.solana.pumpswap.model.PumpswapPool
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.matchmaking.command.BotsSellPromotedTokenCommand
import com.cleevio.fatbot.application.module.token.command.TokenPromotedToPumpswapCommand
import com.cleevio.fatbot.application.module.token.exception.FailedToVerifyPumpswapPairException
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.token.TokenPairInfoRepository
import com.cleevio.fatbot.setAndReturnPrivatePropertyOfProxy
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.types.config.Commitment
import org.p2p.solanaj.rpc.types.config.ProgramAccountConfig
import org.springframework.beans.factory.annotation.Autowired

class TokenPromotedToPumpswapCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val tokenPairInfoRepository: TokenPairInfoRepository,
	// Only for verifying calls
	@Autowired private val tokenPromotedToPumpswapCommandHandler: TokenPromotedToPumpswapCommandHandler,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		mockkObject(PumpswapPool.Companion)
	}

	@AfterEach
	fun tearDown() {
		unmockkObject(PumpswapPool.Companion)
	}

	@Test
	fun `should replace old token pair with new`() {
		integrationTestHelper.getEvmTokenInfo(
			id = 1.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
			isToken2022 = false,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getTokenPairInfo(
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
			tokenDecimals = 6.toBigInteger(),
			pairAddress = AddressWrapper("8eFZVuB2ZbpeadUb36Z9KT63vTR4DVKjE1xH6YNGpump"),
			dexType = GetDex.Dex.PUMP_FUN,
			uniswapV3Fee = null,
		)

		// Verify pair call
		every { rpcClient.api.getProgramAccounts(any(), any<ProgramAccountConfig>()) } returns listOf(
			mockk { every { publicKey } returns PublicKey("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc") },
		)

		// getPumpswap pool
		every {
			rpcClient.api.getAccountInfo(
				PublicKey("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc"),
				mapOf("commitment" to Commitment.CONFIRMED),
			)
		} returns mockk { every { decodedData } returns byteArrayOf(1) }

		every { PumpswapPool.read(byteArrayOf(1)) } returns mockk {
			every { coinCreator } returns AddressWrapper("AVsRdizrKsZ3MP5y7bW9Yihr7aBvF4zu4s8s9Ltqm4fu")
		}

		commandBus(
			TokenPromotedToPumpswapCommand(
				blockSlot = 1000,
				tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
				pairAddress = AddressWrapper("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc"),
				creator = AddressWrapper("AVsRdizrKsZ3MP5y7bW9Yihr7aBvF4zu4s8s9Ltqm4fu"),
			),
		)

		val oldPairInfo = tokenPairInfoRepository.findByTokenAddressAndChainAndDexType(
			tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
			chain = Chain.SOLANA,
			dexType = GetDex.Dex.PUMP_FUN,
		)

		oldPairInfo shouldBe null

		val newPairInfo = tokenPairInfoRepository.findByTokenAddressAndChainAndDexType(
			tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
			chain = Chain.SOLANA,
			dexType = GetDex.Dex.PUMP_SWAP,
		)

		newPairInfo shouldNotBe null
		newPairInfo?.pairAddress shouldBe AddressWrapper("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc")
	}

	@Test
	fun `should do nothing if pair is already promoted`() {
		integrationTestHelper.getEvmTokenInfo(
			id = 1.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
			isToken2022 = false,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getTokenPairInfo(
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
			tokenDecimals = 6.toBigInteger(),
			pairAddress = AddressWrapper("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc"),
			dexType = GetDex.Dex.PUMP_SWAP,
			uniswapV3Fee = null,
		)

		// Verify pair
		every { rpcClient.api.getProgramAccounts(any(), any<ProgramAccountConfig>()) } returns listOf(
			mockk { every { publicKey } returns PublicKey("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc") },
		)

		// Get Pumpswap pool
		every {
			rpcClient.api.getAccountInfo(
				PublicKey("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc"),
				mapOf("commitment" to Commitment.CONFIRMED),
			)
		} returns mockk { every { decodedData } returns byteArrayOf(1) }

		every { PumpswapPool.read(byteArrayOf(1)) } returns mockk {
			every { coinCreator } returns AddressWrapper("AVsRdizrKsZ3MP5y7bW9Yihr7aBvF4zu4s8s9Ltqm4fu")
		}

		commandBus(
			TokenPromotedToPumpswapCommand(
				blockSlot = 1000,
				tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
				pairAddress = AddressWrapper("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc"),
				creator = AddressWrapper("AVsRdizrKsZ3MP5y7bW9Yihr7aBvF4zu4s8s9Ltqm4fu"),
			),
		)

		val pairInfo = tokenPairInfoRepository.findByTokenAddressAndChainAndDexType(
			tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
			chain = Chain.SOLANA,
			dexType = GetDex.Dex.PUMP_SWAP,
		)

		pairInfo shouldNotBe null
		pairInfo?.pairAddress shouldBe AddressWrapper("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc")
	}

	@Test
	fun `should invoke BotsSellPromotedTokenCommand even if pumpfun pair does not exist`() {
		// Insert a spy to verify BotsSellPromotedTokenCommand call
		val commandBusSpy = spyk(commandBus, recordPrivateCalls = true)
		tokenPromotedToPumpswapCommandHandler.setAndReturnPrivatePropertyOfProxy("commandBus", commandBusSpy)

		// Is verified
		every { rpcClient.api.getProgramAccounts(any(), any<ProgramAccountConfig>()) } returns listOf(
			mockk { every { publicKey } returns PublicKey("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc") },
		)

		// Get pumpswap pair
		every {
			rpcClient.api.getAccountInfo(
				PublicKey("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc"),
				mapOf("commitment" to Commitment.CONFIRMED),
			)
		} returns mockk { every { decodedData } returns byteArrayOf(1) }

		every { PumpswapPool.read(byteArrayOf(1)) } returns mockk {
			every { coinCreator } returns AddressWrapper("AVsRdizrKsZ3MP5y7bW9Yihr7aBvF4zu4s8s9Ltqm4fu")
		}

		commandBus(
			TokenPromotedToPumpswapCommand(
				blockSlot = 1000,
				tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
				pairAddress = AddressWrapper("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc"),
				creator = AddressWrapper("AVsRdizrKsZ3MP5y7bW9Yihr7aBvF4zu4s8s9Ltqm4fu"),
			),
		)

		verifySequence {
			commandBusSpy(
				BotsSellPromotedTokenCommand(
					blockSlot = 1000,
					tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
					pairAddress = AddressWrapper("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc"),
					creator = AddressWrapper("AVsRdizrKsZ3MP5y7bW9Yihr7aBvF4zu4s8s9Ltqm4fu"),
				),
			)
		}
	}

	@Test
	fun `should throw if pumpswap pair fails to verify`() {
		integrationTestHelper.getEvmTokenInfo(
			id = 1.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
			isToken2022 = false,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getTokenPairInfo(
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
			tokenDecimals = 6.toBigInteger(),
			pairAddress = AddressWrapper("8eFZVuB2ZbpeadUb36Z9KT63vTR4DVKjE1xH6YNGpump"),
			dexType = GetDex.Dex.PUMP_FUN,
			uniswapV3Fee = null,
		)

		val commandBusSpy = spyk(commandBus, recordPrivateCalls = true)
		tokenPromotedToPumpswapCommandHandler.setAndReturnPrivatePropertyOfProxy("commandBus", commandBusSpy)

		every { rpcClient.api.getProgramAccounts(any(), any<ProgramAccountConfig>()) } returns emptyList()

		shouldThrow<FailedToVerifyPumpswapPairException> {
			commandBus(
				TokenPromotedToPumpswapCommand(
					blockSlot = 1000,
					tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
					pairAddress = AddressWrapper("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc"),
					creator = AddressWrapper("AVsRdizrKsZ3MP5y7bW9Yihr7aBvF4zu4s8s9Ltqm4fu"),
				),
			)
		}

		verify(exactly = 0) { commandBusSpy(any<BotsSellPromotedTokenCommand>()) }
	}

	@Test
	fun `should throw if pumpswap pair creator does not match creator param`() {
		integrationTestHelper.getEvmTokenInfo(
			id = 1.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
			isToken2022 = false,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getTokenPairInfo(
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
			tokenDecimals = 6.toBigInteger(),
			pairAddress = AddressWrapper("8eFZVuB2ZbpeadUb36Z9KT63vTR4DVKjE1xH6YNGpump"),
			dexType = GetDex.Dex.PUMP_FUN,
			uniswapV3Fee = null,
		)

		val commandBusSpy = spyk(commandBus, recordPrivateCalls = true)
		tokenPromotedToPumpswapCommandHandler.setAndReturnPrivatePropertyOfProxy("commandBus", commandBusSpy)

		// Is verified
		every { rpcClient.api.getProgramAccounts(any(), any<ProgramAccountConfig>()) } returns listOf(
			mockk { every { publicKey } returns PublicKey("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc") },
		)

		// Get pumpswap pool
		every {
			rpcClient.api.getAccountInfo(
				PublicKey("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc"),
				mapOf("commitment" to Commitment.CONFIRMED),
			)
		} returns mockk { every { decodedData } returns byteArrayOf(1) }

		// Different coin creator
		every { PumpswapPool.read(byteArrayOf(1)) } returns mockk {
			every { coinCreator } returns AddressWrapper("FQ2Fr3tkBuXeppXMqRAjNTvMRPJgfzXSYZpy6vZ9ePy5")
		}

		shouldThrow<IllegalArgumentException> {
			commandBus(
				TokenPromotedToPumpswapCommand(
					blockSlot = 1000,
					tokenAddress = AddressWrapper("7bzvxNy49C8r5s47zuPMMLuvKWVz2fJsVM38A3BQpump"),
					pairAddress = AddressWrapper("A8CSmQ7s6o4YewfdKeYmntNEK83NFU2tabBS6kGYBVpc"),
					creator = AddressWrapper("AVsRdizrKsZ3MP5y7bW9Yihr7aBvF4zu4s8s9Ltqm4fu"),
				),
			)
		}

		verify(exactly = 0) { commandBusSpy(any<BotsSellPromotedTokenCommand>()) }
	}
}
