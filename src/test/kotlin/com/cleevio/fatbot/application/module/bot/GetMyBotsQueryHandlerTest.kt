package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.`in`.rest.dto.PatchBotSettingsRequest
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bot.query.GetMyBotsQuery
import com.cleevio.fatbot.application.module.botdraft.model.toValidBotDraftUpdate
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.botdraft.BotDraftRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.net.URI
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Optional

class GetMyBotsQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val botDraftRepository: BotDraftRepository,
	@Autowired private val clock: IntegrationTestClock,
) : IntegrationTest() {

	@Test
	fun `should return multiple bots for user`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val file1 = integrationTestHelper.getFile(id = 2.toUUID())
		val file2 = integrationTestHelper.getFile(id = 3.toUUID())

		integrationTestHelper.getBot(
			id = 11.toUUID(),
			userId = 1.toUUID(),
			isActive = true,
			name = "TestBot1",
			avatarFileId = 2.toUUID(),
			tradeAmount = 10.toBigDecimal(),
			buyFrequency = 5,
			profitTargetFraction = 0.1.toBigDecimal(),
			stopLossFraction = 0.05.toBigDecimal(),
		)
		integrationTestHelper.getBotWallet(id = 21.toUUID(), botId = 11.toUUID()) {
			increaseBalance(1e9.toLong().toBigInteger())
		}
		integrationTestHelper.getBotTransaction(botWalletId = 21.toUUID(), type = BotTransactionType.BUY)
		integrationTestHelper.getBotTransaction(
			botWalletId = 21.toUUID(),
			txHash = TxHash("txHash1"),
			type = BotTransactionType.SELL,
		)
		integrationTestHelper.getBotTransaction(
			botWalletId = 21.toUUID(),
			txHash = TxHash("txHash2"),
			type = BotTransactionType.SELL,
		)

		val now = clock.currentTime()
		integrationTestHelper.getBotPortfolioValueSnapshot(
			botId = 11.toUUID(),
			snapshotMadeAt = now.minus(1, ChronoUnit.DAYS),
			chain = Chain.SOLANA,
			portfolioValueUsd = 123.321.toBigDecimal(),
			acquisitionValueUsd = BigDecimal("110.0"),
		)

		integrationTestHelper.getBot(
			id = 12.toUUID(),
			userId = 1.toUUID(),
			isActive = false,
			name = "TestBot2",
			avatarFileId = 3.toUUID(),
			tradeAmount = 11.toBigDecimal(),
			buyFrequency = 6,
			profitTargetFraction = 0.11.toBigDecimal(),
			stopLossFraction = 0.051.toBigDecimal(),
		)
		integrationTestHelper.getBotWallet(id = 22.toUUID(), botId = 12.toUUID()) {
			increaseBalance("150".toBigInteger())
		}
		integrationTestHelper.getBotTransaction(
			botWalletId = 22.toUUID(),
			type = BotTransactionType.BUY,
			txHash = TxHash("txHash3"),
		)
		integrationTestHelper.getBotTransaction(
			botWalletId = 22.toUUID(),
			type = BotTransactionType.SELL,
			txHash = TxHash("txHash4"),
		)

		val draft = integrationTestHelper.getBotDraft(
			id = 13.toUUID(),
			userId = 1.toUUID(),
		)
		draft.update(
			PatchBotSettingsRequest(name = "Draft1").toValidBotDraftUpdate(draft),
		)
		botDraftRepository.save(draft)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns "150".toBigDecimal()

		val results = queryBus(GetMyBotsQuery(userId = 1.toUUID(), timeRange = TimeRange.DAY))

		results shouldHaveSize 3
		results.single { it.id == 11.toUUID() }.run {
			isActive shouldBe true
			numberOfActiveDays shouldBe 1
			name shouldBe "TestBot1"
			avatarFileUrl shouldBe URI("http://localhost:8080/${file1.fileName()}")
			botWalletAddress shouldBe AddressWrapper("CAJM1r7SjSFxv99tRjpKUVQUPyq3CwtASyYLQU8EPrEA")
			balanceUsd!! shouldBeEqualComparingTo "150".toBigDecimal()
			timeRangeChangeUsd!! shouldBeEqualComparingTo "150.000000000".toBigDecimal()
			timeRangeChangeFraction!! shouldBeEqualComparingTo "0".toBigDecimal()
			buyCount shouldBe 1.toBigInteger()
			sellCount shouldBe 2.toBigInteger()
			draftCompleteness shouldBe null
		}
		results.single { it.id == 12.toUUID() }.run {
			isActive shouldBe false
			numberOfActiveDays shouldBe 1
			name shouldBe "TestBot2"
			avatarFileUrl shouldBe URI("http://localhost:8080/${file2.fileName()}")
			botWalletAddress shouldBe AddressWrapper("CAJM1r7SjSFxv99tRjpKUVQUPyq3CwtASyYLQU8EPrEA")
			balanceUsd!! shouldBeEqualComparingTo "0.0000225".toBigDecimal()
			timeRangeChangeUsd!! shouldBeEqualComparingTo "0.0000225".toBigDecimal()
			timeRangeChangeFraction!! shouldBeEqualComparingTo 0.toBigDecimal()
			buyCount shouldBe 1.toBigInteger()
			sellCount shouldBe 1.toBigInteger()
			draftCompleteness shouldBe null
		}
		results.single { it.id == 13.toUUID() }.run {
			isActive shouldBe false
			name shouldBe "Draft1"
			avatarFileUrl shouldBe null
			botWalletAddress shouldBe null
			balanceUsd shouldBe null
			timeRangeChangeUsd shouldBe null
			timeRangeChangeFraction shouldBe null
			buyCount shouldBe null
			sellCount shouldBe null
			draftCompleteness!! shouldBeEqualComparingTo 0.08.toBigDecimal() // 100/12
		}
	}

	@Test
	fun `should return multiple bots with different time ranges`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val file1 = integrationTestHelper.getFile(id = 2.toUUID())
		val file2 = integrationTestHelper.getFile(id = 3.toUUID())

		integrationTestHelper.getBot(
			id = 11.toUUID(),
			userId = 1.toUUID(),
			isActive = true,
			name = "TestBot1",
			avatarFileId = 2.toUUID(),
			tradeAmount = 10.toBigDecimal(),
			buyFrequency = 5,
			profitTargetFraction = 0.1.toBigDecimal(),
			stopLossFraction = 0.05.toBigDecimal(),
		)
		integrationTestHelper.getBotWallet(id = 21.toUUID(), botId = 11.toUUID())
		integrationTestHelper.getBotTransaction(
			botWalletId = 21.toUUID(),
			type = BotTransactionType.BUY,
			// buy transaction must be first chronologically
			createdAt = Instant.now(clock).minus(361, ChronoUnit.DAYS),
		)
		integrationTestHelper.getBotTransaction(
			botWalletId = 21.toUUID(),
			type = BotTransactionType.SELL,
			txHash = TxHash("txHash1"),
			exchangeRateUsd = 250.toBigDecimal(),
			createdAt = Instant.now(clock).minus(360, ChronoUnit.DAYS),
		)
		integrationTestHelper.getBotTransaction(
			botWalletId = 21.toUUID(),
			type = BotTransactionType.SELL,
			txHash = TxHash("txHash2"),
			exchangeRateUsd = 250.toBigDecimal(),
			createdAt = Instant.now(clock).minus(30, ChronoUnit.DAYS),
		)
		integrationTestHelper.getBotTransaction(
			botWalletId = 21.toUUID(),
			type = BotTransactionType.SELL,
			txHash = TxHash("txHash3"),
			exchangeRateUsd = 250.toBigDecimal(),
			createdAt = Instant.now(clock).minus(6, ChronoUnit.DAYS),
		)
		integrationTestHelper.getBotTransaction(
			botWalletId = 21.toUUID(),
			type = BotTransactionType.SELL,
			txHash = TxHash("txHash4"),
			exchangeRateUsd = 250.toBigDecimal(),
			createdAt = Instant.now(clock).minus(23, ChronoUnit.HOURS),
		)
		integrationTestHelper.getBotTransaction(
			botWalletId = 21.toUUID(),
			type = BotTransactionType.SELL,
			txHash = TxHash("txHash5"),
			exchangeRateUsd = 250.toBigDecimal(),
			createdAt = Instant.now(clock).minus(59, ChronoUnit.MINUTES),
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns "150".toBigDecimal()

		val resultsHour = queryBus(GetMyBotsQuery(userId = 1.toUUID(), timeRange = TimeRange.HOUR))
		resultsHour shouldHaveSize 1
		resultsHour.first().run {
			isActive shouldBe true
			numberOfActiveDays shouldBe 1
			name shouldBe "TestBot1"
			avatarFileUrl shouldBe URI("http://localhost:8080/${file1.fileName()}")
			botWalletAddress shouldBe AddressWrapper("CAJM1r7SjSFxv99tRjpKUVQUPyq3CwtASyYLQU8EPrEA")
			// TODO bot market positions -> balance + PnL
			// balanceUsd shouldBeEqualComparingTo 0.toBigDecimal()
			// timeRangeChangeUsd shouldBeEqualComparingTo 0.toBigDecimal()
			// timeRangeChangeFraction shouldBeEqualComparingTo 0.toBigDecimal()
			buyCount shouldBe 0.toBigInteger()
			sellCount shouldBe 1.toBigInteger()
			draftCompleteness shouldBe null
		}

		val resultsDay = queryBus(GetMyBotsQuery(userId = 1.toUUID(), timeRange = TimeRange.DAY))
		resultsDay shouldHaveSize 1
		resultsDay.first().run {
			isActive shouldBe true
			numberOfActiveDays shouldBe 1
			name shouldBe "TestBot1"
			avatarFileUrl shouldBe URI("http://localhost:8080/${file1.fileName()}")
			botWalletAddress shouldBe AddressWrapper("CAJM1r7SjSFxv99tRjpKUVQUPyq3CwtASyYLQU8EPrEA")
			// TODO bot market positions -> balance + PnL
			// balanceUsd shouldBeEqualComparingTo 0.toBigDecimal()
			// timeRangeChangeUsd shouldBeEqualComparingTo 0.toBigDecimal()
			// timeRangeChangeFraction shouldBeEqualComparingTo 0.toBigDecimal()
			buyCount shouldBe 0.toBigInteger()
			sellCount shouldBe 2.toBigInteger()
			draftCompleteness shouldBe null
		}

		val resultsWeek = queryBus(GetMyBotsQuery(userId = 1.toUUID(), timeRange = TimeRange.WEEK))
		resultsWeek shouldHaveSize 1
		resultsWeek.first().run {
			isActive shouldBe true
			numberOfActiveDays shouldBe 1
			name shouldBe "TestBot1"
			avatarFileUrl shouldBe URI("http://localhost:8080/${file1.fileName()}")
			botWalletAddress shouldBe AddressWrapper("CAJM1r7SjSFxv99tRjpKUVQUPyq3CwtASyYLQU8EPrEA")
			// TODO bot market positions -> balance + PnL
			// balanceUsd shouldBeEqualComparingTo 0.toBigDecimal()
			// timeRangeChangeUsd shouldBeEqualComparingTo 0.toBigDecimal()
			// timeRangeChangeFraction shouldBeEqualComparingTo 0.toBigDecimal()
			buyCount shouldBe 0.toBigInteger()
			sellCount shouldBe 3.toBigInteger()
			draftCompleteness shouldBe null
		}

		val resultsMonth = queryBus(GetMyBotsQuery(userId = 1.toUUID(), timeRange = TimeRange.MONTH))
		resultsMonth shouldHaveSize 1
		resultsMonth.first().run {
			isActive shouldBe true
			numberOfActiveDays shouldBe 1
			name shouldBe "TestBot1"
			avatarFileUrl shouldBe URI("http://localhost:8080/${file1.fileName()}")
			botWalletAddress shouldBe AddressWrapper("CAJM1r7SjSFxv99tRjpKUVQUPyq3CwtASyYLQU8EPrEA")
			// TODO bot market positions -> balance + PnL
			// balanceUsd shouldBeEqualComparingTo 0.toBigDecimal()
			// timeRangeChangeUsd shouldBeEqualComparingTo 0.toBigDecimal()
			// timeRangeChangeFraction shouldBeEqualComparingTo 0.toBigDecimal()
			buyCount shouldBe 0.toBigInteger()
			sellCount shouldBe 4.toBigInteger()
			draftCompleteness shouldBe null
		}

		val resultsYear = queryBus(GetMyBotsQuery(userId = 1.toUUID(), timeRange = TimeRange.YEAR))
		resultsYear shouldHaveSize 1
		resultsYear.first().run {
			isActive shouldBe true
			numberOfActiveDays shouldBe 1
			name shouldBe "TestBot1"
			avatarFileUrl shouldBe URI("http://localhost:8080/${file1.fileName()}")
			botWalletAddress shouldBe AddressWrapper("CAJM1r7SjSFxv99tRjpKUVQUPyq3CwtASyYLQU8EPrEA")
			// TODO bot market positions -> balance + PnL
			// balanceUsd shouldBeEqualComparingTo 0.toBigDecimal()
			// timeRangeChangeUsd shouldBeEqualComparingTo 0.toBigDecimal()
			// timeRangeChangeFraction shouldBeEqualComparingTo 0.toBigDecimal()
			buyCount shouldBe 1.toBigInteger()
			sellCount shouldBe 5.toBigInteger()
			draftCompleteness shouldBe null
		}
	}

	@Test
	fun `should return draft bots with correct completeness`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val file1 = integrationTestHelper.getFile(id = 2.toUUID())

		val draft1 = integrationTestHelper.getBotDraft(
			id = 6.toUUID(),
			userId = 1.toUUID(),
		)

		val draft2 = integrationTestHelper.getBotDraft(
			id = 7.toUUID(),
			userId = 1.toUUID(),
		)
		draft2.update(
			PatchBotSettingsRequest(
				name = "Draft2",
				avatarFileId = 2.toUUID(),
				tradeAmount = 100.toBigDecimal(),
				buyFrequency = 10.toBigInteger(),
				profitTargetFraction = 0.1.toBigDecimal(),
				stopLossFraction = 0.2.toBigDecimal(),
			).toValidBotDraftUpdate(draft2),
		)
		botDraftRepository.save(draft2)

		val draft3 = integrationTestHelper.getBotDraft(
			id = 8.toUUID(),
			userId = 1.toUUID(),
		)
		draft3.update(
			PatchBotSettingsRequest(
				name = "Draft3",
				avatarFileId = 2.toUUID(),
				tradeAmount = 100.toBigDecimal(),
				buyFrequency = 10.toBigInteger(),
				profitTargetFraction = 0.1.toBigDecimal(),
				stopLossFraction = 0.2.toBigDecimal(),
				marketCapFromUsd = Optional.ofNullable(1000.toBigDecimal()),
				marketCapToUsd = null,
				liquidityFromUsd = null,
				liquidityToUsd = Optional.ofNullable(10000.toBigDecimal()),
				dailyVolumeFromUsd = null,
				dailyVolumeToUsd = Optional.ofNullable(10000.toBigDecimal()),
				numberOfHoldersFrom = Optional.ofNullable(1000.toBigInteger()),
				numberOfHoldersTo = null,
				buyVolume = Optional.ofNullable(BigDecimal(0.5)),
				sellVolume = Optional.ofNullable(BigDecimal(0.5)),
				sellTransactionFraction = Optional.ofNullable(BigDecimal(0.3)),
				buyTransactionFraction = Optional.ofNullable(BigDecimal(0.7)),
			).toValidBotDraftUpdate(draft3),
		)
		botDraftRepository.save(draft3)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns "150".toBigDecimal()

		val results = queryBus(GetMyBotsQuery(userId = 1.toUUID(), timeRange = TimeRange.DAY))

		results shouldHaveSize 3
		results.single { it.id == 6.toUUID() }.run {
			isActive shouldBe false
			numberOfActiveDays shouldBe 0
			name shouldBe null
			avatarFileUrl shouldBe null
			botWalletAddress shouldBe null
			balanceUsd shouldBe null
			timeRangeChangeUsd shouldBe null
			timeRangeChangeFraction shouldBe null
			buyCount shouldBe null
			sellCount shouldBe null
			draftCompleteness!! shouldBeEqualComparingTo 0.toBigDecimal()
		}
		results.single { it.id == 7.toUUID() }.run {
			isActive shouldBe false
			numberOfActiveDays shouldBe 0
			name shouldBe "Draft2"
			avatarFileUrl shouldBe URI("http://localhost:8080/${file1.fileName()}")
			botWalletAddress shouldBe null
			balanceUsd shouldBe null
			timeRangeChangeUsd shouldBe null
			timeRangeChangeFraction shouldBe null
			buyCount shouldBe null
			sellCount shouldBe null
			draftCompleteness!! shouldBeEqualComparingTo 0.5.toBigDecimal() // half of the fields are filled
		}
		results.single { it.id == 8.toUUID() }.run {
			isActive shouldBe false
			numberOfActiveDays shouldBe 0
			name shouldBe "Draft3"
			avatarFileUrl shouldBe URI("http://localhost:8080/${file1.fileName()}")
			botWalletAddress shouldBe null
			balanceUsd shouldBe null
			timeRangeChangeUsd shouldBe null
			timeRangeChangeFraction shouldBe null
			buyCount shouldBe null
			sellCount shouldBe null
			draftCompleteness!! shouldBeEqualComparingTo 1.toBigDecimal()
		}
	}
}
