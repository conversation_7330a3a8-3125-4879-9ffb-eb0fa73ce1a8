package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.service.MarketPositionSnapshotService
import com.cleevio.fatbot.application.module.referral.ReferralRewardService.Companion.ZERO_UUID
import com.cleevio.fatbot.application.module.transaction.constant.BuySellTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailure
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailureReason
import com.cleevio.fatbot.application.module.transaction.constant.TransactionPending
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.constant.TransferTokenTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.event.BuyTransactionSuccessfulEvent
import com.cleevio.fatbot.application.module.user.exception.UserNotFoundException
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.asEventPayload
import com.cleevio.fatbot.domain.market.MarketPositionRepository
import com.cleevio.fatbot.domain.market.MarketPositionSnapshotRepository
import com.cleevio.fatbot.domain.referral.ReferralRewardRepository
import com.cleevio.fatbot.domain.transaction.TransactionRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger
import java.util.UUID

@Suppress("ktlint:standard:max-line-length")
class TransactionStatusProcessingServiceTest(
	@Autowired private val transactionRepository: TransactionRepository,
	@Autowired private val referralRewardRepository: ReferralRewardRepository,
	@Autowired private val marketPositionRepository: MarketPositionRepository,
	@Autowired private val marketPositionSnapshotRepository: MarketPositionSnapshotRepository,
	@Autowired private val marketPositionSnapshotService: MarketPositionSnapshotService,
	@Autowired private val underTest: TransactionStatusProcessingService,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		// Capture BuyTransactionSuccessfulEvent
		every {
			applicationEventMulticaster.multicastEvent(any(), BuyTransactionSuccessfulEvent::class.asEventPayload())
		} just Runs
	}

	@Test
	fun `should process single transaction for single chain`() {
		integrationTestHelper.generateFattyCards()
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(id = 21.toUUID(), userId = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		val signedTxInput =
			"f86f808504a817c80082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc10000808401546d71a06f217d459b2288b716a27f478c9b267148c46ebc3f341c1e0eda9c8e92bd388fa00c27f7c3486f3ed278796516240fbc014d9e4584d76d79aee82f268bdcfe0c75"

		val transaction = integrationTestHelper.getTransaction(
			id = 10.toUUID(),
			walletId = wallet.id,
			status = TransactionStatus.PENDING,
			signedTx = signedTxInput,
		)

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
		)

		every { getTransactionResultsEVM(any(), any()) } returns mapOf(
			transaction.txHash!! to BuySellTransactionSuccess(
				amountIn = BigInteger.valueOf(100),
				amountOut = BigInteger.valueOf(100_000),
				fee = BigInteger.valueOf(1),
				referralFee = BigInteger.ZERO,
				referralRewardRecipient = ZERO_UUID,
				balanceChange = BaseAmount.ZERO,
			),
		)
		every { publishUserTransactionStatusChanged(any(), any()) } just Runs
		every { walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any()) } just Runs

		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		underTest.processTransactionStatuses()

		transactionRepository.count() shouldBe 1
		transactionRepository.findAll().first().run {
			id shouldBe 10.toUUID()
			type shouldBe TransactionType.BUY
			status shouldBe TransactionStatus.SUCCESS
			chain shouldBe Chain.EVM_MAINNET
			verificationCount shouldBe 0

			amountIn shouldBe BigInteger.valueOf(100)
			amountOut shouldBe BigInteger.valueOf(100_000)
			fatbotFee shouldBe BigInteger.valueOf(1)
			exchangeRateUsd shouldBe BigDecimal("2400.001")
		}

		verify(exactly = 1) {
			publishUserTransactionStatusChanged(any(), any())
			walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any())
		}
	}

	@Test
	fun `should process single transfer token transaction`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		val signedTxInput =
			"02f8b00118843b9aca00843b9aca01830222e0946982508145454ce325ddbe47a25d4ec3d231193380b844a9059cbb000000000000000000000000574abce64dd40d19d4076c453e8ed762551701da0000000000000000000000000000000000000000001a6357912ea5a9f79c0000c001a0f65209691c70c92dc0da611bc7d07822232f1eea6bf5b93827c785dc01eda1f5a053b4c9cc78368ab0e64b2b947200345004b5deeb33d57428c531d668a57788f8"

		val transaction = integrationTestHelper.getTransaction(
			id = 10.toUUID(),
			walletId = wallet.id,
			type = TransactionType.TRANSFER_TOKEN,
			status = TransactionStatus.PENDING,
			signedTx = signedTxInput,
		)

		every { getTransactionResultsEVM(any(), any()) } returns mapOf(
			transaction.txHash!! to TransferTokenTransactionSuccess(
				amountIn = 1.toBigInteger(),
				amountOut = "31901200920000000000000000".toBigInteger(),
			),
		)
		every { publishUserTransactionStatusChanged(any(), any()) } just Runs
		every { walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any()) } just Runs

		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		// GetTokenPricesEVM call - token price is 5000000000 wei
		every { web3jWrapper.getWeb3j() } returns web3j
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// ["******************************************"]
				"0xe016d3ab00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000001000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000046BbABE36c18422303aB83AdB74BcDaA4e8Ee1b" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000000000000000000000000000000000012a05f200"

		marketPositionRepository.count() shouldBe 0

		underTest.processTransactionStatuses()

		transactionRepository.count() shouldBe 1
		transactionRepository.findAll().first().run {
			id shouldBe 10.toUUID()
			type shouldBe TransactionType.TRANSFER_TOKEN
			status shouldBe TransactionStatus.SUCCESS
			chain shouldBe Chain.EVM_MAINNET
			verificationCount shouldBe 0

			amountIn shouldBe "159506004600000000".toBigInteger()
			amountOut shouldBe "31901200920000000000000000".toBigInteger()
			fatbotFee shouldBe null
			exchangeRateUsd shouldBe BigDecimal("2400.001")
		}

		marketPositionRepository.count() shouldBe 1
		marketPositionRepository.findAll().first().run {
			walletId shouldBe wallet.id
			tokenAddress shouldBe transaction.tokenAddress
			totalTokenAmountBought shouldBe 0.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 0.toBigInteger()
			totalTokenAmountSold shouldBe "31901200920000000000000000".toBigInteger()
		}

		verify(exactly = 1) {
			publishUserTransactionStatusChanged(any(), any())
			walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any())
		}
	}

	@Test
	fun `should process transaction with sell of all token assets and delete all related market positions & their snapshots`() {
		integrationTestHelper.generateFattyCards()
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(id = 21.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
		)
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		val signedTxBuyInput =
			"02f87583014a348084773594008506fc23ac0082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c001a0fbda2ff592b2db1fdc21a075431178901c5aaaf6206c90ba1da5e85ce489b6d7a0502e9be99007d4f6fa13e3b9a50b8ba6bb822f516d477ec513413803565ef683"
		val signedTxSellInput =
			"02f8b483014a340184773594008506fc23ac0082ea6094046bbabe36c18422303ab83adb74bcdaa4e8ee1b80b845a9059cbb2a000000000000000000000000b934d7659c7168635a881fcc3efafb3af64a0c070000000000000000000000000000000000000000000000000de0b6b3a7640000c001a0bfd38d5300c634f92bece9af60d61d48c17da4c9f07fb737405b2403a95e2581a00d09568c99fc7d3b4c07427a6e38729a085ec6a450f231b062b8ccd607eeaea0"

		// create first (BUY) transaction and simulate it finishing, market position is created
		val transaction1 = integrationTestHelper.getTransaction(
			id = 10.toUUID(),
			walletId = wallet.id,
			status = TransactionStatus.PENDING,
			type = TransactionType.BUY,
			signedTx = signedTxBuyInput,
		)

		every { getTransactionResultsEVM(any(), any()) } returns mapOf(
			transaction1.txHash!! to BuySellTransactionSuccess(
				amountIn = BigInteger.valueOf(100),
				amountOut = BigInteger.valueOf(100_000),
				fee = BigInteger.valueOf(1),
				referralFee = BigInteger.ZERO,
				referralRewardRecipient = 0.toUUID(),
				balanceChange = BaseAmount.ZERO,
			),
		)
		every { publishUserTransactionStatusChanged(any(), any()) } just Runs
		every { walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any()) } just Runs
		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		underTest.processTransactionStatuses()

		// manually also create market position snapshot
		marketPositionSnapshotService.createNewSnapshotsOfMarketPositionTableAndDeleteOldSnapshots()

		transactionRepository.count() shouldBe 1
		marketPositionRepository.count() shouldBe 1
		marketPositionSnapshotRepository.count() shouldBe 1

		// create second (SELL) transaction and simulate it finishing, market position is updated and deleted
		val transaction2 = integrationTestHelper.getTransaction(
			id = 11.toUUID(),
			walletId = wallet.id,
			status = TransactionStatus.PENDING,
			type = TransactionType.SELL,
			signedTx = signedTxSellInput,
		)

		every { getTransactionResultsEVM(any(), any()) } returns mapOf(
			transaction2.txHash!! to BuySellTransactionSuccess(
				amountIn = BigInteger.valueOf(100_000),
				amountOut = BigInteger.valueOf(100),
				fee = BigInteger.valueOf(1),
				referralFee = BigInteger.ZERO,
				referralRewardRecipient = ZERO_UUID,
				balanceChange = BaseAmount.ZERO,
			),
		)

		underTest.processTransactionStatuses()

		val transactions = transactionRepository.findAll().sortedBy { it.createdAt }
		transactions.size shouldBe 2
		transactions[0].run {
			id shouldBe 10.toUUID()
			type shouldBe TransactionType.BUY
			status shouldBe TransactionStatus.SUCCESS
			amountIn shouldBe BigInteger.valueOf(100)
			amountOut shouldBe BigInteger.valueOf(100_000)
		}
		transactions[1].run {
			id shouldBe 11.toUUID()
			type shouldBe TransactionType.SELL
			status shouldBe TransactionStatus.SUCCESS
			amountIn shouldBe BigInteger.valueOf(100_000)
			amountOut shouldBe BigInteger.valueOf(100)
		}

		marketPositionRepository.count() shouldBe 0

		verify(exactly = 2) {
			publishUserTransactionStatusChanged(any(), any())
			walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any())
		}
	}

	@Test
	fun `should process multiple transactions for single chain`() {
		integrationTestHelper.generateFattyCards()
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(id = 21.toUUID(), userId = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
		)

		val signedTxInput1 =
			"02f87583aa36a78084773594008506fc23ac0082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c080a0698aa6103964f9cb951138833513da76d62c87f9fa8a88bfb0ce22a5b3f3cf6ea072fc1fbbd6a56f8947ff142cf40aded549a4e42790ed8851c9d74809bcaea862"
		val signedTxInput2 =
			"02f87583aa36a78084b2d05e008509502f900082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c080a093275187f5ef2611c7c974f4fa5a86d736f914ffaa343b47cb2b1083778083efa0695069939a9f085d1e8d561d0429d9608147136b417d744f0756e27d30dcaed4"
		val signedTxInput3 =
			"02f87583aa36a78084ee6b2800850ba43b740082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c080a077ec7294db7eeb358b1df1befef13eb91e1e4bb64c1783e08fbab8e4202bd300a0202998b3f84bccf83c5384fc702a414c0187115511d9966e4a1e353d19eb345e"
		val signedTxInput4 =
			"02f87683aa36a78085012a05f200850df847580082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c001a03a8172f158f51f6753880e559e6cba0908a093962b27f3e20a4071c151492be2a01e65af1f38b557a0da6c872ac8e751745d9e95049ae0a655170a249ac3d070f5"
		val signedTxInput5 =
			"02f87683aa36a78085012a05f200850df847580082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c001a03a8172f158f51f6753880e559e6cba0908a093962b27f3e20a4071c151492be2a01e65af1f38b557a0da6c872ac8e751745d9e95049ae0a655170a24134546324t"

		val transaction1 = integrationTestHelper.getTransaction(
			id = 10.toUUID(),
			walletId = wallet.id,
			status = TransactionStatus.PENDING,
			signedTx = signedTxInput1,
		)
		val transaction2 = integrationTestHelper.getTransaction(
			id = 11.toUUID(),
			walletId = wallet.id,
			status = TransactionStatus.PENDING,
			signedTx = signedTxInput2,
		)
		val transaction3 = integrationTestHelper.getTransaction(
			id = 12.toUUID(),
			walletId = wallet.id,
			status = TransactionStatus.PENDING,
			signedTx = signedTxInput3,
		)
		val transaction4 = integrationTestHelper.getTransaction(
			id = 13.toUUID(),
			walletId = wallet.id,
			status = TransactionStatus.PENDING,
			signedTx = signedTxInput4,
			verificationCount = 50,
		)
		val transaction5 = integrationTestHelper.getTransaction(
			id = 14.toUUID(),
			walletId = wallet.id,
			status = TransactionStatus.PENDING,
			signedTx = signedTxInput5,
		)

		every { getTransactionResultsEVM(any(), any()) } returns mapOf(
			transaction1.txHash!! to BuySellTransactionSuccess(
				amountIn = BigInteger.valueOf(100),
				amountOut = BigInteger.valueOf(100_000),
				fee = BigInteger.valueOf(1),
				referralFee = BigInteger.ZERO,
				referralRewardRecipient = ZERO_UUID,
				balanceChange = BaseAmount.ZERO,
			),
			transaction2.txHash!! to TransactionFailure(
				balanceChange = BaseAmount(BigInteger("5000")),
				isNoTokensOwnedErrorHint = false,
				failReason = TransactionFailureReason.UNDEFINED,
			),
			transaction3.txHash!! to TransactionPending(),
			transaction4.txHash!! to TransactionPending(),
			transaction5.txHash!! to TransactionFailure(
				balanceChange = BaseAmount(BigInteger("6000")),
				isNoTokensOwnedErrorHint = false,
				failReason = TransactionFailureReason.SLIPPAGE,
			),
		)
		every { publishUserTransactionStatusChanged(any(), any()) } just Runs
		every { walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any()) } just Runs

		every { titanbuilderConnector.sendPrivateEthTransaction(any(), BigInteger.TWO) } just Runs
		every { beaverbuildConnector.sendPrivateEthTransaction(any()) } just Runs
		every { web3jWrapper.getBlockNumber() } returns BigInteger.ZERO

		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		underTest.processTransactionStatuses()

		transactionRepository.count() shouldBe 5
		val transactions = transactionRepository.findAll().sortedBy { it.createdAt }
		transactions[0].run {
			id shouldBe 10.toUUID()
			type shouldBe TransactionType.BUY
			status shouldBe TransactionStatus.SUCCESS
			chain shouldBe Chain.EVM_MAINNET
			verificationCount shouldBe 0

			amountIn shouldBe BigInteger.valueOf(100)
			amountOut shouldBe BigInteger.valueOf(100_000)
			fatbotFee shouldBe BigInteger.valueOf(1)
			exchangeRateUsd shouldBe BigDecimal("2400.001")
		}
		transactions[1].run {
			id shouldBe 11.toUUID()
			type shouldBe TransactionType.BUY
			status shouldBe TransactionStatus.FAILED
			chain shouldBe Chain.EVM_MAINNET
			verificationCount shouldBe 0

			amountIn shouldBe null
			amountOut shouldBe null
			fatbotFee shouldBe null
			exchangeRateUsd shouldBe null
		}
		transactions[2].run {
			id shouldBe 12.toUUID()
			type shouldBe TransactionType.BUY
			status shouldBe TransactionStatus.PENDING
			chain shouldBe Chain.EVM_MAINNET
			verificationCount shouldBe 1

			amountIn shouldBe null
			amountOut shouldBe null
			fatbotFee shouldBe null
			exchangeRateUsd shouldBe null
		}
		transactions[3].run {
			id shouldBe 13.toUUID()
			type shouldBe TransactionType.BUY
			status shouldBe TransactionStatus.FAILED
			chain shouldBe Chain.EVM_MAINNET
			verificationCount shouldBe 51

			amountIn shouldBe null
			amountOut shouldBe null
			fatbotFee shouldBe null
			exchangeRateUsd shouldBe null
		}
		transactions[4].run {
			id shouldBe 14.toUUID()
			type shouldBe TransactionType.BUY
			status shouldBe TransactionStatus.FAILED
			chain shouldBe Chain.EVM_MAINNET
			verificationCount shouldBe 0
			failReason shouldBe TransactionFailureReason.SLIPPAGE
		}

		verify {
			publishUserTransactionStatusChanged(any(), any())
			walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any())
		}
	}

	@Test
	fun `should process multiple transactions for multiple chains`() {
		integrationTestHelper.generateFattyCards()
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(id = 21.toUUID(), userId = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_BASE,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
		)

		val signedTxInput1 =
			"02f87583aa36a78084773594008506fc23ac0082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c080a0698aa6103964f9cb951138833513da76d62c87f9fa8a88bfb0ce22a5b3f3cf6ea072fc1fbbd6a56f8947ff142cf40aded549a4e42790ed8851c9d74809bcaea862"
		val signedTxInput2 =
			"02f87583014a348084773594008506fc23ac0082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c001a0fbda2ff592b2db1fdc21a075431178901c5aaaf6206c90ba1da5e85ce489b6d7a0502e9be99007d4f6fa13e3b9a50b8ba6bb822f516d477ec513413803565ef683"

		val chain1Id = Chain.EVM_MAINNET.id!!
		val chain2Id = Chain.EVM_BASE.id!!
		val transaction1 = integrationTestHelper.getTransaction(
			id = 10.toUUID(),
			walletId = wallet.id,
			status = TransactionStatus.PENDING,
			signedTx = signedTxInput1,
			chainId = chain1Id,
		)
		val transaction2 = integrationTestHelper.getTransaction(
			id = 11.toUUID(),
			walletId = wallet.id,
			status = TransactionStatus.PENDING,
			signedTx = signedTxInput2,
			chainId = chain2Id,
		)

		every { getTransactionResultsEVM(any(), chain1Id) } returns mapOf(
			transaction1.txHash!! to TransactionFailure(
				balanceChange = BaseAmount(BigInteger("5000")),
				isNoTokensOwnedErrorHint = false,
				failReason = TransactionFailureReason.UNDEFINED,
			),
		)
		every { getTransactionResultsEVM(any(), chain2Id) } returns mapOf(
			transaction2.txHash!! to BuySellTransactionSuccess(
				amountIn = BigInteger.valueOf(100),
				amountOut = BigInteger.valueOf(100_000),
				fee = BigInteger.valueOf(1),
				referralFee = BigInteger.ZERO,
				referralRewardRecipient = ZERO_UUID,
				balanceChange = BaseAmount.ZERO,
			),
		)
		every { publishUserTransactionStatusChanged(any(), any()) } just Runs
		every { walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any()) } just Runs

		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		underTest.processTransactionStatuses()

		transactionRepository.count() shouldBe 2
		val transactions = transactionRepository.findAll().sortedBy { it.createdAt }
		transactions[0].run {
			id shouldBe 10.toUUID()
			type shouldBe TransactionType.BUY
			status shouldBe TransactionStatus.FAILED
			chain shouldBe Chain.EVM_MAINNET
			verificationCount shouldBe 0

			amountIn shouldBe null
			amountOut shouldBe null
			fatbotFee shouldBe null
		}
		transactions[1].run {
			id shouldBe 11.toUUID()
			type shouldBe TransactionType.BUY
			status shouldBe TransactionStatus.SUCCESS
			chain shouldBe Chain.EVM_BASE
			verificationCount shouldBe 0

			amountIn shouldBe BigInteger.valueOf(100)
			amountOut shouldBe BigInteger.valueOf(100_000)
			fatbotFee shouldBe BigInteger.valueOf(1)
			exchangeRateUsd shouldBe BigDecimal("2400.001")
		}

		verify(exactly = 1) {
			publishUserTransactionStatusChanged(any(), any())
			walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any())
		}
	}

	@Test
	fun `should skip execution because no pending transactions exist`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		val signedTxInput1 =
			"02f87583aa36a78084773594008506fc23ac0082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c080a0698aa6103964f9cb951138833513da76d62c87f9fa8a88bfb0ce22a5b3f3cf6ea072fc1fbbd6a56f8947ff142cf40aded549a4e42790ed8851c9d74809bcaea862"
		val signedTxInput2 =
			"02f87583aa36a78084b2d05e008509502f900082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c080a093275187f5ef2611c7c974f4fa5a86d736f914ffaa343b47cb2b1083778083efa0695069939a9f085d1e8d561d0429d9608147136b417d744f0756e27d30dcaed4"

		integrationTestHelper.getTransaction(
			id = 10.toUUID(),
			walletId = wallet.id,
			status = TransactionStatus.SUCCESS,
			signedTx = signedTxInput1,
		)
		integrationTestHelper.getTransaction(
			id = 11.toUUID(),
			walletId = wallet.id,
			status = TransactionStatus.FAILED,
			signedTx = signedTxInput2,
		)

		underTest.processTransactionStatuses()

		transactionRepository.count() shouldBe 2

		verify { getTransactionResultsEVM wasNot Called }
		verify { publishUserTransactionStatusChanged wasNot Called }
		verify { walletCurrencyPositionEventListener wasNot Called }
	}

	@Test
	fun `should save a new referral reward for BuySellTransactionSuccess tx`() {
		integrationTestHelper.generateFattyCards()
		referralRewardRepository.findAll().size shouldBe 0

		val referrer = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(id = 21.toUUID(), userId = 1.toUUID())
		val referee = integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")
		integrationTestHelper.getUserStatistics(id = 22.toUUID(), userId = 2.toUUID())

		val referrerWallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = referrer.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		val refereeWallet = integrationTestHelper.getWallet(
			id = 6.toUUID(),
			userId = referee.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
		)

		val signedTxInput1 =
			"02f87583aa36a78084773594008506fc23ac0082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c080a0698aa6103964f9cb951138833513da76d62c87f9fa8a88bfb0ce22a5b3f3cf6ea072fc1fbbd6a56f8947ff142cf40aded549a4e42790ed8851c9d74809bcaea862"

		val transaction = integrationTestHelper.getTransaction(
			id = 10.toUUID(),
			walletId = refereeWallet.id,
			status = TransactionStatus.PENDING,
			signedTx = signedTxInput1,
			chainId = 1,
		)

		every { getTransactionResultsEVM(any(), any()) } returns mapOf(
			transaction.txHash!! to BuySellTransactionSuccess(
				amountIn = BigInteger.valueOf(100),
				amountOut = BigInteger.valueOf(100_000),
				fee = BigInteger.valueOf(1),
				referralFee = BigInteger.valueOf(100),
				referralRewardRecipient = referrerWallet.userId,
				balanceChange = BaseAmount.ZERO,
			),
		)
		every { publishUserTransactionStatusChanged(any(), any()) } just Runs
		every { walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any()) } just Runs

		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		underTest.processTransactionStatuses()

		val rewards = referralRewardRepository.findAll()
		rewards.size shouldBe 1
		with(rewards.first()) {
			userId shouldBe referrer.id
			txHash shouldBe transaction.txHash
			chain shouldBe Chain.EVM_MAINNET
			baseAmount shouldBe BigInteger.valueOf(100)
		}

		verify(exactly = 1) {
			publishUserTransactionStatusChanged(any(), any())
			walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any())
		}
	}

	@Test
	fun `should not save any referral reward if referral userId is ZERO_UUID`() {
		integrationTestHelper.generateFattyCards()
		referralRewardRepository.findAll().size shouldBe 0

		val referrer = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(id = 21.toUUID(), userId = 1.toUUID())
		val referee = integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")
		integrationTestHelper.getUserStatistics(id = 22.toUUID(), userId = 2.toUUID())
		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
		)
		// referrerWallet
		integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = referrer.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		val refereeWallet = integrationTestHelper.getWallet(
			id = 6.toUUID(),
			userId = referee.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		val signedTxInput1 =
			"02f87583aa36a78084773594008506fc23ac0082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c080a0698aa6103964f9cb951138833513da76d62c87f9fa8a88bfb0ce22a5b3f3cf6ea072fc1fbbd6a56f8947ff142cf40aded549a4e42790ed8851c9d74809bcaea862"

		val transaction = integrationTestHelper.getTransaction(
			id = 10.toUUID(),
			walletId = refereeWallet.id,
			status = TransactionStatus.PENDING,
			signedTx = signedTxInput1,
			chainId = 1,
		)

		every { getTransactionResultsEVM(any(), any()) } returns mapOf(
			transaction.txHash!! to BuySellTransactionSuccess(
				amountIn = BigInteger.valueOf(100),
				amountOut = BigInteger.valueOf(100_000),
				fee = BigInteger.valueOf(1),
				referralFee = BigInteger.ZERO,
				referralRewardRecipient = ZERO_UUID,
				balanceChange = BaseAmount.ZERO,
			),
		)
		every { publishUserTransactionStatusChanged(any(), any()) } just Runs
		every { walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any()) } just Runs

		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		underTest.processTransactionStatuses()

		referralRewardRepository.findAll().size shouldBe 0

		verify(exactly = 1) {
			publishUserTransactionStatusChanged(any(), any())
			walletCurrencyPositionEventListener.onTransactionSuccessfulEvent(any())
		}
	}

	@Test
	fun `should throw error if referral user does not exist`() {
		referralRewardRepository.findAll().size shouldBe 0

		val referee = integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		val refereeWallet = integrationTestHelper.getWallet(
			id = 6.toUUID(),
			userId = referee.id,
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		val signedTxInput1 =
			"02f87583aa36a78084773594008506fc23ac0082520894b934d7659c7168635a881fcc3efafb3af64a0c07872386f26fc1000080c080a0698aa6103964f9cb951138833513da76d62c87f9fa8a88bfb0ce22a5b3f3cf6ea072fc1fbbd6a56f8947ff142cf40aded549a4e42790ed8851c9d74809bcaea862"

		val transaction = integrationTestHelper.getTransaction(
			id = 10.toUUID(),
			walletId = refereeWallet.id,
			status = TransactionStatus.PENDING,
			signedTx = signedTxInput1,
			chainId = 1,
		)

		every { getTransactionResultsEVM(any(), any()) } returns mapOf(
			transaction.txHash!! to BuySellTransactionSuccess(
				amountIn = BigInteger.valueOf(100),
				amountOut = BigInteger.valueOf(100_000),
				fee = BigInteger.valueOf(1),
				referralFee = BigInteger.ZERO,
				referralRewardRecipient = UUID.randomUUID(),
				balanceChange = BaseAmount.ZERO,
			),
		)
		every { publishUserTransactionStatusChanged(any(), any()) } just Runs
		every { coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH) } returns BigDecimal("2400.001")

		shouldThrow<UserNotFoundException> {
			underTest.processTransactionStatuses()
		}
	}
}
