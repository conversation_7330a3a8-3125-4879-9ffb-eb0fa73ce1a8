package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator.plus
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.platform.commons.annotation.Testable
import java.math.BigDecimal
import java.math.BigInteger

@Testable
@Suppress("ktlint:standard:max-line-length")
class MarketPositionCalculatorTest {

	@Test
	fun `should calculate market position when token has 18 decimals`() {
		val result = MarketPositionCalculator.calculate(
			chain = Chain.EVM_MAINNET,
			tokenDecimals = 18,
			tokenAmountBought = BigInteger("1000000000000000000").asBaseAmount(),
			totalTokenAcquisitionCost = BigInteger("1000000000000000000").asBaseAmount(),
			totalTokenAcquisitionCostUsd = BigDecimal("3"),
			tokenAmountSold = BigInteger("0").asBaseAmount(),
			tokenDispositionCost = BigInteger("0").asBaseAmount(),
			currentPricePerNativeToken = BigDecimal("1").asNativeAmount(),
			currentExchangeRate = BigDecimal("1.5"),
		)

		result.currentUserTokenAmount shouldBe BigInteger("1000000000000000000").asBaseAmount()
		result.acquisitionValue shouldBe BigDecimal("1.000000000000000000").asNativeAmount()
		result.currentValue shouldBe BigDecimal("1.000000000000000000").asNativeAmount()
		result.currentPnl shouldBe BigDecimal("0.000000000000000000").asNativeAmount()
		result.currentPnlFraction shouldBe BigDecimal("0")
	}

	@Test
	fun `should calculate market position when token has 18 decimals and current price increased`() {
		val result = MarketPositionCalculator.calculate(
			chain = Chain.EVM_MAINNET,
			tokenDecimals = 18,
			tokenAmountBought = BigInteger("1000000000000000000").asBaseAmount(),
			totalTokenAcquisitionCost = BigInteger("1000000000000000000").asBaseAmount(),
			totalTokenAcquisitionCostUsd = BigDecimal("3"),
			tokenAmountSold = BigInteger("0").asBaseAmount(),
			tokenDispositionCost = BigInteger("0").asBaseAmount(),
			currentPricePerNativeToken = BigDecimal("1.1").asNativeAmount(),
			currentExchangeRate = BigDecimal("1.5"),
		)

		result.currentUserTokenAmount shouldBe BigInteger("1000000000000000000").asBaseAmount()
		result.acquisitionValue shouldBe BigDecimal("1.000000000000000000").asNativeAmount()
		result.currentValue shouldBe BigDecimal("1.1000000000000000000").asNativeAmount()
		result.currentPnl shouldBe BigDecimal("0.1000000000000000000").asNativeAmount()
		result.currentPnlFraction shouldBe BigDecimal("0.1") // 10% profit made
	}

	@Test
	fun `should calculate market position when token has 18 decimals and current price decreased`() {
		val result = MarketPositionCalculator.calculate(
			chain = Chain.EVM_MAINNET,
			tokenDecimals = 18,
			tokenAmountBought = BigInteger("1000000000000000000").asBaseAmount(),
			totalTokenAcquisitionCost = BigInteger("1000000000000000000").asBaseAmount(),
			totalTokenAcquisitionCostUsd = BigDecimal("3"),
			tokenAmountSold = BigInteger("0").asBaseAmount(),
			tokenDispositionCost = BigInteger("0").asBaseAmount(),
			currentPricePerNativeToken = BigDecimal("0.8").asNativeAmount(),
			currentExchangeRate = BigDecimal("1.5"),
		)

		result.currentUserTokenAmount shouldBe BigInteger("1000000000000000000").asBaseAmount()
		result.acquisitionValue shouldBe BigDecimal("1.000000000000000000").asNativeAmount()
		result.currentValue shouldBe BigDecimal("0.8000000000000000000").asNativeAmount()
		result.currentPnl shouldBe BigDecimal("-0.2000000000000000000").asNativeAmount()
		result.currentPnlFraction shouldBe BigDecimal("-0.2") // -20% loss
	}

	@Test
	fun `should calculate market position when token has 18 decimals and some tokens were sold in profit`() {
		val result = MarketPositionCalculator.calculate(
			chain = Chain.EVM_MAINNET,
			tokenDecimals = 18,
			tokenAmountBought = BigInteger("1000000000000000000").asBaseAmount(),
			totalTokenAcquisitionCost = BigInteger("1000000000000000000").asBaseAmount(),
			totalTokenAcquisitionCostUsd = BigDecimal("3"),
			tokenAmountSold = BigInteger("600000000000000000").asBaseAmount(), // sold 0.6 of token
			tokenDispositionCost = BigInteger("1000000000000000000").asBaseAmount(), // but that sell was worth 1 ETH
			currentPricePerNativeToken = BigDecimal("1").asNativeAmount(),
			currentExchangeRate = BigDecimal("1.5"),
		)

		result.currentUserTokenAmount shouldBe BigInteger("400000000000000000").asBaseAmount() // have only the remaining tokens
		result.acquisitionValue shouldBe BigDecimal("0.400000000000000000").asNativeAmount() // here we only care about unrealized tokens
		result.currentValue shouldBe BigDecimal("0.400000000000000000").asNativeAmount()
		// right now we are in profit of 0.4 ETH (from selling)
		// tokens that we still own have currently same price as acquisition price, so they don't contribute to PnL
		result.currentPnl shouldBe BigDecimal("0.400000000000000000").asNativeAmount()
		// 40% profit. We bought for 1 ETH. 1 ETH we already liquidated, and if we sold what we own,
		// we would get another 0.4 ETH thus getting us to 1.4 ETH so on this token we made 40% profit
		result.currentPnlFraction shouldBe BigDecimal("0.4")
	}

	@Test
	fun `should calculate market position when token has 18 decimals, and some tokens were sold in loss`() {
		val result = MarketPositionCalculator.calculate(
			chain = Chain.EVM_MAINNET,
			tokenDecimals = 18,
			tokenAmountBought = BigInteger("1000000000000000000").asBaseAmount(),
			totalTokenAcquisitionCost = BigInteger("1000000000000000000").asBaseAmount(),
			totalTokenAcquisitionCostUsd = BigDecimal("3"),
			tokenAmountSold = BigInteger("600000000000000000").asBaseAmount(), // sold 0.6 of token
			tokenDispositionCost = BigInteger("100000000000000000").asBaseAmount(), // but that sell was worth only 0.1 ETH
			currentPricePerNativeToken = BigDecimal("1").asNativeAmount(),
			currentExchangeRate = BigDecimal("1.5"),
		)

		result.currentUserTokenAmount shouldBe BigInteger("400000000000000000").asBaseAmount() // have only the remaining tokens
		result.acquisitionValue shouldBe BigDecimal("0.400000000000000000").asNativeAmount() // here we only care about unrealized tokens
		result.currentValue shouldBe BigDecimal("0.400000000000000000").asNativeAmount()
		// right now we are in loss of - 0.5 ETH (to be on PnL 0 we would have to sell for 0.6 ETH, but only 0.1 ETH was made)
		result.currentPnl shouldBe BigDecimal("-0.500000000000000000").asNativeAmount()
		// -50% loss. That is because we already have 1 ETH liquidated, and the rest we could sell for 0.4
		// thus bringing total to 0.5 ETH which is 50% from original acquisition price of 1 ETH
		result.currentPnlFraction shouldBe BigDecimal("-0.5")
	}

	@Test
	fun `should calculate market position when token has 18 decimals, with partial sell and price change`() {
		val result = MarketPositionCalculator.calculate(
			chain = Chain.EVM_MAINNET,
			tokenDecimals = 18,
			tokenAmountBought = BigInteger("1000000000000000000").asBaseAmount(),
			totalTokenAcquisitionCost = BigInteger("1000000000000000000").asBaseAmount(),
			totalTokenAcquisitionCostUsd = BigDecimal("3"),
			tokenAmountSold = BigInteger("600000000000000000").asBaseAmount(), // sold 0.6 of token
			tokenDispositionCost = BigInteger("100000000000000000").asBaseAmount(), // but that sell was worth only 0.1 ETH
			currentPricePerNativeToken = BigDecimal("3").asNativeAmount(), // but the remaining token increased in price 3 times
			currentExchangeRate = BigDecimal("1.5"),
		)

		result.currentUserTokenAmount shouldBe BigInteger("400000000000000000").asBaseAmount()
		result.acquisitionValue shouldBe BigDecimal("0.400000000000000000").asNativeAmount()
		result.currentValue shouldBe BigDecimal("1.200000000000000000").asNativeAmount()
		// By selling 0.6 of token at 0.1 ETH we were in PnL of -0.5
		// However the remaining part (0.4 ETH) increased to 1.2 ETH
		// Thus if we would liquidate this, we gain 1.2 - 0.4 ETH = 0.8 ETH
		// And so 0.8 ETH potential gain and -0.5 loss that is realized put us to 0.3 ETH of PnL
		result.currentPnl shouldBe BigDecimal("0.300000000000000000").asNativeAmount()
		result.currentPnlFraction shouldBe BigDecimal("0.3")
	}

	@Test
	fun `should throw on market position when all tokens were sold`() {
		shouldThrow<IllegalStateException> {
			MarketPositionCalculator.calculate(
				chain = Chain.EVM_MAINNET,
				tokenDecimals = 18,
				tokenAmountBought = BigInteger("1000000000000000000").asBaseAmount(),
				totalTokenAcquisitionCost = BigInteger("1000000000000000000").asBaseAmount(),
				totalTokenAcquisitionCostUsd = BigDecimal("3"),
				tokenAmountSold = BigInteger("1000000000000000000").asBaseAmount(),
				tokenDispositionCost = BigInteger("1000000000000000000").asBaseAmount(),
				currentPricePerNativeToken = BigDecimal("1").asNativeAmount(),
				currentExchangeRate = BigDecimal("1.5"),
			)
		}
	}

	@Test
	fun `should work with token with 9 decimals`() {
		val result = MarketPositionCalculator.calculate(
			chain = Chain.EVM_MAINNET,
			tokenDecimals = 9,
			tokenAmountBought = BigInteger("1000000000").asBaseAmount(),
			totalTokenAcquisitionCost = BigInteger("1000000000000000000").asBaseAmount(),
			totalTokenAcquisitionCostUsd = BigDecimal("3"),
			tokenAmountSold = BigInteger("600000000").asBaseAmount(), // sold 0.6 of token
			tokenDispositionCost = BigInteger("100000000000000000").asBaseAmount(), // but that sell was worth only 0.1 ETH
			currentPricePerNativeToken = BigDecimal("1").asNativeAmount(),
			currentExchangeRate = BigDecimal("1.5"),
		)

		result.currentUserTokenAmount shouldBe BigInteger("400000000").asBaseAmount() // have only the remaining tokens
		result.acquisitionValue shouldBe BigDecimal("0.400000000000000000").asNativeAmount() // here we only care about unrealized tokens
		result.currentValue shouldBe BigDecimal("0.400000000").asNativeAmount()
		// right now we are in loss of - 0.5 ETH (to be on PnL 0 we would have to sell for 0.6 ETH, but only 0.1 ETH was made)
		result.currentPnl shouldBe BigDecimal("-0.500000000000000000").asNativeAmount()
		// -50% loss. That is because we already have 1 ETH liquidated, and the rest we could sell for 0.4
		// thus bringing total to 0.5 ETH which is 50% from original acquisition price of 1 ETH
		result.currentPnlFraction shouldBe BigDecimal("-0.5")
	}

	@Test
	fun `should work with token with 6 decimals on Solana`() {
		val result = MarketPositionCalculator.calculate(
			chain = Chain.SOLANA,
			tokenDecimals = 6,
			tokenAmountBought = BigInteger("1000000").asBaseAmount(),
			totalTokenAcquisitionCost = BigInteger("1000000000").asBaseAmount(),
			totalTokenAcquisitionCostUsd = BigDecimal("3"),
			tokenAmountSold = BigInteger("600000").asBaseAmount(), // sold 0.6 of token
			tokenDispositionCost = BigInteger("100000000").asBaseAmount(), // but that sell was worth only 0.1 SOL
			currentPricePerNativeToken = BigDecimal("1").asNativeAmount(),
			currentExchangeRate = BigDecimal("1.5"),
		)

		result.currentUserTokenAmount shouldBe BigInteger("400000").asBaseAmount() // have only the remaining tokens
		result.acquisitionValue shouldBe BigDecimal("0.400000000").asNativeAmount() // here we only care about unrealized tokens
		result.currentValue shouldBe BigDecimal("0.400000").asNativeAmount()
		// right now we are in loss of - 0.5 ETH (to be on PnL 0 we would have to sell for 0.6 SOL, but only 0.1 SOL was made)
		result.currentPnl shouldBe BigDecimal("-0.500000000").asNativeAmount()
		// -50% loss. That is because we already have 1 SOL liquidated, and the rest we could sell for 0.4
		// thus bringing total to 0.5 SOL which is 50% from original acquisition price of 1 SOL
		result.currentPnlFraction shouldBe BigDecimal("-0.5")
	}

	@Test
	fun `should correctly plus market positions`() {
		val first = MarketPositionCalculator.Result(
			currentUserTokenAmount = BigInteger("271802855737082836816759765").asBaseAmount(),
			currentUserTokenAmountNative = BigInteger("271802855737082836816759765").asBaseAmount().toNative(decimals = 6),
			userBoughtTokenAmountNative = BigInteger("271802855737082836816759765").asBaseAmount().toNative(decimals = 6),
			acquisitionValue = BigDecimal("1.000000000000000000000000000000000").asNativeAmount(),
			acquisitionValueUsd = BigDecimal("3"),
			acquisitionValueOwnedTokenUsd = BigDecimal("2"),
			currentValue = BigDecimal("1.774329042251676758739807745920").asNativeAmount(),
			currentValueUsd = BigDecimal("10"),
			currentPnl = BigDecimal("0.774329042251676758739807745920000").asNativeAmount(),
			currentPnlFraction = BigDecimal("0.774329042251676758739807745920000"),
			currentPnlUsd = BigDecimal("0.774329042251676758739807745920000"),
			currentPnlFractionUsd = BigDecimal("0.774329042251676758739807745920000"),
		)

		val second = MarketPositionCalculator.Result(
			currentUserTokenAmount = BigInteger("271802855737082836816759765").asBaseAmount(),
			currentUserTokenAmountNative = BigInteger("271802855737082836816759765").asBaseAmount().toNative(decimals = 6),
			userBoughtTokenAmountNative = BigInteger("271802855737082836816759765").asBaseAmount().toNative(decimals = 6),
			acquisitionValue = BigDecimal("1.000000000000000000000000000000000").asNativeAmount(),
			acquisitionValueUsd = BigDecimal("3"),
			acquisitionValueOwnedTokenUsd = BigDecimal("2"),
			currentValue = BigDecimal("1.774329042251676758739807745920").asNativeAmount(),
			currentValueUsd = BigDecimal("10"),
			currentPnl = BigDecimal("0.774329042251676758739807745920000").asNativeAmount(),
			currentPnlFraction = BigDecimal("0.774329042251676758739807745920000"),
			currentPnlUsd = BigDecimal("0.774329042251676758739807745920000"),
			currentPnlFractionUsd = BigDecimal("0.774329042251676758739807745920000"),
		)

		val result = first + second

		result.acquisitionValue shouldBe BigDecimal("2.000000000000000000000000000000000").asNativeAmount()
		result.currentValue shouldBe BigDecimal("3.548658084503353517479615491840").asNativeAmount()
		result.currentValueUsd shouldBe BigDecimal("20")
		result.acquisitionValueOwnedTokenUsd shouldBe BigDecimal("4")
		result.currentPnl shouldBe BigDecimal("1.548658084503353517479615491840000").asNativeAmount()

		// same PnL% as the two market positions were identical
		result.currentPnlFraction shouldBe BigDecimal("0.77432904225167675873980774592")
	}
}
