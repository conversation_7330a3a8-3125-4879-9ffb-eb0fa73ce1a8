package com.cleevio.fatbot.application.module.matchmaking

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.solana.pumpfun.model.PumpFunCurveState
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.matchmaking.command.TokenStateChangesBuyingCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.bot.BotRepository
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionRepository
import com.cleevio.fatbot.domain.botwallet.BotWalletRepository
import com.cleevio.fatbot.domain.token.BotTokenInfoRepository
import com.cleevio.fatbot.setAndReturnPrivateProperty
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import java.time.temporal.ChronoUnit
import kotlin.test.Test
import kotlin.time.Duration
import kotlin.time.toKotlinDuration

class TokenStateChangesBuyingCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val botWalletRepository: BotWalletRepository,
	@Autowired private val botRepository: BotRepository,
	@Autowired private val botMarketPositionRepository: BotMarketPositionRepository,
	@Autowired private val botTokenInfoRepository: BotTokenInfoRepository,
) : IntegrationTest() {

	@Test
	fun `should match buying bot with just mandatory matching rules set and enough balance and buy freq`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(80000000000)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1840.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 14

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		// has less than buy amount because of frozen funds for fees
		updatedBotWallet.balance shouldBe BigInteger.valueOf(49997904720)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"

		// and bot token info created
		val botTokenInfo = botTokenInfoRepository.findAll()
		botTokenInfo.size shouldBe 1
		with(botTokenInfo.first()) {
			address shouldBe AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump")
			name shouldBe "A token"
			symbol shouldBe "A-TKN"
		}
	}

	@Test
	fun `should match multiple tokens with single bot with mandatory settings`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(80000000000)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1840.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("2sCUCJdVkmyXp4dT8sFaA9LKgSMK4yDPi9zLHiwXpump"),
						name = "B token",
						symbol = "B-TKN",
						marketCapUsd = BigDecimal.valueOf(1290.0),
						liquidityUsd = BigDecimal.valueOf(906.0),
						volumeUsd = BigDecimal.valueOf(8.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 14

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		// has less than buy amount because of frozen funds for fees
		updatedBotWallet.balance shouldBe BigInteger.valueOf(49997904720)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1
	}

	@Test
	fun `should match only single token in block even when multiple are possible`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(80000000000)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1840.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("2sCUCJdVkmyXp4dT8sFaA9LKgSMK4yDPi9zLHiwXpump"),
						name = "B token",
						symbol = "B-TKN",
						marketCapUsd = BigDecimal.valueOf(1290.0),
						liquidityUsd = BigDecimal.valueOf(906.0),
						volumeUsd = BigDecimal.valueOf(8.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("DgQBv9Ef1YkFc587XfsJKk6jzdanyA5Tj95UDjcfMksH"),
						name = "B token",
						symbol = "B-TKN",
						marketCapUsd = BigDecimal.valueOf(1200.0),
						liquidityUsd = BigDecimal.valueOf(86.0),
						volumeUsd = BigDecimal.valueOf(5.7),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 14

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		// has less than buy amount because of frozen funds for fees
		updatedBotWallet.balance shouldBe BigInteger.valueOf(49997904720)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1
	}

	@Test
	fun `should match single token with mandatory properties and market cap from is set`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			marketCapFromUsd = BigDecimal.valueOf(1290.0),
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(80000000000)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1840.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("2sCUCJdVkmyXp4dT8sFaA9LKgSMK4yDPi9zLHiwXpump"),
						name = "B token",
						symbol = "B-TKN",
						marketCapUsd = BigDecimal.valueOf(1290.0),
						liquidityUsd = BigDecimal.valueOf(906.0),
						volumeUsd = BigDecimal.valueOf(8.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("DgQBv9Ef1YkFc587XfsJKk6jzdanyA5Tj95UDjcfMksH"),
						name = "B token",
						symbol = "B-TKN",
						marketCapUsd = BigDecimal.valueOf(1200.0),
						liquidityUsd = BigDecimal.valueOf(86.0),
						volumeUsd = BigDecimal.valueOf(5.7),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 14

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		// has less than buy amount because of frozen funds for fees
		updatedBotWallet.balance shouldBe BigInteger.valueOf(49997904720)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1
	}

	@Test
	fun `should match with mandatory properties and market cap from is set - should not match mc smaller than min`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			marketCapFromUsd = BigDecimal.valueOf(1290.0),
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1200.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should match with mandatory properties and market cap to is set - should not match mc larger than max`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			marketCapToUsd = BigDecimal.valueOf(1800.0),
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1900.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should match with mandatory properties and liq from is set - should not match liquidity smaller than min`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			liquidityFromUsd = BigDecimal.valueOf(250.0),
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1200.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should match with mandatory properties and liq to is set - should not match liquidity larger than max`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			liquidityToUsd = BigDecimal(150.0),
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1900.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should match with mandatory properties and daily vol from is set - should not match vol smaller than min`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			dailyVolumeFromUsd = BigDecimal(11.0),
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1200.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should match with mandatory properties and daily vol to is set - should not match vol larger than max`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			dailyVolumeToUsd = BigDecimal(12.0),
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1900.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(13.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should not match with mandatory properties - bot has no buying cap left`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 0,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1840.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 0
		bot.remainingBuyFrequency shouldBe 0

		val notUpdatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		notUpdatedBotWallet shouldNotBe null

		notUpdatedBotWallet!!.id shouldBe 4.toUUID()
		notUpdatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true

		// and no bot token info was created
		botTokenInfoRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should match with mandatory properties and everything is set - match in range`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			marketCapFromUsd = BigDecimal.valueOf(1290.0),
			marketCapToUsd = BigDecimal.valueOf(1900.0),
			liquidityFromUsd = BigDecimal.valueOf(250.0),
			liquidityToUsd = BigDecimal.valueOf(1000.0),
			dailyVolumeFromUsd = BigDecimal.valueOf(5.0),
			dailyVolumeToUsd = BigDecimal.valueOf(12.0),
			numberOfHoldersFrom = 5,
			numberOfHoldersTo = 12,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(120000000000)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1840.0),
						liquidityUsd = BigDecimal.valueOf(260.9),
						volumeUsd = BigDecimal.valueOf(6.0),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 14

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		// has less than buy amount because of frozen funds for fees
		updatedBotWallet.balance shouldBe BigInteger.valueOf(89997904720)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
	}

	@Test
	fun `should match with mandatory properties and everything is set - match equal to max range`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			marketCapFromUsd = BigDecimal.valueOf(1290.0),
			marketCapToUsd = BigDecimal.valueOf(1900.0),
			liquidityFromUsd = BigDecimal.valueOf(250.0),
			liquidityToUsd = BigDecimal.valueOf(1000.0),
			dailyVolumeFromUsd = BigDecimal.valueOf(5.0),
			dailyVolumeToUsd = BigDecimal.valueOf(12.0),
			numberOfHoldersFrom = 3,
			numberOfHoldersTo = 10,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(120000000000)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1900.0),
						liquidityUsd = BigDecimal.valueOf(1000.0),
						volumeUsd = BigDecimal.valueOf(12.0),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 14

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		// has less than buy amount because of frozen funds for fees
		updatedBotWallet.balance shouldBe BigInteger.valueOf(89997904720)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
	}

	@Test
	fun `should match with mandatory properties and everything is set - match equal to min range`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			marketCapFromUsd = BigDecimal.valueOf(1290.0),
			marketCapToUsd = BigDecimal.valueOf(1900.0),
			liquidityFromUsd = BigDecimal.valueOf(250.0),
			liquidityToUsd = BigDecimal.valueOf(1000.0),
			dailyVolumeFromUsd = BigDecimal.valueOf(5.0),
			dailyVolumeToUsd = BigDecimal.valueOf(12.0),
			numberOfHoldersFrom = 10,
			numberOfHoldersTo = 20,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(120000000000)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1290.0),
						liquidityUsd = BigDecimal.valueOf(250.0),
						volumeUsd = BigDecimal.valueOf(5.0),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 14

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		// has less than buy amount because of frozen funds for fees
		updatedBotWallet.balance shouldBe BigInteger.valueOf(89997904720)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
	}

	@Test
	fun `should not match with mandatory properties and everything is set - match less than min range`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			marketCapFromUsd = BigDecimal.valueOf(1290.0),
			marketCapToUsd = BigDecimal.valueOf(1900.0),
			liquidityFromUsd = BigDecimal.valueOf(250.0),
			liquidityToUsd = BigDecimal.valueOf(1000.0),
			dailyVolumeFromUsd = BigDecimal.valueOf(5.0),
			dailyVolumeToUsd = BigDecimal.valueOf(12.0),
			numberOfHoldersFrom = 11,
			numberOfHoldersTo = 12,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(120)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1200.0),
						liquidityUsd = BigDecimal.valueOf(200.0),
						volumeUsd = BigDecimal.valueOf(3.0),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val notUpdatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		notUpdatedBotWallet shouldNotBe null

		notUpdatedBotWallet!!.id shouldBe 4.toUUID()
		notUpdatedBotWallet.balance shouldBe BigInteger.valueOf(120)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should not match with mandatory properties and everything is set - match more than max range`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			marketCapFromUsd = BigDecimal.valueOf(1290.0),
			marketCapToUsd = BigDecimal.valueOf(1900.0),
			liquidityFromUsd = BigDecimal.valueOf(250.0),
			liquidityToUsd = BigDecimal.valueOf(1000.0),
			dailyVolumeFromUsd = BigDecimal.valueOf(5.0),
			dailyVolumeToUsd = BigDecimal.valueOf(12.0),
			numberOfHoldersFrom = 3,
			numberOfHoldersTo = 9,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(120)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1950.0),
						liquidityUsd = BigDecimal.valueOf(1100.0),
						volumeUsd = BigDecimal.valueOf(16.0),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val notUpdatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		notUpdatedBotWallet shouldNotBe null

		notUpdatedBotWallet!!.id shouldBe 4.toUUID()
		notUpdatedBotWallet.balance shouldBe BigInteger.valueOf(120)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should not match - market position for token exists`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		integrationTestHelper.getBotMarketPosition(
			id = 5.toUUID(),
			botWalletId = 4.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			assumedBuyPrice = BigDecimal(1),
			blockSlot = 4,
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1900.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(13.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
	}

	@Test
	fun `should not match - sell volume is higher than bot settings sell volume`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			buyVolume = BigDecimal.valueOf(0.5),
			sellVolume = BigDecimal.valueOf(0.5),
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(120)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1950.0),
						liquidityUsd = BigDecimal.valueOf(1100.0),
						volumeUsd = BigDecimal.valueOf(16.0),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val notUpdatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		notUpdatedBotWallet shouldNotBe null

		notUpdatedBotWallet!!.id shouldBe 4.toUUID()
		notUpdatedBotWallet.balance shouldBe BigInteger.valueOf(120)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should match - sell volume of new token state change is less than sell volume of bot`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			buyVolume = BigDecimal(0.5),
			sellVolume = BigDecimal(0.5),
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		integrationTestHelper.getBotMarketPosition(
			id = 5.toUUID(),
			botWalletId = 4.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			assumedBuyPrice = BigDecimal(1),
			blockSlot = 4,
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1900.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(13.9),
						buyVolume = BigDecimal.valueOf(0.8),
						sellVolume = BigDecimal.valueOf(0.2),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
	}

	@Test
	fun `should match with mandatory properties and num of hold from is set - should not match hold smaller than min`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			numberOfHoldersFrom = 15,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1200.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should match with mandatory properties and num of hold to is set - should not match hold larger than max`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			numberOfHoldersTo = 20,
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1900.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(13.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 22L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should match with mandatory properties and buy tokens alive for set - token is older`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			beforeSave = {
				setAndReturnPrivateProperty("buyTokensAliveAtLeastFor", java.time.Duration.of(5, ChronoUnit.SECONDS))
			},
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = java.time.Duration.of(5, ChronoUnit.SECONDS).toKotlinDuration(),
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1900.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(13.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 22L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 14

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		// has less than buy amount because of frozen funds for fees
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
	}

	@Test
	fun `should not match with mandatory properties and buy tokens alive for set - token is younger`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			beforeSave = {
				setAndReturnPrivateProperty("buyTokensAliveAtLeastFor", java.time.Duration.of(5, ChronoUnit.SECONDS))
			},
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = java.time.Duration.of(400, ChronoUnit.MILLIS).toKotlinDuration(),
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1900.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(13.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 22L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 15

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should match with mandatory properties and sell fraction set`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			sellTransactionFraction = BigDecimal("0.49"),
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = java.time.Duration.of(5, ChronoUnit.SECONDS).toKotlinDuration(),
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1900.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(13.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 22L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 14

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		// has less than buy amount because of frozen funds for fees
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
	}

	@Test
	fun `should match if everything is checked for red flags but no red flags came in`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			beforeSave = {
				setAndReturnPrivateProperty("tokenTickerCopyIsChecked", true)
				setAndReturnPrivateProperty("creatorHighBuyIsChecked", true)
				setAndReturnPrivateProperty("bundledBuysDetectedIsChecked", true)
				setAndReturnPrivateProperty("suspiciousWalletsDetectedIsChecked", true)
				setAndReturnPrivateProperty("singleHighBuyIsChecked", true)
			},
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1840.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = false,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 10
		bot.remainingBuyFrequency shouldBe 9

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		// has less than buy amount because of frozen funds for fees
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"

		// and bot token info created
		val botTokenInfo = botTokenInfoRepository.findAll()
		botTokenInfo.size shouldBe 1
		with(botTokenInfo.first()) {
			address shouldBe AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump")
			name shouldBe "A token"
			symbol shouldBe "A-TKN"
		}
	}

	@Test
	fun `should match if red flags are present but no checks are set`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1840.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = true,
						isRedFlagCreatorHighBuy = true,
						isRedFlagBundledBuysDetected = true,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 10
		bot.remainingBuyFrequency shouldBe 9

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		// has less than buy amount because of frozen funds for fees
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 1

		botMarketPositions.first().botWalletId shouldBe 4.toUUID()
		botMarketPositions.first().tokenAddress.getAddressString() shouldBe "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"

		// and bot token info created
		val botTokenInfo = botTokenInfoRepository.findAll()
		botTokenInfo.size shouldBe 1
		with(botTokenInfo.first()) {
			address shouldBe AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump")
			name shouldBe "A token"
			symbol shouldBe "A-TKN"
		}
	}

	@Test
	fun `should not match if red flag for ticker copy is checked and is present`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			beforeSave = {
				setAndReturnPrivateProperty("tokenTickerCopyIsChecked", true)
			},
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1200.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = true,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 10
		bot.remainingBuyFrequency shouldBe 10

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should not match if red flag for creator high buy is checked and is present`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			beforeSave = {
				setAndReturnPrivateProperty("creatorHighBuyIsChecked", true)
			},
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1200.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = true,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 10
		bot.remainingBuyFrequency shouldBe 10

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should not match if red flag for bundled buys is checked and is present`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			beforeSave = {
				setAndReturnPrivateProperty("bundledBuysDetectedIsChecked", true)
			},
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1200.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = true,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 10
		bot.remainingBuyFrequency shouldBe 10

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should not match if red flag for suspicious wallets is checked and is present`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			beforeSave = {
				setAndReturnPrivateProperty("suspiciousWalletsDetectedIsChecked", true)
			},
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1200.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 10
		bot.remainingBuyFrequency shouldBe 10

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `should not match if red flag for single high buy is checked and is present`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			beforeSave = {
				setAndReturnPrivateProperty("singleHighBuyIsChecked", true)
			},
		)

		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(************)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1200.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = false,
						isRedFlagSingleHighBuy = true,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 10
		bot.remainingBuyFrequency shouldBe 10

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		updatedBotWallet.balance shouldBe BigInteger.valueOf(************)

		botMarketPositionRepository.findAll().isEmpty() shouldBe true
	}

	@Test
	fun `follower bot should do exact same transaction as bot`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(
			id = 333.toUUID(),
			userId = 2.toUUID(),
			name = "follower bot",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			marketCapFromUsd = BigDecimal("500000"), // intentionally too high, so follower is not matched
			buyFrequency = 15,
		)
		integrationTestHelper.getBotWallet(
			id = 444.toUUID(),
			botId = 333.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(80000000000)) },
		)

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 2.toUUID(),
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			tradeAmount = BigDecimal(30.0),
			buyFrequency = 15,
			beforeSave = { setAndReturnPrivateProperty("followerBotId", 333.toUUID()) },
		)
		integrationTestHelper.getBotWallet(
			id = 4.toUUID(),
			botId = 3.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(80000000000)) },
		)

		// when
		commandBus(
			TokenStateChangesBuyingCommand(
				blockSlot = 4,
				listOf(
					TokenStateChangesBuyingCommand.NewState(
						tokenAliveFor = Duration.ZERO,
						tokenAddress = AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"),
						name = "A token",
						symbol = "A-TKN",
						marketCapUsd = BigDecimal.valueOf(1840.0),
						liquidityUsd = BigDecimal.valueOf(224.9),
						volumeUsd = BigDecimal.valueOf(10.9),
						buyVolume = BigDecimal.valueOf(0.5),
						sellVolume = BigDecimal.valueOf(0.5),
						numOfAccountHolders = 10L,
						curveState = PumpFunCurveState.fromVirtual(
							virtualSolReserves = BigDecimal("********").asNativeAmount().toBase(chain = Chain.SOLANA),
							virtualTokenReserves = BigDecimal("300456").asNativeAmount().toBase(decimals = 6),
							creator = AddressWrapper("D6qRGLFmSdRZJVmvGeybcQDAh74b5NSnzLmPcFZAZMBD"),
						),
						tokenPrice = BigDecimal.valueOf(33.0),
						infoUrl = null,
						fractionOfSellTransactions = BigDecimal("0.5"),
						isRedFlagTokenTickerCopy = false,
						isRedFlagCreatorHighBuy = false,
						isRedFlagBundledBuysDetected = false,
						isRedFlagSuspiciousWalletsDetected = true,
						isRedFlagSingleHighBuy = false,
						creatorGraduationSuccessRateFraction = BigDecimal.ZERO,
					),
				),
			),
		)

		// then
		val bot = botRepository.findByIdAndUserId(id = 3.toUUID(), userId = 1.toUUID())
		bot shouldNotBe null

		bot!!.id shouldBe 3.toUUID()
		bot.buyFrequency shouldBe 15
		bot.remainingBuyFrequency shouldBe 14

		// and follower
		val followerBot = botRepository.findByIdAndUserId(id = 333.toUUID(), userId = 2.toUUID())!!
		followerBot.buyFrequency shouldBe 15
		followerBot.remainingBuyFrequency shouldBe 14

		val updatedBotWallet = botWalletRepository.findByBotId(botId = 3.toUUID())
		updatedBotWallet shouldNotBe null

		updatedBotWallet!!.id shouldBe 4.toUUID()
		// has less than buy amount because of frozen funds for fees
		updatedBotWallet.balance shouldBe BigInteger.valueOf(49997904720)

		val botMarketPositions = botMarketPositionRepository.findAll()
		botMarketPositions.size shouldBe 2

		botMarketPositions.map { it.botWalletId } shouldContainExactlyInAnyOrder listOf(4.toUUID(), 444.toUUID())
		botMarketPositions.all {
			it.tokenAddress.getAddressString() == "7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump"
		} shouldBe true

		// and bot token info created
		val botTokenInfo = botTokenInfoRepository.findAll()
		botTokenInfo.size shouldBe 1
		with(botTokenInfo.first()) {
			address shouldBe AddressWrapper("7DdHyxLZQuudndfrX3ZDDqgK6zPFbm17wGwKJqgjpump")
			name shouldBe "A token"
			symbol shouldBe "A-TKN"
		}
	}
}
