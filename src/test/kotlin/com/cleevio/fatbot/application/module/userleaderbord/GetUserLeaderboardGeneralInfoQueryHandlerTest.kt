package com.cleevio.fatbot.application.module.userleaderbord

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.userleaderbord.exception.IllegalUserLeaderboardAccessException
import com.cleevio.fatbot.application.module.userleaderbord.query.GetUserLeaderboardGeneralInfoQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class GetUserLeaderboardGeneralInfoQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should throw exception when user is not on leaderboard`() {
		integrationTestHelper.getFirebaseUser(id = 999.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 999.toUUID(),
			beforeSave = { updateDonuts(100.toBigDecimal()) },
		)

		assertThrows<IllegalUserLeaderboardAccessException> {
			queryBus(GetUserLeaderboardGeneralInfoQuery(userId = 999.toUUID()))
		}
	}

	@Test
	fun `should return thresholds sorted by rank in descending order`() {
		val userId = 2.toUUID()
		integrationTestHelper.getFirebaseUser(id = 2.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 2.toUUID(),
			beforeSave = { updateDonuts(1000.toBigDecimal()) },
		)
		integrationTestHelper.getUserLeaderboard(
			userId = 2.toUUID(),
			rank = 2,
			donutMultiplier = 1.8.toBigDecimal(),
			donutGainedSnapshot = 1000.toBigDecimal(),
		)

		val result = queryBus(GetUserLeaderboardGeneralInfoQuery(userId = userId))

		result.size shouldBe 5
		result.map { it.rank } shouldBe listOf(10, 50, 100, 250, 1000) // Should be sorted in descending order
		result.map { it.multiplier } shouldBe listOf(
			BigDecimal.valueOf(2),
			BigDecimal.valueOf(1.75),
			BigDecimal.valueOf(1.5),
			BigDecimal.valueOf(1.25),
			BigDecimal.valueOf(1.1),
		)
	}
}
