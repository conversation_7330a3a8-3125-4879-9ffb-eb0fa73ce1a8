package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.query.SearchUserMarketPositionQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.p2p.solanaj.core.PublicKey
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger
import java.net.URI
import java.time.temporal.ChronoUnit

@Suppress("ktlint:standard:max-line-length")
class SearchUserMarketPositionQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val clock: IntegrationTestClock,
) : IntegrationTest() {

	@BeforeEach
	fun setup() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet1 = integrationTestHelper.getWallet(id = 10.toUUID(), userId = user.id)
		val wallet2 = integrationTestHelper.getWallet(
			id = 11.toUUID(),
			userId = user.id,
			address = AddressWrapper("******************************************"),
		)
		val file1 = integrationTestHelper.getFile(id = 21.toUUID())
		val file2 = integrationTestHelper.getFile(id = 22.toUUID())
		val token1 = integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("******************************************"),
			name = "ALPHA token",
			symbol = "ALPHA",
			imageFileId = file1.id,
		)
		val token2 = integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("******************************************"),
			name = "BETA token",
			symbol = "BETA",
			imageFileId = file2.id,
		)
		val token3 = integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("******************************************"),
			name = "GAMMA token",
			symbol = "GAMMA",
		)
		integrationTestHelper.getMarketPosition(
			walletId = wallet1.id,
			tokenAddress = token1.address,
			beforeSave = {
				newBuy(
					amountOfTokensReceived = "500000000000000000000000000".toBigInteger(),
					amountOfBaseCurrencyPaid = "10000000000000000000".toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
				newSell(
					amountOfTokensSold = "0".toBigInteger(),
					amountOfBaseCurrencyReceived = "0".toBigInteger(),
				)
			},
		)
		integrationTestHelper.getMarketPosition(
			walletId = wallet2.id,
			tokenAddress = token2.address,
			beforeSave = {
				newBuy(
					amountOfTokensReceived = "150000000000000000000000000".toBigInteger(),
					amountOfBaseCurrencyPaid = "1000000000000000000".toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
				newBuy(
					amountOfTokensReceived = "50000000000000000000000000".toBigInteger(),
					amountOfBaseCurrencyPaid = "1000000000000000000".toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
				newSell(
					amountOfTokensSold = "150000000000000000000000000".toBigInteger(),
					amountOfBaseCurrencyReceived = "1000000000000000000".toBigInteger(),
				)
			},
		)
		integrationTestHelper.getMarketPosition(
			walletId = wallet1.id,
			tokenAddress = token3.address,
			beforeSave = {
				newBuy(
					amountOfTokensReceived = "250000000000000000000000000".toBigInteger(),
					amountOfBaseCurrencyPaid = "1000000000000000000".toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)

		// token1 current price:  16528000000
		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = token1.address,
			chain = Chain.EVM_MAINNET,
			priceWei = BigInteger("16528000000"), // No change
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("1750.0"),
		)
		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = token1.address,
			chain = Chain.EVM_MAINNET,
			priceWei = BigInteger("16528000000"), // No change
			validAt = clock.currentTime().minus(24, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3500.0"),
		)

		// token2 current price: 26528000000
		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = token2.address,
			chain = Chain.EVM_MAINNET,
			priceWei = BigInteger("13264000000"), // -50% change
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3500.0"),
		)
		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = token2.address,
			chain = Chain.EVM_MAINNET,
			priceWei = BigInteger("53056000000"), // +50% change
			validAt = clock.currentTime().minus(24, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3500.0"),
		)

		// token3 current price: 6528000000
		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = token3.address,
			chain = Chain.EVM_MAINNET,
			priceWei = BigInteger("6400000000"), // 2% change
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3500.0"),
		)
		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = token3.address,
			chain = Chain.EVM_MAINNET,
			priceWei = BigInteger("6189320388"), // 0.05472% change
			validAt = clock.currentTime().minus(24, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3500.0"),

		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("3500.0")
		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j
	}

	@Test
	fun `should search user's market positions - missing price snapshots`() {
		val file = integrationTestHelper.getFile()
		val token = integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("******************************************"),
			name = "DELTA token",
			symbol = "DELTA",
			imageFileId = file.id,
		)
		integrationTestHelper.getMarketPosition(
			walletId = 10.toUUID(),
			tokenAddress = token.address,
			beforeSave = {
				newBuy(
					amountOfTokensReceived = "500000000000000000000000000".toBigInteger(),
					amountOfBaseCurrencyPaid = "10000000000000000000".toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)
		// GetTokenPricesEVM call
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************"]
				"0xe016d3ab00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000001000000000000000000000000add39272e83895e7d3f244f696b7a25635f34234",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000add39272E83895E7d3f244f696B7a25635F34234" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"00000000000000000000000000000000000000000000000000000003d9254400"

		val result = queryBus(
			SearchUserMarketPositionQuery(
				userId = 1.toUUID(),
				useSelectedChains = true,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				filter = SearchUserMarketPositionQuery.Filter(
					walletId = null,
					searchString = "******************************************",
				),
			),
		)

		result.content.size shouldBe 1
		result.content[0].run {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("******************************************")
			tokenDetailUrl shouldBe URI("https://etherscan.io/address/******************************************")
			tokenName shouldBe "DELTA token"
			tokenSymbol shouldBe "DELTA"
			tokenImageUrl shouldBe URI("http://localhost:8080/${file.id}.png")
			pricePerTokenInUsd shouldBeEqualComparingTo 0.000057848.toBigDecimal()
			tokenChain shouldBe Chain.EVM_MAINNET
			tokenNativeAmount shouldBe NativeAmount(BigDecimal("500000000.000000000000000000"))
			oneHourChangeFraction?.shouldBeNull()
			oneDayChangeFraction?.shouldBeNull()
			currentValueUsd shouldBeEqualComparingTo 28924.0.toBigDecimal()
			currentValueChangeUsd shouldBeEqualComparingTo (28909.0).toBigDecimal()
			currentValueChangeFraction shouldBeEqualComparingTo BigDecimal("1927.266666666666666666666666666667")
		}
	}

	@Test
	fun `should search user's market positions - no filter & all chains`() {
		integrationTestHelper.getWallet(
			id = 12.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper("EjwYJMj7wyP9w29WttYBSfh4dfFSuCFiSpAmjhiU72yp"),
			chain = Chain.SOLANA,
		)
		val solanaToken = integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			name = "VINE VINE",
			symbol = "VINE",
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getTokenPairInfo(
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			pairAddress = AddressWrapper("DRWyFG6KtxDwv41qjfSFF6aA1VHsWqUtwCkGhT4Ur7fe"),
			dexType = GetDex.Dex.PUMP_FUN,
			tokenDecimals = 6.toBigInteger(),
			uniswapV3Fee = null,
		)

		integrationTestHelper.getMarketPosition(
			walletId = 12.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = "10".toBigInteger(),
					amountOfBaseCurrencyPaid = "480".toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = solanaToken.address,
			chain = Chain.SOLANA,
			priceWei = BigInteger("24"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3500.0"),
		)
		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = solanaToken.address,
			chain = Chain.SOLANA,
			priceWei = BigInteger("96"),
			validAt = clock.currentTime().minus(24, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("1750.0"),
		)

		// GetTokenPricesEVM call
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************","******************************************","******************************************"]
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000030000000000000000000000006982508145454ce325ddbe47a25d4ec3d23119330000000000000000000000006982508145454ce325ddbe47a25d4ec3d23119340000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311935",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"00000000000000000000000000000000000000000000000000000000000000c0" +
			"0000000000000000000000000000000000000000000000000000000000000003" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311934" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311935" +
			"0000000000000000000000000000000000000000000000000000000000000003" +
			"00000000000000000000000000000000000000000000000000000003d9254400" +
			"000000000000000000000000000000000000000000000000000000062d312800" +
			"0000000000000000000000000000000000000000000000000000000185196000"

		every { rpcApi.getMultipleAccountsOptional(any(), any()) } returns listOf()

		every {
			rpcApi.getMultipleAccounts(listOf(PublicKey("DRWyFG6KtxDwv41qjfSFF6aA1VHsWqUtwCkGhT4Ur7fe")), any())
		} returns listOf(
			mockk {
				every { data } returns listOf(
					"F7f4N2DYrGDEtoNK7+cCAOjXqikJAAAAxB5x/l3pAQDoK4ctAgAAAACAxqR+jQMAAA==",
					"base64",
				)
			},
		)

		integrationTestHelper.setSelectedChains(userId = 1.toUUID(), selectedChains = setOf(Chain.EVM_ARBITRUM_ONE))

		val result = queryBus(
			SearchUserMarketPositionQuery(
				userId = 1.toUUID(),
				useSelectedChains = false,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				filter = SearchUserMarketPositionQuery.Filter(
					walletId = null,
					searchString = null,
				),
			),
		)

		result.content.size shouldBe 4
		result.content[0].run {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("******************************************")
			tokenDetailUrl shouldBe URI("https://etherscan.io/address/******************************************")
			tokenName shouldBe "ALPHA token"
			tokenSymbol shouldBe "ALPHA"
			tokenImageUrl shouldBe URI("http://localhost:8080/00000000-0000-0000-0000-000000000021.png")
			pricePerTokenInUsd shouldBeEqualComparingTo 0.0000578480000000000.toBigDecimal()
			tokenChain shouldBe Chain.EVM_MAINNET
			tokenNativeAmount shouldBe NativeAmount(BigDecimal("500000000.000000000000000000"))
			oneHourChangeFraction?.shouldBeEqualComparingTo((1).toBigDecimal())
			oneDayChangeFraction?.shouldBeEqualComparingTo((0).toBigDecimal()) // Rounding range
			currentValueUsd shouldBeEqualComparingTo 28924.0000000000000000000000000000000000.toBigDecimal()
			currentValueChangeUsd shouldBeEqualComparingTo 28909.0000000000000000000000000000000000000.toBigDecimal()
			currentValueChangeFraction shouldBeEqualComparingTo BigDecimal("1927.266666666666666666666666666667")
		}
		result.content[1].run {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("******************************************")
			tokenDetailUrl shouldBe URI("https://etherscan.io/address/******************************************")
			tokenName shouldBe "GAMMA token"
			tokenSymbol shouldBe "GAMMA"
			tokenImageUrl shouldBe null
			pricePerTokenInUsd shouldBeEqualComparingTo 0.0000228480000000000.toBigDecimal()
			tokenChain shouldBe Chain.EVM_MAINNET
			tokenNativeAmount shouldBe NativeAmount(BigDecimal("250000000.000000000000000000"))
			oneHourChangeFraction?.shouldBeEqualComparingTo(0.02.toBigDecimal())
			oneDayChangeFraction?.shouldBeEqualComparingTo(BigDecimal("0.054720000059560658826892837204342"))
			currentValueUsd shouldBeEqualComparingTo 5712.0000000000000000000000000000000000.toBigDecimal()
			currentValueChangeUsd shouldBeEqualComparingTo 5710.5000000000000000000000000000000000000.toBigDecimal()
			currentValueChangeFraction shouldBeEqualComparingTo 3807.toBigDecimal()
		}
		result.content[2].run {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("******************************************")
			tokenDetailUrl shouldBe URI("https://etherscan.io/address/******************************************")
			tokenName shouldBe "BETA token"
			tokenSymbol shouldBe "BETA"
			tokenImageUrl shouldBe URI("http://localhost:8080/00000000-0000-0000-0000-000000000022.png")
			pricePerTokenInUsd shouldBeEqualComparingTo "0.0000928480000000000".toBigDecimal()
			tokenChain shouldBe Chain.EVM_MAINNET
			tokenNativeAmount shouldBe NativeAmount(BigDecimal("50000000.000000000000000000"))
			oneHourChangeFraction?.shouldBeEqualComparingTo("1".toBigDecimal())
			oneDayChangeFraction?.shouldBeEqualComparingTo("-0.5".toBigDecimal()) // -50% token price & +50% sol price
			currentValueUsd shouldBeEqualComparingTo "4642.4000000000000000000000000000000000".toBigDecimal()
			currentValueChangeUsd shouldBeEqualComparingTo BigDecimal("4641.6500000000000000000000000000000000000")
			currentValueChangeFraction shouldBeEqualComparingTo "6188.866666666666666666666666666667".toBigDecimal()
		}
		result.content[3].run {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump")
			tokenDetailUrl shouldBe URI("https://solscan.io/address/6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump")
			tokenName shouldBe "VINE VINE"
			tokenSymbol shouldBe "VINE"
			tokenImageUrl shouldBe null
			pricePerTokenInUsd shouldBeEqualComparingTo 0.0001680000.toBigDecimal()
			tokenChain shouldBe Chain.SOLANA
			tokenNativeAmount shouldBe NativeAmount(BigDecimal("0.000010"))
			oneHourChangeFraction?.shouldBeEqualComparingTo("1".toBigDecimal())
			oneDayChangeFraction?.shouldBeEqualComparingTo(0.toBigDecimal())
			currentValueUsd shouldBeEqualComparingTo 1.6800000E-9.toBigDecimal()
			currentValueChangeUsd shouldBeEqualComparingTo (-7.183200000E-7).toBigDecimal()
			currentValueChangeFraction shouldBeEqualComparingTo BigDecimal("-0.997666666666666666666666666666666667")
		}
	}

	@Test
	fun `should search user's market positions - no filter & selected chains`() {
		// given in addition to @BeforeEach setup
		integrationTestHelper.getWallet(
			id = 12.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper("EjwYJMj7wyP9w29WttYBSfh4dfFSuCFiSpAmjhiU72yp"),
			chain = Chain.SOLANA,
		)
		val solanaToken = integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			name = "VINE VINE",
			symbol = "VINE",
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getTokenPairInfo(
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			pairAddress = AddressWrapper("DRWyFG6KtxDwv41qjfSFF6aA1VHsWqUtwCkGhT4Ur7fe"),
			dexType = GetDex.Dex.PUMP_FUN,
			tokenDecimals = 6.toBigInteger(),
			uniswapV3Fee = null,
		)

		integrationTestHelper.getMarketPosition(
			walletId = 12.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = "10".toBigInteger(),
					amountOfBaseCurrencyPaid = "480".toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = solanaToken.address,
			chain = Chain.SOLANA,
			priceWei = BigInteger("24"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3500.0"),
		)
		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = solanaToken.address,
			chain = Chain.SOLANA,
			priceWei = BigInteger("96"),
			validAt = clock.currentTime().minus(24, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("1750.0"),
		)

		// GetTokenPricesEVM call
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************","******************************************","******************************************"]
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000030000000000000000000000006982508145454ce325ddbe47a25d4ec3d23119330000000000000000000000006982508145454ce325ddbe47a25d4ec3d23119340000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311935",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"00000000000000000000000000000000000000000000000000000000000000c0" +
			"0000000000000000000000000000000000000000000000000000000000000003" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311934" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311935" +
			"0000000000000000000000000000000000000000000000000000000000000003" +
			"00000000000000000000000000000000000000000000000000000003d9254400" +
			"000000000000000000000000000000000000000000000000000000062d312800" +
			"0000000000000000000000000000000000000000000000000000000185196000"

		every { rpcApi.getMultipleAccountsOptional(any(), any()) } returns listOf()

		every {
			rpcApi.getMultipleAccounts(listOf(PublicKey("DRWyFG6KtxDwv41qjfSFF6aA1VHsWqUtwCkGhT4Ur7fe")), any())
		} returns listOf(
			mockk {
				every { data } returns listOf(
					"F7f4N2DYrGDEtoNK7+cCAOjXqikJAAAAxB5x/l3pAQDoK4ctAgAAAACAxqR+jQMAAA==",
					"base64",
				)
			},
		)

		integrationTestHelper.setSelectedChains(userId = 1.toUUID(), selectedChains = setOf(Chain.SOLANA))

		val result = queryBus(
			SearchUserMarketPositionQuery(
				userId = 1.toUUID(),
				useSelectedChains = true,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				filter = SearchUserMarketPositionQuery.Filter(
					walletId = null,
					searchString = null,
				),
			),
		)

		result.content.size shouldBe 1
		with(result.content[0]) {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump")
			tokenDetailUrl shouldBe URI("https://solscan.io/address/6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump")
			tokenName shouldBe "VINE VINE"
			tokenSymbol shouldBe "VINE"
			tokenImageUrl shouldBe null
			pricePerTokenInUsd shouldBeEqualComparingTo "0.000168".toBigDecimal()
			tokenChain shouldBe Chain.SOLANA
			tokenNativeAmount shouldBe NativeAmount(BigDecimal("0.000010"))
			oneHourChangeFraction?.shouldBeEqualComparingTo("1".toBigDecimal())
			oneDayChangeFraction?.shouldBeEqualComparingTo("0".toBigDecimal()) // -50% token price & +50% sol price
			currentValueUsd shouldBeEqualComparingTo "0.00000000168".toBigDecimal()
			currentValueChangeUsd shouldBeEqualComparingTo BigDecimal("-7.183200000E-7")
			currentValueChangeFraction shouldBeEqualComparingTo "-0.997666666666666666666666666666666667".toBigDecimal()
		}
	}

	@Test
	fun `should search user's market positions - walletId filter`() {
		// GetTokenPricesEVM call
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************"]
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311934",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311934" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000000000000000000000000000000000062d312800"

		val result = queryBus(
			SearchUserMarketPositionQuery(
				userId = 1.toUUID(),
				useSelectedChains = true,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				filter = SearchUserMarketPositionQuery.Filter(
					walletId = 11.toUUID(),
					searchString = null,
				),
			),
		)

		result.content.size shouldBe 1
		result.content[0].run {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("******************************************")
			tokenDetailUrl shouldBe URI("https://etherscan.io/address/******************************************")
			tokenName shouldBe "BETA token"
			tokenSymbol shouldBe "BETA"
			tokenImageUrl shouldBe URI("http://localhost:8080/${22.toUUID()}.png")
			pricePerTokenInUsd shouldBeEqualComparingTo 0.000092848.toBigDecimal()
			tokenChain shouldBe Chain.EVM_MAINNET
			tokenNativeAmount shouldBe NativeAmount(BigDecimal("50000000.000000000000000000"))
			oneHourChangeFraction?.shouldBeEqualComparingTo(1.toBigDecimal())
			oneDayChangeFraction?.shouldBeEqualComparingTo((-0.5).toBigDecimal())
			currentValueUsd shouldBeEqualComparingTo 4642.4.toBigDecimal()
			currentValueChangeUsd shouldBeEqualComparingTo 4641.65.toBigDecimal()
			currentValueChangeFraction shouldBeEqualComparingTo BigDecimal("6188.866666666666666666666666666667")
		}
	}

	@Test
	fun `should search user's market positions - searchString filter`() {
		// GetTokenPricesEVM call
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************"]
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"00000000000000000000000000000000000000000000000000000003d9254400"

		val result = queryBus(
			SearchUserMarketPositionQuery(
				userId = 1.toUUID(),
				useSelectedChains = true,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				filter = SearchUserMarketPositionQuery.Filter(
					walletId = null,
					searchString = "******************************************",
				),
			),
		)

		result.content.size shouldBe 1
		result.content[0].run {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("******************************************")
			tokenDetailUrl shouldBe URI("https://etherscan.io/address/******************************************")
			tokenName shouldBe "ALPHA token"
			tokenSymbol shouldBe "ALPHA"
			tokenImageUrl shouldBe URI("http://localhost:8080/${21.toUUID()}.png")
			pricePerTokenInUsd shouldBeEqualComparingTo 0.000057848.toBigDecimal()
			tokenChain shouldBe Chain.EVM_MAINNET
			tokenNativeAmount shouldBe NativeAmount(BigDecimal("500000000.000000000000000000"))
			oneHourChangeFraction?.shouldBeEqualComparingTo("1".toBigDecimal())
			oneDayChangeFraction?.shouldBeEqualComparingTo(0.toBigDecimal())
			currentValueUsd shouldBeEqualComparingTo 28924.0.toBigDecimal()
			currentValueChangeUsd shouldBeEqualComparingTo (28909.0).toBigDecimal()
			currentValueChangeFraction shouldBeEqualComparingTo BigDecimal("1927.266666666666666666666666666667")
		}
	}

	@Test
	fun `should search user market position of Solana wallet`() {
		// given in addition to @BeforeEach setup
		integrationTestHelper.getWallet(
			id = 12.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper("EjwYJMj7wyP9w29WttYBSfh4dfFSuCFiSpAmjhiU72yp"),
			chain = Chain.SOLANA,
		)
		val solanaToken = integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			name = "VINE VINE",
			symbol = "VINE",
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getTokenPairInfo(
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			pairAddress = AddressWrapper("DRWyFG6KtxDwv41qjfSFF6aA1VHsWqUtwCkGhT4Ur7fe"),
			dexType = GetDex.Dex.PUMP_FUN,
			tokenDecimals = 6.toBigInteger(),
			uniswapV3Fee = null,
		)

		integrationTestHelper.getMarketPosition(
			walletId = 12.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = "10".toBigInteger(),
					amountOfBaseCurrencyPaid = "480".toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = solanaToken.address,
			chain = Chain.SOLANA,
			priceWei = BigInteger("24"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3500.0"),
		)
		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = solanaToken.address,
			chain = Chain.SOLANA,
			priceWei = BigInteger("96"),
			validAt = clock.currentTime().minus(24, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("1750.0"),
		)

		every { rpcApi.getMultipleAccountsOptional(any(), any()) } returns listOf()

		every {
			rpcApi.getMultipleAccounts(listOf(PublicKey("DRWyFG6KtxDwv41qjfSFF6aA1VHsWqUtwCkGhT4Ur7fe")), any())
		} returns listOf(
			mockk {
				every { data } returns listOf(
					"F7f4N2DYrGDEtoNK7+cCAOjXqikJAAAAxB5x/l3pAQDoK4ctAgAAAACAxqR+jQMAAA==",
					"base64",
				)
			},
		)

		val result = queryBus(
			SearchUserMarketPositionQuery(
				userId = 1.toUUID(),
				useSelectedChains = true,
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				filter = SearchUserMarketPositionQuery.Filter(
					walletId = 12.toUUID(),
					searchString = "6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump",
				),
			),
		)

		result.content.size shouldBe 1
		with(result.content[0]) {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump")
			tokenDetailUrl shouldBe URI("https://solscan.io/address/6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump")
			tokenName shouldBe "VINE VINE"
			tokenSymbol shouldBe "VINE"
			tokenImageUrl shouldBe null
			pricePerTokenInUsd shouldBeEqualComparingTo "0.000168".toBigDecimal()
			tokenChain shouldBe Chain.SOLANA
			tokenNativeAmount shouldBe NativeAmount(BigDecimal("0.000010"))
			oneHourChangeFraction?.shouldBeEqualComparingTo("1".toBigDecimal())
			oneDayChangeFraction?.shouldBeEqualComparingTo("0".toBigDecimal()) // -50% token price & +50% sol price
			currentValueUsd shouldBeEqualComparingTo "0.00000000168".toBigDecimal()
			currentValueChangeUsd shouldBeEqualComparingTo BigDecimal("-7.183200000E-7")
			currentValueChangeFraction shouldBeEqualComparingTo "-0.997666666666666666666666666666666667".toBigDecimal()
		}
	}
}
