package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.wallet.exception.WalletNotFoundException
import com.cleevio.fatbot.application.module.wallet.query.ExportUserWalletQuery
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ExportUserWalletQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should export user wallet private key`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			privateKey = "547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
		)

		// when
		val result = queryBus(
			ExportUserWalletQuery(
				userId = 1.toUUID(),
				walletId = 10.toUUID(),
				password = "dummy value",
			),
		)

		// then
		result.privateKey shouldBe "547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6"
	}

	@Test
	fun `should throw when trying to export wallet of different user`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			privateKey = "547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
		)

		// when then
		shouldThrow<WalletNotFoundException> {
			queryBus(
				ExportUserWalletQuery(
					userId = 2.toUUID(),
					walletId = 10.toUUID(),
					password = "dummy value",
				),
			)
		}
	}
}
