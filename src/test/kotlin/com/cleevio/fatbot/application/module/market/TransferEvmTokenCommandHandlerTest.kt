package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.evm.model.FeesPerGas
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.command.TransferTokenCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.exception.InsufficientFundsForTransactionException
import com.cleevio.fatbot.application.module.wallet.exception.InsufficientFundsForTransactionFeesException
import com.cleevio.fatbot.application.module.wallet.exception.WalletNotFoundException
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal

const val BALANCE_OF_SELECTOR = "70a08231"

@Suppress("ktlint:standard:max-line-length")
class TransferEvmTokenCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
) : IntegrationTest() {

	@Test
	fun `should transfer token with 18 decimals from one address to another`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 1000.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = "18".toBigInteger(),
		)

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = **********.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getWalletBalance(any()) } returns **********000000.toBigInteger()

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					BALANCE_OF_SELECTOR + // selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB", // wallet address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x0000000000000000000000000000000000000000000000000000000000000064" // 100

		/*
		{
		  "chainId": "1",
		  "type": "EIP-1559",
		  "valid": true,
		  "hash": "0xacbf0193d1f56dcbe82a5095b9d15d08fd5a0c743618a80352a16c58d75bc116",
		  "nonce": "1",
		  "gasLimit": "140000",
		  "maxFeePerGas": "1000001500",
		  "maxPriorityFeePerGas": "**********",
		  "from": "******************************************",
		  "to": "******************************************",
		  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
		  "v": "00",
		  "r": "11740cfd0d3cd95106073f6732d94f4b63a9839e34bf223115b4d20aeb0671d7",
		  "s": "2b9de96063d9fd3c8d9f9fbe6fc4d88a8af86daed2a135d54d300879286f5c45",
		  "value": "0",
		  "input": "0xa9059cbb0000000000000000000000001d5cb2e5355858fddbad1f595581b5aa07902c260000000000000000000000000000000000000000000000000000000000000001",
		  "functionHash": "0xa9059cbb", // Transfer function selector

		  "decodedInputs": [
				"******************************************", // destinationWalletAddress
				"1" // nativeAmount converted to base amount
      		]
		  }
		 */
		every {
			web3jWrapper.sendRawTransaction(
				signedTransaction = "0x02f8b00101843b9aca00843b9acfdc830222e094046bbabe36c18422303ab83adb74bcdaa4e8ee1b80b844a9059cbb0000000000000000000000001d5cb2e5355858fddbad1f595581b5aa07902c260000000000000000000000000000000000000000000000000000000000000001c080a011740cfd0d3cd95106073f6732d94f4b63a9839e34bf223115b4d20aeb0671d7a02b9de96063d9fd3c8d9f9fbe6fc4d88a8af86daed2a135d54d300879286f5c45",
			)
		} just Runs
		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()

		val result = commandBus(
			TransferTokenCommand(
				userId = 1.toUUID(),
				walletId = 1000.toUUID(),
				tokenAddress = AddressWrapper("******************************************"),
				destinationWalletAddress = AddressWrapper("******************************************"),
				nativeAmount = 1e-18.toBigDecimal().asNativeAmount(), // 1 WEI
				password = "password",
			),
		)

		result.txHash shouldBe TxHash("0xacbf0193d1f56dcbe82a5095b9d15d08fd5a0c743618a80352a16c58d75bc116")
	}

	@Test
	fun `should transfer token with 9 decimals from one address to another`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 1000.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = "9".toBigInteger(),
		)

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = **********.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getWalletBalance(any()) } returns **********000000.toBigInteger()

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					BALANCE_OF_SELECTOR + // selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB", // wallet address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x5f5e100" // 0.1 in token base amount

		/*
		Decoded signedTransaction:
		{
		  "chainId": "1",
		  "type": "EIP-1559",
		  "valid": true,
		  "hash": "0xd04fbcc7a49b0e12b6a0f9ea650f0af1cc47401066a1dda2f17f1aae7b050600",
		  "nonce": "1",
		  "gasLimit": "140000",
		  "maxFeePerGas": "1000001500",
		  "maxPriorityFeePerGas": "**********",
		  "from": "******************************************",
		  "to": "******************************************",
		  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
		  "v": "01",
		  "r": "20d3f6759f22a35ab3bf831d9dd2aae4518a452bbd6618c54cd91c2cc170573c",
		  "s": "65b0b3446536504102085e334b7b53c96071972a0cde7dc65c6dee7386a60a47",
		  "value": "0",
		  "input": "0xa9059cbb0000000000000000000000001d5cb2e5355858fddbad1f595581b5aa07902c260000000000000000000000000000000000000000000000000000000005f5e100",
		  "functionHash": "0xa9059cbb", // Transfer function selector

		  "decodedInputs": [
				"******************************************", // destinationWalletAddress
				"100000000" // nativeAmount converted to base amount
      		]
		}
		 */
		every {
			web3jWrapper.sendRawTransaction(
				signedTransaction = "0x02f8b00101843b9aca00843b9acfdc830222e094046bbabe36c18422303ab83adb74bcdaa4e8ee1b80b844a9059cbb0000000000000000000000001d5cb2e5355858fddbad1f595581b5aa07902c260000000000000000000000000000000000000000000000000000000005f5e100c001a020d3f6759f22a35ab3bf831d9dd2aae4518a452bbd6618c54cd91c2cc170573ca065b0b3446536504102085e334b7b53c96071972a0cde7dc65c6dee7386a60a47",
			)
		} just Runs
		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()

		val result = commandBus(
			TransferTokenCommand(
				userId = 1.toUUID(),
				walletId = 1000.toUUID(),
				tokenAddress = AddressWrapper("******************************************"),
				destinationWalletAddress = AddressWrapper("******************************************"),
				nativeAmount = BigDecimal("0.1").asNativeAmount(),
				password = "password",
			),
		)

		result.txHash shouldBe TxHash("0xd04fbcc7a49b0e12b6a0f9ea650f0af1cc47401066a1dda2f17f1aae7b050600")
	}

	@Test
	fun `should throw when user is using other user's wallet`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 2.toUUID())

		shouldThrow<WalletNotFoundException> {
			commandBus(
				TransferTokenCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					destinationWalletAddress = AddressWrapper("******************************************"),
					nativeAmount = NativeAmount(BigDecimal("0.000000000000000001")),
					password = "password",
				),
			)
		}
	}

	@ParameterizedTest
	@ValueSource(longs = [-1, 0])
	fun `should throw when trying to transfer zero amount of ETH`(ethToTransfer: Long) {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 1000.toUUID(), userId = 1.toUUID())

		val toTransfer = ethToTransfer.toBigInteger().asBaseAmount().toNative(Chain.EVM_MAINNET)
		shouldThrow<ConstraintViolationException> {
			commandBus(
				TransferTokenCommand(
					userId = 1.toUUID(),
					walletId = 1000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					destinationWalletAddress = AddressWrapper("******************************************"),
					nativeAmount = toTransfer,
					password = "password",
				),
			)
		}
	}

	@Test
	fun `should throw because user has insufficient wallet balance to pay for transaction fees`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("******************************************"),
		)

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = **********.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getWalletBalance(any()) } returns 140000209999999.toBigInteger()
		every { web3jWrapper.estimateGas(any()) } returns **********0.toBigInteger()
		every { web3jWrapper.getNonce(any()) } returns 0.toBigInteger()

		shouldThrow<InsufficientFundsForTransactionFeesException> {
			commandBus(
				TransferTokenCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					destinationWalletAddress = AddressWrapper("******************************************"),
					nativeAmount = NativeAmount(BigDecimal("0.000000000000000001")),
					password = "password",
				),
			)
		}
	}

	@Test
	fun `should throw because user has insufficient token amount to transfer`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper("******************************************"),
		)

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = **********.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getWalletBalance(any()) } returns 140000210000000.toBigInteger()
		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()
		every { web3jWrapper.getNonce(any()) } returns 0.toBigInteger()

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"70a08231" + // selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB", // wallet address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x0000000000000000000000000000000000000000000000000000000000000064" // 100

		shouldThrow<InsufficientFundsForTransactionException> {
			commandBus(
				TransferTokenCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					destinationWalletAddress = AddressWrapper("******************************************"),
					nativeAmount = NativeAmount(BigDecimal("0.000000000000000101")),
					password = "password",
				),
			)
		}
	}
}
