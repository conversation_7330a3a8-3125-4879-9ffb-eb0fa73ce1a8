package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.command.TrackTokenCommand
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.market.MarketPositionRepository
import com.cleevio.fatbot.domain.transaction.TransactionRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger

@Suppress("ktlint:standard:max-line-length")
class TrackTokenCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val transactionRepository: TransactionRepository,
	@Autowired private val marketPositionRepository: MarketPositionRepository,
) : IntegrationTest() {

	@Test
	fun `should track token because its platform balance is lower than on chain balance`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		integrationTestHelper.getMarketPosition(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 100000.toBigInteger(),
					amountOfBaseCurrencyPaid = 100000.toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
				newSell(
					amountOfTokensSold = 20000.toBigInteger(),
					amountOfBaseCurrencyReceived = 20000.toBigInteger(),
				)
			},
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			tokenDecimals = 6.toBigInteger(),
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j

		// GetTokenPricesEVM call - token price is 5 wei
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// ["******************************************"]
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000000000000000000000000000000000000000c350"

		// GetErc20TokenBalancesEVM call - on chain balance is 100000, platform balance is 80000
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f4700000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"00000000000000000000000000000000000000000000000000000000000186a0"

		transactionRepository.count() shouldBe 0

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("1.5")

		val result = commandBus(
			TrackTokenCommand(
				userId = user.id,
				walletId = wallet.id,
				tokenAddress = AddressWrapper("******************************************"),
			),
		)

		result shouldBe TrackTokenCommand.Result(
			TrackTokenCommand.TrackTokenResult.BALANCES_EVENED,
		)

		transactionRepository.count() shouldBe 1
		transactionRepository.findAll().first().run {
			walletId shouldBe wallet.id
			type shouldBe TransactionType.BUY
			status shouldBe TransactionStatus.SUCCESS
			nonce shouldBe null
			baseValue shouldBe 1000.toBigInteger()
			amountIn shouldBe 1000.toBigInteger()
			amountOut shouldBe 20000.toBigInteger()
			fatbotFee shouldBe null
			signedTx shouldBe null
			tokenAddress shouldBe AddressWrapper("******************************************")
			chain shouldBe Chain.EVM_MAINNET
		}

		marketPositionRepository.findAll().first().run {
			totalTokenAmountBought shouldBe 120000.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 101000.toBigInteger()
			totalTokenAmountSold shouldBe 20000.toBigInteger()
		}
	}

	@Test
	fun `should track token because its platform balance is lower than on chain balance, and wallet has no market position`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			tokenDecimals = 6.toBigInteger(),
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j

		// GetTokenPricesEVM call - token price is 5 wei
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// ["******************************************"]
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000000000000000000000000000000000000000c350"

		// GetErc20TokenBalancesEVM call - on chain balance is 100000, platform balance is 0
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f4700000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"00000000000000000000000000000000000000000000000000000000000186a0"

		transactionRepository.count() shouldBe 0

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("1.5")

		val result = commandBus(
			TrackTokenCommand(
				userId = user.id,
				walletId = wallet.id,
				tokenAddress = AddressWrapper("******************************************"),
			),
		)

		result shouldBe TrackTokenCommand.Result(
			TrackTokenCommand.TrackTokenResult.BALANCES_EVENED,
		)

		transactionRepository.count() shouldBe 1
		transactionRepository.findAll().first().run {
			walletId shouldBe wallet.id
			type shouldBe TransactionType.BUY
			status shouldBe TransactionStatus.SUCCESS
			nonce shouldBe null
			baseValue shouldBe 5000.toBigInteger()
			amountIn shouldBe 5000.toBigInteger()
			amountOut shouldBe 100000.toBigInteger()
			fatbotFee shouldBe null
			signedTx shouldBe null
			tokenAddress shouldBe AddressWrapper("******************************************")
			chain shouldBe Chain.EVM_MAINNET
		}

		marketPositionRepository.findAll().first().run {
			totalTokenAmountBought shouldBe 100000.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 5000.toBigInteger()
			totalTokenAmountSold shouldBe 0.toBigInteger()
		}
	}

	@Test
	fun `shouldn't track token because its platform balance is higher than on chain balance`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		integrationTestHelper.getMarketPosition(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 120000.toBigInteger(),
					amountOfBaseCurrencyPaid = 120000.toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = "******************************************"),
		)

		// GetTokenPricesEVM call - token price is 50000 wei
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// ["******************************************"]
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000000000000000000000000000000000000000c350"

		// GetErc20TokenBalancesEVM call - on chain balance is 100000, platform balance is 120000
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f4700000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"00000000000000000000000000000000000000000000000000000000000186a0"

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("1.5")

		val result = commandBus(
			TrackTokenCommand(
				userId = user.id,
				walletId = wallet.id,
				tokenAddress = AddressWrapper("******************************************"),
			),
		)

		result shouldBe TrackTokenCommand.Result(
			TrackTokenCommand.TrackTokenResult.PLATFORM_BALANCE_HIGHER,
		)

		transactionRepository.count() shouldBe 0

		marketPositionRepository.findAll().first().run {
			totalTokenAmountBought shouldBe 120000.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 120000.toBigInteger()
			totalTokenAmountSold shouldBe 0.toBigInteger()
		}
	}

	@Test
	fun `shouldn't track token because its platform balance and on chain balance are equal`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
		)
		integrationTestHelper.getMarketPosition(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 100000.toBigInteger(),
					amountOfBaseCurrencyPaid = 100000.toBigInteger(),
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j

		// GetTokenPricesEVM call - token price is 50000 wei
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// ["******************************************"]
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000000000000000000000000000000000000000c350"

		// GetErc20TokenBalancesEVM call - on chain balance is 100000, platform balance is 100000
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f4700000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"00000000000000000000000000000000000000000000000000000000000186a0"

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = "******************************************"),
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("1.5")

		val result = commandBus(
			TrackTokenCommand(
				userId = user.id,
				walletId = wallet.id,
				tokenAddress = AddressWrapper("******************************************"),
			),
		)

		result shouldBe TrackTokenCommand.Result(
			TrackTokenCommand.TrackTokenResult.BALANCES_ARE_EQUAL,
		)

		transactionRepository.count() shouldBe 0

		marketPositionRepository.findAll().first().run {
			totalTokenAmountBought shouldBe 100000.toBigInteger()
			totalTokenAcquisitionCostBaseAmount shouldBe 100000.toBigInteger()
			totalTokenAmountSold shouldBe 0.toBigInteger()
		}
	}

	@Test
	fun `no market position is created when user does not have market position and has no token on chain`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(
			id = 5.toUUID(),
			userId = user.id,
			address = AddressWrapper(addressString = "******************************************"),
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j

		// GetErc20TokenBalancesEVM call - on chain balance is 100000, platform balance is 100000
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xde1b5fd70000000000000000000000000000000000000000000000000000000000000040000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f4700000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000000000000000000"

		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = "******************************************"),
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("1.5")

		val result = commandBus(
			TrackTokenCommand(
				userId = user.id,
				walletId = wallet.id,
				tokenAddress = AddressWrapper("******************************************"),
			),
		)

		result shouldBe TrackTokenCommand.Result(
			TrackTokenCommand.TrackTokenResult.BALANCES_ARE_EQUAL,
		)

		transactionRepository.count() shouldBe 0
		marketPositionRepository.count() shouldBe 0
	}
}
