package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.evm.context.UniswapV3Fee
import com.cleevio.fatbot.adapter.out.evm.model.FeesPerGas
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.command.BuyTokenCommand
import com.cleevio.fatbot.application.module.market.exception.InvalidDexPairProvidedException
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.token.exception.TokenNotFoundException
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.exception.InsufficientFundsForTransactionException
import com.cleevio.fatbot.application.module.wallet.exception.InsufficientFundsForTransactionFeesException
import com.cleevio.fatbot.application.module.wallet.exception.WalletNotFoundException
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger

@Suppress("ktlint:standard:max-line-length")
class BuyTokenCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
) : IntegrationTest() {

	@Test
	@Disabled("Enable this when anti-mev will be optional (and not forced)")
	fun `should categorize token as from UniswapV2 and buy it`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 1000.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
		)

		every { web3jWrapper.getBaseFeePerGas() } returns 1000.toBigInteger()
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getWalletBalance(any()) } returns 1000000000000000.toBigInteger()

		/* // Decoded via https://rawtxdecode.in/
			{
			  "chainId": "1",
			  "type": "EIP-1559",
			  "valid": true,
			  "hash": "0x18e0d91ec1614db90412fdef0f93968cbc5829476dcff0023548620f0d77f9f7",
			  "nonce": "1",
			  "gasLimit": "28635",
			  "maxFeePerGas": "1000001500",
			  "maxPriorityFeePerGas": "1000000000",
			  "from": "******************************************",
			  "to": "******************************************",
			  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
			  "v": "00",
			  "r": "7d1b5e9ccdb315107c5be38255cfbeb0827f302fd7af73bee6bb624393f6c902",
			  "s": "5fc4418a74a7c10efd4f10a8944275aa97bf46e9c9ad40a3e6b22167c7bf1eb1",
			  "value": "1",
			  "input": "0x9b38e5ae0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000",
			  "functionHash": "0x9b38e5ae",
			  "functionName": "buyV2",
			  "decodedInputs": {
				"amountIn": "1",
				"token": "******************************************",
				"to": "******************************************",
				"uuid": "00000000-0000-0000-0000-000000000000",
				"feePercentage": "100",
				"referralFeePercentage": "0"
			  }
			}
		 */
		every {
			web3jWrapper.sendRawTransactionAsync(
				signedTransaction = "0x02f901900101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba1301b901249b38e5ae0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c080a07d1b5e9ccdb315107c5be38255cfbeb0827f302fd7af73bee6bb624393f6c902a05fc4418a74a7c10efd4f10a8944275aa97bf46e9c9ad40a3e6b22167c7bf1eb1",
			)
		} just Runs

		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()

		val result = commandBus(
			BuyTokenCommand(
				userId = 1.toUUID(),
				walletId = 1000.toUUID(),
				tokenAddress = AddressWrapper("******************************************"),
				buyForNativeAmount = BigInteger.ONE.asBaseAmount().toNative(Chain.EVM_MAINNET),
				dexPairInfo = GetDex.UniswapV2(
					pairAddress = AddressWrapper("******************************************"),
				),
			),
		)

		result.txHash shouldBe TxHash("0x18e0d91ec1614db90412fdef0f93968cbc5829476dcff0023548620f0d77f9f7")
	}

	@Test
	fun `should categorize token as from UniswapV2 and buy it privately, when wallet buy anti mev is turned ON`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 1000.toUUID(), userId = 1.toUUID()) {
			patchSettings(
				customName = null,
				buyAntiMevProtection = true,
				sellAntiMevProtection = null,
			)
		}

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
		)

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = 1000000000.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getWalletBalance(any()) } returns 1000000000000000.toBigInteger()
		every { web3jWrapper.getBlockNumber() } returns 99.toBigInteger()

		/* Decoded via https://rawtxdecode.in/
		{
		  "chainId": "1",
		  "type": "EIP-1559",
		  "valid": true,
		  "hash": "0xc6c86c01f39422aea4379deb39656e8d4bbf160506527fbec5aadfa096ff82c7",
		  "nonce": "1",
		  "gasLimit": "28635",
		  "maxFeePerGas": "1000001500",
		  "maxPriorityFeePerGas": "1000000000",
		  "from": "******************************************",
		  "to": "******************************************",
		  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
		  "v": "00",
		  "r": "7d1b5e9ccdb315107c5be38255cfbeb0827f302fd7af73bee6bb624393f6c902",
		  "s": "5fc4418a74a7c10efd4f10a8944275aa97bf46e9c9ad40a3e6b22167c7bf1eb1",
		  "value": "1",
		  "input": "0x9b38e5ae0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000",
		  "functionHash": "0x9b38e5ae",
		  "functionName": "buyV2",
		  "decodedInputs": {
			"amountIn": "1",
			"token": "******************************************",
			"to": "******************************************",
			"uuid": "00000000-0000-0000-0000-000000000000",
			"feePercentage": "100",
			"referralFeePercentage": "0",
			"amountOutMin": "117333334"
		  }
		}
		 */
		every {
			beaverbuildConnector.sendPrivateEthTransaction(
				SignedTx(
					signedTx = "0x02f901b00101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba1301b901448c46a0d400000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000006fe5d56000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c001a0e6137c86e8e0a839ddea4d20ed5c5ae9326f6856319a2a01a267618d72db82a9a029b25957fef7bf02f5ead0e48ff3e34558bb0fe4ac69acb53816a0b420d690f6",
				),
			)
		} just Runs
		every {
			titanbuilderConnector.sendPrivateEthTransaction(
				signedTx = SignedTx(
					signedTx = "0x02f901b00101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba1301b901448c46a0d400000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000006fe5d56000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c001a0e6137c86e8e0a839ddea4d20ed5c5ae9326f6856319a2a01a267618d72db82a9a029b25957fef7bf02f5ead0e48ff3e34558bb0fe4ac69acb53816a0b420d690f6",
				),
				maxAllowedBlockNumberToInclude = BigInteger.valueOf(101),
			)
		} just Runs

		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()

		// Amount out call
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0x242f1fc80000000000000000000000000000000000000000000000000000000000000001000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b0000000000000000000000000000000000000000000000000000000000000001",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000075BCD15"

		val result = commandBus(
			BuyTokenCommand(
				userId = 1.toUUID(),
				walletId = 1000.toUUID(),
				tokenAddress = AddressWrapper("******************************************"),
				buyForNativeAmount = BigInteger.ONE.asBaseAmount().toNative(Chain.EVM_MAINNET),
				dexPairInfo = GetDex.UniswapV2(
					pairAddress = AddressWrapper("******************************************"),
				),
			),
		)

		verify(exactly = 0) { web3jWrapper.sendRawTransactionAsync(any()) }

		result.txHash shouldBe TxHash("0xc6c86c01f39422aea4379deb39656e8d4bbf160506527fbec5aadfa096ff82c7")
	}

	@Test
	@Disabled("Enable this when anti-mev will be optional (and not forced)")
	fun `should categorize token as from UniswapV3, with FEE 10000, and buy it`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 1000.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
		)

		every { web3jWrapper.getBaseFeePerGas() } returns 1000.toBigInteger()
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getWalletBalance(any()) } returns 1000000000000000.toBigInteger()

		/* // some fields left out for clarity. Decoded via https://rawtxdecode.in/
			{
			  "chainId": "1",
			  "type": "EIP-1559",
			  "valid": true,
			  "hash": "0x8877a208acf4f67618e8aec60d1a38d4e24200326d9c247b11afe01f98af716b",
			  "nonce": "1",
			  "gasLimit": "28635",
			  "maxFeePerGas": "1000001500",
			  "maxPriorityFeePerGas": "1000000000",
			  "from": "******************************************",
			  "to": "******************************************",
			  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
			  "v": "01",
			  "r": "71e47922dd719e96e81bd26d05cf0d8ca1578eb9ac4882abdeb7524b1260eda4",
			  "s": "062e6b3d10e438dde79448e8a7af94fc42eb8190127b1eb8d0cf3f4662f746ca",
			  "value": "1",
			  "input": "0x83ec4e1f0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb000000000000000000000000000000000000000000000000000000000000271000000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000",
			  "functionHash": "0x83ec4e1f",
			  "functionName": "buyV3",
			  "decodedInputs": {
				"amountIn": "1",
				"token": "******************************************",
				"to": "******************************************",
				"fee": "10000",
				"uuid": "00000000-0000-0000-0000-000000000000",
				"feePercentage": "100",
				"referralFeePercentage": "0"
			  }
			}
		 */
		every {
			web3jWrapper.sendRawTransactionAsync(
				signedTransaction = "0x02f901b00101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba1301b9014483ec4e1f0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb000000000000000000000000000000000000000000000000000000000000271000000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c001a071e47922dd719e96e81bd26d05cf0d8ca1578eb9ac4882abdeb7524b1260eda4a0062e6b3d10e438dde79448e8a7af94fc42eb8190127b1eb8d0cf3f4662f746ca",
			)
		} just Runs

		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()

		val result = commandBus(
			BuyTokenCommand(
				userId = 1.toUUID(),
				walletId = 1000.toUUID(),
				tokenAddress = AddressWrapper("******************************************"),
				buyForNativeAmount = BigInteger.ONE.asBaseAmount().toNative(Chain.EVM_MAINNET),
				dexPairInfo = GetDex.UniswapV3(
					pairAddress = AddressWrapper("******************************************"),
					fee = UniswapV3Fee.FEE_10000,
				),
			),
		)

		result.txHash shouldBe TxHash("0x8877a208acf4f67618e8aec60d1a38d4e24200326d9c247b11afe01f98af716b")
	}

	@Test
	fun `should categorize token as from UniswapV3, with FEE 10000, and buy it privately, when wallet buy anti mev is turned ON`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 1000.toUUID(), userId = 1.toUUID()) {
			patchSettings(
				customName = null,
				buyAntiMevProtection = true,
				sellAntiMevProtection = null,
			)
		}

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
		)

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = 1000000000.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getWalletBalance(any()) } returns 1000000000000000.toBigInteger()
		every { web3jWrapper.getBlockNumber() } returns 99.toBigInteger()

		/* // some fields left out for clarity. Decoded via https://rawtxdecode.in/
			{
			  "chainId": "1",
			  "type": "EIP-1559",
			  "valid": true,
			  "hash": "0x8f77b3ec6ded8a5e1eee972b6d8b7768afbcf9f9214299a6e1bc3e2fec278ad8",
			  "nonce": "1",
			  "gasLimit": "28635",
			  "maxFeePerGas": "1000001500",
			  "maxPriorityFeePerGas": "1000000000",
			  "from": "******************************************",
			  "to": "******************************************",
			  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
			  "v": "01",
			  "r": "71e47922dd719e96e81bd26d05cf0d8ca1578eb9ac4882abdeb7524b1260eda4",
			  "s": "062e6b3d10e438dde79448e8a7af94fc42eb8190127b1eb8d0cf3f4662f746ca",
			  "value": "1",
			  "input": "0x83ec4e1f0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb000000000000000000000000000000000000000000000000000000000000271000000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000",
			  "functionHash": "0x83ec4e1f",
			  "functionName": "buyV3",
			  "decodedInputs": {
				"amountIn": "1",
				"token": "******************************************",
				"to": "******************************************",
				"fee": "10000",
				"uuid": "00000000-0000-0000-0000-000000000000",
				"feePercentage": "100",
				"referralFeePercentage": "0",
    			"amountOutMin": "117333334"
			  }
			}
		 */
		every {
			beaverbuildConnector.sendPrivateEthTransaction(
				signedTx = SignedTx(
					signedTx = "0x02f901d00101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba1301b901646406df8100000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000006fe5d56000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb0000000000000000000000000000000000000000000000000000000000002710000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c080a0a08fb42f97cb15660ecd07bb276f05ac59bafead0420ab7d839cc63e50a34c36a06bea40436e0893e1656b20ef6a98e0a9c8f29e4c5cab501311c711413b3272ec",
				),
			)
		} just Runs
		every {
			titanbuilderConnector.sendPrivateEthTransaction(
				signedTx = SignedTx(
					signedTx = "0x02f901d00101843b9aca00843b9acfdc826fdb94daed7d2d1287bac5bf53fec60f617def24a7ba1301b901646406df8100000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000006fe5d56000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb0000000000000000000000000000000000000000000000000000000000002710000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c080a0a08fb42f97cb15660ecd07bb276f05ac59bafead0420ab7d839cc63e50a34c36a06bea40436e0893e1656b20ef6a98e0a9c8f29e4c5cab501311c711413b3272ec",
				),
				maxAllowedBlockNumberToInclude = BigInteger.valueOf(101),
			)
		} just Runs

		// Amount out call
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0x55658993000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b00000000000000000000000000000000000000000000000000000000000027100000000000000000000000000000000000000000000000000000000000000001",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000075BCD15"

		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()

		val result = commandBus(
			BuyTokenCommand(
				userId = 1.toUUID(),
				walletId = 1000.toUUID(),
				tokenAddress = AddressWrapper("******************************************"),
				buyForNativeAmount = BigInteger.ONE.asBaseAmount().toNative(Chain.EVM_MAINNET),
				dexPairInfo = GetDex.UniswapV3(
					pairAddress = AddressWrapper("******************************************"),
					fee = UniswapV3Fee.FEE_10000,
				),
			),
		)

		verify(exactly = 0) { web3jWrapper.sendRawTransactionAsync(any()) }

		result.txHash shouldBe TxHash("0x8f77b3ec6ded8a5e1eee972b6d8b7768afbcf9f9214299a6e1bc3e2fec278ad8")
	}

	@Test
	fun `should throw when user using different user wallet`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 2.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
		)

		shouldThrow<WalletNotFoundException> {
			commandBus(
				BuyTokenCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					buyForNativeAmount = BigInteger.ONE.asBaseAmount().toNative(Chain.EVM_MAINNET),
					dexPairInfo = GetDex.UniswapV2(
						pairAddress = AddressWrapper("******************************************"),
					),
				),
			)
		}
	}

	@Test
	fun `should throw when user provided non-solana dex pair to solana buy`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getWallet(
			id = 1000.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper("7EcDhSYGxXyscszYEp35KHN8vvw3svAuLKTzXwCFLtV"),
			chain = Chain.SOLANA,
		)

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("7EcDhSYGxXyscszYEp35KHN8vvw3svAuLKTzXwCFLtV"),
		)

		shouldThrow<InvalidDexPairProvidedException> {
			commandBus(
				BuyTokenCommand(
					userId = 1.toUUID(),
					walletId = 1000.toUUID(),
					tokenAddress = AddressWrapper("7EcDhSYGxXyscszYEp35KHN8vvw3svAuLKTzXwCFLtV"),
					buyForNativeAmount = BigInteger.ONE.asBaseAmount().toNative(Chain.EVM_MAINNET),
					dexPairInfo = GetDex.UniswapV2(
						pairAddress = AddressWrapper("******************************************"),
					),
				),
			)
		}
	}

	@Test
	fun `should throw when user provided non-EVM dex pair to EVM buy`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 1000.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
		)

		shouldThrow<InvalidDexPairProvidedException> {
			commandBus(
				BuyTokenCommand(
					userId = 1.toUUID(),
					walletId = 1000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					buyForNativeAmount = BigInteger.ONE.asBaseAmount().toNative(Chain.EVM_MAINNET),
					dexPairInfo = GetDex.PumpFun(
						pairAddress = AddressWrapper("J4JbUQRaZMxdoQgY6oEHdkPttoLtZ1oKpBThic76pump"),
					),
				),
			)
		}
	}

	@ParameterizedTest
	@ValueSource(longs = [-1, 0])
	fun `should throw when trying to buy zero amount of tokens`(tokensToBuy: Long) {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 1000.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
		)

		shouldThrow<ConstraintViolationException> {
			commandBus(
				BuyTokenCommand(
					userId = 1.toUUID(),
					walletId = 1000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					buyForNativeAmount = BigInteger.valueOf(tokensToBuy).asBaseAmount().toNative(Chain.EVM_MAINNET),
					dexPairInfo = GetDex.UniswapV2(
						pairAddress = AddressWrapper("******************************************"),
					),
				),
			)
		}
	}

	@Test
	fun `should throw because user has insufficient wallet balance to pay for transaction fees`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
		)

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = 1000000000.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getWalletBalance(any()) } returns 400000599999999.toBigInteger()
		every { web3jWrapper.estimateGas(any()) } returns 1000000000.toBigInteger()
		every { web3jWrapper.getNonce(any()) } returns 0.toBigInteger()

		shouldThrow<InsufficientFundsForTransactionFeesException> {
			commandBus(
				BuyTokenCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					buyForNativeAmount = BigInteger.ONE.asBaseAmount().toNative(Chain.EVM_MAINNET),
					dexPairInfo = GetDex.UniswapV2(
						pairAddress = AddressWrapper("******************************************"),
					),
				),
			)
		}
	}

	@Test
	fun `should throw because user has insufficient wallet balance to pay for transaction amount with fees`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
		)

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = 1000000000.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getWalletBalance(any()) } returns 400000600001000.toBigInteger()
		every { web3jWrapper.estimateGas(any()) } returns 250000.toBigInteger()
		every { web3jWrapper.getNonce(any()) } returns 0.toBigInteger()

		shouldThrow<InsufficientFundsForTransactionException> {
			commandBus(
				BuyTokenCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					buyForNativeAmount = BigInteger("1000000000000000").asBaseAmount().toNative(Chain.EVM_MAINNET),
					dexPairInfo = GetDex.UniswapV2(
						pairAddress = AddressWrapper("******************************************"),
					),
				),
			)
		}
	}

	@Test
	fun `should throw when trying to buy token that is missing EvmTokenInfo`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 1.toUUID())

		shouldThrow<TokenNotFoundException> {
			commandBus(
				BuyTokenCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					buyForNativeAmount = NativeAmount(BigDecimal.ONE),
					dexPairInfo = GetDex.UniswapV2(
						pairAddress = AddressWrapper("******************************************"),
					),
				),
			)
		}
	}
}
