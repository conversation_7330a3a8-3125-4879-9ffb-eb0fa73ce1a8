package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.limitorder.query.GetAllUserLimitOrdersQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigInteger

class GetAllUserLimitOrdersQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should return all limit orders for user`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getFirebaseUser(id = 3.toUUID(), email = "<EMAIL>")
		integrationTestHelper.getWallet(id = 4.toUUID(), userId = 3.toUUID())

		integrationTestHelper.getLimitOrder(
			id = 5.toUUID(),
			userId = 1.toUUID(),
			walletId = 2.toUUID(),
			tokenAddress = AddressWrapper("******************************************"),
			limitPrice = BigInteger.valueOf(1000000000000000L),
			initialAmount = BigInteger.valueOf(1000000000000000000L),
			type = LimitOrderType.BUY,
		)

		integrationTestHelper.getLimitOrder(
			id = 6.toUUID(),
			userId = 1.toUUID(),
			walletId = 2.toUUID(),
			tokenAddress = AddressWrapper("******************************************"),
			limitPrice = BigInteger.valueOf(2000000000000000L),
			initialAmount = BigInteger.valueOf(2000000000000000000L),
			type = LimitOrderType.SELL,
		)

		integrationTestHelper.getLimitOrder(
			id = 7.toUUID(),
			userId = 3.toUUID(),
			walletId = 4.toUUID(),
			tokenAddress = AddressWrapper("******************************************"),
			limitPrice = BigInteger.valueOf(3000000000000000L),
			initialAmount = BigInteger.valueOf(3000000000000000000L),
			type = LimitOrderType.BUY,
		)

		// when
		val firstUserResults = queryBus(GetAllUserLimitOrdersQuery(userId = 1.toUUID()))
		val secondUserResults = queryBus(GetAllUserLimitOrdersQuery(userId = 3.toUUID()))

		// then
		firstUserResults shouldHaveSize 2
		secondUserResults shouldHaveSize 1

		firstUserResults.find { it.limitOrderId == 3.toUUID() }?.let {
			it.walletId shouldBe 2.toUUID()
			it.tokenAddress shouldBe AddressWrapper("******************************************")
			it.chain shouldBe Chain.SOLANA
			it.type shouldBe LimitOrderType.BUY
			it.isLocked shouldBe false
		}

		firstUserResults.find { it.limitOrderId == 4.toUUID() }?.let {
			it.walletId shouldBe 2.toUUID()
			it.tokenAddress shouldBe AddressWrapper("******************************************")
			it.chain shouldBe Chain.SOLANA
			it.type shouldBe LimitOrderType.SELL
			it.isLocked shouldBe false
		}

		secondUserResults.find { it.limitOrderId == 5.toUUID() }?.let {
			it.walletId shouldBe 3.toUUID()
			it.tokenAddress shouldBe AddressWrapper("******************************************")
			it.chain shouldBe Chain.SOLANA
			it.type shouldBe LimitOrderType.BUY
			it.isLocked shouldBe false
		}
	}
}
