package com.cleevio.fatbot.application.module.referral

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.referral.query.GetReferralRewardsSummaryQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger

class GetReferralRewardsSummaryQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should calculate rewards correctly when no rewards`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val referredUser = integrationTestHelper.getFirebaseUser(
			id = 2.toUUID(),
			email = "<EMAIL>",
			referredByUserId = user.id,
		)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns BigDecimal("2400.001")
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("230")
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.BNB) } returns BigDecimal("300")

		val result = queryBus(GetReferralRewardsSummaryQuery(userId = user.id))

		result.numberOfReferrals shouldBe 1

		result.referralsOnChains.forEach {
			it.totalAmountNative.amount shouldBeEqualComparingTo BigDecimal.ZERO
			it.totalAmountUsd shouldBeEqualComparingTo BigDecimal.ZERO
			it.unclaimedAmountNative.amount shouldBeEqualComparingTo BigDecimal.ZERO
			it.unclaimedAmountUsd shouldBeEqualComparingTo BigDecimal.ZERO
		}
	}

	@Test
	fun `should count numberOfReferrals correctly`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val firstReferredUser = integrationTestHelper.getFirebaseUser(
			id = 2.toUUID(),
			email = "<EMAIL>",
			referredByUserId = user.id,
		)
		val secondReferredUser = integrationTestHelper.getFirebaseUser(
			id = 3.toUUID(),
			email = "<EMAIL>",
			referredByUserId = user.id,
		)
		val differentUser = integrationTestHelper.getFirebaseUser(
			id = 4.toUUID(),
			email = "<EMAIL>",
			referredByUserId = firstReferredUser.id,
		)
		integrationTestHelper.getReferralReward(
			id = 1.toUUID(),
			userId = user.id,
		)
		integrationTestHelper.getReferralReward(
			id = 2.toUUID(),
			userId = user.id,
			txHash = TxHash("0x43f3fe5a8da9ac9260f2b592cf207176477cfcdc6ce903b415b0a59f2a50afb0"),
			baseAmount = BigInteger("2000000000000"),
		)

		integrationTestHelper.getReferralReward(
			id = 3.toUUID(),
			userId = firstReferredUser.id,
			txHash = TxHash("0x3ed334ccc21e23af2b86221b2d9d7377b8fe27e4d3136de5df06ce4b1d7ca848"),
			baseAmount = BigInteger("3000000000000"),
		)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns BigDecimal("2400.001")
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("230")
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.BNB) } returns BigDecimal("300")

		val result = queryBus(GetReferralRewardsSummaryQuery(userId = user.id))

		result.numberOfReferrals shouldBe 2
	}

	@Test
	fun `should compute referrals correctly across chains`() {
		// given
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val referredUser = integrationTestHelper.getFirebaseUser(
			id = 2.toUUID(),
			email = "<EMAIL>",
			referredByUserId = user.id,
		)

		// and on main net
		// 1 ETH claimed
		integrationTestHelper.getReferralReward(
			userId = user.id,
			chain = Chain.EVM_MAINNET,
			txHash = TxHash("1"),
			baseAmount = BigInteger("1000000000000000000"),
			beforeSave = { claim() },
		)

		// and on base
		// 2 ETH claimed
		integrationTestHelper.getReferralReward(
			userId = user.id,
			chain = Chain.EVM_BASE,
			txHash = TxHash("3"),
			baseAmount = BigInteger("500000000000000000"), // 0.5 ETH
			beforeSave = { claim() },
		)
		integrationTestHelper.getReferralReward(
			userId = user.id,
			chain = Chain.EVM_BASE,
			txHash = TxHash("4"),
			baseAmount = BigInteger("1500000000000000000"), // 1.5 ETH
			beforeSave = { claim() },
		)
		// 0.2 ETH unclaimed
		integrationTestHelper.getReferralReward(
			userId = user.id,
			chain = Chain.EVM_BASE,
			txHash = TxHash("5"),
			baseAmount = BigInteger("100000000000000000"), // 0.1 ETH
		)
		integrationTestHelper.getReferralReward(
			userId = user.id,
			chain = Chain.EVM_BASE,
			txHash = TxHash("6"),
			baseAmount = BigInteger("100000000000000000"), // 0.1 ETH
		)

		// and on solana
		// 1 SOL unclaimed
		integrationTestHelper.getReferralReward(
			userId = user.id,
			chain = Chain.SOLANA,
			txHash = TxHash("7"),
			baseAmount = BigInteger("1000000000"),
		)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns BigDecimal("2000")
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("100")
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.BNB) } returns BigDecimal("300")

		// when
		val result = queryBus(GetReferralRewardsSummaryQuery(userId = user.id))

		// then
		result.numberOfReferrals shouldBe 1
		result.referralsOnChains.size shouldBe 5

		with(result.referralsOnChains.first { it.chain == Chain.EVM_MAINNET }) {
			totalAmountNative shouldBe NativeAmount(BigDecimal("1.000000000000000000"))
			totalAmountUsd shouldBe BigDecimal("2000.000000000000000000")

			unclaimedAmountNative shouldBe NativeAmount(BigDecimal("0.000000000000000000"))
			unclaimedAmountUsd shouldBe BigDecimal("0.000000000000000000")
		}

		with(result.referralsOnChains.first { it.chain == Chain.EVM_BASE }) {
			totalAmountNative shouldBe NativeAmount(BigDecimal("2.200000000000000000"))
			totalAmountUsd shouldBe BigDecimal("4400.000000000000000000")

			unclaimedAmountNative shouldBe NativeAmount(BigDecimal("0.200000000000000000"))
			unclaimedAmountUsd shouldBe BigDecimal("400.000000000000000000")
		}

		with(result.referralsOnChains.first { it.chain == Chain.EVM_BSC }) {
			totalAmountNative shouldBe NativeAmount(BigDecimal("0.000000000000000000"))
			totalAmountUsd shouldBe BigDecimal("0.000000000000000000")

			unclaimedAmountNative shouldBe NativeAmount(BigDecimal("0.000000000000000000"))
			unclaimedAmountUsd shouldBe BigDecimal("0.000000000000000000")
		}

		with(result.referralsOnChains.first { it.chain == Chain.EVM_ARBITRUM_ONE }) {
			totalAmountNative shouldBe NativeAmount(BigDecimal("0.000000000000000000"))
			totalAmountUsd shouldBe BigDecimal("0.000000000000000000")

			unclaimedAmountNative shouldBe NativeAmount(BigDecimal("0.000000000000000000"))
			unclaimedAmountUsd shouldBe BigDecimal("0.000000000000000000")
		}

		with(result.referralsOnChains.first { it.chain == Chain.SOLANA }) {
			totalAmountNative shouldBe NativeAmount(BigDecimal("1.000000000"))
			totalAmountUsd shouldBe BigDecimal("100.000000000")

			unclaimedAmountNative shouldBe NativeAmount(BigDecimal("1.000000000"))
			unclaimedAmountUsd shouldBe BigDecimal("100.000000000")
		}
	}
}
