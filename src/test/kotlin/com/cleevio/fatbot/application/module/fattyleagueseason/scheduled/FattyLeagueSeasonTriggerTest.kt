package com.cleevio.fatbot.application.module.fattyleagueseason.scheduled

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.domain.fattyleagueseason.FattyLeagueSeasonRepository
import com.cleevio.fatbot.domain.userstatistics.UserStatisticsRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.Instant

class FattyLeagueSeasonTriggerTest(
	@Autowired private val underTest: FattyLeagueSeasonTrigger,
	@Autowired private val fattyLeagueSeasonRepository: FattyLeagueSeasonRepository,
	@Autowired private val userStatisticsRepository: UserStatisticsRepository,
) : IntegrationTest() {

	@Test
	fun `should close last season, create new season and reset league donuts for all users`() {
		// Create a test user with statistics
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 1.toUUID(),
			beforeSave = { this.updateDonuts(BigDecimal.valueOf(100)) },
		)

		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")
		integrationTestHelper.getUserStatistics(
			userId = 2.toUUID(),
			beforeSave = { this.updateDonuts(BigDecimal.valueOf(200)) },
		)

		// Create a fatty league season
		val initialSeason = integrationTestHelper.createFattyLeagueSeason(
			id = 1.toUUID(),
			from = Instant.now().minusSeconds(3600), // 1 hour ago
			name = "Fatty League 1",
			seasonNumber = 1,
			claimCooldownPeriodInMinutes = 10L,
			donutsToFattyTokensRatio = BigDecimal.TEN,
			initialReleasePercentage = BigDecimal.TEN,
			remainingUnlockPeriodInMonths = 3L,
			remainingUnlockPeriodParts = 3,
		)

		// Trigger the fatty league season trigger
		underTest.trigger()

		// Verify that the last season was closed
		fattyLeagueSeasonRepository.findAll().first { it.id == initialSeason.id }.closedAt shouldNotBe null

		// Verify that a new season was created
		fattyLeagueSeasonRepository.findAll().first { it.seasonNumber == 2 }.run {
			name shouldBe "Fatty League 2"
			claimCooldownPeriodInMinutes shouldBe 10L
			donutsToFattyTokensRatio shouldBe 0.5.toBigDecimal()
			initialReleasePercentage shouldBe BigDecimal.TEN
			remainingUnlockPeriodInMonths shouldBe 3L
			remainingUnlockPeriodParts shouldBe 3L
			closedAt.shouldBeNull()
		}

		// Verify that league donuts were reset for all users
		userStatisticsRepository.findByUserId(1.toUUID())!!.leagueDonuts shouldBeEqualComparingTo BigDecimal.ZERO
		userStatisticsRepository.findByUserId(2.toUUID())!!.leagueDonuts shouldBeEqualComparingTo BigDecimal.ZERO
	}
}
