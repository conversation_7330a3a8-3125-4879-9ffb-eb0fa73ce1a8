package com.cleevio.fatbot.application.module.userfattyleagueseason

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.userfattyleagueseason.command.ClaimUserFattyTokensCommand
import com.cleevio.fatbot.application.module.userfattyleagueseason.exception.UserFattyTokensNotClaimableException
import com.cleevio.fatbot.domain.userfattyleagueseason.UserFattyLeagueSeasonRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.temporal.ChronoUnit
import kotlin.test.Test

class ClaimFattyTokensCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val integrationTestClock: IntegrationTestClock,
	@Autowired private val userFattyLeagueSeasonRepository: UserFattyLeagueSeasonRepository,
) : IntegrationTest() {

	@Test
	fun `should make initial claim fatty tokens`() {
		// given
		val now = integrationTestClock.currentTime()
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		// Create two fatty league seasons
		integrationTestHelper.createFattyLeagueSeason(
			id = 1.toUUID(),
			from = now.minus(2, ChronoUnit.DAYS),
			name = "Fatty League 1",
			seasonNumber = 1,
			claimCooldownPeriodInMinutes = 10L,
			donutsToFattyTokensRatio = BigDecimal.TEN,
			initialReleasePercentage = BigDecimal.TEN,
			remainingUnlockPeriodInMonths = 3L,
			remainingUnlockPeriodParts = 3,
			beforeSave = { closeSeason(now) }, // open season is not claimable
		)

		// Create user fatty league seasons
		integrationTestHelper.createUserFattyLeagueSeason(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyLeagueSeasonId = 1.toUUID(),
			totalTokens = 1000.0.toBigDecimal(),
			lastClaimedAt = null,
		)

		commandBus(ClaimUserFattyTokensCommand(userId = 1.toUUID(), userFattyLeagueId = 1.toUUID()))

		userFattyLeagueSeasonRepository.findAll().first().run {
			totalTokens shouldBeEqualComparingTo 1000.toBigDecimal()
			claimedTokens shouldBeEqualComparingTo 100.toBigDecimal() // 10% from 100
			lastClaimedAt shouldNotBe null
		}
	}

	@Test
	fun `should be able to claim 24h after initial claim`() {
		// given
		val now = integrationTestClock.currentTime()
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		// Create two fatty league seasons
		integrationTestHelper.createFattyLeagueSeason(
			id = 1.toUUID(),
			from = now.minus(2, ChronoUnit.DAYS),
			name = "Fatty League 1",
			seasonNumber = 1,
			claimCooldownPeriodInMinutes = 10L,
			donutsToFattyTokensRatio = BigDecimal.TEN,
			initialReleasePercentage = BigDecimal.TEN,
			remainingUnlockPeriodInMonths = 3L,
			remainingUnlockPeriodParts = 3,
			beforeSave = { closeSeason(now) },
		)

		// Create user fatty league seasons
		integrationTestHelper.createUserFattyLeagueSeason(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyLeagueSeasonId = 1.toUUID(),
			totalTokens = 1000.0.toBigDecimal(),
			lastClaimedAt = null,
		)
		// check initial claim
		commandBus(ClaimUserFattyTokensCommand(userId = 1.toUUID(), userFattyLeagueId = 1.toUUID()))
		userFattyLeagueSeasonRepository.findAll().single().run {
			lastClaimedAt shouldBe now

			totalTokens shouldBeEqualComparingTo 1000.toBigDecimal()
			claimedTokens shouldBeEqualComparingTo 100.toBigDecimal()
		}

		integrationTestClock.advanceBy(1, ChronoUnit.DAYS)

		commandBus(ClaimUserFattyTokensCommand(userId = 1.toUUID(), userFattyLeagueId = 1.toUUID()))

		// claim is made to 1 period
		userFattyLeagueSeasonRepository.findAll().first().run {
			totalTokens shouldBeEqualComparingTo 1000.toBigDecimal()
			claimedTokens shouldBeEqualComparingTo 109.7826087520.toBigDecimal() // initial claim + 24h
			this.getRemainingTokens() shouldBeEqualComparingTo 890.2173912480.toBigDecimal()
			lastClaimedAt shouldNotBe null
		}
	}

	@Test
	fun `should be able to claim all tokens with initial claim`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		// Create two fatty league seasons
		integrationTestHelper.createFattyLeagueSeason(
			id = 1.toUUID(),
			from = integrationTestClock.currentTime().minus(2, ChronoUnit.DAYS),
			name = "Fatty League 1",
			seasonNumber = 1,
			claimCooldownPeriodInMinutes = 10L,
			donutsToFattyTokensRatio = BigDecimal.TEN,
			initialReleasePercentage = BigDecimal.TEN,
			remainingUnlockPeriodInMonths = 3L,
			remainingUnlockPeriodParts = 3,
			beforeSave = { closeSeason(integrationTestClock.currentTime()) },
		)

		// Create user fatty league seasons
		integrationTestHelper.createUserFattyLeagueSeason(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyLeagueSeasonId = 1.toUUID(),
			totalTokens = 1000.0.toBigDecimal(),
		)
		// initial claim
		commandBus(ClaimUserFattyTokensCommand(userId = 1.toUUID(), userFattyLeagueId = 1.toUUID()))

		integrationTestClock.advanceBy(3, ChronoUnit.MONTHS) // all tokens released in 3 months

		commandBus(ClaimUserFattyTokensCommand(userId = 1.toUUID(), userFattyLeagueId = 1.toUUID()))

		// claim is made to initial tokens + 3 months
		userFattyLeagueSeasonRepository.findAll().first().run {
			totalTokens shouldBeEqualComparingTo 1000.toBigDecimal()
			claimedTokens shouldBeEqualComparingTo 1000.toBigDecimal()
			this.getRemainingTokens() shouldBeEqualComparingTo 0.toBigDecimal()
			lastClaimedAt shouldNotBe null
		}
	}

	@Test
	fun `should not be able to claim in cooldown period`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		// Create two fatty league seasons
		val fattyLeague = integrationTestHelper.createFattyLeagueSeason(
			id = 1.toUUID(),
			from = integrationTestClock.currentTime().minus(2, ChronoUnit.DAYS),
			name = "Fatty League 1",
			seasonNumber = 1,
			claimCooldownPeriodInMinutes = 10L,
			donutsToFattyTokensRatio = BigDecimal.TEN,
			initialReleasePercentage = BigDecimal.TEN,
			remainingUnlockPeriodInMonths = 3L,
			remainingUnlockPeriodParts = 3,
			beforeSave = { closeSeason(integrationTestClock.currentTime()) },
		)

		// Create user fatty league seasons
		integrationTestHelper.createUserFattyLeagueSeason(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyLeagueSeasonId = 1.toUUID(),
			totalTokens = 1000.0.toBigDecimal(),
			lastClaimedAt = null,
			beforeSave = {
				val claimableTokens = fattyLeague.computeClaimableTokens(
					lastClaimedAt = null,
					totalTokens = totalTokens,
					remainingTokens = getRemainingTokens(),
					now = integrationTestClock.currentTime(),
				)

				claimTokens(integrationTestClock.currentTime(), claimableTokens)
			},
		)

		shouldThrow<UserFattyTokensNotClaimableException> {
			commandBus(ClaimUserFattyTokensCommand(userId = 1.toUUID(), userFattyLeagueId = 1.toUUID()))
		}
	}

	@Test
	fun `should not be able to claim when already claimed in current period`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		// Create two fatty league seasons
		integrationTestHelper.createFattyLeagueSeason(
			id = 1.toUUID(),
			from = integrationTestClock.currentTime().minus(2, ChronoUnit.DAYS),
			name = "Fatty League 1",
			seasonNumber = 1,
			claimCooldownPeriodInMinutes = 10L,
			donutsToFattyTokensRatio = BigDecimal.TEN,
			initialReleasePercentage = BigDecimal.TEN,
			remainingUnlockPeriodInMonths = 3L,
			remainingUnlockPeriodParts = 3,
			beforeSave = { closeSeason(integrationTestClock.currentTime()) },
		)

		// Create user fatty league seasons
		integrationTestHelper.createUserFattyLeagueSeason(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyLeagueSeasonId = 1.toUUID(),
			totalTokens = 1000.0.toBigDecimal(),
		)

		integrationTestClock.advanceBy(1, ChronoUnit.MONTHS)
		integrationTestClock.advanceBy(1, ChronoUnit.DAYS)
		// is ok
		commandBus(ClaimUserFattyTokensCommand(userId = 1.toUUID(), userFattyLeagueId = 1.toUUID()))

		// new claimable period is not yet opened
		shouldThrow<UserFattyTokensNotClaimableException> {
			commandBus(ClaimUserFattyTokensCommand(userId = 1.toUUID(), userFattyLeagueId = 1.toUUID()))
		}
	}
}
