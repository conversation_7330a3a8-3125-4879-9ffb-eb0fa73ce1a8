package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.query.GetAllZeroFeeTokensQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.net.URI

class GetAllZeroFeeTokensQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should get all zero fee tokens in exact order`() {
		val result = queryBus(GetAllZeroFeeTokensQuery())

		result.map { it.tokenAddress } shouldContainExactly listOf(
			AddressWrapper("******************************************"),
			AddressWrapper("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"),
			AddressWrapper("******************************************"),
			AddressWrapper("******************************************"),
			AddressWrapper("******************************************"),
			AddressWrapper("******************************************"),
		)
	}

	@Test
	fun `should get zero fee tokens when some have evm token info`() {
		val catchTokenImage = integrationTestHelper.getFile(id = 1.toUUID(), extension = "png")
		integrationTestHelper.getEvmTokenInfo(
			id = 1.toUUID(),
			chain = Chain.EVM_ARBITRUM_ONE,
			tokenAddress = AddressWrapper("******************************************"),
			name = "Space catch",
			symbol = "CATCH",
			imageFileId = catchTokenImage.id,
		)

		integrationTestHelper.getEvmTokenInfo(
			id = 2.toUUID(),
			chain = Chain.SOLANA,
			tokenAddress = AddressWrapper("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"),
			name = "USDC",
			symbol = "USDC",
			imageFileId = null,
		)

		val result = queryBus(GetAllZeroFeeTokensQuery())

		val catchToken = result
			.first { it.tokenAddress == AddressWrapper("******************************************") }

		with(catchToken) {
			chain shouldBe Chain.EVM_ARBITRUM_ONE
			imageUrl shouldBe URI.create("http://localhost:8080/${catchTokenImage.id}.png")
			tokenName shouldBe "Space catch"
			tokenSymbol shouldBe "CATCH"
			tokenDetailUrl shouldBe URI.create("https://arbiscan.io/address/0xbC4c97Fb9befaa8B41448e1dFcC5236dA543217F")
		}

		val usdcSolana = result
			.first { it.tokenAddress == AddressWrapper("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v") }

		with(usdcSolana) {
			chain shouldBe Chain.SOLANA
			imageUrl shouldBe null
			tokenName shouldBe "USDC"
			tokenSymbol shouldBe "USDC"
			tokenDetailUrl shouldBe URI.create("https://solscan.io/address/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")
		}
	}
}
