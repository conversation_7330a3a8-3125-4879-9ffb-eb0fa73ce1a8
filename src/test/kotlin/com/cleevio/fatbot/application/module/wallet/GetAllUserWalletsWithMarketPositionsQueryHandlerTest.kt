package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.query.GetAllUserWalletsQuery
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.maps.shouldContainKey
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger

class GetAllUserWalletsWithMarketPositionsQuesryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@BeforeEach
	fun setup() {
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns BigDecimal.valueOf(3000)
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal.valueOf(200)
		every { web3jWrapper.getWalletBalance(any()) } returns BigInteger.ZERO
	}

	@Test
	fun `should get all user wallets`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID(), selectedChains = setOf(Chain.SOLANA))
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getWallet(
			id = 20.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		// and unrelated wallet
		integrationTestHelper.getWallet(
			id = 30.toUUID(),
			userId = 2.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("1.5")

		// when
		val result = queryBus(GetAllUserWalletsQuery(userId = 1.toUUID(), useSelectedChains = false))

		// then
		result.size shouldBe 2

		val walletIdToResult = result.associateBy { it.walletId }

		walletIdToResult shouldContainKey 10.toUUID()
		walletIdToResult shouldContainKey 20.toUUID()

		walletIdToResult.getValue(10.toUUID()).run {
			walletAddress shouldBe AddressWrapper(addressString = "******************************************")
			customName shouldBe "ETH wallet 1"
			chain shouldBe Chain.EVM_MAINNET
			currentPortfolioValueUsd shouldBeEqualComparingTo BigDecimal.ZERO
			currentPortfolioValueChangeUsd shouldBeEqualComparingTo BigDecimal.ZERO
			currentPortfolioValueChangeFraction shouldBeEqualComparingTo BigDecimal.ZERO
		}

		walletIdToResult.getValue(20.toUUID()).run {
			walletAddress shouldBe AddressWrapper(addressString = "******************************************")
			customName shouldBe "ETH wallet 1"
			chain shouldBe Chain.EVM_MAINNET
			currentPortfolioValueUsd shouldBeEqualComparingTo BigDecimal.ZERO
			currentPortfolioValueChangeUsd shouldBeEqualComparingTo BigDecimal.ZERO
			currentPortfolioValueChangeFraction shouldBeEqualComparingTo BigDecimal.ZERO
		}
	}

	@Test
	fun `should get user wallets on selected chains`() {
		// given
		integrationTestHelper.getFirebaseUser(
			id = 1.toUUID(),
			selectedChains = setOf(Chain.EVM_MAINNET, Chain.EVM_ARBITRUM_ONE),
		)
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getWallet(
			id = 20.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getWallet(
			id = 30.toUUID(),
			userId = 1.toUUID(),
			address = "ccc".toAddress(),
			chain = Chain.EVM_BSC,
		)
		integrationTestHelper.getWallet(
			id = 40.toUUID(),
			userId = 1.toUUID(),
			address = "ddd".toAddress(),
			chain = Chain.EVM_ARBITRUM_ONE,
			beforeSave = { patchSettings(customName = "arb", null, null) },
		)

		// and unrelated wallet
		integrationTestHelper.getWallet(
			id = 50.toUUID(),
			userId = 2.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
			chain = Chain.EVM_MAINNET,
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("1.5")

		// when
		val result = queryBus(GetAllUserWalletsQuery(userId = 1.toUUID(), useSelectedChains = true))

		// then
		result.size shouldBe 3

		val walletIdToResult = result.associateBy { it.walletId }

		walletIdToResult shouldContainKey 10.toUUID()
		walletIdToResult shouldContainKey 20.toUUID()
		walletIdToResult shouldContainKey 40.toUUID()

		walletIdToResult.getValue(10.toUUID()).run {
			walletAddress shouldBe AddressWrapper(addressString = "******************************************")
			customName shouldBe "ETH wallet 1"
			chain shouldBe Chain.EVM_MAINNET
			currentPortfolioValueUsd shouldBeEqualComparingTo BigDecimal.ZERO
			currentPortfolioValueChangeUsd shouldBeEqualComparingTo BigDecimal.ZERO
			currentPortfolioValueChangeFraction shouldBeEqualComparingTo BigDecimal.ZERO
		}

		walletIdToResult.getValue(20.toUUID()).run {
			walletAddress shouldBe AddressWrapper(addressString = "******************************************")
			customName shouldBe "ETH wallet 1"
			chain shouldBe Chain.EVM_MAINNET
			currentPortfolioValueUsd shouldBeEqualComparingTo BigDecimal.ZERO
			currentPortfolioValueChangeUsd shouldBeEqualComparingTo BigDecimal.ZERO
			currentPortfolioValueChangeFraction shouldBeEqualComparingTo BigDecimal.ZERO
		}

		walletIdToResult.getValue(40.toUUID()).run {
			walletAddress shouldBe "ddd".toAddress()
			customName shouldBe "arb"
			chain shouldBe Chain.EVM_ARBITRUM_ONE
			currentPortfolioValueUsd shouldBeEqualComparingTo BigDecimal.ZERO
			currentPortfolioValueChangeUsd shouldBeEqualComparingTo BigDecimal.ZERO
			currentPortfolioValueChangeFraction shouldBeEqualComparingTo BigDecimal.ZERO
		}
	}

	@Test
	fun `should get all user wallets with market positions calculated`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		// and two tokens
		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = "111".toAddress(),
			tokenDecimals = BigInteger("18"),
		)
		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = "222".toAddress(),
			tokenDecimals = BigInteger("9"),
		)

		// and one user solana Wallet without market positions
		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH"),
			chain = Chain.SOLANA,
		)

		// and one user EVM wallet with one market position
		integrationTestHelper.getWallet(
			id = 20.toUUID(),
			userId = 1.toUUID(),
			address = "aaa".toAddress(),
			chain = Chain.EVM_MAINNET,
			walletCount = 0,
		)

		integrationTestHelper.getMarketPosition(
			walletId = 20.toUUID(),
			chain = Chain.EVM_MAINNET,
			tokenAddress = "111".toAddress(),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = BigInteger("1000000000000000000"), // 18x 0
					amountOfBaseCurrencyPaid = BigInteger("1000000000000000000"), // 18x 0 -> 1ETH
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)

		integrationTestHelper.getWalletCurrencyPosition(
			walletId = 20.toUUID(),
			totalBought = BigInteger("1000000000000000000"),
			totalSold = BigInteger.ZERO,
			totalAcquisitionCostUsd = BigDecimal("3000"),
		)

		// and another user EVM wallet with two market positions and 2 ETH on wallet
		integrationTestHelper.getWallet(
			id = 30.toUUID(),
			userId = 1.toUUID(),
			address = "bbb".toAddress(),
			chain = Chain.EVM_MAINNET,
			walletCount = 1,
		)
		integrationTestHelper.getMarketPosition(
			walletId = 30.toUUID(),
			chain = Chain.EVM_MAINNET,
			tokenAddress = "111".toAddress(),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = BigInteger("1000000000000000000"), // 18x 0
					amountOfBaseCurrencyPaid = BigInteger("1000000000000000000"), // 18x 0 -> 1ETH
					exchangeRate = BigDecimal("1.5"),
				)
				newSell(
					amountOfTokensSold = BigInteger("500000000000000000"),
					amountOfBaseCurrencyReceived = BigInteger("500000000000000000"),
				) // half of the tokens sold
			},
		)
		integrationTestHelper.getMarketPosition(
			walletId = 30.toUUID(),
			chain = Chain.EVM_MAINNET,
			tokenAddress = "222".toAddress(),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = BigInteger("1000000000"), // 9x 0
					amountOfBaseCurrencyPaid = BigInteger("1000000000000000000"), // 18x 0 -> 1ETH
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)
		integrationTestHelper.getWalletCurrencyPosition(
			walletId = 30.toUUID(),
			totalBought = BigInteger("2000000000000000000"),
			totalSold = BigInteger.ZERO,
			totalAcquisitionCostUsd = BigDecimal("6000"),
		)

		// and rest of mocks
		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.ONE
		every { web3jWrapper.getWeb3j() } returns web3j

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************","******************************************"]
				any(),
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"00000000000000000000000000000000000000000000000000000000000000a0" +
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"0000000000000000000000001110000000000000000000000000000000000000" + // first token address
			"0000000000000000000000002220000000000000000000000000000000000000" + // second token address
			"0000000000000000000000000000000000000000000000000000000000000002" +
			"0000000000000000000000000000000000000000000000000f43fc2c04ee0000" + // 1.1ETH in Wei, first token price
			"0000000000000000000000000000000000000000000000000f43fc2c04ee0000" // 1.1ETH in Wei, second token price

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("1.5")

		// when
		val result = queryBus(GetAllUserWalletsQuery(userId = 1.toUUID(), useSelectedChains = true))

		// then
		result.size shouldBe 3

		val walletIdToResult = result.associateBy { it.walletId }

		walletIdToResult shouldContainKey 10.toUUID()
		walletIdToResult shouldContainKey 20.toUUID()
		walletIdToResult shouldContainKey 30.toUUID()

		walletIdToResult.getValue(10.toUUID()).run {
			walletAddress shouldBe AddressWrapper(addressString = "HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH")
			customName shouldBe "SOL wallet 1"
			chain shouldBe Chain.SOLANA
			currentPortfolioValueUsd shouldBeEqualComparingTo BigDecimal.ZERO
			currentPortfolioValueChangeUsd shouldBeEqualComparingTo BigDecimal.ZERO
			currentPortfolioValueChangeFraction shouldBeEqualComparingTo BigDecimal.ZERO
		}

		walletIdToResult.getValue(20.toUUID()).run {
			walletAddress shouldBe "aaa".toAddress()
			customName shouldBe "ETH wallet 1"
			chain shouldBe Chain.EVM_MAINNET
			currentPortfolioValueUsd shouldBeEqualComparingTo BigDecimal("3.150")
			currentPortfolioValueChangeUsd shouldBeEqualComparingTo BigDecimal("-2998.35")
			currentPortfolioValueChangeFraction shouldBeEqualComparingTo BigDecimal("-0.998950524737631184407796101949025487")
		}

		walletIdToResult.getValue(30.toUUID()).run {
			walletAddress shouldBe "bbb".toAddress()
			customName shouldBe "ETH wallet 2"
			chain shouldBe Chain.EVM_MAINNET
			currentPortfolioValueUsd shouldBeEqualComparingTo BigDecimal("5.475")
			currentPortfolioValueChangeUsd shouldBeEqualComparingTo BigDecimal("-5996.775")
			currentPortfolioValueChangeFraction shouldBeEqualComparingTo BigDecimal("-0.9990878420592277895789079095339247782")
		}
	}
}
