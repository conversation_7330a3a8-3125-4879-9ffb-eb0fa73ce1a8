package com.cleevio.fatbot.application.module.userfattycard

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.userfattycard.command.DisplayUserFattyCardsCommand
import com.cleevio.fatbot.domain.userfattycard.UserFattyCardRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class DisplayUserFattyCardsCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val userFattyCardRepository: UserFattyCardRepository,
) : IntegrationTest() {

	@Test
	fun `should display multiple user fatty cards`() {
		// Given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 3.toUUID())
		integrationTestHelper.getFattyCard(
			id = 5.toUUID(),
			donutReward = BigDecimal.valueOf(100),
			avatarFileId = 3.toUUID(),
		)
		integrationTestHelper.getFattyCard(
			id = 6.toUUID(),
			donutReward = BigDecimal.valueOf(200),
			avatarFileId = 3.toUUID(),
		)
		// userFattyCard1
		integrationTestHelper.getUserFattyCard(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 5.toUUID(),
		)
		// userFattyCard2
		integrationTestHelper.getUserFattyCard(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 6.toUUID(),
		)

		commandBus(
			DisplayUserFattyCardsCommand(
				userId = 1.toUUID(),
				userFattyCardIds = setOf(1.toUUID(), 2.toUUID()),
			),
		)

		userFattyCardRepository.findById(1.toUUID()).get().isDisplayed shouldBe true
		userFattyCardRepository.findById(2.toUUID()).get().isDisplayed shouldBe true
	}

	@Test
	fun `should only display cards belonging to the user`() {
		// Given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")
		integrationTestHelper.getFile(id = 3.toUUID())
		integrationTestHelper.getFattyCard(
			id = 5.toUUID(),
			donutReward = BigDecimal.valueOf(100),
			avatarFileId = 3.toUUID(),
		)

		// Card for user 1
		integrationTestHelper.getUserFattyCard(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 5.toUUID(),
		)
		// Card for user 2
		integrationTestHelper.getUserFattyCard(
			id = 2.toUUID(),
			userId = 2.toUUID(),
			fattyCardId = 5.toUUID(),
		)

		commandBus(
			DisplayUserFattyCardsCommand(
				userId = 1.toUUID(),
				userFattyCardIds = setOf(1.toUUID(), 2.toUUID()), // Try to display both cards
			),
		)

		userFattyCardRepository.findById(1.toUUID()).get().isDisplayed shouldBe true
		userFattyCardRepository.findById(2.toUUID()).get().isDisplayed shouldBe false
	}
}
