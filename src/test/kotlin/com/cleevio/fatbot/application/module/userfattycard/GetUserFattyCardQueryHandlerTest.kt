package com.cleevio.fatbot.application.module.userfattycard

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.userfattycard.exceptions.UserFattyCardNotFoundException
import com.cleevio.fatbot.application.module.userfattycard.query.GetUserFattyCardQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class GetUserFattyCardQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should get user fatty card details`() {
		// Given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 3.toUUID())
		integrationTestHelper.getFattyCard(
			id = 5.toUUID(),
			rarity = "RARE",
			probability = BigDecimal.valueOf(30),
			donutReward = BigDecimal.valueOf(200),
			avatarFileId = 3.toUUID(),
		)
		integrationTestHelper.getUserFattyCard(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 5.toUUID(),
		)

		// When
		val result = queryBus(
			GetUserFattyCardQuery(
				userId = 1.toUUID(),
				userFattyCardId = 1.toUUID(),
			),
		)

		// Then
		result.userFattyCardId shouldBe 1.toUUID()
		result.rarity shouldBe "RARE"
		result.probability shouldBeEqualComparingTo BigDecimal.valueOf(30)
		result.donutReward shouldBeEqualComparingTo BigDecimal.valueOf(200)
	}

	@Test
	fun `should throw exception when user fatty card belongs to different user`() {
		// Given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")
		integrationTestHelper.getFile(id = 3.toUUID())
		integrationTestHelper.getFattyCard(
			id = 5.toUUID(),
			donutReward = BigDecimal.valueOf(100),
			avatarFileId = 3.toUUID(),
		)
		integrationTestHelper.getUserFattyCard(
			id = 1.toUUID(),
			userId = 2.toUUID(), // Card belongs to user 2
			fattyCardId = 5.toUUID(),
		)

		assertThrows<UserFattyCardNotFoundException> {
			queryBus(
				GetUserFattyCardQuery(
					userId = 1.toUUID(), // Trying to get card as user 1
					userFattyCardId = 1.toUUID(),
				),
			)
		}
	}
}
