package com.cleevio.fatbot.application.module.tokenaudit

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.tokenaudit.command.PerformTokenAuditCommand
import com.cleevio.fatbot.application.module.tokenaudit.port.out.PerformTokenAudit
import com.cleevio.fatbot.application.module.tokenaudit.service.TokenAuditFinderService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.tokenaudit.TokenAuditResult
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class PerformTokenAuditCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val tokenAuditFinderService: TokenAuditFinderService,
	@Autowired private val clock: IntegrationTestClock,
	@Autowired private val performTokenAudit: PerformTokenAudit,
) : IntegrationTest() {

	@Test
	fun `should save audit result`() {
		// given
		integrationTestHelper.getTokenAudit(
			tokenAddress = AddressWrapper("******************************************"),
			chain = Chain.EVM_MAINNET,
			entityModifier = { it.markNewProcessing(clock.currentTime()) },
		)

		every {
			etherscanConnector.getSourceCode(any(), any())
		} returns EtherscanConnector.GetSourceCodeResponse(
			result = listOf(
				EtherscanConnector.GetSourceCodeResultResponse(
					sourceCode = "sourceCode",
				),
			),
		)

		every {
			openAIConnector.responses(any(), PerformTokenAudit.Result::class.java)
		} returns PerformTokenAudit.Result(
			issues = listOf(
				PerformTokenAudit.AuditIssue(
					summary = "This token bad",
					detail = "This token bad because reason",
					severity = PerformTokenAudit.IssueSeverity.HIGH,
				),
			),
			riskFactor = PerformTokenAudit.RiskFactor.RED,
			riskFactorReason = "Trust me bro",
		)

		// when
		commandBus(
			PerformTokenAuditCommand(
				tokenAddress = AddressWrapper("******************************************")
					.toChainAddress(Chain.EVM_MAINNET),
			),
		)

		// then
		val audits = tokenAuditFinderService.findAll()
		audits.size shouldBe 1
		with(audits.first()) {
			tokenAddress shouldBe AddressWrapper("******************************************")
			chain shouldBe Chain.EVM_MAINNET
			auditResult shouldBe TokenAuditResult(
				issues = listOf(
					TokenAuditResult.TokenAuditIssue(
						summary = "This token bad",
						detail = "This token bad because reason",
						severity = PerformTokenAudit.IssueSeverity.HIGH,
					),
				),
				riskFactor = PerformTokenAudit.RiskFactor.RED,
				riskFactorReason = "Trust me bro",
			)
		}
	}
}
