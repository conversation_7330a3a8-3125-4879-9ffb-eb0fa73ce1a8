package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.user.query.IsUniqueReferralCodeQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class IsUniqueReferralCodeQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should return false if no one has same referral code yet`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID(), referralCode = "some_random_code")

		queryBus(IsUniqueReferralCodeQuery("CLEEVIO_X")).isUnique shouldBe true
	}

	@Test
	fun `should return true if other user has same referral code`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID(), referralCode = "CLEEVIO_X")

		queryBus(IsUniqueReferralCodeQuery("CLEEVIO_X")).isUnique shouldBe false
		queryBus(IsUniqueReferralCodeQuery("cleevio_x")).isUnique shouldBe false
		queryBus(IsUniqueReferralCodeQuery("clEEvIo_X")).isUnique shouldBe false
	}
}
