package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.user.query.GetUserQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class GetUserQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should get user`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>", referralCode = "CODE")

		val result1 = queryBus(GetUserQuery(userId = 1.toUUID()))
		result1.userId shouldBe 1.toUUID()
		result1.email shouldBe "<EMAIL>"
		result1.referralCode shouldBe null
		result1.quickBuyAmountUsd shouldBe BigDecimal.TEN
		result1.selectedChains shouldContainExactlyInAnyOrder setOf(
			Chain.EVM_MAINNET,
			Chain.EVM_BASE,
			Chain.EVM_BSC,
			Chain.EVM_ARBITRUM_ONE,
			Chain.SOLANA,
		)

		val result2 = queryBus(GetUserQuery(userId = 2.toUUID()))
		result2.userId shouldBe 2.toUUID()
		result2.email shouldBe "<EMAIL>"
		result2.referralCode shouldBe "code"
		result2.quickBuyAmountUsd shouldBe BigDecimal.TEN
		result2.selectedChains shouldContainExactlyInAnyOrder setOf(
			Chain.EVM_MAINNET,
			Chain.EVM_BASE,
			Chain.EVM_BSC,
			Chain.EVM_ARBITRUM_ONE,
			Chain.SOLANA,
		)
	}

	@Test
	fun `should mark user as referred`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(
			id = 2.toUUID(),
			email = "<EMAIL>",
			referralCode = "CODE",
			referredByUserId = 1.toUUID(),
		)

		val result1 = queryBus(GetUserQuery(userId = 1.toUUID()))
		result1.isReferred shouldBe false

		val result2 = queryBus(GetUserQuery(userId = 2.toUUID()))
		result2.isReferred shouldBe true
	}
}
