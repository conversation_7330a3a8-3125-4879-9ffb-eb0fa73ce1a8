package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.evm.model.FeesPerGas
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.command.TransferCurrencyCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.exception.InsufficientFundsForTransactionException
import com.cleevio.fatbot.application.module.wallet.exception.InsufficientFundsForTransactionFeesException
import com.cleevio.fatbot.application.module.wallet.exception.WalletNotFoundException
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldNotThrowAny
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

@Suppress("ktlint:standard:max-line-length")
class TransferCurrencyCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
) : IntegrationTest() {

	@Test
	fun `should transfer ETH from one address to another`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 1000.toUUID(), userId = 1.toUUID())

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = **********.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getWalletBalance(any()) } returns **********000000.toBigInteger()

		every {
			web3jWrapper.sendRawTransaction(
				signedTransaction = "0x02f86a0101843b9aca00843b9acfdc826fdb941d5cb2e5355858fddbad1f595581b5aa07902c260180c080a0fac29b9c48c38160d80d9c6c70c9780b413d009bcb82538f825d9ffd118387a8a0535ea9a158cd3e9f8688db805496ac3acc92b0be36e3690d2686f9406d8a170c",
			)
		} just Runs

		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()

		val result = commandBus(
			TransferCurrencyCommand(
				userId = 1.toUUID(),
				walletId = 1000.toUUID(),
				destinationWalletAddress = AddressWrapper("******************************************"),
				toTransferNativeAmount = NativeAmount(BigDecimal("0.000000000000000001")),
				password = "password",
			),
		)

		result.txHash shouldBe TxHash("0x0ef4583177114b2abcaf0a8bd29f574d47b3d023bbbd67f8e190f18700a4b438")
	}

	@Test
	fun `should throw when user using different user wallet`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 2.toUUID())

		shouldThrow<WalletNotFoundException> {
			commandBus(
				TransferCurrencyCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					destinationWalletAddress = AddressWrapper("******************************************"),
					toTransferNativeAmount = NativeAmount(BigDecimal("0.000000000000000001")),
					password = "password",
				),
			)
		}
	}

	@Test
	@Disabled("Currently broken, FIX ME!")
	fun `should throw when user using non-EVM wallet`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getWallet(
			id = 1000.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper("7EcDhSYGxXyscszYEp35KHN8vvw3svAuLKTzXwCFLtV"),
			chain = Chain.SOLANA,
		)

		shouldNotThrowAny {
			commandBus(
				TransferCurrencyCommand(
					userId = 1.toUUID(),
					walletId = 1000.toUUID(),
					destinationWalletAddress = AddressWrapper("******************************************"),
					toTransferNativeAmount = NativeAmount(BigDecimal("0.000000000000000001")),
					password = "password",
				),
			)
		}
	}

	@Test
	@Disabled("Currently broken, FIX ME!")
	fun `should not throw when using solana address as token address`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 1000.toUUID(),
			userId = 1.toUUID(),
			chain = Chain.SOLANA,
		)

		shouldNotThrowAny {
			commandBus(
				TransferCurrencyCommand(
					userId = 1.toUUID(),
					walletId = 1000.toUUID(),
					destinationWalletAddress = AddressWrapper("DpNXPNWvWoHaZ9P3WtfGCb2ZdLihW8VW1w1Ph4KDH9iG"),
					toTransferNativeAmount = NativeAmount(BigDecimal("0.000000000000000001")),
					password = "password",
				),
			)
		}
	}

	@ParameterizedTest
	@ValueSource(longs = [-1, 0])
	fun `should throw when trying to transfer zero amount of ETH`(ethToTransfer: Long) {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 1000.toUUID(), userId = 1.toUUID())

		val toTransfer = ethToTransfer.toBigInteger().asBaseAmount().toNative(Chain.EVM_MAINNET)
		shouldThrow<ConstraintViolationException> {
			commandBus(
				TransferCurrencyCommand(
					userId = 1.toUUID(),
					walletId = 1000.toUUID(),
					destinationWalletAddress = AddressWrapper("******************************************"),
					toTransferNativeAmount = toTransfer,
					password = "password",
				),
			)
		}
	}

	@Test
	fun `should throw because user has insufficient wallet balance to pay for transaction fees`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 1.toUUID())

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = **********.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getWalletBalance(any()) } returns 21000031499999.toBigInteger()
		every { web3jWrapper.estimateGas(any()) } returns 1909000000.toBigInteger()
		every { web3jWrapper.getNonce(any()) } returns 0.toBigInteger()

		shouldThrow<InsufficientFundsForTransactionFeesException> {
			commandBus(
				TransferCurrencyCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					destinationWalletAddress = AddressWrapper("******************************************"),
					toTransferNativeAmount = NativeAmount(BigDecimal("0.000000000000000001")),
					password = "password",
				),
			)
		}
	}

	@Test
	fun `should throw because user has insufficient wallet balance to pay for transaction amount with fees`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 1.toUUID())

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = **********.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getWalletBalance(any()) } returns 21000031500000.toBigInteger()
		every { web3jWrapper.estimateGas(any()) } returns 13010.toBigInteger()
		every { web3jWrapper.getNonce(any()) } returns 0.toBigInteger()

		shouldThrow<InsufficientFundsForTransactionException> {
			commandBus(
				TransferCurrencyCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					destinationWalletAddress = AddressWrapper("******************************************"),
					toTransferNativeAmount = NativeAmount(BigDecimal("0.0000**********0000")),
					password = "password",
				),
			)
		}
	}
}
