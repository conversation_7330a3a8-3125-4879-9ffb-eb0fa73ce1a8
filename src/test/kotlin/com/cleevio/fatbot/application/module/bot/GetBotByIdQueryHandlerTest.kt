package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.bot.exception.BotNotFoundException
import com.cleevio.fatbot.application.module.bot.query.GetBotQuery
import com.cleevio.fatbot.setAndReturnPrivateProperty
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger

class GetBotByIdQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should return bot by id for user`() {
		// Setup test data
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getFirebaseUser(id = 10.toUUID(), email = "<EMAIL>")

		// Create test bots
		integrationTestHelper.getBot(
			id = 4.toUUID(),
			userId = 1.toUUID(),
			name = "TestBot",
			avatarFileId = 2.toUUID(),
			tradeAmount = BigDecimal("10.123"),
			buyFrequency = 5,
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
		)

		// Bot with zero balance (insufficient for trade)
		integrationTestHelper.getBotWallet(id = 3.toUUID(), botId = 4.toUUID())

		// Create another bot with sufficient balance for trade
		integrationTestHelper.getBot(
			id = 6.toUUID(),
			userId = 1.toUUID(),
			name = "TestBotWithBalance",
			avatarFileId = 2.toUUID(),
			tradeAmount = BigDecimal("10.123"),
			buyFrequency = 5,
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.05"),
		)

		// Bot with large balance (sufficient for trade)
		integrationTestHelper.getBotWallet(
			id = 7.toUUID(),
			botId = 6.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.valueOf(1000000000000)) },
		)

		integrationTestHelper.getBot(
			id = 5.toUUID(),
			userId = 10.toUUID(),
			name = "AnotherUserBot",
			avatarFileId = 2.toUUID(),
			tradeAmount = BigDecimal("11"),
			buyFrequency = 6,
			profitTargetFraction = BigDecimal("0.11"),
			stopLossFraction = BigDecimal("0.051"),
		)

		integrationTestHelper.getBotWallet(id = 30.toUUID(), botId = 5.toUUID())

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2")

		// Test bot with insufficient balance
		val result = queryBus(GetBotQuery(userId = 1.toUUID(), botId = 4.toUUID()))

		result.run {
			id shouldBe 4.toUUID()
			userReadableId shouldBe "00000000000000000000000000000004fatbot"
			name shouldBe "TestBot"
			avatarFileId shouldBe 2.toUUID()
			tradeAmount shouldBe BigDecimal("10.123")
			buyFrequency shouldBe 5.toBigInteger()
			profitTargetFraction shouldBe BigDecimal("0.1")
			stopLossFraction shouldBe BigDecimal("0.05")
			hasSufficientBalanceForTrade shouldBe false
			tokenTickerCopyIsChecked shouldBe false
			creatorHighBuyIsChecked shouldBe false
			bundledBuysDetectedIsChecked shouldBe false
			suspiciousWalletsDetectedIsChecked shouldBe false
			shouldWaitBeforeBuying shouldBe false
			shouldAutoSellAfterHoldTime shouldBe false
		}

		// Test bot with sufficient balance
		val resultWithBalance = queryBus(GetBotQuery(userId = 1.toUUID(), botId = 6.toUUID()))

		resultWithBalance.run {
			id shouldBe 6.toUUID()
			userReadableId shouldBe "00000000000000000000000000000006fatbot"
			name shouldBe "TestBotWithBalance"
			avatarFileId shouldBe 2.toUUID()
			tradeAmount shouldBe BigDecimal("10.123")
			buyFrequency shouldBe 5.toBigInteger()
			profitTargetFraction shouldBe BigDecimal("0.1")
			stopLossFraction shouldBe BigDecimal("0.05")
			hasSufficientBalanceForTrade shouldBe true
			tokenTickerCopyIsChecked shouldBe false
			creatorHighBuyIsChecked shouldBe false
			bundledBuysDetectedIsChecked shouldBe false
			suspiciousWalletsDetectedIsChecked shouldBe false
			shouldWaitBeforeBuying shouldBe false
			shouldAutoSellAfterHoldTime shouldBe false
		}

		// Test bot not found for user
		shouldThrow<BotNotFoundException> {
			queryBus(GetBotQuery(userId = 1.toUUID(), botId = 5.toUUID()))
		}
	}
}
