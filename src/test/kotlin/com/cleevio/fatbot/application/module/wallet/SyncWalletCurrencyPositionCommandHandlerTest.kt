package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.walletposition.command.SyncWalletCurrencyPositionCommand
import com.cleevio.fatbot.domain.walletposition.WalletCurrencyPositionRepository
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import java.time.temporal.ChronoUnit

class SyncWalletCurrencyPositionCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val positionRepository: WalletCurrencyPositionRepository,
	@Autowired private val integrationTestClock: IntegrationTestClock,
) : IntegrationTest() {

	@Test
	fun `should correctly get the user wallet balance from chain`() {
		// given user wallet
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = "abc".toAddress(),
		)

		every { web3jWrapper.getWalletBalance("abc".toAddress()) } returns BigInteger.ZERO
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns BigDecimal("3200.01")

		// when it's refreshed and wallet has 0 ETH on chain
		commandBus(SyncWalletCurrencyPositionCommand(walletId = 10.toUUID()))

		// then nothing should change in the wallet
		with(positionRepository.findFirstByWalletIdOrderByCreatedAtDesc(10.toUUID())!!) {
			totalBought shouldBe BigInteger.ZERO
			totalAcquisitionCostUsd shouldBeEqualComparingTo BigDecimal.ZERO
			totalSold shouldBe BigInteger.ZERO
		}

		// and when user gets 1 ETH on wallet from some source
		every { web3jWrapper.getWalletBalance("abc".toAddress()) } returns BigInteger("1000000000000000000")

		commandBus(SyncWalletCurrencyPositionCommand(walletId = 10.toUUID()))

		// then wallet should contain 1 ETH
		with(positionRepository.findFirstByWalletIdOrderByCreatedAtDesc(10.toUUID())!!) {
			totalBought shouldBe BigInteger("1000000000000000000")
			totalAcquisitionCostUsd shouldBeEqualComparingTo BigDecimal("3200.01")
			totalSold shouldBe BigInteger.ZERO
		}

		// and then gets another 0.5 ETH at different exchange rate
		every { web3jWrapper.getWalletBalance("abc".toAddress()) } returns BigInteger("1500000000000000000")

		integrationTestClock.advanceBy(2, ChronoUnit.MINUTES) // to invalidate exchange rate cache
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns BigDecimal("2000.01")

		commandBus(SyncWalletCurrencyPositionCommand(walletId = 10.toUUID()))

		// then wallet should contain 1.5 ETH
		with(positionRepository.findFirstByWalletIdOrderByCreatedAtDesc(10.toUUID())!!) {
			totalBought shouldBe BigInteger("1500000000000000000")
			totalAcquisitionCostUsd shouldBeEqualComparingTo BigDecimal("4200.015")
			totalSold shouldBe BigInteger.ZERO
		}

		// and then sells 1 ETH
		every { web3jWrapper.getWalletBalance("abc".toAddress()) } returns BigInteger("500000000000000000")

		commandBus(SyncWalletCurrencyPositionCommand(walletId = 10.toUUID()))

		// then wallet should contain 0.5 ETH
		with(positionRepository.findFirstByWalletIdOrderByCreatedAtDesc(10.toUUID())!!) {
			totalBought shouldBe BigInteger("1500000000000000000")
			totalAcquisitionCostUsd shouldBeEqualComparingTo BigDecimal("4200.015")
			totalSold shouldBe BigInteger("1000000000000000000")
		}

		val allPositions = positionRepository.findAll()

		// Each refresh generates a position
		allPositions shouldHaveSize 4

		// the first inserted position is the initial position
		with(allPositions.minBy { it.id }) {
			totalBought shouldBe BigInteger.ZERO
			totalAcquisitionCostUsd shouldBeEqualComparingTo BigDecimal.ZERO
			totalSold shouldBe BigInteger.ZERO
		}
	}
}
