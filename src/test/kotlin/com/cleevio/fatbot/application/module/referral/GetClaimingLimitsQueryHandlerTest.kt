package com.cleevio.fatbot.application.module.referral

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.referral.query.GetClaimingLimitsQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class GetClaimingLimitsQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should return claim limits`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val result = queryBus(GetClaimingLimitsQuery(userId = 1.toUUID()))

		result shouldContainExactlyInAnyOrder listOf(
			GetClaimingLimitsQuery.Result(
				chain = Chain.EVM_MAINNET,
				minimalClaimNativeAmount = NativeAmount(BigDecimal("0.00000000000000050")),
			),
			GetClaimingLimitsQuery.Result(
				chain = Chain.EVM_BASE,
				minimalClaimNativeAmount = NativeAmount(BigDecimal("0.00000000000000060")),
			),
			GetClaimingLimitsQuery.Result(
				chain = Chain.EVM_BSC,
				minimalClaimNativeAmount = NativeAmount(BigDecimal("0.00000000000000060")),
			),
			GetClaimingLimitsQuery.Result(
				chain = Chain.EVM_ARBITRUM_ONE,
				minimalClaimNativeAmount = NativeAmount(BigDecimal("0.00000000000000060")),
			),
			GetClaimingLimitsQuery.Result(
				chain = Chain.SOLANA,
				minimalClaimNativeAmount = NativeAmount(BigDecimal("0.5")),
			),
		)
	}
}
