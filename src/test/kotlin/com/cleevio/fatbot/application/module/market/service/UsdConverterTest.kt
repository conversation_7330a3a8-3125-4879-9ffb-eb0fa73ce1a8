package com.cleevio.fatbot.application.module.market.service

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.mockk.every
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger

class UsdConverterTest(
	@Autowired private val usdConverter: UsdConverter,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns BigDecimal.valueOf(3000)
	}

	@Test
	fun `should correctly convert value`() {
		val amountInWei = BigInteger.valueOf(123).multiply(BigInteger.TEN.pow(18))
		val expectedUsdAmount = BigDecimal.valueOf(369000)

		val result = usdConverter.baseToUsd(amountInWei, CryptoCurrency.ETH)

		result shouldBeEqualComparingTo expectedUsdAmount
	}

	@Test
	fun `should correctly convert large amounts`() {
		val amountInWei = BigInteger.valueOf(999_999_999_999).multiply(BigInteger.TEN.pow(18))
		val expectedUsd = BigDecimal.valueOf(2999999999997000)

		val result = usdConverter.baseToUsd(amountInWei, CryptoCurrency.ETH)

		result shouldBeEqualComparingTo expectedUsd
	}

	@Test
	fun `should correctly convert zero amount`() {
		val result = usdConverter.baseToUsd(BigInteger.ZERO, CryptoCurrency.ETH)

		result shouldBeEqualComparingTo BigDecimal.ZERO
	}

	@Test
	fun `should correctly convert small value to USD`() {
		val amountInWei = BigInteger.valueOf(333333330000000) // 1$ at price of 3000$ per ETH
		val expectedUsdAmount = BigDecimal.valueOf(0.999999990000000000)

		val result = usdConverter.baseToUsd(amountInWei, CryptoCurrency.ETH)

		result shouldBeEqualComparingTo expectedUsdAmount
	}
}
