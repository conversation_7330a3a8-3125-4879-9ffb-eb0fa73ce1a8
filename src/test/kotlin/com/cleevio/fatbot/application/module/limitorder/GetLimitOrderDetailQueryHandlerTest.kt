package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.limitorder.query.GetLimitOrderDetailQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigInteger

class GetLimitOrderDetailQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should return limit order detail`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getLimitOrder(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			walletId = 2.toUUID(),
			tokenAddress = AddressWrapper("******************************************"),
			limitPrice = BigInteger.valueOf(1000000000000000L),
			initialAmount = BigInteger.valueOf(1000000000000000000L),
			type = LimitOrderType.BUY,
		)

		// when
		val result = queryBus(GetLimitOrderDetailQuery(userId = 1.toUUID(), limitOrderId = 3.toUUID()))

		// then
		result.limitOrderId shouldBe 3.toUUID()
		result.walletId shouldBe 2.toUUID()
		result.tokenAddress shouldBe AddressWrapper("******************************************")
		result.chain shouldBe Chain.SOLANA
		result.type shouldBe LimitOrderType.BUY
		result.isLocked shouldBe false
	}
}
