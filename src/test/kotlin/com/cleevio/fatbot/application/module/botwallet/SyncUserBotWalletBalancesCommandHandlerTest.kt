package com.cleevio.fatbot.application.module.botwallet

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.crypto.asTxHash
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.botwallet.command.SyncUserBotWalletBalancesCommand
import com.cleevio.fatbot.domain.botwallet.BotWalletRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.types.SignatureInformation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.util.AbstractMap

class SyncUserBotWalletBalancesCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val botWalletRepository: BotWalletRepository,
) : IntegrationTest() {

	@Test
	fun `should sync balances of multiple bot wallets`() {
		integrationTestHelper.getFirebaseUser(1.toUUID())
		integrationTestHelper.getFile(50.toUUID())

		integrationTestHelper.getBot(id = 10.toUUID(), userId = 1.toUUID(), avatarFileId = 50.toUUID())
		integrationTestHelper.getBotWallet(
			id = 20.toUUID(),
			botId = 10.toUUID(),
			address = AddressWrapper("9rP1kBpqtSp9d78j9UMtpyEgMbnySVhrJMgJRabxZDtC"),
		)

		integrationTestHelper.getBot(id = 100.toUUID(), userId = 1.toUUID(), avatarFileId = 50.toUUID())
		integrationTestHelper.getBotWallet(
			id = 200.toUUID(),
			botId = 100.toUUID(),
			address = AddressWrapper("DZPXBxp1HNo44jQA7wWHPzDjZp3uZpam3KHvuzfnHHqs"),
		)

		integrationTestHelper.getBot(id = 1000.toUUID(), userId = 1.toUUID(), avatarFileId = 50.toUUID())
		integrationTestHelper.getBotWallet(
			id = 2000.toUUID(),
			botId = 1000.toUUID(),
			address = AddressWrapper("CTU1EQyV8eN4GHQsBLEXsmZwKSUHavqkqCdyEH5N1RUZ"),
			beforeSave = { setLatestSignature("signature3".asTxHash()) },
		)

		every {
			rpcClient.api.getSignaturesForAddress(
				PublicKey("9rP1kBpqtSp9d78j9UMtpyEgMbnySVhrJMgJRabxZDtC"),
				any(),
				any(),
				null,
				any(),
			)
		} returns listOf(
			SignatureInformation(mapOf("signature" to "signature1", "slot" to 1.0, "blockTime" to 2.0) as AbstractMap<*, *>),
		)

		every {
			rpcClient.api.getSignaturesForAddress(
				PublicKey("9rP1kBpqtSp9d78j9UMtpyEgMbnySVhrJMgJRabxZDtC"),
				any(),
				any(),
				"signature1",
				any(),
			)
		} returns emptyList()

		every {
			rpcClient.api.getSignaturesForAddress(
				PublicKey("DZPXBxp1HNo44jQA7wWHPzDjZp3uZpam3KHvuzfnHHqs"),
				any(),
				any(),
				null,
				any(),
			)
		} returns listOf(
			SignatureInformation(mapOf("signature" to "signature2", "slot" to 1.0, "blockTime" to 2.0) as AbstractMap<*, *>),
		)

		every {
			rpcClient.api.getSignaturesForAddress(
				PublicKey("DZPXBxp1HNo44jQA7wWHPzDjZp3uZpam3KHvuzfnHHqs"),
				any(),
				any(),
				"signature2",
				any(),
			)
		} returns emptyList()

		every {
			rpcClient.api.getSignaturesForAddress(
				PublicKey("CTU1EQyV8eN4GHQsBLEXsmZwKSUHavqkqCdyEH5N1RUZ"),
				any(),
				any(),
				any(),
				"signature3",
			)
		} returns emptyList()

		every { rpcClient.api.getTransactions(listOf("signature1", "signature2"), any()) } returns listOf(
			mockk {
				every { transaction.message.instructions } returns listOf(
					mockk { every { accounts } returns emptyList() },
					mockk { every { accounts } returns emptyList() },
					mockk {
						every { accounts } returns listOf(0L, 1L)
						every { programIdIndex } returns 2L
						every { data } returns "3Bxs412MvVNQj175"
					},
				)
				every { transaction.message.accountKeys } returns listOf(
					"48xNtEfPS5JdRQnGCDKCBMt8hAkccFqTCFacMtGjLjqY",
					"9rP1kBpqtSp9d78j9UMtpyEgMbnySVhrJMgJRabxZDtC",
					"********************************",
				)
			},

			mockk {
				every { transaction.message.instructions } returns listOf(
					mockk { every { accounts } returns emptyList() },
					mockk { every { accounts } returns emptyList() },
					mockk {
						every { accounts } returns listOf(0L, 1L)
						every { programIdIndex } returns 2L
						every { data } returns "3Bxs41C4bWBEDcNb"
					},
				)
				every { transaction.message.accountKeys } returns listOf(
					"48xNtEfPS5JdRQnGCDKCBMt8hAkccFqTCFacMtGjLjqY",
					"DZPXBxp1HNo44jQA7wWHPzDjZp3uZpam3KHvuzfnHHqs",
					"********************************",
				)
			},
		)
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "125.0".toBigDecimal()

		commandBus(SyncUserBotWalletBalancesCommand(userId = 1.toUUID(), botId = null))

		val botWallet1 = botWalletRepository.findByIdOrNull(20.toUUID())
		botWallet1 shouldNotBe null

		botWallet1!!.run {
			latestSignature shouldBe "signature1".asTxHash()
			balance shouldBe "1".toBigInteger()
		}

		val botWallet2 = botWalletRepository.findByIdOrNull(200.toUUID())
		botWallet2 shouldNotBe null

		botWallet2!!.run {
			latestSignature shouldBe "signature2".asTxHash()
			balance shouldBe "2".toBigInteger()
		}

		val botWallet3 = botWalletRepository.findByIdOrNull(2000.toUUID())
		botWallet3 shouldNotBe null

		botWallet3!!.run {
			latestSignature shouldBe "signature3".asTxHash()
			balance shouldBe "0".toBigInteger()
		}
	}
}
