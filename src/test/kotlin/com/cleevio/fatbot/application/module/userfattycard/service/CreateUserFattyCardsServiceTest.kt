package com.cleevio.fatbot.application.module.userfattycard.service

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.domain.userfattycard.UserFattyCardRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant
import java.util.UUID
import kotlin.math.abs

class CreateUserFattyCardsServiceTest(
	@Autowired private val createUserFattyCardsService: CreateUserFattyCardsService,
	@Autowired private val userFattyCardRepository: UserFattyCardRepository,
	@Autowired private val integrationTestClock: IntegrationTestClock,
) : IntegrationTest() {

	@Test
	fun `should create fatty cards based on traded amount`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.generateFattyCards()

		createUserFattyCardsService.create(
			newAmount = 2500.toBigDecimal(),
			originalAmount = BigDecimal.ZERO,
			userId = 1.toUUID(),
		)

		userFattyCardRepository.findAll().size shouldBe 2 // 2500 / 1000 = 2 cards
	}

	@Test
	fun `should accumulate traded amounts`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.generateFattyCards()

		createUserFattyCardsService.create(
			newAmount = 1500.toBigDecimal(),
			originalAmount = 500.toBigDecimal(),
			userId = 1.toUUID(),
		)

		userFattyCardRepository.findAll().size shouldBe 2 // (15000 + 5000) / 10000 = 2 cards
	}

	@Test
	fun `should create fatty cards with respect to max fatty cards limit`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.generateFattyCards()

		createUserFattyCardsService.create(
			userId = 1.toUUID(),
			originalAmount = BigDecimal.ZERO,
			newAmount = 10000000.toBigDecimal(),
		)

		userFattyCardRepository.findAll().size shouldBe 10
	}

	@Test
	fun `should create fatty cards with respect to max fatty cards limit when some already exists`() {
		integrationTestClock.setTo(Instant.now())
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.generateFattyCards()

		// create 3 cards
		createUserFattyCardsService.create(
			userId = 1.toUUID(),
			originalAmount = BigDecimal.ZERO,
			newAmount = 3000.toBigDecimal(),
		)
		userFattyCardRepository.findAll().size shouldBe 3

		// should create 7 cards
		createUserFattyCardsService.create(
			userId = 1.toUUID(),
			originalAmount = BigDecimal.ZERO,
			newAmount = 1000000.toBigDecimal(),
		)

		userFattyCardRepository.findAll().size shouldBe 10
	}

	@Test
	fun `should select cards according to their probabilities`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val allFattyCards = integrationTestHelper.generateFattyCards()

		val selectedCards = createUserFattyCardsService.selectCardsBasedOnProbability(numOfCards = 1000000)

		// Calculate expected probabilities
		val totalProbability = allFattyCards.sumOf { it.probability.toDouble() }
		val expectedProbabilities = allFattyCards.associate {
			it.id to (it.probability.toDouble() / totalProbability)
		}

		// Count occurrences of each card
		val cardCounts: Map<UUID, Int> = selectedCards.groupingBy { it }.eachCount()

		// Assert - Print results for debugging
		println("CARD SELECTION DISTRIBUTION (1000000 trials):")
		println("------------------------------------------------------")
		println("Rarity    | Expected %  | Actual %    | Difference  | Count")
		println("------------------------------------------------------")

		allFattyCards.forEach { card ->
			val expected = expectedProbabilities[card.id] ?: 0.0
			val count: Int = cardCounts.getOrDefault(card.id, 0)
			val actual = count.toDouble() / 1000000
			val diff = abs(actual - expected) * 100

			println(
				"${card.rarity.padEnd(10)} | " +
					"${(expected * 100).toBigDecimal().setScale(2, RoundingMode.HALF_UP).toString().padEnd(11)} | " +
					"${(actual * 100).toBigDecimal().setScale(2, RoundingMode.HALF_UP).toString().padEnd(11)} | " +
					"${diff.toBigDecimal().setScale(2, RoundingMode.HALF_UP)}% | " +
					"$count",
			)

			// Allow wider margins for rarer cards
			val marginOfError = when {
				expected < 0.005 -> 0.003 // For extremely rare cards (<0.5%)
				expected < 0.05 -> 0.01 // For rare cards (<5%)
				else -> 0.02 // For common cards
			}

			assert(
				abs(actual - expected) <= marginOfError,
			)
		}
	}
}
