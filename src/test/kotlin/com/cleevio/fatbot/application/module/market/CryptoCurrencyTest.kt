package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import io.kotest.assertions.throwables.shouldNotThrowAny
import org.junit.jupiter.api.Test

class CryptoCurrencyTest : IntegrationTest() {

	@Test
	fun `all chains should have crypto currency`() {
		shouldNotThrowAny {
			chainProperties.enabledEvmChains.forEach {
				CryptoCurrency.ofChainId(it.evmId)
			}
		}
	}
}
