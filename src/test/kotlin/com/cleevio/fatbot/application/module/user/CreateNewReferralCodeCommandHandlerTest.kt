package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.user.command.CreateNewReferralCodeCommand
import com.cleevio.fatbot.application.module.user.exception.ReferralCodeAlreadySetException
import com.cleevio.fatbot.application.module.user.exception.ReferralCodeAlreadyTakenException
import com.cleevio.fatbot.application.module.user.exception.ReferralCodeNotValidException
import com.cleevio.fatbot.domain.user.FirebaseUserRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class CreateNewReferralCodeCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val firebaseUserRepository: FirebaseUserRepository,
) : IntegrationTest() {

	@Test
	fun `should create new referral code`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		commandBus(CreateNewReferralCodeCommand(userId = 1.toUUID(), referralCode = "CLEEVIO_X"))

		val users = firebaseUserRepository.findAll()

		users.size shouldBe 1
		users.first().referralCode shouldBe "cleevio_x"
	}

	@Test
	fun `should throw error when creating referral code with invalid value`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		shouldThrowExactly<ReferralCodeNotValidException> {
			commandBus(CreateNewReferralCodeCommand(userId = 1.toUUID(), referralCode = "MIN"))
		}

		shouldThrowExactly<ReferralCodeNotValidException> {
			commandBus(CreateNewReferralCodeCommand(userId = 1.toUUID(), referralCode = "CLEEVIO_*"))
		}

		shouldThrowExactly<ReferralCodeNotValidException> {
			commandBus(CreateNewReferralCodeCommand(userId = 1.toUUID(), referralCode = "THIS_CODE_IS_TOO_LONG"))
		}
	}

	@Test
	fun `should throw error if referral code is already taken`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		commandBus(CreateNewReferralCodeCommand(userId = 1.toUUID(), referralCode = "CLEEVIO_X"))

		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		shouldThrowExactly<ReferralCodeAlreadyTakenException> {
			commandBus(CreateNewReferralCodeCommand(userId = 2.toUUID(), referralCode = "CLEEVIO_X"))
		}

		shouldThrowExactly<ReferralCodeAlreadyTakenException> {
			commandBus(CreateNewReferralCodeCommand(userId = 2.toUUID(), referralCode = "cleevio_x"))
		}
	}

	@Test
	fun `should throw error if referral code is already set`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		commandBus(CreateNewReferralCodeCommand(userId = 1.toUUID(), referralCode = "CLEEVIO"))

		shouldThrowExactly<ReferralCodeAlreadySetException> {
			commandBus(CreateNewReferralCodeCommand(userId = 1.toUUID(), referralCode = "CLEEVIO_X"))
		}
	}
}
