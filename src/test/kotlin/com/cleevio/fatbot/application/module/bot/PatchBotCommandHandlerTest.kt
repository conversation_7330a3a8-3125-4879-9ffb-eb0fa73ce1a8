package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.`in`.rest.dto.PatchBotSettingsRequest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.bot.command.PatchBotCommand
import com.cleevio.fatbot.domain.bot.BotRepository
import com.cleevio.fatbot.setAndReturnPrivateProperty
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Duration
import java.util.Optional

class PatchBotCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val botRepository: BotRepository,
) : IntegrationTest() {

	@Test
	fun `should update non-nullable properties`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 20.toUUID())

		integrationTestHelper.getBot(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 20.toUUID(),
			profitTargetFraction = BigDecimal("0.30"),
			stopLossFraction = BigDecimal("0.15"),
			tradeAmount = BigDecimal("500.4"),
			buyFrequency = 15,
			beforeSave = {
				setAndReturnPrivateProperty("suspiciousWalletsDetectedIsChecked", true)
				setAndReturnPrivateProperty("buyTokensAliveAtLeastFor", Duration.ofMinutes(5))
			},
		)

		commandBus(
			PatchBotCommand(
				botId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					name = "testName",
					profitTargetFraction = BigDecimal("0.25"),
					stopLossFraction = null,
					avatarFileId = 20.toUUID(),
					tradeAmount = BigDecimal("400.01"),
					buyFrequency = null,
					tokenTickerCopyIsChecked = null,
					creatorHighBuyIsChecked = true,
					bundledBuysDetectedIsChecked = true,
					suspiciousWalletsDetectedIsChecked = false,
					singleHighBuyIsChecked = true,
					shouldWaitBeforeBuying = false,
					shouldAutoSellAfterHoldTime = true,
				),
			),
		)

		botRepository.findByIdOrNull(2.toUUID())!!.run {
			name shouldBe "testName"
			profitTargetFraction shouldBe BigDecimal("0.25")
			stopLossFraction shouldBe BigDecimal("0.15")
			avatarFileId shouldBe 20.toUUID()
			tradeAmount shouldBe BigDecimal("400.01")
			buyFrequency shouldBe 15
			tokenTickerCopyIsChecked shouldBe false
			creatorHighBuyIsChecked shouldBe true
			bundledBuysDetectedIsChecked shouldBe true
			suspiciousWalletsDetectedIsChecked shouldBe false
			singleHighBuyIsChecked shouldBe true
			buyTokensAliveAtLeastFor shouldBe null
			shouldAutoSellAfterHoldTime shouldBe true
		}

		commandBus(
			PatchBotCommand(
				botId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					name = null,
					profitTargetFraction = BigDecimal("0.30"),
					stopLossFraction = null,
					buyFrequency = 20.toBigInteger(),
					tokenTickerCopyIsChecked = true,
					suspiciousWalletsDetectedIsChecked = true,
					shouldWaitBeforeBuying = true,
					shouldAutoSellAfterHoldTime = true,
					singleHighBuyIsChecked = false,
					sellTransactionFraction = Optional.of(BigDecimal("0.3")),
					buyTransactionFraction = Optional.of(BigDecimal("0.7")),
				),
			),
		)

		botRepository.findByIdOrNull(2.toUUID())!!.run {
			name shouldBe "testName"
			profitTargetFraction shouldBe BigDecimal("0.30")
			stopLossFraction shouldBe BigDecimal("0.15")
			buyFrequency shouldBe 20
			tokenTickerCopyIsChecked shouldBe true
			creatorHighBuyIsChecked shouldBe true
			bundledBuysDetectedIsChecked shouldBe true
			suspiciousWalletsDetectedIsChecked shouldBe true
			buyTokensAliveAtLeastFor shouldBe Duration.ofMinutes(5)
			shouldAutoSellAfterHoldTime shouldBe true
			singleHighBuyIsChecked shouldBe false
			sellTransactionFraction shouldBe BigDecimal("0.3")
			buyTransactionFraction shouldBe BigDecimal("0.7")
			sellToBuyTransactionRatio shouldBe BigDecimal("0.4285714285714285714285714285714286")
		}
	}

	@Test
	fun `should update nullable properties`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 20.toUUID())

		integrationTestHelper.getBot(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 20.toUUID(),
			profitTargetFraction = BigDecimal("0.30"),
			stopLossFraction = BigDecimal("0.15"),
			tradeAmount = BigDecimal("500.4"),
			buyFrequency = 15,
		)

		commandBus(
			PatchBotCommand(
				botId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					numberOfHoldersFrom = Optional.empty(), // Update to null
					numberOfHoldersTo = Optional.of(20.toBigInteger()), // Update to 20
					dailyVolumeFromUsd = Optional.of(BigDecimal("12345")), // Update to 12345
					dailyVolumeToUsd = Optional.of(BigDecimal("45678")), // Update to 12345
					buyVolume = Optional.ofNullable(BigDecimal("0.5")), // Update to 50/50 ratio of buy/sell volume
					sellVolume = Optional.ofNullable(BigDecimal("0.5")),
					liquidityFromUsd = null, // No Update
					liquidityToUsd = Optional.of(BigDecimal("30")), // Update to 30
					marketCapFromUsd = Optional.empty(), // Update to null
					marketCapToUsd = Optional.of(BigDecimal("500")), // // Update to 500
					sellTransactionFraction = Optional.of(BigDecimal("1")),
					buyTransactionFraction = Optional.of(BigDecimal("0")),
				),
			),
		)

		botRepository.findByIdOrNull(2.toUUID())!!.run {
			numberOfHoldersFrom shouldBe null
			numberOfHoldersTo shouldBe 20
			dailyVolumeFromUsd shouldBe BigDecimal("12345")
			dailyVolumeToUsd shouldBe BigDecimal("45678")
			buyVolume shouldBe BigDecimal("0.5")
			sellVolume shouldBe BigDecimal("0.5")
			liquidityFromUsd shouldBe null
			liquidityToUsd shouldBe BigDecimal("30")
			marketCapFromUsd shouldBe null
			marketCapToUsd shouldBe BigDecimal("500")
			sellTransactionFraction shouldBe BigDecimal("1")
			buyTransactionFraction shouldBe BigDecimal("0")
			sellToBuyTransactionRatio shouldBe BigDecimal("1000")
		}

		commandBus(
			PatchBotCommand(
				botId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					buyVolume = null, // "undefined" value - No update
					sellVolume = null, // "undefined" value - No update
					numberOfHoldersFrom = Optional.of(15.toBigInteger()), // Update to 15
					numberOfHoldersTo = null, //  "undefined" value - No update
					dailyVolumeFromUsd = Optional.empty(), // Update to null
					liquidityFromUsd = Optional.of(BigDecimal("20")),
					sellTransactionFraction = null, // "undefined" value - No update
					buyTransactionFraction = null, // "undefined" value - No update
				),
			),
		)

		botRepository.findByIdOrNull(2.toUUID())!!.run {
			numberOfHoldersFrom shouldBe 15
			numberOfHoldersTo shouldBe 20
			dailyVolumeFromUsd shouldBe null
			dailyVolumeToUsd shouldBe BigDecimal("45678")
			buyVolume shouldBe BigDecimal("0.5")
			sellVolume shouldBe BigDecimal("0.5")
			liquidityFromUsd shouldBe BigDecimal("20")
			liquidityToUsd shouldBe BigDecimal("30")
			marketCapFromUsd shouldBe null
			marketCapToUsd shouldBe BigDecimal("500")
			sellTransactionFraction shouldBe BigDecimal("1")
			buyTransactionFraction shouldBe BigDecimal("0")
			sellToBuyTransactionRatio shouldBe BigDecimal("1000")
		}
	}

	@Test
	fun `when buy frequency is updated and remaining frequency is higher than new value, it's coerced`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 20.toUUID())

		integrationTestHelper.getBot(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 20.toUUID(),
			buyFrequency = 350,
		)

		commandBus(
			PatchBotCommand(
				botId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					buyFrequency = BigInteger("100"),
				),
			),
		)

		botRepository.findByIdOrNull(2.toUUID())!!.run {
			buyFrequency shouldBe 100L
			remainingBuyFrequency shouldBe 100L
		}

		commandBus(
			PatchBotCommand(
				botId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					buyFrequency = BigInteger("150"),
				),
			),
		)

		botRepository.findByIdOrNull(2.toUUID())!!.run {
			buyFrequency shouldBe 150L
			remainingBuyFrequency shouldBe 150L
		}
	}

	@Test
	fun `should update remaining buy frequency based on current number of buys`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 20.toUUID())

		integrationTestHelper.getBot(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 20.toUUID(),
			buyFrequency = 100,
			beforeSave = { setAndReturnPrivateProperty("remainingBuyFrequency", 60L) },
		)

		// when
		commandBus(
			PatchBotCommand(
				botId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					buyFrequency = BigInteger("50"),
				),
			),
		)

		// then
		botRepository.findByIdOrNull(2.toUUID())!!.run {
			buyFrequency shouldBe 50L
			remainingBuyFrequency shouldBe 10L
		}

		// when
		commandBus(
			PatchBotCommand(
				botId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					buyFrequency = BigInteger("30"),
				),
			),
		)

		// then
		botRepository.findByIdOrNull(2.toUUID())!!.run {
			buyFrequency shouldBe 30L
			remainingBuyFrequency shouldBe 0L
		}
	}

	@Test
	fun `should throw on invalid input ranges`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 20.toUUID())

		integrationTestHelper.getBot(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			name = "initialName",
			avatarFileId = 20.toUUID(),
			profitTargetFraction = BigDecimal("0.30"),
			stopLossFraction = BigDecimal("0.15"),
			tradeAmount = BigDecimal("500.4"),
			buyFrequency = 15,
		)

		shouldThrow<ConstraintViolationException> {
			commandBus(
				PatchBotCommand(
					botId = 2.toUUID(),
					userId = 1.toUUID(),
					patchRequest = PatchBotSettingsRequest(
						buyVolume = Optional.of(BigDecimal("0.4")),
						sellVolume = Optional.of(BigDecimal("0.6")),
					),
				),
			)
		}

		shouldThrow<IllegalArgumentException> {
			commandBus(
				PatchBotCommand(
					botId = 2.toUUID(),
					userId = 1.toUUID(),
					patchRequest = PatchBotSettingsRequest(
						buyVolume = Optional.of(BigDecimal("0.7")),
						sellVolume = Optional.of(BigDecimal("0.4")),
					),
				),
			)
		}

		val invalidRangeRequests = listOf(
			PatchBotSettingsRequest(
				buyVolume = Optional.of(BigDecimal("0.8")),
				sellVolume = Optional.of(BigDecimal("0.3")),
			),
			PatchBotSettingsRequest(
				dailyVolumeFromUsd = Optional.of(BigDecimal("123")),
				dailyVolumeToUsd = Optional.of(BigDecimal("122")),
			),
			PatchBotSettingsRequest(
				marketCapFromUsd = Optional.of(BigDecimal("1")),
				marketCapToUsd = Optional.of(BigDecimal("0")),
			),
			PatchBotSettingsRequest(
				liquidityFromUsd = Optional.of(BigDecimal("999")),
				liquidityToUsd = Optional.of(BigDecimal("1")),
			),
			PatchBotSettingsRequest(
				numberOfHoldersFrom = Optional.of(1.toBigInteger()),
				numberOfHoldersTo = Optional.of(1.toBigInteger()),
			),
		)

		invalidRangeRequests.forEach {
			shouldThrow<IllegalArgumentException> {
				commandBus(
					PatchBotCommand(
						botId = 2.toUUID(),
						userId = 1.toUUID(),
						patchRequest = it,
					),
				)
			}
		}

		// Test that present data in the DB are taken into account
		commandBus(
			PatchBotCommand(
				botId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					liquidityFromUsd = Optional.of(BigDecimal("100")),
					buyVolume = Optional.of(BigDecimal("0.5")),
					sellVolume = Optional.of(BigDecimal("0.5")),
					marketCapToUsd = Optional.of(BigDecimal("500")),
				),
			),
		)

		val invalidRangeRequests2 = listOf(
			PatchBotSettingsRequest(
				// buyVolume set to 0.5,
				sellVolume = Optional.of(BigDecimal("0.3")),
			),
			PatchBotSettingsRequest(
				// liquidityFrom set to 100,
				liquidityToUsd = Optional.of(BigDecimal("50")),
			),
			PatchBotSettingsRequest(
				marketCapFromUsd = Optional.of(BigDecimal("300")),
				marketCapToUsd = Optional.of(BigDecimal("200")),
			),
		)

		invalidRangeRequests2.forEach {
			shouldThrow<IllegalArgumentException> {
				commandBus(
					PatchBotCommand(
						botId = 2.toUUID(),
						userId = 1.toUUID(),
						patchRequest = it,
					),
				)
			}
		}
	}
}
