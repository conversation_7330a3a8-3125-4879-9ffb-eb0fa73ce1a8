package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.evm.context.UniswapV3Fee
import com.cleevio.fatbot.adapter.out.evm.model.FeesPerGas
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.command.SellTokenCommand
import com.cleevio.fatbot.application.module.market.exception.UnsupportedChainIdException
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.exception.InsufficientFundsForTransactionException
import com.cleevio.fatbot.application.module.wallet.exception.InsufficientFundsForTransactionFeesException
import com.cleevio.fatbot.application.module.wallet.exception.WalletNotFoundException
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger

@Suppress("ktlint:standard:max-line-length")
class SellTokenCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
) : IntegrationTest() {

	@Test
	@Disabled("Enable this when anti-mev will be optional (and not forced)")
	fun `should sell token on UniswapV2, with approval if not enough is approved for sell`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 1000.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
		)

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = BigInteger.valueOf(18),
		)

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = 1000000000.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getWalletBalance(any()) } returns 1000000000000000.toBigInteger()

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"70a08231" + // selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB", // wallet address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x0000000000000000000000000000000000000000000000000000000000000064" // 100

		// call to token contract to see current allowance balance
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"dd62ed3e" + // allowance selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB" + // wallet address padded to 64
					"000000000000000000000000Daed7d2D1287BaC5bF53fEC60F617Def24a7BA13", // Fatbot router address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000000f" // currently allowance is only 15 WEI

		/* // some fields left out for clarity. Decoded via https://rawtxdecode.in/
			{
			  "chainId": "1",
			  "type": "EIP-1559",
			  "valid": true,
			  "hash": "0x9918907cfc0a8ed8b7b943d436fc982a92724ef7ceabd6818da38a313c7cd8db",
			  "nonce": "1",
			  "gasLimit": "60000",
			  "maxFeePerGas": "1000001500",
			  "maxPriorityFeePerGas": "1000000000",
			  "from": "******************************************",
			  "to": "******************************************",
			  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
			  "v": "01",
			  "r": "98f7b32f1cf80789dc770ab4606c2531cf2d1f4654d8f15fed0f0eb7d5e0dd4b",
			  "s": "20bf5072ffdedd0dd8a7b38ae776792dc929c3c30f0cf56350f38463d7e37e29",
			  "value": "0",
			  "input": "0x095ea7b3000000000000000000000000daed7d2d1287bac5bf53fec60f617def24a7ba13ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff",
			  "functionHash": "0x095ea7b3",
			  "possibleFunctions": [
			    {
			      "definition": "approve(address,uint256)",
			      "decodedInputs": [
			        "0xDaed7d2D1287BaC5bF53fEC60F617Def24a7BA13",
			        "115792089237316195423570985008687907853269984665640564039457584007913129639935"
			      ]
			    }
			  ]
			}
		 */
		every {
			web3jWrapper.sendRawTransactionAsync(
				signedTransaction = "0x02f8af0101843b9aca00843b9acfdc82ea6094046bbabe36c18422303ab83adb74bcdaa4e8ee1b80b844095ea7b3000000000000000000000000daed7d2d1287bac5bf53fec60f617def24a7ba13ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc080a088307bbefe34f4197b88b725bceab2391030453b6f37403617f235ca29b86b4ea07499a6ac6403326db6564e870492878a31a9d6c58426ff6601234b8c2eefb6b5",
			)
		} just Runs

		/* // some fields left out for clarity. Decoded via https://rawtxdecode.in/
			{
			  "chainId": "1",
			  "type": "EIP-1559",
			  "valid": true,
			  "hash": "0x7682ee8dc5e954a808d82be6e5a6e78219cb4fcce8e206d6cea7258502e7fdc6",
			  "nonce": "2",
			  "gasLimit": "15",
			  "maxFeePerGas": "1000001500",
			  "maxPriorityFeePerGas": "1000000000",
			  "from": "******************************************",
			  "to": "******************************************",
			  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
			  "v": "00",
			  "r": "c11d222843c6171bc997a96a56dcb835852b583df9a2b4875f6e59e321a6fe04",
			  "s": "605173c11596f50dbc3b37141c7e21e27a445ff85032d685cfbf7ab1ded29bde",
			  "value": "0",
			  "input": "0x86e5e60d0000000000000000000000000000000000000000000000000000000000000010000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000",
			  "functionHash": "0x86e5e60d",
			  "functionName": "sellV2",
			  "decodedInputs": {
				"amountIn": "16",
				"token": "******************************************",
				"to": "******************************************",
				"uuid": "00000000-0000-0000-0000-000000000000",
				"feePercentage": "100",
				"referralFeePercentage": "0"
			  }
			}
		 */
		every {
			web3jWrapper.sendRawTransactionAsync(
				signedTransaction = "0x02f9018e0102843b9aca00843b9acfdc0f94daed7d2d1287bac5bf53fec60f617def24a7ba1380b9012486e5e60d0000000000000000000000000000000000000000000000000000000000000010000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c080a0c11d222843c6171bc997a96a56dcb835852b583df9a2b4875f6e59e321a6fe04a0605173c11596f50dbc3b37141c7e21e27a445ff85032d685cfbf7ab1ded29bde",
			)
		} just Runs

		every { web3jWrapper.estimateGas(any()) } returns BigInteger.valueOf(10)

		val result = commandBus(
			SellTokenCommand(
				userId = 1.toUUID(),
				walletId = 1000.toUUID(),
				tokenAddress = AddressWrapper("******************************************"),
				toSellNativeAmount = NativeAmount(BigDecimal("0.000000000000000016")),
				dexPairInfo = GetDex.UniswapV2(
					pairAddress = AddressWrapper("******************************************"),
				),
			),
		)

		with(result.txHashes) {
			size shouldBe 2
			this[0] shouldBe TxHash("0x9918907cfc0a8ed8b7b943d436fc982a92724ef7ceabd6818da38a313c7cd8db")
			this[1] shouldBe TxHash("0x7682ee8dc5e954a808d82be6e5a6e78219cb4fcce8e206d6cea7258502e7fdc6")
		}
	}

	@Test
	fun `should sell token on UniswapV2, with approval if not enough is approved for sell privately, when sell anti mev is ON`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 1000.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				patchSettings(
					customName = null,
					buyAntiMevProtection = null,
					sellAntiMevProtection = true,
				)
			},
		)

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = BigInteger.valueOf(18),
		)

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // UniswapV2 factory contract
				"0x" + // 0x
					"e6a43905" + // selector
					"000000000000000000000000046BbABE36c18422303aB83AdB74BcDaA4e8Ee1b" + // token address padded to 64
					"000000000000000000000000C02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2", // weth address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000090892282A68DAd38ac77Cec3e80D008210830b5B" // non null address

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // UniswapV2 pair that was found
				"0x0902f1ac", // get reserves selector
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000000186A0" + // 100k in decimal, first chunk is liquidity of token0
			"00000000000000000000000000000000000000000000000000000000000186A0" + // 100k in decimal, second chunk is liquidity of token1
			"0000000000000000000000000000000000000000000000000000000000000016" // there is also third param, but we ignore it

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = 1000000000.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getWeb3j() } returns web3j

		// Approval + sell
		every { web3jWrapper.getNonce(any()) } returnsMany listOf(1.toBigInteger(), 2.toBigInteger())
		every { web3jWrapper.getWalletBalance(any()) } returns 1000000000000000.toBigInteger()
		every { web3jWrapper.getBlockNumber() } returns 99.toBigInteger()

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"70a08231" + // selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB", // wallet address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x0000000000000000000000000000000000000000000000000000000000000064" // 100

		// call to token contract to see current allowance balance
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"dd62ed3e" + // allowance selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB" + // wallet address padded to 64
					"000000000000000000000000Daed7d2D1287BaC5bF53fEC60F617Def24a7BA13", // Fatbot router address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000000f" // currently allowance is only 15 WEI

		/* // some fields left out for clarity. Decoded via https://rawtxdecode.in/
			{
			  "chainId": "1",
			  "type": "EIP-1559",
			  "valid": true,
			  "hash": "0x8454bf736aaf942da248f095d08f7046c1b4d7c4a6ed7404b35722bffbf8ed9a",
			  "nonce": "1",
			  "gasLimit": "120000",
			  "maxFeePerGas": "1000001500",
			  "maxPriorityFeePerGas": "1000000000",
			  "from": "******************************************",
			  "to": "******************************************",
			  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
			  "v": "01",
			  "r": "98f7b32f1cf80789dc770ab4606c2531cf2d1f4654d8f15fed0f0eb7d5e0dd4b",
			  "s": "20bf5072ffdedd0dd8a7b38ae776792dc929c3c30f0cf56350f38463d7e37e29",
			  "value": "0",
			  "input": "0x095ea7b3000000000000000000000000daed7d2d1287bac5bf53fec60f617def24a7ba13ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff",
			  "functionHash": "0x095ea7b3",
			  "possibleFunctions": [
			    {
			      "definition": "approve(address,uint256)",
			      "decodedInputs": [
			        "0xDaed7d2D1287BaC5bF53fEC60F617Def24a7BA13",
			        "115792089237316195423570985008687907853269984665640564039457584007913129639935"
			      ]
			    }
			  ]
			}
		 */
		every {
			web3jWrapper.sendRawTransaction(
				"0x02f8b00101843b9aca00843b9acfdc8301d4c094046bbabe36c18422303ab83adb74bcdaa4e8ee1b80b844095ea7b3000000000000000000000000daed7d2d1287bac5bf53fec60f617def24a7ba13ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc080a0a6cff265b3074394d808128a4dd6f4bcddae615bb3a98f6ce631037350626782a00fea333cdb6f3fc8ca3f03c5cda9c7ffd9cfc0c774a406ffc387f459fb8626c7",
			)
		} just Runs

		every {
			web3jWrapper.getTransactionReceipt("0x8454bf736aaf942da248f095d08f7046c1b4d7c4a6ed7404b35722bffbf8ed9a")
		} returns mockk { every { isStatusOK } returns true }

		/* // some fields left out for clarity. Decoded via https://rawtxdecode.in/
			{
			  "chainId": "1",
			  "type": "EIP-1559",
			  "valid": true,
			  "hash": "0x9edf2c5e38e134ea4da7486346e31acd21b6139652ab99bd5d2e112d4766b0ae",
			  "nonce": "2",
			  "gasLimit": "15",
			  "maxFeePerGas": "1000001500",
			  "maxPriorityFeePerGas": "1000000000",
			  "from": "******************************************",
			  "to": "******************************************",
			  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
			  "v": "00",
			  "r": "c11d222843c6171bc997a96a56dcb835852b583df9a2b4875f6e59e321a6fe04",
			  "s": "605173c11596f50dbc3b37141c7e21e27a445ff85032d685cfbf7ab1ded29bde",
			  "value": "0",
			  "input": "0x86e5e60d0000000000000000000000000000000000000000000000000000000000000010000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000",
			  "functionHash": "0x86e5e60d",
			  "functionName": "sellV2",
			  "decodedInputs": {
				"amountIn": "16",
				"token": "******************************************",
				"to": "******************************************",
				"uuid": "00000000-0000-0000-0000-000000000000",
				"feePercentage": "100",
				"referralFeePercentage": "0",
				 "amountOutMin": "118518518"
			  }
			}
		 */
		every {
			beaverbuildConnector.sendPrivateEthTransaction(
				SignedTx(
					signedTx = "0x02f901ae0102843b9aca00843b9acfdc0f94daed7d2d1287bac5bf53fec60f617def24a7ba1380b90144464c433f000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000071072f6000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c001a030a6f106d4623901b8c56fe4ed3898dd2c1b7f65617d55b3a96fb5145baaca6aa041cafc61af6b2aad37000e0026f5fcba80d6cff0b2f3a53fd30f1e8ad94befd5",
				),
			)
		} just Runs
		every {
			titanbuilderConnector.sendPrivateEthTransaction(
				SignedTx(
					signedTx = "0x02f901ae0102843b9aca00843b9acfdc0f94daed7d2d1287bac5bf53fec60f617def24a7ba1380b90144464c433f000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000071072f6000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c001a030a6f106d4623901b8c56fe4ed3898dd2c1b7f65617d55b3a96fb5145baaca6aa041cafc61af6b2aad37000e0026f5fcba80d6cff0b2f3a53fd30f1e8ad94befd5",
				),
				maxAllowedBlockNumberToInclude = BigInteger.valueOf(101),
			)
		} just Runs

		// Amount out call
		every {
			readOnlyTransactionManager.sendCall(
				"0xBfEa493c17B9Af8Aca1aFbb20D8400236D2dB794",
				"0x242f1fc80000000000000000000000000000000000000000000000000000000000000010000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b0000000000000000000000000000000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000075BCD15"

		every { web3jWrapper.estimateGas(any()) } returnsMany listOf(120_000.toBigInteger(), 10.toBigInteger())

		val result = commandBus(
			SellTokenCommand(
				userId = 1.toUUID(),
				walletId = 1000.toUUID(),
				tokenAddress = AddressWrapper("******************************************"),
				toSellNativeAmount = NativeAmount(BigDecimal("0.000000000000000016")),
				dexPairInfo = GetDex.UniswapV2(
					pairAddress = AddressWrapper("******************************************"),
				),
			),
		)

		verify(exactly = 0) { web3jWrapper.sendRawTransactionAsync(any()) }
		with(result.txHashes) {
			size shouldBe 2
			this[0] shouldBe TxHash("0x8454bf736aaf942da248f095d08f7046c1b4d7c4a6ed7404b35722bffbf8ed9a")
			this[1] shouldBe TxHash("0x9edf2c5e38e134ea4da7486346e31acd21b6139652ab99bd5d2e112d4766b0ae")
		}
	}

	@Test
	@Disabled("Enable this when anti-mev will be optional (and not forced)")
	fun `should sell token on UniswapV2, without approval if enough is approved for sell`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 1000.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
		)

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = BigInteger.valueOf(18),
		)

		every { web3jWrapper.getBaseFeePerGas() } returns BigInteger.valueOf(1000)
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getNonce(any()) } returns BigInteger.ONE
		every { web3jWrapper.getWalletBalance(any()) } returns 1000000000000000.toBigInteger()

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"70a08231" + // selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB", // wallet address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x0000000000000000000000000000000000000000000000000000000000000064" // 100

		// call to token contract to see current allowance balance
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"dd62ed3e" + // allowance selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB" + // wallet address padded to 64
					"000000000000000000000000Daed7d2D1287BaC5bF53fEC60F617Def24a7BA13", // Fatbot router address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x000000000000000000000000000000000000000000000000000000000010" // currently allowance is exactly 16 WEI

		/* // some fields left out for clarity. Decoded via https://rawtxdecode.in/
			{
			  "chainId": "1",
			  "type": "EIP-1559",
			  "valid": true,
			  "hash": "0x03da49015311643608fca45def1c1d846870f83f9cb27482d20ece81814b51c1",
			  "nonce": "1",
			  "gasLimit": "15",
			  "maxFeePerGas": "1000001500",
			  "maxPriorityFeePerGas": "1000000000",
			  "from": "******************************************",
			  "to": "******************************************",
			  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
			  "v": "01",
			  "r": "8af0b8bfd2c3d67275f345c6b3d3a225f6be02bbaf27c9932489523c59ca2b0c",
			  "s": "1b4201825a3bbd02c4df98eaade1490720a9c376870ecadef271eb788ff6b4f2",
			  "value": "0",
			  "input": "0x86e5e60d0000000000000000000000000000000000000000000000000000000000000010000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000",
			  "functionHash": "0x86e5e60d",
			  "functionName": "sellV2",
			  "decodedInputs": {
				"amountIn": "16",
				"token": "******************************************",
				"to": "******************************************",
				"uuid": "00000000-0000-0000-0000-000000000000",
				"feePercentage": "100",
				"referralFeePercentage": "0"
			  }
			}
		 */
		every {
			web3jWrapper.sendRawTransactionAsync(
				signedTransaction = "0x02f9018e0101843b9aca00843b9acfdc0f94daed7d2d1287bac5bf53fec60f617def24a7ba1380b9012486e5e60d0000000000000000000000000000000000000000000000000000000000000010000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb00000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c001a08af0b8bfd2c3d67275f345c6b3d3a225f6be02bbaf27c9932489523c59ca2b0ca01b4201825a3bbd02c4df98eaade1490720a9c376870ecadef271eb788ff6b4f2",
			)
		} just Runs

		every { web3jWrapper.estimateGas(any()) } returns BigInteger.valueOf(10)

		val result = commandBus(
			SellTokenCommand(
				userId = 1.toUUID(),
				walletId = 1000.toUUID(),
				tokenAddress = AddressWrapper("******************************************"),
				toSellNativeAmount = NativeAmount(BigDecimal("0.000000000000000016")),
				dexPairInfo = GetDex.UniswapV2(
					pairAddress = AddressWrapper("******************************************"),
				),
			),
		)

		with(result.txHashes) {
			size shouldBe 1
			this[0] shouldBe TxHash("0x03da49015311643608fca45def1c1d846870f83f9cb27482d20ece81814b51c1")
		}
	}

	@Test
	@Disabled("Enable this when anti-mev will be optional (and not forced)")
	fun `should sell token on UniswapV3, with approval if not enough is approved for sell`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 1000.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
		)

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = BigInteger.valueOf(18),
		)

		every { web3jWrapper.getBaseFeePerGas() } returns 1000.toBigInteger()
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getWalletBalance(any()) } returns 1000000000000000.toBigInteger()

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"70a08231" + // selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB", // wallet address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x0000000000000000000000000000000000000000000000000000000000000064" // 100

		// call to token contract to see current allowance balance
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"dd62ed3e" + // allowance selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB" + // wallet address padded to 64
					"000000000000000000000000Daed7d2D1287BaC5bF53fEC60F617Def24a7BA13", // Fatbot router address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000000f" // currently allowance is only 15 WEI

		/* // some fields left out for clarity. Decoded via https://rawtxdecode.in/
				{
				  "chainId": "1",
				  "type": "EIP-1559",
				  "valid": true,
				  "hash": "0x9918907cfc0a8ed8b7b943d436fc982a92724ef7ceabd6818da38a313c7cd8db",
				  "nonce": "1",
				  "gasLimit": "60000",
				  "maxFeePerGas": "1000001500",
				  "maxPriorityFeePerGas": "1000000000",
				  "from": "******************************************",
				  "to": "******************************************",
				  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
				  "v": "01",
				  "r": "98f7b32f1cf80789dc770ab4606c2531cf2d1f4654d8f15fed0f0eb7d5e0dd4b",
				  "s": "20bf5072ffdedd0dd8a7b38ae776792dc929c3c30f0cf56350f38463d7e37e29",
				  "value": "0",
				  "input": "0x095ea7b3000000000000000000000000daed7d2d1287bac5bf53fec60f617def24a7ba13ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff",
				  "functionHash": "0x095ea7b3",
				  "possibleFunctions": [
				    {
				      "definition": "approve(address,uint256)",
				      "decodedInputs": [
				        "0xDaed7d2D1287BaC5bF53fEC60F617Def24a7BA13",
				        "115792089237316195423570985008687907853269984665640564039457584007913129639935"
				      ]
				    }
				  ]
				}
		 */
		every {
			web3jWrapper.sendRawTransactionAsync(
				signedTransaction = "0x02f8af0101843b9aca00843b9acfdc82ea6094046bbabe36c18422303ab83adb74bcdaa4e8ee1b80b844095ea7b3000000000000000000000000daed7d2d1287bac5bf53fec60f617def24a7ba13ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc080a088307bbefe34f4197b88b725bceab2391030453b6f37403617f235ca29b86b4ea07499a6ac6403326db6564e870492878a31a9d6c58426ff6601234b8c2eefb6b5",
			)
		} just Runs

		/* // some fields left out for clarity. Decoded via https://rawtxdecode.in/
			{
			  "chainId": "1",
			  "type": "EIP-1559",
			  "valid": true,
			  "hash": "0xab39ace92665731df4b70dd8ae6b6917bfeba9e57050cc1fcd7b51db515ee4be",
			  "nonce": "2",
			  "gasLimit": "15",
			  "maxFeePerGas": "1000001500",
			  "maxPriorityFeePerGas": "1000000000",
			  "from": "******************************************",
			  "to": "******************************************",
			  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
			  "v": "00",
			  "r": "c1d94c88dc9b2c5482955ec9e1e8b786599f4842ea8d2c94ae0d91d2697ac517",
			  "s": "6726487b5c6c7610b60265a352d7e33aaca9a2753731e3090796209526b8587e",
			  "value": "0",
			  "input": "0xbc6c4aa90000000000000000000000000000000000000000000000000000000000000010000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb000000000000000000000000000000000000000000000000000000000000271000000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000",
			  "functionHash": "0xbc6c4aa9",
			  "functionName": "sellV3",
			  "decodedInputs": {
				"amountIn": "16",
				"token": "******************************************",
				"to": "******************************************",
				"fee": "10000",
				"uuid": "00000000-0000-0000-0000-000000000000",
				"feePercentage": "100",
				"referralFeePercentage": "0"
			  }
			}
		 */
		every {
			web3jWrapper.sendRawTransactionAsync(
				signedTransaction = "0x02f901ae0102843b9aca00843b9acfdc0f94daed7d2d1287bac5bf53fec60f617def24a7ba1380b90144bc6c4aa90000000000000000000000000000000000000000000000000000000000000010000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb000000000000000000000000000000000000000000000000000000000000271000000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c080a0c1d94c88dc9b2c5482955ec9e1e8b786599f4842ea8d2c94ae0d91d2697ac517a06726487b5c6c7610b60265a352d7e33aaca9a2753731e3090796209526b8587e",
			)
		} just Runs

		every { web3jWrapper.estimateGas(any()) } returns BigInteger.valueOf(10)

		val result = commandBus(
			SellTokenCommand(
				userId = 1.toUUID(),
				walletId = 1000.toUUID(),
				tokenAddress = AddressWrapper("******************************************"),
				toSellNativeAmount = NativeAmount(BigDecimal("0.000000000000000016")),
				dexPairInfo = GetDex.UniswapV3(
					pairAddress = AddressWrapper("******************************************"),
					fee = UniswapV3Fee.FEE_10000,
				),
			),
		)

		with(result.txHashes) {
			size shouldBe 2
			this[0] shouldBe TxHash("0x9918907cfc0a8ed8b7b943d436fc982a92724ef7ceabd6818da38a313c7cd8db")
			this[1] shouldBe TxHash("0xab39ace92665731df4b70dd8ae6b6917bfeba9e57050cc1fcd7b51db515ee4be")
		}
	}

	@Test
	fun `should sell token on UniswapV3, with approval if not enough is approved for sell privately, when anti mev is ON`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 1000.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				patchSettings(
					customName = null,
					buyAntiMevProtection = null,
					sellAntiMevProtection = true,
				)
			},
		)

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = BigInteger.valueOf(18),
		)

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = 1000000000.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getWeb3j() } returns web3j
		// Approval + sell
		every { web3jWrapper.getNonce(any()) } returnsMany listOf(1.toBigInteger(), 2.toBigInteger())
		every { web3jWrapper.getWalletBalance(any()) } returns 1000000000000000.toBigInteger()
		every { web3jWrapper.getBlockNumber() } returns 99.toBigInteger()

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"70a08231" + // selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB", // wallet address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x0000000000000000000000000000000000000000000000000000000000000064" // 100

		// call to token contract to see current allowance balance
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"dd62ed3e" + // allowance selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB" + // wallet address padded to 64
					"000000000000000000000000Daed7d2D1287BaC5bF53fEC60F617Def24a7BA13", // Fatbot router address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000000f" // currently allowance is only 15 WEI

		/* // some fields left out for clarity. Decoded via https://rawtxdecode.in/
				{
				  "chainId": "1",
				  "type": "EIP-1559",
				  "valid": true,
				  "hash": "0x468614367c3943e8aac4e473cb07975b77af223c0164ac9dbcb3c2e36b78bde6",
				  "nonce": "1",
				  "gasLimit": "80000",
				  "maxFeePerGas": "1000001500",
				  "maxPriorityFeePerGas": "1000000000",
				  "from": "******************************************",
				  "to": "******************************************",
				  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
				  "v": "01",
				  "r": "98f7b32f1cf80789dc770ab4606c2531cf2d1f4654d8f15fed0f0eb7d5e0dd4b",
				  "s": "20bf5072ffdedd0dd8a7b38ae776792dc929c3c30f0cf56350f38463d7e37e29",
				  "value": "0",
				  "input": "0x095ea7b3000000000000000000000000daed7d2d1287bac5bf53fec60f617def24a7ba13ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff",
				  "functionHash": "0x095ea7b3",
				  "possibleFunctions": [
				    {
				      "definition": "approve(address,uint256)",
				      "decodedInputs": [
				        "0xDaed7d2D1287BaC5bF53fEC60F617Def24a7BA13",
				        "115792089237316195423570985008687907853269984665640564039457584007913129639935"
				      ]
				    }
				  ]
				}
		 */

		every {
			web3jWrapper.sendRawTransaction(
				"0x02f8b00101843b9aca00843b9acfdc8301388094046bbabe36c18422303ab83adb74bcdaa4e8ee1b80b844095ea7b3000000000000000000000000daed7d2d1287bac5bf53fec60f617def24a7ba13ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc001a0e20987a170508018de051776272e8759d3e1056e017f86c92873f9bda66d4448a0564756ac1ab39081e8746e7f80090741d00bde6ba0d32dc2ad9f4489c7089c93",
			)
		} just Runs

		every {
			web3jWrapper.getTransactionReceipt("0x468614367c3943e8aac4e473cb07975b77af223c0164ac9dbcb3c2e36b78bde6")
		} returnsMany listOf(null, mockk { every { isStatusOK } returns true })

		/* // some fields left out for clarity. Decoded via https://rawtxdecode.in/
			{
			  "chainId": "1",
			  "type": "EIP-1559",
			  "valid": true,
			  "hash": "0x925a729ea05d1a941ed01e875992908bd1c79c234f780cb78af7fba31e61f0b8",
			  "nonce": "2",
			  "gasLimit": "15",
			  "maxFeePerGas": "1000001500",
			  "maxPriorityFeePerGas": "1000000000",
			  "from": "******************************************",
			  "to": "******************************************",
			  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
			  "v": "00",
			  "r": "c1d94c88dc9b2c5482955ec9e1e8b786599f4842ea8d2c94ae0d91d2697ac517",
			  "s": "6726487b5c6c7610b60265a352d7e33aaca9a2753731e3090796209526b8587e",
			  "value": "0",
			  "input": "0xbc6c4aa90000000000000000000000000000000000000000000000000000000000000010000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb000000000000000000000000000000000000000000000000000000000000271000000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000",
			  "functionHash": "0xbc6c4aa9",
			  "functionName": "sellV3",
			  "decodedInputs": {
				"amountIn": "16",
				"token": "******************************************",
				"to": "******************************************",
				"fee": "10000",
				"uuid": "00000000-0000-0000-0000-000000000000",
				"feePercentage": "100",
				"referralFeePercentage": "0",
				"amountOutMin": "118518518"
			  }
			}
		 */
		every {
			beaverbuildConnector.sendPrivateEthTransaction(
				SignedTx(
					signedTx = "0x02f901ce0102843b9aca00843b9acfdc0f94daed7d2d1287bac5bf53fec60f617def24a7ba1380b90164afd21312000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000071072f6000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb0000000000000000000000000000000000000000000000000000000000002710000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c080a0c5c6a9fc150b10becf03df75b8e67a892c2c8903db14ab6c32f7fba584082aaea02b6ffe9a8306a05b8c1316360448b5f9c1fe9c66965b81d4a65395457ef8893e",
				),
			)
		} just Runs
		every {
			titanbuilderConnector.sendPrivateEthTransaction(
				SignedTx(
					signedTx = "0x02f901ce0102843b9aca00843b9acfdc0f94daed7d2d1287bac5bf53fec60f617def24a7ba1380b90164afd21312000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000071072f6000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb0000000000000000000000000000000000000000000000000000000000002710000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c080a0c5c6a9fc150b10becf03df75b8e67a892c2c8903db14ab6c32f7fba584082aaea02b6ffe9a8306a05b8c1316360448b5f9c1fe9c66965b81d4a65395457ef8893e",
				),
				maxAllowedBlockNumberToInclude = BigInteger.valueOf(101),
			)
		} just Runs

		every {
			readOnlyTransactionManager.sendCall(
				"0xBfEa493c17B9Af8Aca1aFbb20D8400236D2dB794",
				"0x55658993000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000c02aaa39b223fe8d0a0e5c4f27ead9083c756cc200000000000000000000000000000000000000000000000000000000000027100000000000000000000000000000000000000000000000000000000000000010",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000075BCD15"

		// Must use the fallback gasLimit for estimation
		every { web3jWrapper.estimateGas(any()) } throws
			IllegalStateException("Estimation fail") andThen BigInteger.valueOf(10)

		val result = commandBus(
			SellTokenCommand(
				userId = 1.toUUID(),
				walletId = 1000.toUUID(),
				tokenAddress = AddressWrapper("******************************************"),
				toSellNativeAmount = NativeAmount(BigDecimal("0.000000000000000016")),
				dexPairInfo = GetDex.UniswapV3(
					pairAddress = AddressWrapper("******************************************"),
					fee = UniswapV3Fee.FEE_10000,
				),
			),
		)

		with(result.txHashes) {
			size shouldBe 2
			this[0] shouldBe TxHash("0x468614367c3943e8aac4e473cb07975b77af223c0164ac9dbcb3c2e36b78bde6")
			this[1] shouldBe TxHash("0x925a729ea05d1a941ed01e875992908bd1c79c234f780cb78af7fba31e61f0b8")
		}
	}

	@Test
	@Disabled("Enable this when anti-mev will be optional (and not forced)")
	fun `should sell token on UniswapV3, without approval if enough is approved for sell`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(
			id = 1000.toUUID(),
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "******************************************"),
		)

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = BigInteger.valueOf(18),
		)

		every { web3jWrapper.getBaseFeePerGas() } returns 1000.toBigInteger()
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getWalletBalance(any()) } returns 1000000000000000.toBigInteger()

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"70a08231" + // selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB", // wallet address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x0000000000000000000000000000000000000000000000000000000000000064" // 100

		// call to token contract to see current allowance balance
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"dd62ed3e" + // allowance selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB" + // wallet address padded to 64
					"000000000000000000000000Daed7d2D1287BaC5bF53fEC60F617Def24a7BA13", // Fatbot router address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x000000000000000000000000000000000000000000000000000000000010" // currently allowance is exactly 16 WEI

		/* // some fields left out for clarity. Decoded via https://rawtxdecode.in/
			{
			  "chainId": "1",
			  "type": "EIP-1559",
			  "valid": true,
			  "hash": "0xdd2394b3f029e18a1e8d41d453ca5e3aa0d42687b3d789bba7aefdbb04ca12a6",
			  "nonce": "1",
			  "gasLimit": "15",
			  "maxFeePerGas": "1000001500",
			  "maxPriorityFeePerGas": "1000000000",
			  "from": "******************************************",
			  "to": "******************************************",
			  "publicKey": "0x04dac3a324f1d7a7b490e8196f060ee8d41fa94d4dbf69c8609a28e5ca2ebb7bb24906c03df14e8325dcd68033ee9a9dcf41a55d2ae116f8038e8df9bec8cd4571",
			  "v": "00",
			  "r": "d6651021e76ee9cb204728db3156decf5a91460b53343570e98e3a3860acaf4a",
			  "s": "1ab8ae3bad1e8ba53e1fedad5f20dc99324eff845df7276e6aeb1af92063bb23",
			  "value": "0",
			  "input": "0xbc6c4aa90000000000000000000000000000000000000000000000000000000000000010000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb000000000000000000000000000000000000000000000000000000000000271000000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000",
			  "functionHash": "0xbc6c4aa9",
			  "functionName": "sellV3",
			  "decodedInputs": {
				"amountIn": "16",
				"token": "******************************************",
				"to": "******************************************",
				"fee": "10000",
				"uuid": "00000000-0000-0000-0000-000000000000",
				"feePercentage": "100",
				"referralFeePercentage": "0"
			  }
			}
		 */
		every {
			web3jWrapper.sendRawTransactionAsync(
				signedTransaction = "0x02f901ae0101843b9aca00843b9acfdc0f94daed7d2d1287bac5bf53fec60f617def24a7ba1380b90144bc6c4aa90000000000000000000000000000000000000000000000000000000000000010000000000000000000000000046bbabe36c18422303ab83adb74bcdaa4e8ee1b000000000000000000000000d356504bbf10204ade9ab6afe8ca8c7252f47ccb000000000000000000000000000000000000000000000000000000000000271000000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002430303030303030302d303030302d303030302d303030302d30303030303030303030303000000000000000000000000000000000000000000000000000000000c080a0d6651021e76ee9cb204728db3156decf5a91460b53343570e98e3a3860acaf4aa01ab8ae3bad1e8ba53e1fedad5f20dc99324eff845df7276e6aeb1af92063bb23",
			)
		} just Runs

		every { web3jWrapper.estimateGas(any()) } returns BigInteger.valueOf(10)

		val result = commandBus(
			SellTokenCommand(
				userId = 1.toUUID(),
				walletId = 1000.toUUID(),
				tokenAddress = AddressWrapper("******************************************"),
				toSellNativeAmount = NativeAmount(BigDecimal("0.000000000000000016")),
				dexPairInfo = GetDex.UniswapV3(
					pairAddress = AddressWrapper("******************************************"),
					fee = UniswapV3Fee.FEE_10000,
				),
			),
		)

		with(result.txHashes) {
			size shouldBe 1
			this[0] shouldBe TxHash("0xdd2394b3f029e18a1e8d41d453ca5e3aa0d42687b3d789bba7aefdbb04ca12a6")
		}
	}

	@Test
	fun `should throw when user using different user wallet`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 2.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = BigInteger.valueOf(18),
		)

		shouldThrow<WalletNotFoundException> {
			commandBus(
				SellTokenCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					toSellNativeAmount = NativeAmount(BigDecimal("0.000000000000000001")),
					dexPairInfo = GetDex.UniswapV3(
						pairAddress = AddressWrapper("******************************************"),
						fee = UniswapV3Fee.FEE_10000,
					),
				),
			)
		}
	}

	@ParameterizedTest
	@ValueSource(longs = [-1, 0])
	fun `should throw when trying to sell zero amount of tokens`(tokensToSell: Long) {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 1000.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = BigInteger.valueOf(18),
		)

		val toSellNative = BaseAmount(BigInteger.valueOf(tokensToSell)).toNative(Chain.EVM_MAINNET)
		shouldThrow<ConstraintViolationException> {
			commandBus(
				SellTokenCommand(
					userId = 1.toUUID(),
					walletId = 1000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					toSellNativeAmount = toSellNative,
					dexPairInfo = GetDex.UniswapV3(
						pairAddress = AddressWrapper("******************************************"),
						fee = UniswapV3Fee.FEE_10000,
					),
				),
			)
		}
	}

	@Test
	@Disabled
	fun `should throw when using unsupported chainId`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 1000.toUUID(), userId = 1.toUUID(), chain = Chain.EVM_MAINNET)

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = BigInteger.valueOf(18),
		)

		shouldThrow<UnsupportedChainIdException> {
			commandBus(
				SellTokenCommand(
					userId = 1.toUUID(),
					walletId = 1000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					toSellNativeAmount = NativeAmount(BigDecimal("0.000000000000000001")),
					dexPairInfo = GetDex.UniswapV3(
						pairAddress = AddressWrapper("******************************************"),
						fee = UniswapV3Fee.FEE_10000,
					),
				),
			)
		}
	}

	@Test
	fun `should throw because user has insufficient wallet balance to pay for approval transaction fees`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = BigInteger.valueOf(18),
		)

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // UniswapV2 factory contract
				"0x" + // 0x
					"e6a43905" + // selector
					"000000000000000000000000046BbABE36c18422303aB83AdB74BcDaA4e8Ee1b" + // token address padded to 64
					"000000000000000000000000C02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2", // weth address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000090892282A68DAd38ac77Cec3e80D008210830b5B" // non null address

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // UniswapV2 pair that was found
				"0x0902f1ac", // get reserves selector
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000000186A0" + // 100k in decimal, first chunk is liquidity of token0
			"00000000000000000000000000000000000000000000000000000000000186A0" + // 100k in decimal, second chunk is liquidity of token1
			"0000000000000000000000000000000000000000000000000000000000000016" // there is also third param, but we ignore it

		// call to token contract to see current allowance balance
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"dd62ed3e" + // allowance selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB" + // wallet address padded to 64
					"000000000000000000000000Daed7d2D1287BaC5bF53fEC60F617Def24a7BA13", // Fatbot router address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x0"

		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getWalletBalance(any()) } returns 1.toBigInteger()
		every { web3jWrapper.getBaseFeePerGas() } returns 100.toBigInteger()
		every { web3jWrapper.getBlockNumber() } returns 99.toBigInteger()

		shouldThrow<InsufficientFundsForTransactionFeesException> {
			commandBus(
				SellTokenCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					toSellNativeAmount = NativeAmount(BigDecimal("0.000000000000000001")),
					dexPairInfo = GetDex.UniswapV3(
						pairAddress = AddressWrapper("******************************************"),
						fee = UniswapV3Fee.FEE_10000,
					),
				),
			)
		}
	}

	@Test
	fun `should throw because user has insufficient wallet balance to pay for transaction fees`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = BigInteger.valueOf(18),
		)

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // UniswapV2 factory contract
				"0x" + // 0x
					"e6a43905" + // selector
					"000000000000000000000000046BbABE36c18422303aB83AdB74BcDaA4e8Ee1b" + // token address padded to 64
					"000000000000000000000000C02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2", // weth address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000090892282A68DAd38ac77Cec3e80D008210830b5B" // non null address

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // UniswapV2 pair that was found
				"0x0902f1ac", // get reserves selector
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000000186A0" + // 100k in decimal, first chunk is liquidity of token0
			"00000000000000000000000000000000000000000000000000000000000186A0" + // 100k in decimal, second chunk is liquidity of token1
			"0000000000000000000000000000000000000000000000000000000000000016" // there is also third param, but we ignore it

		// call to token contract to see current allowance balance
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"dd62ed3e" + // allowance selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB" + // wallet address padded to 64
					"000000000000000000000000Daed7d2D1287BaC5bF53fEC60F617Def24a7BA13", // Fatbot router address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x1"

		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getWalletBalance(any()) } returns 400000059999999.toBigInteger()
		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 100.toBigInteger(),
			maxPriorityFee = 1000000000.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.estimateGas(any()) } returns 4000000000000000000.toBigInteger()
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()

		shouldThrow<InsufficientFundsForTransactionFeesException> {
			commandBus(
				SellTokenCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					toSellNativeAmount = NativeAmount(BigDecimal("0.000000000000000001")),
					dexPairInfo = GetDex.UniswapV3(
						pairAddress = AddressWrapper("******************************************"),
						fee = UniswapV3Fee.FEE_10000,
					),
				),
			)
		}
	}

	@Test
	fun `should throw because user has insufficient wallet balance to pay for transaction amount with fees`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2000.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = AddressWrapper("******************************************"),
			tokenDecimals = BigInteger.valueOf(18),
		)

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // UniswapV2 factory contract
				"0x" + // 0x
					"e6a43905" + // selector
					"000000000000000000000000046BbABE36c18422303aB83AdB74BcDaA4e8Ee1b" + // token address padded to 64
					"000000000000000000000000C02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2", // weth address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000090892282A68DAd38ac77Cec3e80D008210830b5B" // non null address

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // UniswapV2 pair that was found
				"0x0902f1ac", // get reserves selector
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00000000000000000000000000000000000000000000000000000000000186A0" + // 100k in decimal, first chunk is liquidity of token0
			"00000000000000000000000000000000000000000000000000000000000186A0" + // 100k in decimal, second chunk is liquidity of token1
			"0000000000000000000000000000000000000000000000000000000000000016" // there is also third param, but we ignore it

		// call to token contract to see current allowance balance
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"dd62ed3e" + // allowance selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB" + // wallet address padded to 64
					"000000000000000000000000Daed7d2D1287BaC5bF53fEC60F617Def24a7BA13", // Fatbot router address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x65"

		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getWalletBalance(any()) } returns 400000060001000.toBigInteger()
		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 100.toBigInteger(),
			maxPriorityFee = 1000000000.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.estimateGas(any()) } returns 25000.toBigInteger()
		// Approval + sell
		every { web3jWrapper.getNonce(any()) } returnsMany listOf(1.toBigInteger(), 2.toBigInteger())

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************", // token address
				"0x" + // 0x
					"70a08231" + // selector
					"000000000000000000000000d356504Bbf10204Ade9Ab6aFE8Ca8C7252f47CcB", // wallet address padded to 64
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x0000000000000000000000000000000000000000000000000000000000000064" // 100

		shouldThrow<InsufficientFundsForTransactionException> {
			commandBus(
				SellTokenCommand(
					userId = 1.toUUID(),
					walletId = 2000.toUUID(),
					tokenAddress = AddressWrapper("******************************************"),
					toSellNativeAmount = NativeAmount(BigDecimal("0.000000000000000101")),
					dexPairInfo = GetDex.UniswapV3(
						pairAddress = AddressWrapper("******************************************"),
						fee = UniswapV3Fee.FEE_10000,
					),
				),
			)
		}
	}
}
