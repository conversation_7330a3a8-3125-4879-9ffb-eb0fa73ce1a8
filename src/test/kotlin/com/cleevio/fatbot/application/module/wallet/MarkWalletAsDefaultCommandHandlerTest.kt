package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.wallet.command.MarkWalletAsDefaultCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.wallet.WalletRepository
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class MarkWalletAsDefaultCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val walletRepository: WalletRepository,
) : IntegrationTest() {

	@Test
	fun `should correctly switch default wallet`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		val wallet1 = integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = 1.toAddress(),
			chain = Chain.EVM_MAINNET,
			walletCount = 0,
		)
		val wallet2 = integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = 2.toAddress(),
			chain = Chain.EVM_MAINNET,
			walletCount = 1,
		)
		val wallet3 = integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = 3.toAddress(),
			chain = Chain.EVM_MAINNET,
			walletCount = 2,
		)

		wallet1.isDefault shouldBe true
		wallet2.isDefault shouldBe false
		wallet3.isDefault shouldBe false

		// and other unrelated wallets
		integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			chain = Chain.EVM_BASE,
			walletCount = 0,
		)
		integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			chain = Chain.SOLANA,
			walletCount = 0,
		)

		// when
		commandBus(MarkWalletAsDefaultCommand(userId = 1.toUUID(), walletId = wallet3.id))

		// then
		val wallets = walletRepository.findAll().sortedBy { it.createdAt }

		wallets[0].isDefault shouldBe false
		wallets[1].isDefault shouldBe false
		wallets[2].isDefault shouldBe true

		// and when
		commandBus(MarkWalletAsDefaultCommand(userId = 1.toUUID(), walletId = wallet2.id))

		// then
		val wallets2 = walletRepository.findAll().sortedBy { it.createdAt }

		wallets2[0].isDefault shouldBe false
		wallets2[1].isDefault shouldBe true
		wallets2[2].isDefault shouldBe false
	}

	@Test
	fun `should work on multiple invocations`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		val wallet1 = integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = 1.toAddress(),
			chain = Chain.EVM_MAINNET,
			walletCount = 0,
		)
		val wallet2 = integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = 2.toAddress(),
			chain = Chain.EVM_MAINNET,
			walletCount = 1,
		)
		val wallet3 = integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = 3.toAddress(),
			chain = Chain.EVM_MAINNET,
			walletCount = 2,
		)

		wallet1.isDefault shouldBe true
		wallet2.isDefault shouldBe false
		wallet3.isDefault shouldBe false

		// and other unrelated wallets
		integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			chain = Chain.EVM_BASE,
			walletCount = 0,
		)
		integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			chain = Chain.SOLANA,
			walletCount = 0,
		)

		// when
		commandBus(MarkWalletAsDefaultCommand(userId = 1.toUUID(), walletId = wallet3.id))
		commandBus(MarkWalletAsDefaultCommand(userId = 1.toUUID(), walletId = wallet3.id))
		commandBus(MarkWalletAsDefaultCommand(userId = 1.toUUID(), walletId = wallet3.id))

		// then
		val wallets = walletRepository.findAll().sortedBy { it.createdAt }

		wallets[0].isDefault shouldBe false
		wallets[1].isDefault shouldBe false
		wallets[2].isDefault shouldBe true
	}
}
