package com.cleevio.fatbot.application.module.botdraft

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.botdraft.query.GetAllBotDraftsQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GetAllBotDraftsQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should return all bot drafts for user`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())
		integrationTestHelper.getWallet(id = 3.toUUID(), userId = 1.toUUID())

		integrationTestHelper.getFirebaseUser(id = 10.toUUID(), email = "<EMAIL>")
		integrationTestHelper.getWallet(id = 30.toUUID(), userId = 10.toUUID())

		integrationTestHelper.getBotDraft(
			id = 4.toUUID(),
			userId = 1.toUUID(),
		)

		integrationTestHelper.getBotDraft(
			id = 5.toUUID(),
			userId = 1.toUUID(),
		)

		integrationTestHelper.getBotDraft(
			id = 6.toUUID(),
			userId = 10.toUUID(),
		)

		val user1Results = queryBus(GetAllBotDraftsQuery(userId = 1.toUUID()))
		val user2Results = queryBus(GetAllBotDraftsQuery(userId = 10.toUUID()))

		user1Results shouldHaveSize 2
		user2Results shouldHaveSize 1

		user1Results.map { it.id } shouldContainExactlyInAnyOrder listOf(4.toUUID(), 5.toUUID())
		user2Results.map { it.id } shouldContainExactlyInAnyOrder listOf(6.toUUID())
	}
}
