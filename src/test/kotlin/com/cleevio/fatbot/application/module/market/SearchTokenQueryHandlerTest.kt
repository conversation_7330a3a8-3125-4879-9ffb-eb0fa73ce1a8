package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.dexscreener.response.GetTokenPairsInfoResponse
import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.query.SearchTokenQuery
import com.cleevio.fatbot.application.module.token.event.NewEvmTokenInfoCreatedEvent
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.asEventPayload
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.Ordering
import io.mockk.Runs
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.time.Instant

private const val UNISWAP_V3_FEE_SELECTOR = "0xddca3f43"
private const val FATBOT_UTIL_GET_TOKEN_PRICES_SELECTOR = "0xe016d3ab"

private const val FATBOT_UTIL_MAINNET = "******************************************"
private const val FATBOT_UTIL_BSC = "******************************************"

class SearchTokenQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		every {
			etherscanConnector.getVerifiedContractAbi(any(), any())
		} returns EtherscanConnector.GetVerifiedContractAbiResponse(
			message = "OK",
			result = "ABI",
		)

		// Capture EvmTokenInfoCreatedEvent -> Stops backfilling of prices
		every {
			applicationEventMulticaster.multicastEvent(any(), NewEvmTokenInfoCreatedEvent::class.asEventPayload())
		} just Runs

		// Workaround for issue https://github.com/Ninja-Squad/springmockk/issues/85
		// SpykBeans not clearing after test method
		clearAllMocks(answers = false, recordedCalls = true)
	}

	@Test
	fun `should search all EVM chains matching 1 pair while calling dexscreener only once`() {
		// For token decimals
		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = "aaa".toAddress(),
			tokenDecimals = 9.toBigInteger(),
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns "1001.01".toBigDecimal()

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"aaa1".toAddress().getAddressString(),
				UNISWAP_V3_FEE_SELECTOR,
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00064" // hex of V3Pool fee 100

		every {
			dexScreenerConnector.getTokenPairsInfoByAddress("aaa".toAddress())
		} returns GetTokenPairsInfoResponse(
			pairs = listOf(
				getDefaultTokenPairResponse(
					chainId = "ethereum",
					dexId = "uniswap",
					labels = listOf("v3"),
					pairAddress = "aaa1".toAddress(),
				),
			),
		)

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				FATBOT_UTIL_MAINNET,
				FATBOT_UTIL_GET_TOKEN_PRICES_SELECTOR +
					"0000000000000000000000000000000000000000000000000000000000000020" +
					"0000000000000000000000000000000000000000000000000000000000000001" +
					"000000000000000000000000aaa0000000000000000000000000000000000000",

				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			// 3084000000000000 decimal in Wei, which is 0.003084 ETH
			"000000000000000000000000000000000000000000000000000AF4E1B47CC000"

		val result = queryBus(SearchTokenQuery(tokenAddress = "aaa".toAddress()))

		result shouldHaveSize 1
		result.single().run {
			chain shouldBe Chain.EVM_MAINNET
		}

		verify(exactly = 1) {
			dexScreenerConnector.getTokenPairsInfoByAddress("aaa".toAddress())
		}

		val allChainAddresses = chainProperties.enabledEvmChains.map { "aaa".toAddress().toChainAddress(it) }

		verify(ordering = Ordering.UNORDERED, exactly = 1) {
			allChainAddresses.forEach { getTokenDetailDexScreener(it) }
		}
	}

	@Test
	fun `should search all EVM chains and match 2 pairs while calling dexscreener only once`() {
		// For token decimals
		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = "aaa".toAddress(),
			tokenDecimals = 9.toBigInteger(),
		)

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_BSC,
			tokenAddress = "aaa".toAddress(),
			tokenDecimals = 9.toBigInteger(),
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns "1001.01".toBigDecimal()

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				"aaa1".toAddress().getAddressString(),
				UNISWAP_V3_FEE_SELECTOR,
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x00064" // hex of V3Pool fee 100

		every {
			dexScreenerConnector.getTokenPairsInfoByAddress("aaa".toAddress())
		} returns GetTokenPairsInfoResponse(
			pairs = listOf(
				getDefaultTokenPairResponse(
					chainId = "ethereum",
					dexId = "uniswap",
					labels = listOf("v3"),
					pairAddress = "aaa1".toAddress(),
				),
				getDefaultTokenPairResponse(
					chainId = "bsc",
					dexId = "pancakeswap",
					labels = listOf("v2"),
					pairAddress = "bbb1".toAddress(),
				),
			),
		)
		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				FATBOT_UTIL_MAINNET,
				FATBOT_UTIL_GET_TOKEN_PRICES_SELECTOR +
					"0000000000000000000000000000000000000000000000000000000000000020" +
					"0000000000000000000000000000000000000000000000000000000000000001" +
					"000000000000000000000000aaa0000000000000000000000000000000000000",

				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			// 3084000000000000 Wei, which is 0.003084 ETH
			"000000000000000000000000000000000000000000000000000AF4E1B47CC000"

		every {
			web3jWrapper.getReadOnlyTransactionManager().sendCall(
				FATBOT_UTIL_BSC,
				FATBOT_UTIL_GET_TOKEN_PRICES_SELECTOR +
					"0000000000000000000000000000000000000000000000000000000000000020" +
					"0000000000000000000000000000000000000000000000000000000000000001" +
					"000000000000000000000000aaa0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			// 123456 Wei, which is 0.000000000000123456 ETH
			"000000000000000000000000000000000000000000000000000000000001E240"

		val result = queryBus(SearchTokenQuery(tokenAddress = "aaa".toAddress()))

		result shouldHaveSize 2
		result.map { it.chain } shouldContainExactlyInAnyOrder listOf(Chain.EVM_MAINNET, Chain.EVM_BSC)

		result.single { it.chain == Chain.EVM_MAINNET }.run {
			priceUsd shouldBeEqualComparingTo "3.08711484".toBigDecimal()
		}

		result.single { it.chain == Chain.EVM_BSC }.run {
			priceUsd shouldBe "0.00000000012358069056".toBigDecimal()
		}

		verify(exactly = 1) {
			dexScreenerConnector.getTokenPairsInfoByAddress("aaa".toAddress())
		}

		val allChainAddresses = chainProperties.enabledEvmChains.map { "aaa".toAddress().toChainAddress(it) }

		verify(ordering = Ordering.UNORDERED, exactly = 1) {
			allChainAddresses.forEach { getTokenDetailDexScreener(it) }
		}
	}
}

private fun getDefaultTokenPairResponse(
	chainId: String,
	dexId: String,
	labels: List<String>?,
	pairAddress: AddressWrapper,
) = GetTokenPairsInfoResponse.Pair(
	chainId = chainId,
	dexId = dexId,
	url = "https://dexscreener.com/ethereum/******************************************",
	pairAddress = pairAddress.getAddressString(),
	baseToken = GetTokenPairsInfoResponse.Pair.Token(
		address = AddressWrapper("******************************************"),
		name = "Uniswap",
		symbol = "UNI",
	),
	quoteToken = GetTokenPairsInfoResponse.Pair.Token(
		address = AddressWrapper("******************************************"),
		name = "Wrapper Ether",
		symbol = "WETH",
	),
	labels = labels,
	volume = GetTokenPairsInfoResponse.Pair.TimeIntervals(
		hours24 = BigDecimal("9183404.54"),
		hours06 = BigDecimal("5982758.78"),
		hours01 = BigDecimal("288870.23"),
		minutes05 = BigDecimal("12778.35"),
	),
	priceChange = GetTokenPairsInfoResponse.Pair.TimeIntervals(
		hours24 = BigDecimal("6.94"),
		hours06 = BigDecimal("2.52"),
		hours01 = BigDecimal("0.18"),
		minutes05 = BigDecimal("0.09"),
	),
	fullyDilutedValue = BigDecimal("8126763163"),
	marketCap = BigDecimal("6126023505"),
	liquidity = GetTokenPairsInfoResponse.Pair.Liquidity(
		usd = BigDecimal("27458979.31"),
		base = BigDecimal("2358945"),
		quote = BigDecimal("3145.4293"),
	),
	transactions = GetTokenPairsInfoResponse.Pair.Transactions(
		hours24 = GetTokenPairsInfoResponse.Pair.Transactions.BuySellInfo(
			buys = 218,
			sells = 198,
		),
		hours06 = GetTokenPairsInfoResponse.Pair.Transactions.BuySellInfo(
			buys = 78,
			sells = 95,
		),
		hours01 = GetTokenPairsInfoResponse.Pair.Transactions.BuySellInfo(
			buys = 10,
			sells = 8,
		),
		minutes05 = GetTokenPairsInfoResponse.Pair.Transactions.BuySellInfo(
			buys = 1,
			sells = 0,
		),
	),
	pairCreatedAtMillis = Instant.parse("2024-08-29T10:00:01Z").toEpochMilli(),
	info = null,
)
