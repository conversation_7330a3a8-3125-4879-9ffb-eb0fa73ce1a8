package com.cleevio.fatbot.application.module.botleaderboard.service

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.evm.GetTokenPricesEVM
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.botleaderboard.BotLeaderboardSnapshotRepository
import com.cleevio.fatbot.getPrivatePropertyOfProxy
import com.cleevio.fatbot.setAndReturnPrivateProperty
import com.cleevio.fatbot.toUUID
import com.github.benmanes.caffeine.cache.AsyncCache
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import kotlin.test.Test

class BotLeaderboardSnapshotServiceTest(
	@Autowired private val botLeaderboardSnapshotService: BotLeaderboardSnapshotService,
	@Autowired private val botLeaderboardSnapshotRepository: BotLeaderboardSnapshotRepository,
) : IntegrationTest() {

	@Test
	fun `should create leaderboard snapshots for profitable bots`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getFile(id = 31.toUUID())
		integrationTestHelper.getFile(id = 32.toUUID())
		integrationTestHelper.getFile(id = 33.toUUID())

		integrationTestHelper.getBot(
			id = 100.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 31.toUUID(),
			name = "Bot1",
		)
		integrationTestHelper.getBot(
			id = 101.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 32.toUUID(),
			name = "Bot2",
		)
		integrationTestHelper.getBot(
			id = 102.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 33.toUUID(),
			name = "Bot3",
		)

		integrationTestHelper.getBotWallet(
			id = 200.toUUID(),
			botId = 100.toUUID(),
			chain = Chain.SOLANA,
			beforeSave = {
				setAndReturnPrivateProperty("balance", BigInteger("10000000000"))
				setAndReturnPrivateProperty("acquisitionValueUsd", BigDecimal("13.5"))
			},
		)
		integrationTestHelper.getBotWallet(
			id = 201.toUUID(),
			botId = 101.toUUID(),
			chain = Chain.SOLANA,
			beforeSave = {
				setAndReturnPrivateProperty("balance", BigInteger("15000000000"))
				setAndReturnPrivateProperty("acquisitionValueUsd", BigDecimal("15.3"))
			},
		)
		integrationTestHelper.getBotWallet(
			id = 202.toUUID(),
			botId = 102.toUUID(),
			chain = Chain.SOLANA,
			beforeSave = {
				setAndReturnPrivateProperty("balance", BigInteger("9000000000"))
				setAndReturnPrivateProperty("acquisitionValueUsd", BigDecimal("20.5"))
			},
		)

		integrationTestHelper.getFile(id = 34.toUUID())
		integrationTestHelper.getFile(id = 35.toUUID())

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = AddressWrapper("FYxc7ntfTjV2c86oF4dDp8yYiyNLAR5rdhFhNGSMpump"),
			name = "ADHD",
			symbol = "ADHD",
			imageFileId = 34.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump"),
			name = "yolo",
			symbol = "yolo",
			imageFileId = 35.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 400.toUUID(),
			botWalletId = 200.toUUID(),
			tokenAddress = AddressWrapper("FYxc7ntfTjV2c86oF4dDp8yYiyNLAR5rdhFhNGSMpump"),
			profitTargetFraction = BigDecimal("20.0"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234567,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "50".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)
		integrationTestHelper.getBotMarketPosition(
			id = 401.toUUID(),
			botWalletId = 200.toUUID(),
			tokenAddress = AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump"),
			profitTargetFraction = BigDecimal("31.83"),
			stopLossFraction = BigDecimal("21.22"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234568,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "15".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
				setSellDataFromChain(
					amountOfTokensSold = "15".toBigInteger(),
					amountOfBaseCurrencyReceived = "20".toBigInteger(),
					fee = BigInteger("1"),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)
		integrationTestHelper.getBotMarketPosition(
			id = 402.toUUID(),
			botWalletId = 201.toUUID(),
			tokenAddress = AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump"),
			profitTargetFraction = BigDecimal("20.0"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234567,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "50".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2.0")

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(
						AddressWrapper("FYxc7ntfTjV2c86oF4dDp8yYiyNLAR5rdhFhNGSMpump")
							.toChainAddress(chain = Chain.SOLANA),
						BaseAmount(BigInteger("100")),
					)
					put(
						AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump")
							.toChainAddress(chain = Chain.SOLANA),
						BaseAmount(BigInteger("200")),
					)
				},
			)

		// when
		botLeaderboardSnapshotService.createLeaderboardSnapshot()

		// then
		val snapshots = botLeaderboardSnapshotRepository.findAll()
		snapshots.size shouldBe 2
		snapshots.map { it.botId } shouldContainExactlyInAnyOrder listOf(100.toUUID(), 101.toUUID())
		val firstBotSnapshot = snapshots.find { it.botId == 100.toUUID() }
		firstBotSnapshot shouldNotBe null
		firstBotSnapshot!!.botId shouldBe 100.toUUID()
		firstBotSnapshot.profitValueUsd shouldBeEqualComparingTo BigDecimal("6.5040785600100000")
		firstBotSnapshot.profitValueFraction shouldBeEqualComparingTo BigDecimal("0.481783597037777777777777777777778")
		val secondBotSnapshot = snapshots.find { it.botId == 101.toUUID() }
		secondBotSnapshot shouldNotBe null
		secondBotSnapshot!!.botId shouldBe 101.toUUID()
		secondBotSnapshot.profitValueUsd shouldBeEqualComparingTo BigDecimal("14.7040785600200000")
		secondBotSnapshot.profitValueFraction shouldBeEqualComparingTo BigDecimal("0.961050886275816993464052287581699")
	}
}
