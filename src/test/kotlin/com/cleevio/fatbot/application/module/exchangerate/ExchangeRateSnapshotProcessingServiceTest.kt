package com.cleevio.fatbot.application.module.exchangerate

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.domain.exchangerate.ExchangeRateSnapshotRepository
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.temporal.ChronoUnit

class ExchangeRateSnapshotProcessingServiceTest(
	@Autowired private val exchangeRateSnapshotProcessingService: ExchangeRateSnapshotProcessingService,
	@Autowired private val exchangeRateRateSnapshotRepository: ExchangeRateSnapshotRepository,
	@Autowired private val clock: IntegrationTestClock,
) : IntegrationTest() {

	fun rates0(currency: CryptoCurrency) = when (currency) {
		CryptoCurrency.ETH -> BigDecimal("2000.01")
		CryptoCurrency.BNB -> BigDecimal("12332")
		CryptoCurrency.SOL -> BigDecimal("120.41")
	}

	fun rates1(currency: CryptoCurrency) = when (currency) {
		CryptoCurrency.ETH -> BigDecimal("2002.01")
		CryptoCurrency.BNB -> BigDecimal("12332")
		CryptoCurrency.SOL -> BigDecimal("122.41")
	}

	fun rates2(currency: CryptoCurrency) = when (currency) {
		CryptoCurrency.ETH -> BigDecimal("1982.14")
		CryptoCurrency.BNB -> BigDecimal("12332")
		CryptoCurrency.SOL -> BigDecimal("116.72")
	}

	@Test
	fun `should create new snapshots and delete old ones`() {
		CryptoCurrency.entries.forEach {
			every { coinbaseConnector.getUsdExchangeRate(it) } returns rates0(it)
		}

		val time0 = clock.currentTime()
		// Process first snapshots at rate0 and time0
		exchangeRateSnapshotProcessingService.processSnapshots()

		CryptoCurrency.entries.forEach {
			every { coinbaseConnector.getUsdExchangeRate(it) } returns rates1(it)
		}

		clock.advanceBy(12, ChronoUnit.HOURS)
		val time1 = clock.currentTime()
		// Process second snapshots at rate1 and time1
		exchangeRateSnapshotProcessingService.processSnapshots()

		val initialSnapshots = exchangeRateRateSnapshotRepository.findAll()

		val time0InitialSnapshots = initialSnapshots.filter { it.validAt == time0 }
		val time1InitialSnapshots = initialSnapshots.filter { it.validAt == time1 }

		// Snapshots should consist of only 2 * CryptoCurrency
		initialSnapshots shouldHaveSize (2 * CryptoCurrency.entries.size)
		time0InitialSnapshots shouldHaveSize CryptoCurrency.entries.size
		time1InitialSnapshots shouldHaveSize CryptoCurrency.entries.size

		// Check that the rates are correctly assigned
		time0InitialSnapshots.forEach {
			it.exchangeRateUsd shouldBe rates0(it.currency)
		}
		time1InitialSnapshots.forEach {
			it.exchangeRateUsd shouldBe rates1(it.currency)
		}

		CryptoCurrency.entries.forEach {
			every { coinbaseConnector.getUsdExchangeRate(it) } returns rates2(it)
		}

		clock.advanceBy(30, ChronoUnit.HOURS)
		val time2 = clock.currentTime()
		// Process third snapshots at rate2 and time2
		exchangeRateSnapshotProcessingService.processSnapshots()

		val finalSnapshots = exchangeRateRateSnapshotRepository.findAll()

		val time1FinalSnapshots = finalSnapshots.filter { it.validAt == time1 }
		val time2FinalSnapshots = finalSnapshots.filter { it.validAt == time2 }

		// By this time, snapshots with time0 should be deleted
		finalSnapshots shouldHaveSize 2 * CryptoCurrency.entries.size

		// time1 snapshots are still in the age threshold
		time1FinalSnapshots shouldHaveSize CryptoCurrency.entries.size
		time2FinalSnapshots shouldHaveSize CryptoCurrency.entries.size

		// Check that the rates are correctly assigned
		time1FinalSnapshots.forEach {
			it.exchangeRateUsd shouldBe rates1(it.currency)
		}
		time2FinalSnapshots.forEach {
			it.exchangeRateUsd shouldBe rates2(it.currency)
		}
	}
}
