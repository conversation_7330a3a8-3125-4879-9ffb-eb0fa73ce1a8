package com.cleevio.fatbot.application.module.exchangerate

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.verifySequence
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.temporal.ChronoUnit

/**
Note: Before each integrationTestClock altercation following a call to [underTest], a miniature (1ms) [Thread.sleep]
call is made to avoid a potential race condition between the cache async executor thread and the main thread.

The ideal solution is to configure the executor to launch tasks on the main thread
(as described by the Caffeine documentation).

The [Thread.sleep] solution is used here as a 'hot-fix' and should be swapped to the one described above in the future
The 1ms delay is a value found by manual testing buffered by ~35%.
 */
class GetUsdExchangeRateTest(
	@Autowired private val underTest: GetUsdExchangeRate,
	@Autowired private val integrationTestClock: IntegrationTestClock,
) : IntegrationTest() {

	@Test
	fun `should fetch and cache exchange rate`() {
		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("123.45")

		underTest(cryptoCurrency = CryptoCurrency.ETH) shouldBe BigDecimal("123.45")
		underTest(cryptoCurrency = CryptoCurrency.ETH) shouldBe BigDecimal("123.45")

		// should be called just once during the initial cache load
		verifySequence {
			coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH)
		}
	}

	@Test
	fun `should cache new exchange rate because cache has expired`() {
		every { coinbaseConnector.getUsdExchangeRate(any()) } returnsMany listOf(
			BigDecimal("123.45"),
			BigDecimal("234.56"),
		)

		underTest(cryptoCurrency = CryptoCurrency.ETH) shouldBe BigDecimal("123.45")

		Thread.sleep(1)
		integrationTestClock.advanceBy(62, ChronoUnit.SECONDS)

		underTest(cryptoCurrency = CryptoCurrency.ETH) shouldBe BigDecimal("234.56")

		// should be called twice because cache has expired
		verifySequence {
			coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH)
			coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH)
		}
	}

	@Test
	fun `should cache and reload cache when caching exchange rates for multiple currencies`() {
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returnsMany listOf(
			BigDecimal("123.45"),
			BigDecimal("234.56"),
		)
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returnsMany listOf(
			BigDecimal("12.34"),
			BigDecimal("23.45"),
		)

		underTest(cryptoCurrency = CryptoCurrency.ETH) shouldBe BigDecimal("123.45")
		underTest(cryptoCurrency = CryptoCurrency.SOL) shouldBe BigDecimal("12.34")

		Thread.sleep(1)
		integrationTestClock.advanceBy(40, ChronoUnit.SECONDS)

		underTest(cryptoCurrency = CryptoCurrency.ETH) shouldBe BigDecimal("123.45")
		underTest(cryptoCurrency = CryptoCurrency.SOL) shouldBe BigDecimal("12.34")

		Thread.sleep(1)
		integrationTestClock.advanceBy(40, ChronoUnit.SECONDS)

		underTest(cryptoCurrency = CryptoCurrency.ETH) shouldBe BigDecimal("234.56")
		underTest(cryptoCurrency = CryptoCurrency.SOL) shouldBe BigDecimal("23.45")

		verifySequence {
			coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH)
			coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.SOL)
			coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.ETH)
			coinbaseConnector.getUsdExchangeRate(currency = CryptoCurrency.SOL)
		}
	}
}
