@file:Suppress("ktlint")
package com.cleevio.fatbot.application.module.matchmaking

internal val CONFIRMED_BUY_TX_FIXTURE = """
{
	    "meta": {
	      "err": null,
	      "fee": 22500,
	      "innerInstructions": [
	        {
	          "index": 2.0,
	          "instructions": [
	            {
	              "accounts": [
	                0.0,
	                3.0,
	                0.0,
	                8.0,
	                13.0,
	                15.0
	              ],
	              "data": "1",
	              "programIdIndex": 14.0,
	              "stackHeight": 2.0
	            },
	            {
	              "accounts": [
	                8.0
	              ],
	              "data": "84eT",
	              "programIdIndex": 15.0,
	              "stackHeight": 3.0
	            },
	            {
	              "accounts": [
	                0.0,
	                3.0
	              ],
	              "data": "11119os1e9qSs2u7TsThXqkBSRVFxhmYaFKFZ1waB2X7armDmvK3p5GmLdUxYdg3h7QSrL",
	              "programIdIndex": 13.0,
	              "stackHeight": 3.0
	            },
	            {
	              "accounts": [
	                3.0
	              ],
	              "data": "P",
	              "programIdIndex": 15.0,
	              "stackHeight": 3.0
	            },
	            {
	              "accounts": [
	                3.0,
	                8.0
	              ],
	              "data": "6WaJ7Ty2o7my8QdYuivyr6dEVf7eHA5Lc7ULS8hAcy13o",
	              "programIdIndex": 15.0,
	              "stackHeight": 3.0
	            },
	            {
	              "accounts": [
	                0.0,
	                5.0
	              ],
	              "data": "3Bxs43ZMjSRQLs6o",
	              "programIdIndex": 13.0,
	              "stackHeight": 2.0
	            },
	            {
	              "accounts": [
	                9.0,
	                4.0,
	                8.0,
	                1.0,
	                2.0,
	                3.0,
	                0.0,
	                13.0,
	                15.0,
	                12.0,
	                10.0,
	                11.0
	              ],
	              "data": "AJTQ2h9DXrC881s53Nrv9wttmnVXu8i3q",
	              "programIdIndex": 11.0,
	              "stackHeight": 2.0
	            },
	            {
	              "accounts": [
	                2.0,
	                3.0,
	                1.0
	              ],
	              "data": "3tiLL3znuUH5",
	              "programIdIndex": 15.0,
	              "stackHeight": 3.0
	            },
	            {
	              "accounts": [
	                0.0,
	                1.0
	              ],
	              "data": "3Bxs4fVCNtwRLkBy",
	              "programIdIndex": 13.0,
	              "stackHeight": 3.0
	            },
	            {
	              "accounts": [
	                0.0,
	                4.0
	              ],
	              "data": "3Bxs4DFzYmNR8DTu",
	              "programIdIndex": 13.0,
	              "stackHeight": 3.0
	            },
	            {
	              "accounts": [
	                10.0
	              ],
	              "data": "2K7nL28PxCW8ejnyCeuMpbVuk66NvnTWQLPp3eTd5Ve7PcXu2Pbz43a9i4VoCjaXcm5ojPoRxuSdVLHmnvYth4o5un2q65aZfx5GNFthzjuvTvLPghjXxsJTKE48jyEvgLxkFkdG115PGNJBtet35ZRLKNL1uZob4Dh4Umx71XkkCSYiRkXr6Px8yn5u",
	              "programIdIndex": 11.0,
	              "stackHeight": 3.0
	            }
	          ]
	        }
	      ],
	      "preTokenBalances": [
	        {
	          "accountIndex": 2.0,
	          "mint": "3A9YpoNJCVgJyeZqampgUkoQz5FAnrxyREpdgfrzpump",
	          "uiTokenAmount": {
	            "amount": "***************",
	            "decimals": 6,
	            "uiAmount": 7.05944981551972E8,
	            "uiAmountString": "*********.551972"
	          }
	        }
	      ],
	      "postTokenBalances": [
	        {
	          "accountIndex": 2.0,
	          "mint": "3A9YpoNJCVgJyeZqampgUkoQz5FAnrxyREpdgfrzpump",
	          "uiTokenAmount": {
	            "amount": "***************",
	            "decimals": 6,
	            "uiAmount": 7.05926506056314E8,
	            "uiAmountString": "*********.056314"
	          }
	        },
	        {
	          "accountIndex": 3.0,
	          "mint": "3A9YpoNJCVgJyeZqampgUkoQz5FAnrxyREpdgfrzpump",
	          "uiTokenAmount": {
	            "amount": "***********",
	            "decimals": 6,
	            "uiAmount": 18475.495658,
	            "uiAmountString": "18475.495658"
	          }
	        }
	      ],
	      "postBalances": [
	        924381,
	        ***********,
	        2039280,
	        2039280,
	        **************,
	        **********,
	        *********,
	        1,
	        1461600,
	        *********,
	        *********,
	        1141440,
	        1009200,
	        1,
	        *********,
	        *********,
	        1141440
	      ],
	      "preBalances": [
	        3986161,
	        ***********,
	        2039280,
	        0,
	        **************,
	        **********,
	        *********,
	        1,
	        1461600,
	        *********,
	        *********,
	        1141440,
	        1009200,
	        1,
	        *********,
	        *********,
	        1141440
	      ],
	      "status": {
	        "ok": null
	      },
	      "logMessages": [
	        "Program ComputeBudget****************************11 invoke [1]",
	        "Program ComputeBudget****************************11 success",
	        "Program ComputeBudget****************************11 invoke [1]",
	        "Program ComputeBudget****************************11 success",
	        "Program fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u invoke [1]",
	        "Program log: Instruction: BuyPumpfun",
	        "Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]",
	        "Program log: Create",
	        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]",
	        "Program log: Instruction: GetAccountDataSize",
	        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1569 of 158760 compute units",
	        "Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=",
	        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
	        "Program ******************************** invoke [3]",
	        "Program ******************************** success",
	        "Program log: Initialize the associated token account",
	        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]",
	        "Program log: Instruction: InitializeImmutableOwner",
	        "Program log: Please upgrade to SPL Token 2022 for immutable owner support",
	        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 152173 compute units",
	        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
	        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]",
	        "Program log: Instruction: InitializeAccount3",
	        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4188 of 148291 compute units",
	        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
	        "Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20337 of 164157 compute units",
	        "Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success",
	        "Program ******************************** invoke [2]",
	        "Program ******************************** success",
	        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [2]",
	        "Program log: Instruction: Buy",
	        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]",
	        "Program log: Instruction: Transfer",
	        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4645 of 103224 compute units",
	        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
	        "Program ******************************** invoke [3]",
	        "Program ******************************** success",
	        "Program ******************************** invoke [3]",
	        "Program ******************************** success",
	        "Program data: vdt/007mYe4gD0RlUwyRLxsAePn+w8c63DcLTiIV7jPdNh7z2l7T/+b0DgAAAAAA6rA5TQQAAAABjEek6k+TH7sQq+P6wFD8Xh20ggi+FTMnZaz3Bot9e7I4IhtoAAAAAANaOp8JAAAAehqz9W3EAgADrhajAgAAAHqCoKncxQEA",
	        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [3]",
	        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 1997 of 90675 compute units",
	        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P success",
	        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 36905 of 124808 compute units",
	        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P success",
	        "Program data: kOvr5lzT9wYGm4hX/quBhPtof2NGGMA12sQ53BrrO1WYoPAAAAAAASAPRGVTDJEvGwB4+f7DxzrcNwtOIhXuM902HvPaXtP/MBsPAAAAAADqsDlNBAAAABAnAAAAAAAAAAAAAAAAAAAkAAAAMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAw",
	        "Program fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u consumed 88311 of 174700 compute units",
	        "Program fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u success"
	      ]
	    },
	    "slot": *********,
	    "transaction": {
	      "message": {
	        "accountKeys": [
	          "ASbSXmVzpvhJ99TPdmnWceo7AE4TkL4uQZAae7Uofrcq",
	          "GSRySR6cGVC3USYe9pjqnW2i4yY8By5ChJSs7yrVZiwc",
	          "3UXnQhAer9dEJxaLBBDRDSRm7fDiwYrz26YCp7AHL1rz",
	          "C8xP61xds3PP4b3UwrDXJYp5U4ndQ1A3H17DiEN36BvM",
	          "62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV",
	          "6cb958ECc3ZsY4Ssxztnsb1uPfYXbY6NmizYzHg73arm",
	          "8En4Vb7BeRBDa1Ud3PrQfpiaNVwUfgZC2p6wYkfyiCDy",
	          "ComputeBudget****************************11",
	          "3A9YpoNJCVgJyeZqampgUkoQz5FAnrxyREpdgfrzpump",
	          "4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf",
	          "Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1",
	          "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
	          "SysvarRent********************************1",
	          "********************************",
	          "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
	          "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
	          "fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u"
	        ],
	        "header": {
	          "numReadonlySignedAccounts": 0,
	          "numReadonlyUnsignedAccounts": 10,
	          "numRequiredSignatures": 1
	        },
	        "instructions": [
	          {
	            "accounts": [],
	            "data": "HzQDd1",
	            "programIdIndex": 7
	          },
	          {
	            "accounts": [],
	            "data": "3gJqkocMWaMm",
	            "programIdIndex": 7
	          },
	          {
	            "accounts": [
	              0,
	              1,
	              2,
	              8,
	              3,
	              4,
	              9,
	              10,
	              5,
	              6,
	              11,
	              12,
	              13,
	              14,
	              15
	            ],
	            "data": "Tqbrsj55Vzc7NktDaBp3xansTLzzC4FJ4HrY8zLyEp7xFJ5vvjqAfuZaxb9cx6F7wTrQwegfiwcYNx2KaDC7vBX3VHcBta5BgdVp3swJNvxpP",
	            "programIdIndex": 16
	          }
	        ],
	        "recentBlockhash": "AhrbuNGi3145YgZV1LCS9tgPxW7iY1DQ4k3aD81iKxdS"
	      },
	      "signatures": [
	        "2eYN6s3tzoiUXCqBB1q4MfBFPx66F9iryBwPTwCY4zCSmpY66mhz7rLngnj4wwAJkKHZewaNeKQTBd5mubtftu13"
	      ],
	      "blocktime": null
	    }
	  }
""".trimIndent()

internal val CONFIRMED_SELL_TX_FIXTURE = """
	{
    "meta": {
      "err": null,
      "fee": 10500,
      "innerInstructions": [
        {
          "index": 2.0,
          "instructions": [
            {
              "accounts": [
                9.0,
                4.0,
                8.0,
                1.0,
                2.0,
                3.0,
                0.0,
                13.0,
                14.0,
                15.0,
                10.0,
                11.0
              ],
              "data": "5jRcjdixRUDj3pjtkHgo3SFGFQgGFWVPD",
              "programIdIndex": 11.0,
              "stackHeight": 2.0
            },
            {
              "accounts": [
                3.0,
                2.0,
                0.0
              ],
              "data": "3tiLL3znuUH5",
              "programIdIndex": 15.0,
              "stackHeight": 3.0
            },
            {
              "accounts": [
                10.0
              ],
              "data": "2K7nL28PxCW8ejnyCeuMpbVuk66NvnTWQLPp3eTd5Ve7PcXu2Pbz43a9i4VoCjaXckyX6gzwJcPC5xiaoDobtgAuWcroTHsJv9UZt2i9557w5PP1KCLmmhKwSsdVdLbdf4rJo1a1Sd4KAuU5hXyMrCYpR4dFUeK65EucQJnk9npSveMt9ZJGa1RWNixo",
              "programIdIndex": 11.0,
              "stackHeight": 3.0
            },
            {
              "accounts": [
                3.0,
                0.0,
                0.0
              ],
              "data": "A",
              "programIdIndex": 15.0,
              "stackHeight": 2.0
            },
            {
              "accounts": [
                0.0,
                5.0
              ],
              "data": "3Bxs45491qW4DWMu",
              "programIdIndex": 13.0,
              "stackHeight": 2.0
            }
          ]
        }
      ],
      "preTokenBalances": [
        {
          "accountIndex": 2.0,
          "mint": "3A9YpoNJCVgJyeZqampgUkoQz5FAnrxyREpdgfrzpump",
          "uiTokenAmount": {
            "amount": "***************",
            "decimals": 6,
            "uiAmount": 8.84254443729436E8,
            "uiAmountString": "*********.729436"
          }
        },
        {
          "accountIndex": 3.0,
          "mint": "3A9YpoNJCVgJyeZqampgUkoQz5FAnrxyREpdgfrzpump",
          "uiTokenAmount": {
            "amount": "***********",
            "decimals": 6,
            "uiAmount": 18475.495658,
            "uiAmountString": "18475.495658"
          }
        }
      ],
      "postTokenBalances": [
        {
          "accountIndex": 2.0,
          "mint": "3A9YpoNJCVgJyeZqampgUkoQz5FAnrxyREpdgfrzpump",
          "uiTokenAmount": {
            "amount": "*********225094",
            "decimals": 6,
            "uiAmount": 8.84272919225094E8,
            "uiAmountString": "*********.225094"
          }
        }
      ],
      "postBalances": [
        3589260,
        **********,
        2039280,
        0,
        **************,
        **********,
        *********,
        1,
        1461600,
        *********,
        *********,
        1141440,
        1009200,
        1,
        *********,
        *********,
        1141440
      ],
      "preBalances": [
        924383,
        **********,
        2039280,
        2039280,
        **************,
        **********,
        *********,
        1,
        1461600,
        *********,
        *********,
        1141440,
        1009200,
        1,
        *********,
        *********,
        1141440
      ],
      "status": {
        "ok": null
      },
      "logMessages": [
        "Program ComputeBudget****************************11 invoke [1]",
        "Program ComputeBudget****************************11 success",
        "Program ComputeBudget****************************11 invoke [1]",
        "Program ComputeBudget****************************11 success",
        "Program fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u invoke [1]",
        "Program log: Instruction: SellAllPumpfun",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [2]",
        "Program log: Instruction: Sell",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]",
        "Program log: Instruction: Transfer",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4645 of 65961 compute units",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
        "Program data: vdt/007mYe4gD0RlUwyRLxsAePn+w8c63DcLTiIV7jPdNh7z2l7T/zXnCQAAAAAA6rA5TQQAAAAAjEek6k+TH7sQq+P6wFD8Xh20ggi+FTMnZaz3Bot9e7KuIhtoAAAAAJnaT9QHAAAABrNNeKJmAwCZLizYAAAAAAYbOywRaAIA",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [3]",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 1997 of 57460 compute units",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P success",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 32804 of 87492 compute units",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P success",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [2]",
        "Program log: Instruction: CloseAccount",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 2916 of 50637 compute units",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
        "Program ******************************** invoke [2]",
        "Program ******************************** success",
        "Program data: kOvr5lzT9wYgD0RlUwyRLxsAePn+w8c63DcLTiIV7jPdNh7z2l7T/wabiFf+q4GE+2h/Y0YYwDXaxDncGus7VZig8AAAAAAB6rA5TQQAAADBtAkAAAAAABkZAAAAAAAAAAAAAAAAAAAkAAAAMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAw",
        "Program fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u consumed 65421 of 109700 compute units",
        "Program fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u success"
      ]
    },
    "slot": *********,
    "transaction": {
      "message": {
        "accountKeys": [
          "ASbSXmVzpvhJ99TPdmnWceo7AE4TkL4uQZAae7Uofrcq",
          "GSRySR6cGVC3USYe9pjqnW2i4yY8By5ChJSs7yrVZiwc",
          "3UXnQhAer9dEJxaLBBDRDSRm7fDiwYrz26YCp7AHL1rz",
          "C8xP61xds3PP4b3UwrDXJYp5U4ndQ1A3H17DiEN36BvM",
          "62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV",
          "6cb958ECc3ZsY4Ssxztnsb1uPfYXbY6NmizYzHg73arm",
          "8En4Vb7BeRBDa1Ud3PrQfpiaNVwUfgZC2p6wYkfyiCDy",
          "ComputeBudget****************************11",
          "3A9YpoNJCVgJyeZqampgUkoQz5FAnrxyREpdgfrzpump",
          "4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf",
          "Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1",
          "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
          "SysvarRent********************************1",
          "********************************",
          "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
          "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
          "fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u"
        ],
        "header": {
          "numReadonlySignedAccounts": 0,
          "numReadonlyUnsignedAccounts": 10,
          "numRequiredSignatures": 1
        },
        "instructions": [
          {
            "accounts": [],
            "data": "JbzbGj",
            "programIdIndex": 7
          },
          {
            "accounts": [],
            "data": "3Sy41WEwNLnT",
            "programIdIndex": 7
          },
          {
            "accounts": [
              0,
              1,
              2,
              8,
              3,
              4,
              9,
              10,
              5,
              6,
              11,
              12,
              13,
              14,
              15
            ],
            "data": "2rwGfJ9bMyw6CSxBKUora2DG9or2N9RdbLa9xW1pW5e9pwiWe4W619aPhD7AhdugjU8ZJzJt3gUdvjg2bX5ddYf94JTi4KXkfk3",
            "programIdIndex": 16
          }
        ],
        "recentBlockhash": "3388cK7zw5nV1wRmhD2gf5dFVsfBMi4VbuHwDxS86p7p"
      },
      "signatures": [
        "2X77qXzWm3zvLSmXMDkwvWDPTshEWjkpezwfjempH5V3yA8rzZb9ZboA2LyLrwWmGb6GfTXUszMLg1hzNxQL6KNe"
      ],
      "blocktime": null
    }
  }
""".trimIndent()

internal val SELL_ZERO_AMOUNT_TX_FIXTURE = """
	{
    "meta": {
      "err": {
        "InstructionError": [
          2.0,
          {
            "Custom": 6022.0
          }
        ]
      },
      "fee": 10500,
      "innerInstructions": [
        {
          "index": 2.0,
          "instructions": [
            {
              "accounts": [
                0.0,
                3.0,
                0.0,
                8.0,
                13.0,
                15.0
              ],
              "data": "1",
              "programIdIndex": 14.0,
              "stackHeight": 2.0
            },
            {
              "accounts": [
                8.0
              ],
              "data": "84eT",
              "programIdIndex": 15.0,
              "stackHeight": 3.0
            },
            {
              "accounts": [
                0.0,
                3.0
              ],
              "data": "11119os1e9qSs2u7TsThXqkBSRVFxhmYaFKFZ1waB2X7armDmvK3p5GmLdUxYdg3h7QSrL",
              "programIdIndex": 13.0,
              "stackHeight": 3.0
            },
            {
              "accounts": [
                3.0
              ],
              "data": "P",
              "programIdIndex": 15.0,
              "stackHeight": 3.0
            },
            {
              "accounts": [
                3.0,
                8.0
              ],
              "data": "6a8pgDAK3GD38Ruv1RqP3yXWMdi2z3pEWFuUUkZYJWJQ9",
              "programIdIndex": 15.0,
              "stackHeight": 3.0
            },
            {
              "accounts": [
                9.0,
                4.0,
                8.0,
                1.0,
                2.0,
                3.0,
                0.0,
                13.0,
                14.0,
                15.0,
                10.0,
                11.0
              ],
              "data": "5jRcjdixRUDE4xwW97JdBGmtrZRhCch6P",
              "programIdIndex": 11.0,
              "stackHeight": 2.0
            }
          ]
        }
      ],
      "preTokenBalances": [
        {
          "accountIndex": 2.0,
          "mint": "81DJ9BEvtEwwREWxEgXyNQUbxknnJtMzat353KWJpump",
          "uiTokenAmount": {
            "amount": "***************",
            "decimals": 6,
            "uiAmount": 4.52065178014858E8,
            "uiAmountString": "*********.014858"
          }
        }
      ],
      "postTokenBalances": [
        {
          "accountIndex": 2.0,
          "mint": "81DJ9BEvtEwwREWxEgXyNQUbxknnJtMzat353KWJpump",
          "uiTokenAmount": {
            "amount": "***************",
            "decimals": 6,
            "uiAmount": 4.52065178014858E8,
            "uiAmountString": "*********.014858"
          }
        }
      ],
      "postBalances": [
        ********,
        ***********,
        2039280,
        0,
        **************,
        **********,
        *********,
        1,
        1461600,
        *********,
        *********,
        1141440,
        1009200,
        1,
        *********,
        *********,
        1141440
      ],
      "preBalances": [
        ********,
        ***********,
        2039280,
        0,
        **************,
        **********,
        *********,
        1,
        1461600,
        *********,
        *********,
        1141440,
        1009200,
        1,
        *********,
        *********,
        1141440
      ],
      "status": {
        "ok": null
      },
      "logMessages": [
        "Program ComputeBudget****************************11 invoke [1]",
        "Program ComputeBudget****************************11 success",
        "Program ComputeBudget****************************11 invoke [1]",
        "Program ComputeBudget****************************11 success",
        "Program fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u invoke [1]",
        "Program log: Instruction: SellAllPumpfun",
        "Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]",
        "Program log: Create",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]",
        "Program log: Instruction: GetAccountDataSize",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1569 of 93766 compute units",
        "Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
        "Program ******************************** invoke [3]",
        "Program ******************************** success",
        "Program log: Initialize the associated token account",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]",
        "Program log: Instruction: InitializeImmutableOwner",
        "Program log: Please upgrade to SPL Token 2022 for immutable owner support",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 87179 compute units",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]",
        "Program log: Instruction: InitializeAccount3",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4188 of 83297 compute units",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
        "Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 20337 of 99163 compute units",
        "Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [2]",
        "Program log: Instruction: Sell",
        "Program log: AnchorError thrown in programs/pump/src/lib.rs:330. Error Code: SellZeroAmount. Error Number: 6022. Error Message: Sell zero amount.",
        "Program log: Left: 0",
        "Program log: Right: 0",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 18923 of 63366 compute units",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x1786",
        "Program fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u consumed 65257 of 109700 compute units",
        "Program fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u failed: custom program error: 0x1786"
      ]
    },
    "slot": *********,
    "transaction": {
      "message": {
        "accountKeys": [
          "E181GxnEyMmJARpVLgBiVZ4y8pTAe4xoYzJdFyrVD9yB",
          "5Bwi2Lf4RVMm8H5QvwxriEJn1BdoG1NfCvr7bAWBbRJY",
          "Dd4KD6cQLrm1FfhdmSVRbPURp6zpBvhEytYbbLy8CDjD",
          "5zyFtx6wyjVdhK5yeJrEHxpJkvdA7CCL7f8RTWfsyE3z",
          "62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV",
          "6cb958ECc3ZsY4Ssxztnsb1uPfYXbY6NmizYzHg73arm",
          "8En4Vb7BeRBDa1Ud3PrQfpiaNVwUfgZC2p6wYkfyiCDy",
          "ComputeBudget****************************11",
          "81DJ9BEvtEwwREWxEgXyNQUbxknnJtMzat353KWJpump",
          "4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf",
          "Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1",
          "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
          "SysvarRent********************************1",
          "********************************",
          "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
          "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
          "fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u"
        ],
        "header": {
          "numReadonlySignedAccounts": 0,
          "numReadonlyUnsignedAccounts": 10,
          "numRequiredSignatures": 1
        },
        "instructions": [
          {
            "accounts": [],
            "data": "JbzbGj",
            "programIdIndex": 7
          },
          {
            "accounts": [],
            "data": "3Sy41WEwNLnT",
            "programIdIndex": 7
          },
          {
            "accounts": [
              0,
              1,
              2,
              8,
              3,
              4,
              9,
              10,
              5,
              6,
              11,
              12,
              13,
              14,
              15
            ],
            "data": "2rwGfJ9bMyw6CSxBKUora2DG9or2N9RdbLa9xW1pW5e9pwiWe4W619aPhD7AhdugjU8ZJzJt3gUdvjg2bX5ddYf94JTi4KXkfk3",
            "programIdIndex": 16
          }
        ],
        "recentBlockhash": "FAqpHJnAXYyuAfrqPEeyvYkn2yCC8tbkRLLmqWXKaPZq"
      },
      "signatures": [
        "k4eag8zcbkuZe188xqiPBrv1WcSduGgTvU7Zd4HzoAqg7nVvo76jMD2kRoM7amfTU2rJmi8YY2ZV4FvS8t2KPDr"
      ],
      "blocktime": null
    }
  }
""".trimIndent()

internal val BUY_FAILED_TX_FIXTURE = """
{
    "meta": {
      "err": {
        "InstructionError": [
          2.0,
          {
            "Custom": 2006.0
          }
        ]
      },
      "fee": 22500,
      "innerInstructions": [
        {
          "index": 2.0,
          "instructions": [
            {
              "accounts": [
                0.0,
                3.0,
                0.0,
                8.0,
                13.0,
                15.0
              ],
              "data": "1",
              "programIdIndex": 14.0,
              "stackHeight": 2.0
            },
            {
              "accounts": [
                8.0
              ],
              "data": "84eT",
              "programIdIndex": 15.0,
              "stackHeight": 3.0
            },
            {
              "accounts": [
                0.0,
                3.0
              ],
              "data": "11119os1e9qSs2u7TsThXqkBSRVFxhmYaFKFZ1waB2X7armDmvK3p5GmLdUxYdg3h7QSrL",
              "programIdIndex": 13.0,
              "stackHeight": 3.0
            },
            {
              "accounts": [
                3.0
              ],
              "data": "P",
              "programIdIndex": 15.0,
              "stackHeight": 3.0
            },
            {
              "accounts": [
                3.0,
                8.0
              ],
              "data": "6U8vuGHvoLKFZoci65naMYWyLUvBCcN4WRuGQ7Hcq43LJ",
              "programIdIndex": 15.0,
              "stackHeight": 3.0
            },
            {
              "accounts": [
                0.0,
                5.0
              ],
              "data": "3Bxs43ZMjSRQLs6o",
              "programIdIndex": 13.0,
              "stackHeight": 2.0
            },
            {
              "accounts": [
                9.0,
                4.0,
                8.0,
                1.0,
                2.0,
                3.0,
                0.0,
                13.0,
                15.0,
                12.0,
                10.0,
                11.0
              ],
              "data": "AJTQ2h9DXrC5YYhbo4GkcpjVTw3am6ehM",
              "programIdIndex": 11.0,
              "stackHeight": 2.0
            }
          ]
        }
      ],
      "preTokenBalances": [
        {
          "accountIndex": 2.0,
          "mint": "8kvvvG96urphBs3dc5ixrB5YLurRyD7RRhT94NrYpump",
          "uiTokenAmount": {
            "amount": "***************",
            "decimals": 6,
            "uiAmount": 9.56252571458511E8,
            "uiAmountString": "*********.458511"
          }
        }
      ],
      "postTokenBalances": [
        {
          "accountIndex": 2.0,
          "mint": "8kvvvG96urphBs3dc5ixrB5YLurRyD7RRhT94NrYpump",
          "uiTokenAmount": {
            "amount": "***************",
            "decimals": 6,
            "uiAmount": 9.56252571458511E8,
            "uiAmountString": "*********.458511"
          }
        }
      ],
      "postBalances": [
        ********,
        **********,
        2039280,
        0,
        **************,
        **********,
        *********,
        1,
        1461600,
        *********,
        *********,
        1141440,
        1009200,
        1,
        *********,
        *********,
        1141440
      ],
      "preBalances": [
        ********,
        **********,
        2039280,
        0,
        **************,
        **********,
        *********,
        1,
        1461600,
        *********,
        *********,
        1141440,
        1009200,
        1,
        *********,
        *********,
        1141440
      ],
      "status": {
        "ok": null
      },
      "logMessages": [
        "Program ComputeBudget****************************11 invoke [1]",
        "Program ComputeBudget****************************11 success",
        "Program ComputeBudget****************************11 invoke [1]",
        "Program ComputeBudget****************************11 success",
        "Program fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u invoke [1]",
        "Program log: Instruction: BuyPumpfun",
        "Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL invoke [2]",
        "Program log: Create",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]",
        "Program log: Instruction: GetAccountDataSize",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1569 of 155760 compute units",
        "Program return: TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA pQAAAAAAAAA=",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
        "Program ******************************** invoke [3]",
        "Program ******************************** success",
        "Program log: Initialize the associated token account",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]",
        "Program log: Instruction: InitializeImmutableOwner",
        "Program log: Please upgrade to SPL Token 2022 for immutable owner support",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 1405 of 149173 compute units",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA invoke [3]",
        "Program log: Instruction: InitializeAccount3",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA consumed 4188 of 145291 compute units",
        "Program TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA success",
        "Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL consumed 21837 of 162657 compute units",
        "Program ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL success",
        "Program ******************************** invoke [2]",
        "Program ******************************** success",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [2]",
        "Program log: Instruction: Buy",
        "Program log: AnchorError caused by account: creator_vault. Error Code: ConstraintSeeds. Error Number: 2006. Error Message: A seeds constraint was violated.",
        "Program log: Left:",
        "Program log: SysvarRent********************************1",
        "Program log: Right:",
        "Program log: 2DR3iqRPVThyRLVJnwjPW1qiGWrp8RUFfHVjMbZyhdNc",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 18247 of 123308 compute units",
        "Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x7d6",
        "Program fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u consumed 69639 of 174700 compute units",
        "Program fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u failed: custom program error: 0x7d6"
      ]
    },
    "slot": *********,
    "transaction": {
      "message": {
        "accountKeys": [
          "81EEL6Q13TyjY8cZzdP24YXwz2bPCcnoiz6Ychw1ktuL",
          "A3Cz6z6DWwMXdCAfNcwyagTUHvsgMcPbHzEftudoDPLH",
          "BS6uk23ogBssYLbiVizc5wbG8bvufdVgBjfKVaRf3T6o",
          "3FBFrNYWeK841tbhmP2NpdSk5AWatbBo2EkWkpBBR4Zj",
          "62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV",
          "6cb958ECc3ZsY4Ssxztnsb1uPfYXbY6NmizYzHg73arm",
          "8En4Vb7BeRBDa1Ud3PrQfpiaNVwUfgZC2p6wYkfyiCDy",
          "ComputeBudget****************************11",
          "8kvvvG96urphBs3dc5ixrB5YLurRyD7RRhT94NrYpump",
          "4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf",
          "Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1",
          "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
          "SysvarRent********************************1",
          "********************************",
          "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL",
          "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
          "fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u"
        ],
        "header": {
          "numReadonlySignedAccounts": 0,
          "numReadonlyUnsignedAccounts": 10,
          "numRequiredSignatures": 1
        },
        "instructions": [
          {
            "accounts": [],
            "data": "HzQDd1",
            "programIdIndex": 7
          },
          {
            "accounts": [],
            "data": "3gJqkocMWaMm",
            "programIdIndex": 7
          },
          {
            "accounts": [
              0,
              1,
              2,
              8,
              3,
              4,
              9,
              10,
              5,
              6,
              11,
              12,
              13,
              14,
              15
            ],
            "data": "Tqbrsj55Vzc7NktDaBp3xansTLzzC4FJ4HrY8zLyEp7xFJ5vvjqAfuZaxb9cx6F7wTrQwegfiwcYNx2KaDC7vBX3VHcBta5BgdVp3swJNvxpP",
            "programIdIndex": 16
          }
        ],
        "recentBlockhash": "5jpdydCDuFmRj4xKpcKVQwbFo5n7yEgjKkahYGnniaak"
      },
      "signatures": [
        "45VcfYF6cGY1M3UJYoHN4i6Mszdk3PaGG5par7FdLUeGaE7fzJPVmUtUgx1NF6xwwfxs1tHxgqXQK5CKKSZK6TYw"
      ],
      "blocktime": null
    }
  }
""".trimIndent()