package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.bot.query.GetBotSettingsStatisticsQuery
import com.cleevio.fatbot.application.module.bot.query.GetBotSettingsStatisticsQuery.ValueBucket
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.comparables.shouldBeLessThan
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class GetBotSettingsStatisticsQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should correctly calculate bot setting statistics`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 10.toUUID(), email = "<EMAIL>")
		integrationTestHelper.getFile(id = 20.toUUID())

		integrationTestHelper.getBot(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			name = "test1",
			avatarFileId = 20.toUUID(),
			profitTargetFraction = BigDecimal("0.302"),
			stopLossFraction = BigDecimal("0.154"),
			tradeAmount = BigDecimal("0.053"),
			buyFrequency = 15,
			buyVolume = BigDecimal("0.5"),
			sellVolume = BigDecimal("0.5"),
			numberOfHoldersFrom = null,
			liquidityFromUsd = BigDecimal("9.5"),
			liquidityToUsd = BigDecimal("13.1"),
			sellTransactionFraction = BigDecimal("0.1"),
		)

		integrationTestHelper.getBot(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			name = "test2",
			avatarFileId = 20.toUUID(),
			profitTargetFraction = BigDecimal("0.33"),
			stopLossFraction = BigDecimal("0.145"),
			tradeAmount = BigDecimal("6.00004"),
			buyFrequency = 15,
			buyVolume = BigDecimal("0.8"),
			sellVolume = BigDecimal("0.2"),
			liquidityFromUsd = BigDecimal("11.4"),
			liquidityToUsd = BigDecimal("14.015"),
			sellTransactionFraction = BigDecimal("0.3"),
		)

		integrationTestHelper.getBot(
			id = 4.toUUID(),
			userId = 10.toUUID(),
			name = "test3",
			avatarFileId = 20.toUUID(),
			profitTargetFraction = BigDecimal("0.297"),
			stopLossFraction = BigDecimal("0.152"),
			tradeAmount = BigDecimal("7.3"),
			buyVolume = BigDecimal("0.9"),
			sellVolume = BigDecimal("0.1"),
			liquidityFromUsd = BigDecimal("12.4901"),
			liquidityToUsd = BigDecimal("15.00"),
			sellTransactionFraction = BigDecimal("0.5"),
		)

		val result = queryBus(GetBotSettingsStatisticsQuery())

		result.run {
			liquidityUsd shouldContainExactlyInAnyOrder listOf(
				ValueBucket(from = 0.toBigDecimal(), to = 1.toBigDecimal(), count = 0),
				ValueBucket(from = 1.toBigDecimal(), to = 2.toBigDecimal(), count = 0),
				ValueBucket(from = 2.toBigDecimal(), to = 3.toBigDecimal(), count = 0),
				ValueBucket(from = 3.toBigDecimal(), to = 4.toBigDecimal(), count = 0),
				ValueBucket(from = 4.toBigDecimal(), to = 5.toBigDecimal(), count = 0),
				ValueBucket(from = 5.toBigDecimal(), to = 6.toBigDecimal(), count = 0),
				ValueBucket(from = 6.toBigDecimal(), to = 7.toBigDecimal(), count = 0),
				ValueBucket(from = 7.toBigDecimal(), to = 8.toBigDecimal(), count = 0),
				ValueBucket(from = 8.toBigDecimal(), to = 9.toBigDecimal(), count = 0),
				ValueBucket(from = 9.toBigDecimal(), to = 10.toBigDecimal(), count = 1),
				ValueBucket(from = 10.toBigDecimal(), to = 11.toBigDecimal(), count = 0),
				ValueBucket(from = 11.toBigDecimal(), to = 12.toBigDecimal(), count = 1),
				ValueBucket(from = 12.toBigDecimal(), to = 13.toBigDecimal(), count = 1),
				ValueBucket(from = 13.toBigDecimal(), to = 14.toBigDecimal(), count = 1),
				ValueBucket(from = 14.toBigDecimal(), to = 15.toBigDecimal(), count = 1),
				ValueBucket(from = 15.toBigDecimal(), to = 16.toBigDecimal(), count = 1),
				ValueBucket(from = 16.toBigDecimal(), to = 17.toBigDecimal(), count = 0),
				ValueBucket(from = 17.toBigDecimal(), to = 18.toBigDecimal(), count = 0),
				ValueBucket(from = 18.toBigDecimal(), to = 19.toBigDecimal(), count = 0),
				ValueBucket(from = 19.toBigDecimal(), to = 20.toBigDecimal(), count = 0),
				ValueBucket(from = 20.toBigDecimal(), to = 21.toBigDecimal(), count = 0),
				ValueBucket(from = 21.toBigDecimal(), to = 22.toBigDecimal(), count = 0),
				ValueBucket(from = 22.toBigDecimal(), to = 23.toBigDecimal(), count = 0),
				ValueBucket(from = 23.toBigDecimal(), to = 24.toBigDecimal(), count = 0),
				ValueBucket(from = 24.toBigDecimal(), to = 25.toBigDecimal(), count = 0),
				ValueBucket(from = 25.toBigDecimal(), to = 26.toBigDecimal(), count = 0),
				ValueBucket(from = 26.toBigDecimal(), to = 27.toBigDecimal(), count = 0),
				ValueBucket(from = 27.toBigDecimal(), to = 28.toBigDecimal(), count = 0),
				ValueBucket(from = 28.toBigDecimal(), to = 29.toBigDecimal(), count = 0),
				ValueBucket(from = 29.toBigDecimal(), to = 30.toBigDecimal(), count = 0),
				ValueBucket(from = 30.toBigDecimal(), to = 31.toBigDecimal(), count = 0),
				ValueBucket(from = 31.toBigDecimal(), to = 32.toBigDecimal(), count = 0),
				ValueBucket(from = 32.toBigDecimal(), to = 33.toBigDecimal(), count = 0),
				ValueBucket(from = 33.toBigDecimal(), to = 34.toBigDecimal(), count = 0),
				ValueBucket(from = 34.toBigDecimal(), to = 35.toBigDecimal(), count = 0),
				ValueBucket(from = 35.toBigDecimal(), to = null, count = 0),
			)
			profitTarget shouldContainExactlyInAnyOrder listOf(
				ValueBucket(from = "0.00".toBigDecimal(), to = "0.02".toBigDecimal(), count = 0),
				ValueBucket(from = "0.02".toBigDecimal(), to = "0.04".toBigDecimal(), count = 0),
				ValueBucket(from = "0.04".toBigDecimal(), to = "0.06".toBigDecimal(), count = 0),
				ValueBucket(from = "0.06".toBigDecimal(), to = "0.08".toBigDecimal(), count = 0),
				ValueBucket(from = "0.08".toBigDecimal(), to = "0.10".toBigDecimal(), count = 0),
				ValueBucket(from = "0.10".toBigDecimal(), to = "0.12".toBigDecimal(), count = 0),
				ValueBucket(from = "0.12".toBigDecimal(), to = "0.14".toBigDecimal(), count = 0),
				ValueBucket(from = "0.14".toBigDecimal(), to = "0.16".toBigDecimal(), count = 0),
				ValueBucket(from = "0.16".toBigDecimal(), to = "0.18".toBigDecimal(), count = 0),
				ValueBucket(from = "0.18".toBigDecimal(), to = "0.20".toBigDecimal(), count = 0),
				ValueBucket(from = "0.20".toBigDecimal(), to = "0.22".toBigDecimal(), count = 0),
				ValueBucket(from = "0.22".toBigDecimal(), to = "0.24".toBigDecimal(), count = 0),
				ValueBucket(from = "0.24".toBigDecimal(), to = "0.26".toBigDecimal(), count = 0),
				ValueBucket(from = "0.26".toBigDecimal(), to = "0.28".toBigDecimal(), count = 0),
				ValueBucket(from = "0.28".toBigDecimal(), to = "0.30".toBigDecimal(), count = 1),
				ValueBucket(from = "0.30".toBigDecimal(), to = "0.32".toBigDecimal(), count = 1),
				ValueBucket(from = "0.32".toBigDecimal(), to = "0.34".toBigDecimal(), count = 1),
				ValueBucket(from = "0.34".toBigDecimal(), to = "0.36".toBigDecimal(), count = 0),
				ValueBucket(from = "0.36".toBigDecimal(), to = "0.38".toBigDecimal(), count = 0),
				ValueBucket(from = "0.38".toBigDecimal(), to = "0.40".toBigDecimal(), count = 0),
			)
			stopLoss shouldBe listOf(
				ValueBucket(from = "0.00".toBigDecimal(), to = "0.01".toBigDecimal(), count = 0),
				ValueBucket(from = "0.01".toBigDecimal(), to = "0.02".toBigDecimal(), count = 0),
				ValueBucket(from = "0.02".toBigDecimal(), to = "0.03".toBigDecimal(), count = 0),
				ValueBucket(from = "0.03".toBigDecimal(), to = "0.04".toBigDecimal(), count = 0),
				ValueBucket(from = "0.04".toBigDecimal(), to = "0.05".toBigDecimal(), count = 0),
				ValueBucket(from = "0.05".toBigDecimal(), to = "0.06".toBigDecimal(), count = 0),
				ValueBucket(from = "0.06".toBigDecimal(), to = "0.07".toBigDecimal(), count = 0),
				ValueBucket(from = "0.07".toBigDecimal(), to = "0.08".toBigDecimal(), count = 0),
				ValueBucket(from = "0.08".toBigDecimal(), to = "0.09".toBigDecimal(), count = 0),
				ValueBucket(from = "0.09".toBigDecimal(), to = "0.10".toBigDecimal(), count = 0),
				ValueBucket(from = "0.10".toBigDecimal(), to = "0.11".toBigDecimal(), count = 0),
				ValueBucket(from = "0.11".toBigDecimal(), to = "0.12".toBigDecimal(), count = 0),
				ValueBucket(from = "0.12".toBigDecimal(), to = "0.13".toBigDecimal(), count = 0),
				ValueBucket(from = "0.13".toBigDecimal(), to = "0.14".toBigDecimal(), count = 0),
				ValueBucket(from = "0.14".toBigDecimal(), to = "0.15".toBigDecimal(), count = 1),
				ValueBucket(from = "0.15".toBigDecimal(), to = "0.16".toBigDecimal(), count = 2),
				ValueBucket(from = "0.16".toBigDecimal(), to = "0.17".toBigDecimal(), count = 0),
				ValueBucket(from = "0.17".toBigDecimal(), to = "0.18".toBigDecimal(), count = 0),
				ValueBucket(from = "0.18".toBigDecimal(), to = "0.19".toBigDecimal(), count = 0),
				ValueBucket(from = "0.19".toBigDecimal(), to = "0.20".toBigDecimal(), count = 0),
			)
			tradeAmount shouldContainExactlyInAnyOrder listOf(
				ValueBucket(from = "0".toBigDecimal(), to = "0.74".toBigDecimal(), count = 1),
				ValueBucket(from = "0.74".toBigDecimal(), to = "1.48".toBigDecimal(), count = 0),
				ValueBucket(from = "1.48".toBigDecimal(), to = "2.22".toBigDecimal(), count = 0),
				ValueBucket(from = "2.22".toBigDecimal(), to = "2.96".toBigDecimal(), count = 0),
				ValueBucket(from = "2.96".toBigDecimal(), to = "3.7".toBigDecimal(), count = 0),
				ValueBucket(from = "3.7".toBigDecimal(), to = "4.44".toBigDecimal(), count = 0),
				ValueBucket(from = "4.44".toBigDecimal(), to = "5.18".toBigDecimal(), count = 0),
				ValueBucket(from = "5.18".toBigDecimal(), to = "5.92".toBigDecimal(), count = 0),
				ValueBucket(from = "5.92".toBigDecimal(), to = "6.66".toBigDecimal(), count = 1),
				ValueBucket(from = "6.66".toBigDecimal(), to = "7.4".toBigDecimal(), count = 1),
			)
			buyFrequency shouldContainExactlyInAnyOrder listOf(
				ValueBucket(from = 0.toBigInteger(), to = 1.toBigInteger(), count = 0),
				ValueBucket(from = 1.toBigInteger(), to = 2.toBigInteger(), count = 0),
				ValueBucket(from = 2.toBigInteger(), to = 3.toBigInteger(), count = 0),
				ValueBucket(from = 3.toBigInteger(), to = 4.toBigInteger(), count = 0),
				ValueBucket(from = 4.toBigInteger(), to = 5.toBigInteger(), count = 0),
				ValueBucket(from = 5.toBigInteger(), to = 6.toBigInteger(), count = 0),
				ValueBucket(from = 6.toBigInteger(), to = 7.toBigInteger(), count = 0),
				ValueBucket(from = 7.toBigInteger(), to = 8.toBigInteger(), count = 0),
				ValueBucket(from = 8.toBigInteger(), to = 9.toBigInteger(), count = 0),
				ValueBucket(from = 9.toBigInteger(), to = 10.toBigInteger(), count = 0),
				ValueBucket(from = 10.toBigInteger(), to = 11.toBigInteger(), count = 1),
				ValueBucket(from = 11.toBigInteger(), to = 12.toBigInteger(), count = 0),
				ValueBucket(from = 12.toBigInteger(), to = 13.toBigInteger(), count = 0),
				ValueBucket(from = 13.toBigInteger(), to = 14.toBigInteger(), count = 0),
				ValueBucket(from = 14.toBigInteger(), to = 15.toBigInteger(), count = 0),
				ValueBucket(from = 15.toBigInteger(), to = 16.toBigInteger(), count = 2),
				ValueBucket(from = 16.toBigInteger(), to = 17.toBigInteger(), count = 0),
				ValueBucket(from = 17.toBigInteger(), to = 18.toBigInteger(), count = 0),
				ValueBucket(from = 18.toBigInteger(), to = 19.toBigInteger(), count = 0),
				ValueBucket(from = 19.toBigInteger(), to = 20.toBigInteger(), count = 0),
				ValueBucket(from = 20.toBigInteger(), to = 21.toBigInteger(), count = 0),
				ValueBucket(from = 21.toBigInteger(), to = 22.toBigInteger(), count = 0),
				ValueBucket(from = 22.toBigInteger(), to = 23.toBigInteger(), count = 0),
				ValueBucket(from = 23.toBigInteger(), to = 24.toBigInteger(), count = 0),
				ValueBucket(from = 24.toBigInteger(), to = 25.toBigInteger(), count = 0),
				ValueBucket(from = 25.toBigInteger(), to = 26.toBigInteger(), count = 0),
				ValueBucket(from = 26.toBigInteger(), to = 27.toBigInteger(), count = 0),
				ValueBucket(from = 27.toBigInteger(), to = 28.toBigInteger(), count = 0),
				ValueBucket(from = 28.toBigInteger(), to = 29.toBigInteger(), count = 0),
				ValueBucket(from = 29.toBigInteger(), to = 30.toBigInteger(), count = 0),
				ValueBucket(from = 30.toBigInteger(), to = 31.toBigInteger(), count = 0),
				ValueBucket(from = 31.toBigInteger(), to = 32.toBigInteger(), count = 0),
				ValueBucket(from = 32.toBigInteger(), to = 33.toBigInteger(), count = 0),
				ValueBucket(from = 33.toBigInteger(), to = 34.toBigInteger(), count = 0),
				ValueBucket(from = 34.toBigInteger(), to = 35.toBigInteger(), count = 0),
				ValueBucket(from = 35.toBigInteger(), to = 36.toBigInteger(), count = 0),
				ValueBucket(from = 36.toBigInteger(), to = 37.toBigInteger(), count = 0),
				ValueBucket(from = 37.toBigInteger(), to = 38.toBigInteger(), count = 0),
				ValueBucket(from = 38.toBigInteger(), to = 39.toBigInteger(), count = 0),
				ValueBucket(from = 39.toBigInteger(), to = 40.toBigInteger(), count = 0),
				ValueBucket(from = 40.toBigInteger(), to = 41.toBigInteger(), count = 0),
				ValueBucket(from = 41.toBigInteger(), to = 42.toBigInteger(), count = 0),
				ValueBucket(from = 42.toBigInteger(), to = 43.toBigInteger(), count = 0),
				ValueBucket(from = 43.toBigInteger(), to = 44.toBigInteger(), count = 0),
				ValueBucket(from = 44.toBigInteger(), to = 45.toBigInteger(), count = 0),
				ValueBucket(from = 45.toBigInteger(), to = 46.toBigInteger(), count = 0),
				ValueBucket(from = 46.toBigInteger(), to = 47.toBigInteger(), count = 0),
				ValueBucket(from = 47.toBigInteger(), to = 48.toBigInteger(), count = 0),
				ValueBucket(from = 48.toBigInteger(), to = 49.toBigInteger(), count = 0),
				ValueBucket(from = 49.toBigInteger(), to = 50.toBigInteger(), count = 0),
			)
			buyVolume shouldContainExactlyInAnyOrder listOf(
				ValueBucket(from = "0".toBigDecimal(), to = "0.1".toBigDecimal(), count = 0),
				ValueBucket(from = "0.1".toBigDecimal(), to = "0.2".toBigDecimal(), count = 0),
				ValueBucket(from = "0.2".toBigDecimal(), to = "0.3".toBigDecimal(), count = 0),
				ValueBucket(from = "0.3".toBigDecimal(), to = "0.4".toBigDecimal(), count = 0),
				ValueBucket(from = "0.4".toBigDecimal(), to = "0.5".toBigDecimal(), count = 0),
				ValueBucket(from = "0.5".toBigDecimal(), to = "0.6".toBigDecimal(), count = 1),
				ValueBucket(from = "0.6".toBigDecimal(), to = "0.7".toBigDecimal(), count = 0),
				ValueBucket(from = "0.7".toBigDecimal(), to = "0.8".toBigDecimal(), count = 0),
				ValueBucket(from = "0.8".toBigDecimal(), to = "0.9".toBigDecimal(), count = 1),
				ValueBucket(from = "0.9".toBigDecimal(), to = "1".toBigDecimal(), count = 1),
			)
			sellVolume shouldContainExactlyInAnyOrder listOf(
				ValueBucket(from = "0".toBigDecimal(), to = "0.06".toBigDecimal(), count = 0),
				ValueBucket(from = "0.06".toBigDecimal(), to = "0.12".toBigDecimal(), count = 1),
				ValueBucket(from = "0.12".toBigDecimal(), to = "0.18".toBigDecimal(), count = 0),
				ValueBucket(from = "0.18".toBigDecimal(), to = "0.24".toBigDecimal(), count = 1),
				ValueBucket(from = "0.24".toBigDecimal(), to = "0.3".toBigDecimal(), count = 0),
				ValueBucket(from = "0.3".toBigDecimal(), to = "0.36".toBigDecimal(), count = 0),
				ValueBucket(from = "0.36".toBigDecimal(), to = "0.42".toBigDecimal(), count = 0),
				ValueBucket(from = "0.42".toBigDecimal(), to = "0.48".toBigDecimal(), count = 0),
				ValueBucket(from = "0.48".toBigDecimal(), to = "0.54".toBigDecimal(), count = 1),
				ValueBucket(from = "0.54".toBigDecimal(), to = "0.6".toBigDecimal(), count = 0),
			)
			sellTransactionFractionThreshold shouldContainExactlyInAnyOrder listOf(
				ValueBucket(from = "0".toBigDecimal(), to = "0.06".toBigDecimal(), count = 0),
				ValueBucket(from = "0.06".toBigDecimal(), to = "0.12".toBigDecimal(), count = 1),
				ValueBucket(from = "0.12".toBigDecimal(), to = "0.18".toBigDecimal(), count = 0),
				ValueBucket(from = "0.18".toBigDecimal(), to = "0.24".toBigDecimal(), count = 0),
				ValueBucket(from = "0.24".toBigDecimal(), to = "0.3".toBigDecimal(), count = 0),
				ValueBucket(from = "0.3".toBigDecimal(), to = "0.36".toBigDecimal(), count = 1),
				ValueBucket(from = "0.36".toBigDecimal(), to = "0.42".toBigDecimal(), count = 0),
				ValueBucket(from = "0.42".toBigDecimal(), to = "0.48".toBigDecimal(), count = 0),
				ValueBucket(from = "0.48".toBigDecimal(), to = "0.54".toBigDecimal(), count = 1),
				ValueBucket(from = "0.54".toBigDecimal(), to = "0.6".toBigDecimal(), count = 0),
			)
		}
	}

	@Test
	fun `should drop top 10 percent highest items`() {
		integrationTestHelper.getFirebaseUser(id = 100.toUUID())
		integrationTestHelper.getFile(id = 200.toUUID())

		(1..10).map {
			integrationTestHelper.getBot(
				id = it.toUUID(),
				userId = 100.toUUID(),
				avatarFileId = 200.toUUID(),
				buyVolume = it.toBigDecimal(),
				numberOfHoldersFrom = it.toLong(),
				numberOfHoldersTo = it.toLong(),
				liquidityFromUsd = null,
				liquidityToUsd = it.toBigDecimal(),
			)
		}

		val result = queryBus(GetBotSettingsStatisticsQuery())

		result.run {
			buyVolume.maxOfOrNull { it.to }!! shouldBeLessThan 10.toBigDecimal()
			liquidityUsd.sumOf { it.count } shouldBe 19
			numberOfHolders.sumOf { it.count } shouldBe 19
		}
	}
}
