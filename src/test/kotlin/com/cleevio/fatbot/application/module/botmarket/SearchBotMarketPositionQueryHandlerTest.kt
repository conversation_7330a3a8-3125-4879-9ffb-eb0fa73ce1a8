package com.cleevio.fatbot.application.module.botmarket

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.adapter.out.evm.GetTokenPricesEVM
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.botmarket.query.SearchBotMarketPositionQuery
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.getPrivatePropertyOfProxy
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION
import com.cleevio.fatbot.toUUID
import com.github.benmanes.caffeine.cache.AsyncCache
import io.kotest.matchers.collections.shouldBeIn
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import org.jooq.DSLContext
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import java.net.URI
import java.time.Instant
import java.time.temporal.ChronoUnit

class SearchBotMarketPositionQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val clock: IntegrationTestClock,
	@Autowired private val dslContext: DSLContext,
) : IntegrationTest() {

	@BeforeEach
	fun setup() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		// Create avatar files for bots
		val botAvatar1 = integrationTestHelper.getFile(id = 31.toUUID())
		val botAvatar2 = integrationTestHelper.getFile(id = 32.toUUID())

		val bot1 = integrationTestHelper.getBot(
			id = 100.toUUID(),
			userId = user.id,
			avatarFileId = botAvatar1.id,
		)
		val bot2 = integrationTestHelper.getBot(
			id = 101.toUUID(),
			userId = user.id,
			avatarFileId = botAvatar2.id,
		)

		val botWallet1 = integrationTestHelper.getBotWallet(id = 200.toUUID(), botId = bot1.id)
		val botWallet2 = integrationTestHelper.getBotWallet(id = 201.toUUID(), botId = bot2.id)

		val file1 = integrationTestHelper.getFile(id = 21.toUUID())
		val file2 = integrationTestHelper.getFile(id = 22.toUUID())
		val file3 = integrationTestHelper.getFile(id = 23.toUUID())

		val token1 = integrationTestHelper.getBotTokenInfo(
			tokenAddress = AddressWrapper("FYxc7ntfTjV2c86oF4dDp8yYiyNLAR5rdhFhNGSMpump"),
			name = "ADHD",
			symbol = "ADHD",
			imageFileId = file1.id,
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		val token2 = integrationTestHelper.getBotTokenInfo(
			tokenAddress = AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump"),
			name = "yolo",
			symbol = "yolo",
			imageFileId = file2.id,
			chain = Chain.SOLANA,
			tokenDecimals = 9.toBigInteger(),
		)
		val token3 = integrationTestHelper.getBotTokenInfo(
			tokenAddress = AddressWrapper("HTYLJ5QTnq28HFUWQuBPKdc4RJu6JGb5ucesqAt3xTt8"),
			name = "PumpFunAss",
			symbol = "PumpFunAss",
			imageFileId = file3.id,
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		// Create bot market positions with bot wallet IDs and required parameters
		integrationTestHelper.getBotMarketPosition(
			id = 100.toUUID(),
			botWalletId = botWallet1.id,
			tokenAddress = token1.address,
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			assumedBuyPrice = BigDecimal(10),
			blockSlot = 1234567,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "1000000".toBigInteger(),
					amountIn = "50".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 101.toUUID(),
			botWalletId = botWallet2.id,
			tokenAddress = token2.address,
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			assumedBuyPrice = BigDecimal(10),
			blockSlot = 1234568,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "15".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
				setSellDataFromChain(
					amountOfTokensSold = "15".toBigInteger(),
					amountOfBaseCurrencyReceived = "20".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
					fee = BigInteger("2"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 102.toUUID(),
			botWalletId = botWallet2.id,
			tokenAddress = token3.address,
			profitTargetFraction = BigDecimal(0.30),
			stopLossFraction = BigDecimal(0.15),
			assumedBuyPrice = BigDecimal(10),
			blockSlot = 1234569,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "25".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
				setSellDataFromChain(
					amountOfTokensSold = "25".toBigInteger(),
					amountOfBaseCurrencyReceived = "20".toBigInteger(),
					exchangeRate = BigDecimal("4.5"),
					fee = BigInteger("2"),
				)
			},
		)

		// Quick-fix for clock not being used for createdAt/updatedAt timestamp on JPA entities
		dslContext.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.CREATED_AT, Instant.now(clock))
			.set(BOT_MARKET_POSITION.UPDATED_AT, Instant.now(clock))
			.where(BOT_MARKET_POSITION.ID.`in`(100.toUUID(), 101.toUUID()))
			.execute()

		dslContext.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.CREATED_AT, Instant.now(clock).minus(2, ChronoUnit.DAYS))
			.set(BOT_MARKET_POSITION.UPDATED_AT, Instant.now(clock).minus(2, ChronoUnit.DAYS))
			.where(BOT_MARKET_POSITION.ID.eq(102.toUUID()))
			.execute()

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2")

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(token1.address.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("100")))
					put(token2.address.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("200")))
					put(token3.address.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("300")))
				},
			)
	}

	@Test
	fun `should search bot market positions - no filter`() {
		val result = queryBus(
			SearchBotMarketPositionQuery(
				userId = 1.toUUID(),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				filter = SearchBotMarketPositionQuery.Filter(
					botIds = setOf(100.toUUID(), 101.toUUID()),
					searchString = null,
					timeRange = null,
				),
			),
		)

		result.content.size shouldBe 3
		result.content[0].run {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("FYxc7ntfTjV2c86oF4dDp8yYiyNLAR5rdhFhNGSMpump")
			tokenDetailUrl shouldBe URI("https://solscan.io/address/FYxc7ntfTjV2c86oF4dDp8yYiyNLAR5rdhFhNGSMpump")
			tokenName shouldBe "ADHD"
			tokenSymbol shouldBe "ADHD"
			tokenImageUrl shouldBe URI("http://localhost:8080/${21.toUUID()}.png")
			openValueUsd shouldBeEqualComparingTo "0.0000001785".toBigDecimal()
			closeValueUsd shouldBe null
			openTimeStampAt shouldNotBe null
			closedTimeStampAt shouldBe null
			currentValueUsd shouldBeEqualComparingTo "0.0000002".toBigDecimal()
			pnlAmountUsd shouldBeEqualComparingTo "0.0000000215".toBigDecimal()
			pnlAmountFraction shouldBeEqualComparingTo "0.120448179271708683473389355742297".toBigDecimal()
		}
		result.content[1].run {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump")
			tokenDetailUrl shouldBe URI("https://solscan.io/address/2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump")
			tokenName shouldBe "yolo"
			tokenSymbol shouldBe "yolo"
			tokenImageUrl shouldBe URI("http://localhost:8080/${22.toUUID()}.png")
			openValueUsd shouldBeEqualComparingTo BigDecimal("3.85E-8")
			closeValueUsd shouldNotBe null
			closeValueUsd!! shouldBeEqualComparingTo BigDecimal("7.00E-8")
			openTimeStampAt shouldNotBe null
			closedTimeStampAt shouldNotBe null
			currentValueUsd shouldBeEqualComparingTo BigDecimal("7.00E-8")
			pnlAmountUsd shouldBeEqualComparingTo BigDecimal("3.150E-8")
			pnlAmountFraction shouldBeEqualComparingTo BigDecimal("0.818181818181818181818181818181818")
		}
		result.content[2].run {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("HTYLJ5QTnq28HFUWQuBPKdc4RJu6JGb5ucesqAt3xTt8")
			tokenDetailUrl shouldBe URI("https://solscan.io/address/HTYLJ5QTnq28HFUWQuBPKdc4RJu6JGb5ucesqAt3xTt8")
			tokenName shouldBe "PumpFunAss"
			tokenSymbol shouldBe "PumpFunAss"
			tokenImageUrl shouldBe URI("http://localhost:8080/${23.toUUID()}.png")
			openValueUsd shouldBeEqualComparingTo "0.0000000385".toBigDecimal()
			closeValueUsd shouldNotBe null
			closeValueUsd!! shouldBeEqualComparingTo "0.00000009".toBigDecimal()
			openTimeStampAt shouldNotBe null
			closedTimeStampAt shouldNotBe null
			currentValueUsd shouldBeEqualComparingTo "0.00000009".toBigDecimal()
			pnlAmountUsd shouldBeEqualComparingTo "0.0000000515".toBigDecimal()
			pnlAmountFraction shouldBeEqualComparingTo "1.337662337662337662337662337662338".toBigDecimal()
		}
	}

	@Test
	fun `should search bot market positions - with bot filter`() {
		val result = queryBus(
			SearchBotMarketPositionQuery(
				userId = 1.toUUID(),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				filter = SearchBotMarketPositionQuery.Filter(
					botIds = setOf(101.toUUID()),
					searchString = null,
					timeRange = null,
				),
			),
		)

		result.content.size shouldBe 2
		result.content[0].run {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump")
			tokenDetailUrl shouldBe URI("https://solscan.io/address/2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump")
			tokenName shouldBe "yolo"
			tokenSymbol shouldBe "yolo"
			tokenImageUrl shouldBe URI("http://localhost:8080/${22.toUUID()}.png")
			openValueUsd shouldBeEqualComparingTo BigDecimal("3.850E-8")
			closeValueUsd shouldNotBe null
			closeValueUsd!! shouldBeEqualComparingTo BigDecimal("7.00E-8")
			openTimeStampAt shouldNotBe null
			closedTimeStampAt shouldNotBe null
			currentValueUsd shouldBeEqualComparingTo BigDecimal("7.00E-8")
			pnlAmountUsd shouldBeEqualComparingTo BigDecimal("3.15E-8")
			pnlAmountFraction shouldBeEqualComparingTo BigDecimal("0.818181818181818181818181818181818")
		}
		result.content[1].run {
			id shouldNotBe null
			tokenAddress shouldBe AddressWrapper("HTYLJ5QTnq28HFUWQuBPKdc4RJu6JGb5ucesqAt3xTt8")
			tokenDetailUrl shouldBe URI("https://solscan.io/address/HTYLJ5QTnq28HFUWQuBPKdc4RJu6JGb5ucesqAt3xTt8")
			tokenName shouldBe "PumpFunAss"
			tokenSymbol shouldBe "PumpFunAss"
			tokenImageUrl shouldBe URI("http://localhost:8080/${23.toUUID()}.png")
			openValueUsd shouldBeEqualComparingTo "0.0000000385".toBigDecimal()
			closeValueUsd shouldNotBe null
			closeValueUsd!! shouldBeEqualComparingTo "0.00000009".toBigDecimal()
			openTimeStampAt shouldNotBe null
			closedTimeStampAt shouldNotBe null
			currentValueUsd shouldBeEqualComparingTo "0.00000009".toBigDecimal()
			pnlAmountUsd shouldBeEqualComparingTo "0.0000000515".toBigDecimal()
			pnlAmountFraction shouldBeEqualComparingTo BigDecimal("1.337662337662337662337662337662338")
		}
	}

	@Test
	fun `should search bot market positions - with time range filter`() {
		val result = queryBus(
			SearchBotMarketPositionQuery(
				userId = 1.toUUID(),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				filter = SearchBotMarketPositionQuery.Filter(
					botIds = null,
					searchString = null,
					timeRange = TimeRange.DAY,
				),
			),
		)

		result.content.size shouldBe 2
		// Verify results are within last 24 hours
		result.content.forEach {
			it.openTimeStampAt shouldBe clock.currentTime()
			it.id shouldBeIn setOf(100.toUUID(), 101.toUUID())
		}
	}

	@Test
	fun `should search bot market positions - with search string filter`() {
		val result = queryBus(
			SearchBotMarketPositionQuery(
				userId = 1.toUUID(),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
				filter = SearchBotMarketPositionQuery.Filter(
					botIds = setOf(100.toUUID(), 101.toUUID()),
					searchString = "2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump",
					timeRange = null,
				),
			),
		)

		result.content.size shouldBe 1
		result.content[0].run {
			tokenName shouldBe "yolo"
			tokenSymbol shouldBe "yolo"
		}
	}
}
