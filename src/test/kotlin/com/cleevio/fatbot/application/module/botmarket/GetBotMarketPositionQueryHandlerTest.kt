package com.cleevio.fatbot.application.module.botmarket

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.out.evm.GetTokenPricesEVM
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.asTxHash
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.botmarket.exception.BotMarketPositionTradeStateNotFoundException
import com.cleevio.fatbot.application.module.botmarket.query.GetBotMarketPositionQuery
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.bottransaction.exception.BotTransactionNotFoundException
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateType
import com.cleevio.fatbot.getPrivatePropertyOfProxy
import com.cleevio.fatbot.setAndReturnPrivateProperty
import com.cleevio.fatbot.toUUID
import com.github.benmanes.caffeine.cache.AsyncCache
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.every
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import java.net.URI
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.Test

class GetBotMarketPositionQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val clock: IntegrationTestClock,
) : IntegrationTest() {

	@Test
	fun `should get bot market position detail for closed position`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(id = 3.toUUID(), userId = 1.toUUID(), avatarFileId = 2.toUUID())
		integrationTestHelper.getBotWallet(id = 4.toUUID(), botId = 3.toUUID())

		integrationTestHelper.getBotTokenInfo(
			id = 5.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			name = "GOONER",
			symbol = "GOONER",
			tokenDecimals = BigInteger("6"),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 6.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.3"),
			assumedBuyPrice = BigDecimal("10.5"),
			blockSlot = 1,
			beforeSave = {
				setAndReturnPrivateProperty("buyExchangeRate", BigDecimal("2.0"))
				setAndReturnPrivateProperty("sellExchangeRate", BigDecimal("2.5"))
				setAndReturnPrivateProperty("positionClosedAt", Instant.now(clock).plus(1, ChronoUnit.DAYS))
				setAndReturnPrivateProperty("totalTokenAmountBought", BigInteger("**********"))
				setAndReturnPrivateProperty("totalTokenAcquisitionCostBaseAmount", BigInteger("**********"))
				setAndReturnPrivateProperty("totalTokenAmountSold", BigInteger("**********"))
				setAndReturnPrivateProperty("totalTokenDispositionCostBaseAmount", BigInteger("*********"))
			},
		)

		integrationTestHelper.getBotTransaction(
			id = 7.toUUID(),
			botWalletId = 4.toUUID(),
			type = BotTransactionType.BUY,
			status = TransactionStatus.SUCCESS,
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
		)

		integrationTestHelper.getBotTransaction(
			id = 8.toUUID(),
			botWalletId = 4.toUUID(),
			type = BotTransactionType.SELL,
			txHash = TxHash("txHash1"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
		)

		integrationTestHelper.getBotMarketPositionTradeState(
			id = 9.toUUID(),
			botMarketPositionId = 6.toUUID(),
			type = BotMarketPositionTradeStateType.BUY,
			marketCapUsd = BigDecimal("4.5"),
			liquidityUsd = BigDecimal("6.6"),
			volumeUsd = BigDecimal("4.3"),
			numOfAccountHolders = 15L,
			buyVolume = BigDecimal("0.5"),
			sellVolume = BigDecimal("0.5"),
			fractionOfSellTransactions = BigDecimal("1"),
		)

		integrationTestHelper.getBotMarketPositionTradeState(
			id = 10.toUUID(),
			botMarketPositionId = 6.toUUID(),
			type = BotMarketPositionTradeStateType.SELL,
			marketCapUsd = BigDecimal("5.5"),
			liquidityUsd = BigDecimal("6.3"),
			volumeUsd = BigDecimal("4.2"),
			numOfAccountHolders = 17L,
			buyVolume = BigDecimal("0.6"),
			sellVolume = BigDecimal("0.4"),
			fractionOfSellTransactions = BigDecimal("0"),
		)

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(
						AddressWrapper(
							"FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump",
						).toChainAddress(chain = Chain.SOLANA),
						BaseAmount(BigInteger("*********")),
					)
				},
			)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2.5")

		// when
		val result = queryBus(
			GetBotMarketPositionQuery(
				userId = 1.toUUID(),
				botId = 3.toUUID(),
				botMarketPositionId = 6.toUUID(),
			),
		)

		// then
		result.run {
			id shouldBe 6.toUUID()
			botId shouldBe 3.toUUID()
			tokenAddress shouldBe AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump")
			tokenDetailUrl shouldBe URI("https://solscan.io/address/FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump")
			tokenName shouldBe "GOONER"
			tokenSymbol shouldBe "GOONER"
			tokenChain shouldBe Chain.SOLANA
			tokenImageUrl shouldBe null
			openValueUsd shouldBe BigDecimal("2.0000000000")
			closeValueUsd shouldBe BigDecimal("1.2*********")
			openTimeStampAt shouldNotBe null
			closedTimeStampAt shouldBe Instant.now(clock).plus(1, ChronoUnit.DAYS)
			pnlAmountUsd shouldBe BigDecimal("-0.7*********")
			pnlAmountFraction shouldBe BigDecimal("-0.375")
			currentValueUsd shouldBe BigDecimal("1.2*********")
			buyTradeTransaction.run {
				txId shouldBe 7.toUUID()
				txHash shouldBe "txHash".asTxHash()
				txDetailUrl shouldBe URI("https://solscan.io/tx/txHash")
				txType shouldBe BotTransactionType.BUY
				txStatus shouldBe TransactionStatus.SUCCESS
				txCreatedAt shouldNotBe null
				txChain shouldBe Chain.SOLANA
				botWalletId shouldBe 4.toUUID()
				amountUsd shouldBe BigDecimal("2.0000000000")
				marketCapUsd shouldBe BigDecimal("4.5")
				liquidityUsd shouldBe BigDecimal("6.6")
				volumeUsd shouldBe BigDecimal("4.3")
				numOfHolders shouldBe 15L
				buyVolume shouldBe BigDecimal("0.5")
				sellVolume shouldBe BigDecimal("0.5")
				fractionOfSellTransactions shouldBe BigDecimal("0.5")
			}
			sellTradeTransaction shouldNotBe null
			sellTradeTransaction!!.run {
				txId shouldBe 8.toUUID()
				txHash shouldBe "txHash1".asTxHash()
				txDetailUrl shouldBe URI("https://solscan.io/tx/txHash1")
				txType shouldBe BotTransactionType.SELL
				txStatus shouldBe TransactionStatus.SUCCESS
				txCreatedAt shouldNotBe null
				txChain shouldBe Chain.SOLANA
				botWalletId shouldBe 4.toUUID()
				amountUsd shouldBe BigDecimal("1.2*********")
				marketCapUsd shouldBe BigDecimal("5.5")
				liquidityUsd shouldBe BigDecimal("6.3")
				volumeUsd shouldBe BigDecimal("4.2")
				numOfHolders shouldBe 17L
				buyVolume shouldBe BigDecimal("0.6")
				sellVolume shouldBe BigDecimal("0.4")
				fractionOfSellTransactions shouldBe BigDecimal("0")
			}
		}
	}

	@Test
	fun `should not get bot market position detail with missing successful transaction`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(id = 3.toUUID(), userId = 1.toUUID(), avatarFileId = 2.toUUID())
		integrationTestHelper.getBotWallet(id = 4.toUUID(), botId = 3.toUUID())

		integrationTestHelper.getBotTokenInfo(
			id = 5.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			name = "GOONER",
			symbol = "GOONER",
			tokenDecimals = BigInteger("6"),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 6.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.3"),
			assumedBuyPrice = BigDecimal("10.5"),
			blockSlot = 1,
			beforeSave = {
				setAndReturnPrivateProperty("buyExchangeRate", BigDecimal("2.0"))
				setAndReturnPrivateProperty("totalTokenAmountBought", BigInteger("**********"))
				setAndReturnPrivateProperty("totalTokenAcquisitionCostBaseAmount", BigInteger("**********"))
			},
		)

		integrationTestHelper.getBotTransaction(
			id = 7.toUUID(),
			botWalletId = 4.toUUID(),
			type = BotTransactionType.BUY,
			status = TransactionStatus.FAILED,
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
		)

		integrationTestHelper.getBotMarketPositionTradeState(
			id = 9.toUUID(),
			botMarketPositionId = 6.toUUID(),
			type = BotMarketPositionTradeStateType.BUY,
			marketCapUsd = BigDecimal("4.5"),
			liquidityUsd = BigDecimal("6.6"),
			volumeUsd = BigDecimal("4.3"),
			numOfAccountHolders = 15L,
			buyVolume = BigDecimal("0.5"),
			sellVolume = BigDecimal("0.5"),
			fractionOfSellTransactions = BigDecimal("0.3"),
		)

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(
						AddressWrapper(
							"FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump",
						).toChainAddress(chain = Chain.SOLANA),
						BaseAmount(BigInteger("*********")),
					)
				},
			)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2.5")

		// when & then
		shouldThrow<BotTransactionNotFoundException> {
			queryBus(
				GetBotMarketPositionQuery(
					userId = 1.toUUID(),
					botId = 3.toUUID(),
					botMarketPositionId = 6.toUUID(),
				),
			)
		}
	}

	@Test
	fun `should throw exception when successful buy transaction is missing`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(id = 3.toUUID(), userId = 1.toUUID(), avatarFileId = 2.toUUID())
		integrationTestHelper.getBotWallet(id = 4.toUUID(), botId = 3.toUUID())

		integrationTestHelper.getBotTokenInfo(
			id = 5.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			name = "GOONER",
			symbol = "GOONER",
			tokenDecimals = BigInteger("6"),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 6.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.3"),
			assumedBuyPrice = BigDecimal("10.5"),
			blockSlot = 1,
			beforeSave = {
				setAndReturnPrivateProperty("buyExchangeRate", BigDecimal("2.0"))
				setAndReturnPrivateProperty("totalTokenAmountBought", BigInteger("**********"))
				setAndReturnPrivateProperty("totalTokenAcquisitionCostBaseAmount", BigInteger("**********"))
			},
		)

		integrationTestHelper.getBotMarketPositionTradeState(
			id = 9.toUUID(),
			botMarketPositionId = 6.toUUID(),
			type = BotMarketPositionTradeStateType.BUY,
			marketCapUsd = BigDecimal("4.5"),
			liquidityUsd = BigDecimal("6.6"),
			volumeUsd = BigDecimal("4.3"),
			numOfAccountHolders = 15L,
			buyVolume = BigDecimal("0.5"),
			sellVolume = BigDecimal("0.5"),
			fractionOfSellTransactions = BigDecimal("0.3"),
		)

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(
						AddressWrapper(
							"FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump",
						).toChainAddress(chain = Chain.SOLANA),
						BaseAmount(BigInteger("*********")),
					)
				},
			)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2.5")

		// when & then
		shouldThrow<BotTransactionNotFoundException> {
			queryBus(
				GetBotMarketPositionQuery(
					userId = 1.toUUID(),
					botId = 3.toUUID(),
					botMarketPositionId = 6.toUUID(),
				),
			)
		}
	}

	@Test
	fun `should throw exception when buy trade is missing`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(id = 3.toUUID(), userId = 1.toUUID(), avatarFileId = 2.toUUID())
		integrationTestHelper.getBotWallet(id = 4.toUUID(), botId = 3.toUUID())

		integrationTestHelper.getBotTokenInfo(
			id = 5.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			name = "GOONER",
			symbol = "GOONER",
			tokenDecimals = BigInteger("6"),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 6.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.3"),
			assumedBuyPrice = BigDecimal("10.5"),
			blockSlot = 1,
			beforeSave = {
				setAndReturnPrivateProperty("buyExchangeRate", BigDecimal("2.0"))
				setAndReturnPrivateProperty("totalTokenAmountBought", BigInteger("**********"))
				setAndReturnPrivateProperty("totalTokenAcquisitionCostBaseAmount", BigInteger("**********"))
			},
		)

		integrationTestHelper.getBotTransaction(
			id = 7.toUUID(),
			botWalletId = 4.toUUID(),
			type = BotTransactionType.BUY,
			status = TransactionStatus.SUCCESS,
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
		)

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(
						AddressWrapper(
							"FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump",
						).toChainAddress(chain = Chain.SOLANA),
						BaseAmount(BigInteger("*********")),
					)
				},
			)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2.5")

		// when & then
		shouldThrow<BotMarketPositionTradeStateNotFoundException> {
			queryBus(
				GetBotMarketPositionQuery(
					userId = 1.toUUID(),
					botId = 3.toUUID(),
					botMarketPositionId = 6.toUUID(),
				),
			)
		}
	}

	@Test
	fun `should throw exception when market position is closed but no successful sell transaction is found`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(id = 3.toUUID(), userId = 1.toUUID(), avatarFileId = 2.toUUID())
		integrationTestHelper.getBotWallet(id = 4.toUUID(), botId = 3.toUUID())

		integrationTestHelper.getBotTokenInfo(
			id = 5.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			name = "GOONER",
			symbol = "GOONER",
			tokenDecimals = BigInteger("6"),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 6.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.3"),
			assumedBuyPrice = BigDecimal("10.5"),
			blockSlot = 1,
			beforeSave = {
				setAndReturnPrivateProperty("buyExchangeRate", BigDecimal("2.0"))
				setAndReturnPrivateProperty("sellExchangeRate", BigDecimal("2.5"))
				setAndReturnPrivateProperty("positionClosedAt", Instant.now(clock).plus(1, ChronoUnit.DAYS))
				setAndReturnPrivateProperty("totalTokenAmountBought", BigInteger("**********"))
				setAndReturnPrivateProperty("totalTokenAcquisitionCostBaseAmount", BigInteger("**********"))
				setAndReturnPrivateProperty("totalTokenAmountSold", BigInteger("**********"))
				setAndReturnPrivateProperty("totalTokenDispositionCostBaseAmount", BigInteger("*********"))
			},
		)

		integrationTestHelper.getBotTransaction(
			id = 7.toUUID(),
			botWalletId = 4.toUUID(),
			type = BotTransactionType.BUY,
			status = TransactionStatus.SUCCESS,
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
		)

		integrationTestHelper.getBotMarketPositionTradeState(
			id = 9.toUUID(),
			botMarketPositionId = 6.toUUID(),
			type = BotMarketPositionTradeStateType.BUY,
			marketCapUsd = BigDecimal("4.5"),
			liquidityUsd = BigDecimal("6.6"),
			volumeUsd = BigDecimal("4.3"),
			numOfAccountHolders = 15L,
			buyVolume = BigDecimal("0.5"),
			sellVolume = BigDecimal("0.5"),
			fractionOfSellTransactions = BigDecimal("0.3"),
		)

		integrationTestHelper.getBotMarketPositionTradeState(
			id = 10.toUUID(),
			botMarketPositionId = 6.toUUID(),
			type = BotMarketPositionTradeStateType.SELL,
			marketCapUsd = BigDecimal("5.5"),
			liquidityUsd = BigDecimal("6.3"),
			volumeUsd = BigDecimal("4.2"),
			numOfAccountHolders = 17L,
			buyVolume = BigDecimal("0.6"),
			sellVolume = BigDecimal("0.4"),
			fractionOfSellTransactions = BigDecimal("0.3"),
		)

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(
						AddressWrapper(
							"FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump",
						).toChainAddress(chain = Chain.SOLANA),
						BaseAmount(BigInteger("*********")),
					)
				},
			)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2.5")

		// when & then
		shouldThrow<IllegalArgumentException> {
			queryBus(
				GetBotMarketPositionQuery(
					userId = 1.toUUID(),
					botId = 3.toUUID(),
					botMarketPositionId = 6.toUUID(),
				),
			)
		}
	}

	@Test
	fun `should throw exception when market position is closed but not sell trade is found`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(id = 3.toUUID(), userId = 1.toUUID(), avatarFileId = 2.toUUID())
		integrationTestHelper.getBotWallet(id = 4.toUUID(), botId = 3.toUUID())

		integrationTestHelper.getBotTokenInfo(
			id = 5.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			name = "GOONER",
			symbol = "GOONER",
			tokenDecimals = BigInteger("6"),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 6.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.3"),
			assumedBuyPrice = BigDecimal("10.5"),
			blockSlot = 1,
			beforeSave = {
				setAndReturnPrivateProperty("buyExchangeRate", BigDecimal("2.0"))
				setAndReturnPrivateProperty("sellExchangeRate", BigDecimal("2.5"))
				setAndReturnPrivateProperty("positionClosedAt", Instant.now(clock).plus(1, ChronoUnit.DAYS))
				setAndReturnPrivateProperty("totalTokenAmountBought", BigInteger("**********"))
				setAndReturnPrivateProperty("totalTokenAcquisitionCostBaseAmount", BigInteger("**********"))
				setAndReturnPrivateProperty("totalTokenAmountSold", BigInteger("**********"))
				setAndReturnPrivateProperty("totalTokenDispositionCostBaseAmount", BigInteger("*********"))
			},
		)

		integrationTestHelper.getBotTransaction(
			id = 7.toUUID(),
			botWalletId = 4.toUUID(),
			type = BotTransactionType.BUY,
			txHash = TxHash("txHash1"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
		)

		integrationTestHelper.getBotTransaction(
			id = 8.toUUID(),
			botWalletId = 4.toUUID(),
			type = BotTransactionType.SELL,
			txHash = TxHash("txHash2"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
		)

		integrationTestHelper.getBotMarketPositionTradeState(
			id = 9.toUUID(),
			botMarketPositionId = 6.toUUID(),
			type = BotMarketPositionTradeStateType.BUY,
			marketCapUsd = BigDecimal("4.5"),
			liquidityUsd = BigDecimal("6.6"),
			volumeUsd = BigDecimal("4.3"),
			numOfAccountHolders = 15L,
			buyVolume = BigDecimal("0.5"),
			sellVolume = BigDecimal("0.5"),
			fractionOfSellTransactions = BigDecimal("0.3"),
		)

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(
						AddressWrapper(
							"FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump",
						).toChainAddress(chain = Chain.SOLANA),
						BaseAmount(BigInteger("*********")),
					)
				},
			)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2.5")

		// when & then
		shouldThrow<BotMarketPositionTradeStateNotFoundException> {
			queryBus(
				GetBotMarketPositionQuery(
					userId = 1.toUUID(),
					botId = 3.toUUID(),
					botMarketPositionId = 6.toUUID(),
				),
			)
		}
	}

	@Test
	fun `should get bot market position detail for open position`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())

		integrationTestHelper.getBot(id = 3.toUUID(), userId = 1.toUUID(), avatarFileId = 2.toUUID())
		integrationTestHelper.getBotWallet(id = 4.toUUID(), botId = 3.toUUID())

		integrationTestHelper.getBotTokenInfo(
			id = 5.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			name = "GOONER",
			symbol = "GOONER",
			tokenDecimals = BigInteger("6"),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 6.toUUID(),
			botWalletId = 4.toUUID(),
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
			profitTargetFraction = BigDecimal("0.1"),
			stopLossFraction = BigDecimal("0.3"),
			assumedBuyPrice = BigDecimal("10.5"),
			blockSlot = 1,
			beforeSave = {
				setAndReturnPrivateProperty("buyExchangeRate", BigDecimal("2.0"))
				setAndReturnPrivateProperty("totalTokenAmountBought", BigInteger("**********"))
				setAndReturnPrivateProperty("totalTokenAcquisitionCostBaseAmount", BigInteger("**********"))
			},
		)

		integrationTestHelper.getBotTransaction(
			id = 7.toUUID(),
			botWalletId = 4.toUUID(),
			type = BotTransactionType.BUY,
			status = TransactionStatus.SUCCESS,
			tokenAddress = AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump"),
		)

		integrationTestHelper.getBotMarketPositionTradeState(
			id = 9.toUUID(),
			botMarketPositionId = 6.toUUID(),
			type = BotMarketPositionTradeStateType.BUY,
			marketCapUsd = BigDecimal("4.5"),
			liquidityUsd = BigDecimal("6.6"),
			volumeUsd = BigDecimal("4.3"),
			numOfAccountHolders = 15L,
			buyVolume = BigDecimal("0.5"),
			sellVolume = BigDecimal("0.5"),
			fractionOfSellTransactions = BigDecimal("1"),
		)

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(
						AddressWrapper(
							"FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump",
						).toChainAddress(chain = Chain.SOLANA),
						BaseAmount(BigInteger("*********")),
					)
				},
			)

		every { coinbaseConnector.getUsdExchangeRate(any()) } returns BigDecimal("2.5")

		// when
		val result = queryBus(
			GetBotMarketPositionQuery(
				userId = 1.toUUID(),
				botId = 3.toUUID(),
				botMarketPositionId = 6.toUUID(),
			),
		)

		// then
		result.run {
			id shouldBe 6.toUUID()
			botId shouldBe 3.toUUID()
			tokenAddress shouldBe AddressWrapper("FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump")
			tokenDetailUrl shouldBe URI("https://solscan.io/address/FP9axFNBC1V68BLTDqtWAP6vVVqP9daNr57wmx4Tpump")
			tokenName shouldBe "GOONER"
			tokenSymbol shouldBe "GOONER"
			tokenChain shouldBe Chain.SOLANA
			tokenImageUrl shouldBe null
			openValueUsd shouldBe BigDecimal("2.00000000000")
			closeValueUsd shouldBe null
			openTimeStampAt shouldNotBe null
			closedTimeStampAt shouldBe null
			pnlAmountUsd shouldBe BigDecimal("2498.0000000000000000")
			pnlAmountFraction shouldBe BigDecimal("1249.00000")
			currentValueUsd shouldBe BigDecimal("2500.0000000000000000")
			buyTradeTransaction.run {
				txId shouldBe 7.toUUID()
				txHash shouldBe "txHash".asTxHash()
				txDetailUrl shouldBe URI("https://solscan.io/tx/txHash")
				txType shouldBe BotTransactionType.BUY
				txStatus shouldBe TransactionStatus.SUCCESS
				txCreatedAt shouldNotBe null
				txChain shouldBe Chain.SOLANA
				botWalletId shouldBe 4.toUUID()
				amountUsd shouldBe BigDecimal("2.00000000000")
				marketCapUsd shouldBe BigDecimal("4.5")
				liquidityUsd shouldBe BigDecimal("6.6")
				volumeUsd shouldBe BigDecimal("4.3")
				numOfHolders shouldBe 15L
				buyVolume shouldBe BigDecimal("0.5")
				sellVolume shouldBe BigDecimal("0.5")
				fractionOfSellTransactions shouldBe BigDecimal("0.5")
			}
			sellTradeTransaction shouldBe null
		}
	}
}
