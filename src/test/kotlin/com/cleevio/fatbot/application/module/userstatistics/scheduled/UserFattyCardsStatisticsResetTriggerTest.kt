package com.cleevio.fatbot.application.module.userstatistics.scheduled

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.domain.userstatistics.UserStatisticsRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class UserFattyCardsStatisticsResetTriggerTest(
	@Autowired private val underTest: UserFattyCardsStatisticsResetTrigger,
	@Autowired private val userStatisticsRepository: UserStatisticsRepository,
) : IntegrationTest() {

	@Test
	fun `should reset trade amount for all users`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 1.toUUID(),
			beforeSave = { this.updateTradedAmount(BigDecimal.valueOf(100)) },
		)
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")
		integrationTestHelper.getUserStatistics(
			userId = 2.toUUID(),
			beforeSave = { this.updateTradedAmount(BigDecimal.valueOf(200)) },
		)

		underTest.trigger()

		userStatisticsRepository.findByUserId(1.toUUID())!!.tradedAmountInDay shouldBeEqualComparingTo BigDecimal.ZERO
		userStatisticsRepository.findByUserId(2.toUUID())!!.tradedAmountInDay shouldBeEqualComparingTo BigDecimal.ZERO
	}
}
