package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.wallet.command.PatchUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.exception.WalletNotFoundException
import com.cleevio.fatbot.domain.wallet.WalletRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired

class PatchUserWalletCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val walletRepository: WalletRepository,
) : IntegrationTest() {

	@Test
	fun `should patch user EVM wallet`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 5.toUUID(), userId = user.id, chain = Chain.EVM_MAINNET)

		commandBus(
			PatchUserWalletCommand(
				userId = 1.toUUID(),
				walletId = wallet.id,
				customName = "My ETH wallet",
				buyAntiMevProtection = true,
				sellAntiMevProtection = true,
			),
		)

		walletRepository.count() shouldBe 1
		walletRepository.findAll().first().run {
			userId shouldBe 1.toUUID()
			customName shouldBe "My ETH wallet"
			buyAntiMevProtection shouldBe true
			sellAntiMevProtection shouldBe true
		}
	}

	@Test
	fun `should patch SOLANA wallet`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 5.toUUID(), userId = user.id, chain = Chain.SOLANA)

		commandBus(
			PatchUserWalletCommand(
				userId = 1.toUUID(),
				walletId = wallet.id,
				customName = "My SOLANA wallet",
				buyAntiMevProtection = true,
				sellAntiMevProtection = true,
			),
		)

		walletRepository.count() shouldBe 1
		walletRepository.findAll().first().run {
			userId shouldBe 1.toUUID()
			customName shouldBe "My SOLANA wallet"
			buyAntiMevProtection shouldBe true
			sellAntiMevProtection shouldBe true
		}
	}

	@Test
	fun `should throw exception when patching another user's wallet`() {
		val user1 = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val user2 = integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")
		val wallet1 = integrationTestHelper.getWallet(id = 5.toUUID(), userId = user1.id, chain = Chain.EVM_MAINNET)
		val wallet2 = integrationTestHelper.getWallet(id = 6.toUUID(), userId = user2.id, chain = Chain.EVM_MAINNET)

		assertThrows<WalletNotFoundException> {
			commandBus(
				PatchUserWalletCommand(
					userId = 1.toUUID(),
					walletId = wallet2.id,
					customName = "My ETH wallet",
					buyAntiMevProtection = true,
					sellAntiMevProtection = true,
				),
			)
		}
	}
}
