package com.cleevio.fatbot.application.module.portfolio

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.portfolio.constant.PieChartValueType
import com.cleevio.fatbot.application.module.portfolio.query.GetPortfolioValuesQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.portfolio.PortfolioValueSnapshot
import com.cleevio.fatbot.domain.portfolio.PortfolioValueSnapshotRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger
import java.time.temporal.ChronoUnit

class GetPortfolioValuesQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val portfolioValueSnapshotRepository: PortfolioValueSnapshotRepository,
	@Autowired private val clock: IntegrationTestClock,
) : IntegrationTest() {

	@Test
	fun `should get portfolio value when no positions`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns "2300.001".toBigDecimal()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "230".toBigDecimal()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.BNB) } returns "230".toBigDecimal()
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getBaseFeePerGas() } returns 1000.toBigInteger()

		val result = queryBus(GetPortfolioValuesQuery(user.id))

		result.pastValues shouldBe listOf(
			GetPortfolioValuesQuery.PortfolioPastValue(
				portfolioValueUsd = BigDecimal("0.000000000000000000000"),
				date = clock.getCurrentDate(),
			),
		)

		result.pieChartValues shouldContainExactlyInAnyOrder listOf(
			GetPortfolioValuesQuery.PieChartValue(
				chain = Chain.EVM_MAINNET,
				portfolioValueUsd = BigDecimal("0.000000000000000000000"),
				portfolioPnlFraction = BigDecimal.ZERO,
				type = PieChartValueType.MANUAL,
			),
			GetPortfolioValuesQuery.PieChartValue(
				chain = Chain.EVM_BASE,
				portfolioValueUsd = BigDecimal.ZERO,
				portfolioPnlFraction = BigDecimal.ZERO,
				type = PieChartValueType.MANUAL,
			),
			GetPortfolioValuesQuery.PieChartValue(
				chain = Chain.EVM_BSC,
				portfolioValueUsd = BigDecimal.ZERO,
				portfolioPnlFraction = BigDecimal.ZERO,
				type = PieChartValueType.MANUAL,
			),
			GetPortfolioValuesQuery.PieChartValue(
				chain = Chain.EVM_ARBITRUM_ONE,
				portfolioValueUsd = BigDecimal.ZERO,
				portfolioPnlFraction = BigDecimal.ZERO,
				type = PieChartValueType.MANUAL,
			),
			GetPortfolioValuesQuery.PieChartValue(
				chain = Chain.SOLANA,
				portfolioValueUsd = BigDecimal("0"),
				portfolioPnlFraction = BigDecimal.ZERO,
				type = PieChartValueType.MANUAL,
			),
			GetPortfolioValuesQuery.PieChartValue(
				chain = Chain.SOLANA,
				portfolioValueUsd = BigDecimal("0"),
				portfolioPnlFraction = BigDecimal.ZERO,
				type = PieChartValueType.BOT,
			),
		)
	}

	@Test
	fun `should get portfolio value`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)

		// past values
		portfolioValueSnapshotRepository.save(
			PortfolioValueSnapshot(
				userId = user.id,
				chain = Chain.EVM_MAINNET,
				date = clock.getCurrentDate(),
				valueUsd = BigDecimal("1300.001"),
			),
		)
		portfolioValueSnapshotRepository.save(
			PortfolioValueSnapshot(
				userId = user.id,
				chain = Chain.SOLANA,
				date = clock.getCurrentDate(),
				valueUsd = BigDecimal("1000"),
			),
		)
		clock.advanceBy(1, ChronoUnit.DAYS)

		portfolioValueSnapshotRepository.save(
			PortfolioValueSnapshot(
				userId = user.id,
				chain = Chain.EVM_MAINNET,
				date = clock.getCurrentDate(),
				valueUsd = BigDecimal("2543.05"),
			),
		)

		// 1 ETH in wallet
		integrationTestHelper.getWalletCurrencyPosition(
			walletId = 1.toUUID(),
			totalBought = BigInteger("1500000000000000000"),
			totalSold = BigInteger("500000000000000000"),
			totalAcquisitionCostUsd = BigDecimal("2300.001"),
		)

		clock.advanceBy(1, ChronoUnit.DAYS)

		// current value
		integrationTestHelper.getEvmTokenInfo(
			tokenAddress = AddressWrapper(addressString = "******************************************"),
		)

		integrationTestHelper.getMarketPosition(
			walletId = wallet.id,
			tokenAddress = AddressWrapper(addressString = "******************************************"),
			beforeSave = {
				newBuy(
					amountOfTokensReceived = 1000000000000000000.toBigInteger(), // 1 full token with 18 decimals
					amountOfBaseCurrencyPaid = 1000000000000000000.toBigInteger(), // 1 ETH
					exchangeRate = BigDecimal("1.5"),
				)
			},
		)

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns "2300.001".toBigDecimal()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "230".toBigDecimal()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.BNB) } returns "230".toBigDecimal()
		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getBaseFeePerGas() } returns 1000.toBigInteger()

		// GetTokenPricesEVM call - token price is 0.5 ETH
		every {
			@Suppress("ktlint:standard:max-line-length")
			readOnlyTransactionManager.sendCall(
				"******************************************",
				"0xe016d3ab000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000010000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000006982508145454ce325ddbe47a25d4ec3d2311933" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"00000000000000000000000000000000000000000000000006F05B59D3B20000"

		val result = queryBus(GetPortfolioValuesQuery(user.id))

		result.pastValues shouldContainExactly listOf(
			GetPortfolioValuesQuery.PortfolioPastValue(
				portfolioValueUsd = "2300.001".toBigDecimal(),
				date = clock.getCurrentDate().minusDays(2),
			),
			GetPortfolioValuesQuery.PortfolioPastValue(
				portfolioValueUsd = "2543.05".toBigDecimal(),
				date = clock.getCurrentDate().minusDays(1),
			),
			GetPortfolioValuesQuery.PortfolioPastValue(
				// 1 ETH (wallet) + .5 ETH (market position) = 1.5 ETH
				portfolioValueUsd = "3450.0015000000000000000000000000000000000".toBigDecimal(),
				date = clock.getCurrentDate(),
			),
		)

		result.pieChartValues shouldContainExactlyInAnyOrder listOf(
			GetPortfolioValuesQuery.PieChartValue(
				chain = Chain.EVM_MAINNET,
				portfolioValueUsd = "3450.0015000000000000000000000000000000000".toBigDecimal(),
				portfolioPnlFraction = BigDecimal("1.247801065131473501368877676673829"),
				type = PieChartValueType.MANUAL,
			),
			GetPortfolioValuesQuery.PieChartValue(
				chain = Chain.EVM_BASE,
				portfolioValueUsd = BigDecimal.ZERO,
				portfolioPnlFraction = BigDecimal.ZERO,
				type = PieChartValueType.MANUAL,
			),
			GetPortfolioValuesQuery.PieChartValue(
				chain = Chain.EVM_BSC,
				portfolioValueUsd = BigDecimal.ZERO,
				portfolioPnlFraction = BigDecimal.ZERO,
				type = PieChartValueType.MANUAL,
			),
			GetPortfolioValuesQuery.PieChartValue(
				chain = Chain.EVM_ARBITRUM_ONE,
				portfolioValueUsd = BigDecimal.ZERO,
				portfolioPnlFraction = BigDecimal.ZERO,
				type = PieChartValueType.MANUAL,
			),
			GetPortfolioValuesQuery.PieChartValue(
				chain = Chain.SOLANA,
				portfolioValueUsd = BigDecimal.ZERO,
				portfolioPnlFraction = BigDecimal.ZERO,
				type = PieChartValueType.MANUAL,
			),
			GetPortfolioValuesQuery.PieChartValue(
				chain = Chain.SOLANA,
				portfolioValueUsd = BigDecimal.ZERO,
				portfolioPnlFraction = BigDecimal.ZERO,
				type = PieChartValueType.BOT,
			),
		)
	}
}
