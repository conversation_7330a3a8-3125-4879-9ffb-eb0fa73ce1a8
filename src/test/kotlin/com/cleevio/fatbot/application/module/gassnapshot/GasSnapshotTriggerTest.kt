package com.cleevio.fatbot.application.module.gassnapshot

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.evm.model.FeesPerGas
import com.cleevio.fatbot.application.module.gassnapshot.scheduled.GasInfoSnapshotTrigger
import com.cleevio.fatbot.domain.gassnapshot.GasInfoSnapshotRepository
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.date.shouldBeAfter
import io.mockk.every
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class GasSnapshotTriggerTest(
	@Autowired private val gasInfoSnapshotRepository: GasInfoSnapshotRepository,
	@Autowired private val gasInfoSnapshotTrigger: GasInfoSnapshotTrigger,
) : IntegrationTest() {

	@Test
	fun `should save and update snapshot for all EVM chains`() {
		every { web3jWrapper.getFeesPerGas() } returnsMany
			listOf(
				FeesPerGas(
					baseFee = 10.toBigInteger(),
					maxPriorityFee = 1000000000.toBigInteger(),
				),
				FeesPerGas(
					baseFee = 9.toBigInteger(),
					maxPriorityFee = 1000000000.toBigInteger(),
				),
				FeesPerGas(
					baseFee = 8.toBigInteger(),
					maxPriorityFee = 1000000000.toBigInteger(),
				),
				FeesPerGas(
					baseFee = 7.toBigInteger(),
					maxPriorityFee = 1000000000.toBigInteger(),
				),
			)

		gasInfoSnapshotTrigger.trigger()
		val initialSnapshots = gasInfoSnapshotRepository.findAll()

		gasInfoSnapshotTrigger.trigger()
		val updatedSnapshots = gasInfoSnapshotRepository.findAll()

		verify {
			chainProperties.ofEvmChainId(1)
			chainProperties.ofEvmChainId(8453)
			chainProperties.ofEvmChainId(56)
			chainProperties.ofEvmChainId(42161)

			chainProperties.ofEvmChainId(1)
			chainProperties.ofEvmChainId(8453)
			chainProperties.ofEvmChainId(56)
			chainProperties.ofEvmChainId(42161)
		}

		initialSnapshots shouldHaveSize 4
		updatedSnapshots shouldHaveSize 4

		val initialChain1Snapshot = initialSnapshots.single { it.chainId == 1L }
		val updatedChain1Snapshot = updatedSnapshots.single { it.chainId == 1L }

		updatedChain1Snapshot.updatedAt shouldBeAfter initialChain1Snapshot.updatedAt

		val initialChain2Snapshot = initialSnapshots.single { it.chainId == 8453L }
		val updatedChain2Snapshot = updatedSnapshots.single { it.chainId == 8453L }

		updatedChain2Snapshot.updatedAt shouldBeAfter initialChain2Snapshot.updatedAt
	}
}
