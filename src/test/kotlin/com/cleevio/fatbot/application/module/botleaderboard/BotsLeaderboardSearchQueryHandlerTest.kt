package com.cleevio.fatbot.application.module.botleaderboard

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.adapter.out.evm.GetTokenPricesEVM
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.bot.constant.BotSortParameter
import com.cleevio.fatbot.application.module.botleaderboard.query.BotsLeaderboardSearchQuery
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.getPrivatePropertyOfProxy
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION
import com.cleevio.fatbot.toUUID
import com.github.benmanes.caffeine.cache.AsyncCache
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.jooq.DSLContext
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.test.Test

class BotsLeaderboardSearchQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val clock: IntegrationTestClock,
	@Autowired private val dslContext: DSLContext,
) : IntegrationTest() {

	@Test
	fun `should return leaderboard for all bots sorted by profit USD`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		integrationTestHelper.getFile(id = 31.toUUID())
		integrationTestHelper.getFile(id = 32.toUUID())
		integrationTestHelper.getFile(id = 33.toUUID())

		integrationTestHelper.getBot(
			id = 100.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 31.toUUID(),
			name = "Bot1",
		)
		integrationTestHelper.getBot(
			id = 101.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 32.toUUID(),
			name = "Bot2",
		)
		integrationTestHelper.getBot(
			id = 102.toUUID(),
			userId = 2.toUUID(),
			avatarFileId = 33.toUUID(),
			name = "Bot3",
		)

		integrationTestHelper.getBotWallet(
			id = 200.toUUID(),
			botId = 100.toUUID(),
		)
		integrationTestHelper.getBotWallet(
			id = 201.toUUID(),
			botId = 101.toUUID(),
		)
		integrationTestHelper.getBotWallet(
			id = 202.toUUID(),
			botId = 102.toUUID(),
		)

		val now = Instant.now(clock)

		// Create leaderboard snapshots
		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 100.toUUID(),
			profitValueUsd = BigDecimal("1000.0"),
			profitValueFraction = BigDecimal("0.5"),
			balanceUsd = BigDecimal("500.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 101.toUUID(),
			profitValueUsd = BigDecimal("2000.0"),
			profitValueFraction = BigDecimal("0.8"),
			balanceUsd = BigDecimal("1000.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 102.toUUID(),
			profitValueUsd = BigDecimal("1500.0"),
			profitValueFraction = BigDecimal("0.6"),
			balanceUsd = BigDecimal("750.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		// Set up token information
		integrationTestHelper.getFile(id = 21.toUUID())
		integrationTestHelper.getFile(id = 22.toUUID())

		val tokenAddress1 = AddressWrapper("FYxc7ntfTjV2c86oF4dDp8yYiyNLAR5rdhFhNGSMpump")
		val tokenAddress2 = AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump")

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress1,
			name = "ADHD",
			symbol = "ADHD",
			imageFileId = 21.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress2,
			name = "yolo",
			symbol = "yolo",
			imageFileId = 22.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 400.toUUID(),
			botWalletId = 200.toUUID(),
			tokenAddress = tokenAddress1,
			profitTargetFraction = BigDecimal("20.0"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234567,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "50".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 401.toUUID(),
			botWalletId = 201.toUUID(),
			tokenAddress = tokenAddress2,
			profitTargetFraction = BigDecimal("31.83"),
			stopLossFraction = BigDecimal("21.22"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234568,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "15".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		// Quick-fix for clock not being used for createdAt/updatedAt timestamp on JPA entities
		dslContext.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.CREATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))
			.set(BOT_MARKET_POSITION.UPDATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))
			.execute()

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress1,
			chain = Chain.SOLANA,
			priceWei = BigInteger("16"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress2,
			chain = Chain.SOLANA,
			priceWei = BigInteger("26"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		Chain.entries.forEach { chain ->
			every { coinbaseConnector.getUsdExchangeRate(chain.currency) } returns BigDecimal("100.0")
		}

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(tokenAddress1.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("100")))
					put(tokenAddress2.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("200")))
				},
			)

		// when
		val result = queryBus(
			BotsLeaderboardSearchQuery(
				userId = 1.toUUID(),
				filter = BotsLeaderboardSearchQuery.Filter(
					timeRange = TimeRange.WEEK,
					searchString = null,
					allBots = true,
					sortParameter = BotSortParameter.PROFIT_USD,
				),
				infiniteScroll = InfiniteScrollDesc.BigDecimal(),
			),
		)

		// then
		result.content.size shouldBe 3
		result.content.map { it.botName } shouldContainExactlyInAnyOrder listOf("Bot1", "Bot2", "Bot3")
		result.content.sortedByDescending { it.profitUsd }.map { it.botName } shouldBe listOf("Bot1", "Bot3", "Bot2")
	}

	@Test
	fun `should return leaderboard only for user's bots when allBots is false`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		integrationTestHelper.getFile(id = 31.toUUID())
		integrationTestHelper.getFile(id = 32.toUUID())
		integrationTestHelper.getFile(id = 33.toUUID())

		integrationTestHelper.getBot(
			id = 100.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 31.toUUID(),
			name = "Bot1",
		)
		integrationTestHelper.getBot(
			id = 101.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 32.toUUID(),
			name = "Bot2",
		)
		integrationTestHelper.getBot(
			id = 102.toUUID(),
			userId = 2.toUUID(),
			avatarFileId = 33.toUUID(),
			name = "Bot3",
		)

		integrationTestHelper.getBotWallet(
			id = 200.toUUID(),
			botId = 100.toUUID(),
		)
		integrationTestHelper.getBotWallet(
			id = 201.toUUID(),
			botId = 101.toUUID(),
		)
		integrationTestHelper.getBotWallet(
			id = 202.toUUID(),
			botId = 102.toUUID(),
		)

		val now = Instant.now(clock)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 100.toUUID(),
			profitValueUsd = BigDecimal("1000.0"),
			profitValueFraction = BigDecimal("0.5"),
			balanceUsd = BigDecimal("500.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 101.toUUID(),
			profitValueUsd = BigDecimal("2000.0"),
			profitValueFraction = BigDecimal("0.8"),
			balanceUsd = BigDecimal("1000.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 102.toUUID(),
			profitValueUsd = BigDecimal("1500.0"),
			profitValueFraction = BigDecimal("0.6"),
			balanceUsd = BigDecimal("750.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		integrationTestHelper.getFile(id = 21.toUUID())
		integrationTestHelper.getFile(id = 22.toUUID())
		integrationTestHelper.getFile(id = 23.toUUID())

		val tokenAddress1 = AddressWrapper("FYxc7ntfTjV2c86oF4dDp8yYiyNLAR5rdhFhNGSMpump")
		val tokenAddress2 = AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump")
		val tokenAddress3 = AddressWrapper("HTYLJ5QTnq28HFUWQuBPKdc4RJu6JGb5ucesqAt3xTt8")

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress1,
			name = "ADHD",
			symbol = "ADHD",
			imageFileId = 21.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress2,
			name = "yolo",
			symbol = "yolo",
			imageFileId = 22.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress3,
			name = "PumpFunAss",
			symbol = "PumpFunAss",
			imageFileId = 23.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 400.toUUID(),
			botWalletId = 200.toUUID(),
			tokenAddress = tokenAddress1,
			profitTargetFraction = BigDecimal("20.0"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234567,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "50".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 401.toUUID(),
			botWalletId = 201.toUUID(),
			tokenAddress = tokenAddress2,
			profitTargetFraction = BigDecimal("31.83"),
			stopLossFraction = BigDecimal("21.22"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234568,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "15".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 402.toUUID(),
			botWalletId = 202.toUUID(),
			tokenAddress = tokenAddress3,
			profitTargetFraction = BigDecimal("23.3"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234569,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "25".toBigInteger(),
					amountIn = "20".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		// Quick-fix for clock not being used for createdAt/updatedAt timestamp on JPA entities
		dslContext.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.CREATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))
			.set(BOT_MARKET_POSITION.UPDATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))
			.execute()

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress1,
			chain = Chain.SOLANA,
			priceWei = BigInteger("16"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress2,
			chain = Chain.SOLANA,
			priceWei = BigInteger("26"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress3,
			chain = Chain.SOLANA,
			priceWei = BigInteger("36"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		Chain.entries.forEach { chain ->
			every { coinbaseConnector.getUsdExchangeRate(chain.currency) } returns BigDecimal("100.0")
		}

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(tokenAddress1.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("100")))
					put(tokenAddress2.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("200")))
					put(tokenAddress3.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("300")))
				},
			)

		// when
		val result = queryBus(
			BotsLeaderboardSearchQuery(
				userId = 1.toUUID(),
				filter = BotsLeaderboardSearchQuery.Filter(
					timeRange = TimeRange.WEEK,
					searchString = null,
					allBots = false,
					sortParameter = BotSortParameter.PROFIT_USD,
				),
				infiniteScroll = InfiniteScrollDesc.BigDecimal(),
			),
		)

		// then
		result.content.size shouldBe 2
		result.content.map { it.botName } shouldContainExactlyInAnyOrder listOf("Bot1", "Bot2")
	}

	@Test
	fun `should filter bots by search string`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getFile(id = 31.toUUID())
		integrationTestHelper.getFile(id = 32.toUUID())

		integrationTestHelper.getBot(
			id = 100.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 31.toUUID(),
			name = "TradingBot1",
		)
		integrationTestHelper.getBot(
			id = 101.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 32.toUUID(),
			name = "TradingBot2",
		)

		integrationTestHelper.getBotWallet(
			id = 200.toUUID(),
			botId = 100.toUUID(),
		)
		integrationTestHelper.getBotWallet(
			id = 201.toUUID(),
			botId = 101.toUUID(),
		)

		val now = Instant.now(clock)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 100.toUUID(),
			profitValueUsd = BigDecimal("1000.0"),
			profitValueFraction = BigDecimal("0.5"),
			balanceUsd = BigDecimal("500.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 101.toUUID(),
			profitValueUsd = BigDecimal("2000.0"),
			profitValueFraction = BigDecimal("0.8"),
			balanceUsd = BigDecimal("1000.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		integrationTestHelper.getFile(id = 21.toUUID())
		integrationTestHelper.getFile(id = 22.toUUID())

		val tokenAddress1 = AddressWrapper("FYxc7ntfTjV2c86oF4dDp8yYiyNLAR5rdhFhNGSMpump")
		val tokenAddress2 = AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump")

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress1,
			name = "ADHD",
			symbol = "ADHD",
			imageFileId = 21.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress2,
			name = "yolo",
			symbol = "yolo",
			imageFileId = 22.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 400.toUUID(),
			botWalletId = 200.toUUID(),
			tokenAddress = tokenAddress1,
			profitTargetFraction = BigDecimal("20.0"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234567,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "50".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 401.toUUID(),
			botWalletId = 201.toUUID(),
			tokenAddress = tokenAddress2,
			profitTargetFraction = BigDecimal("31.83"),
			stopLossFraction = BigDecimal("21.22"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234568,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "15".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		// Quick-fix for clock not being used for createdAt/updatedAt timestamp on JPA entities
		dslContext.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.CREATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))
			.set(BOT_MARKET_POSITION.UPDATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))
			.execute()

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress1,
			chain = Chain.SOLANA,
			priceWei = BigInteger("16"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress2,
			chain = Chain.SOLANA,
			priceWei = BigInteger("26"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		Chain.entries.forEach { chain ->
			every { coinbaseConnector.getUsdExchangeRate(chain.currency) } returns BigDecimal("100.0")
		}

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(tokenAddress1.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("100")))
					put(tokenAddress2.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("200")))
				},
			)

		// when
		val result = queryBus(
			BotsLeaderboardSearchQuery(
				userId = 1.toUUID(),
				filter = BotsLeaderboardSearchQuery.Filter(
					timeRange = TimeRange.WEEK,
					searchString = "TradingBot1",
					allBots = true,
					sortParameter = BotSortParameter.PROFIT_USD,
				),
				infiniteScroll = InfiniteScrollDesc.BigDecimal(),
			),
		)

		// then
		result.content.size shouldBe 1
		result.content.first().botName shouldBe "TradingBot1"
	}

	@Test
	fun `should sort by different parameters`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getFile(id = 31.toUUID())
		integrationTestHelper.getFile(id = 32.toUUID())
		integrationTestHelper.getFile(id = 33.toUUID())

		integrationTestHelper.getBot(
			id = 100.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 31.toUUID(),
			name = "Bot1",
		)
		integrationTestHelper.getBot(
			id = 101.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 32.toUUID(),
			name = "Bot2",
		)
		integrationTestHelper.getBot(
			id = 102.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 33.toUUID(),
			name = "Bot3",
		)

		integrationTestHelper.getBotWallet(
			id = 200.toUUID(),
			botId = 100.toUUID(),
		)
		integrationTestHelper.getBotWallet(
			id = 201.toUUID(),
			botId = 101.toUUID(),
		)
		integrationTestHelper.getBotWallet(
			id = 202.toUUID(),
			botId = 102.toUUID(),
		)

		val now = Instant.now(clock)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 100.toUUID(),
			profitValueUsd = BigDecimal("1000.0"),
			profitValueFraction = BigDecimal("0.5"),
			balanceUsd = BigDecimal("1300.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 101.toUUID(),
			profitValueUsd = BigDecimal("2000.0"),
			profitValueFraction = BigDecimal("0.8"),
			balanceUsd = BigDecimal("500.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 102.toUUID(),
			profitValueUsd = BigDecimal("800.0"),
			profitValueFraction = BigDecimal("0.9"),
			balanceUsd = BigDecimal("1200.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 102.toUUID(),
			profitValueUsd = BigDecimal("780.0"),
			profitValueFraction = BigDecimal("0.85"),
			balanceUsd = BigDecimal("1100.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS).minus(30, ChronoUnit.MINUTES),
		)

		integrationTestHelper.getFile(id = 21.toUUID())
		integrationTestHelper.getFile(id = 22.toUUID())
		integrationTestHelper.getFile(id = 23.toUUID())

		val tokenAddress1 = AddressWrapper("FYxc7ntfTjV2c86oF4dDp8yYiyNLAR5rdhFhNGSMpump")
		val tokenAddress2 = AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump")
		val tokenAddress3 = AddressWrapper("HTYLJ5QTnq28HFUWQuBPKdc4RJu6JGb5ucesqAt3xTt8")

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress1,
			name = "ADHD",
			symbol = "ADHD",
			imageFileId = 21.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress2,
			name = "yolo",
			symbol = "yolo",
			imageFileId = 22.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress3,
			name = "PumpFunAss",
			symbol = "PumpFunAss",
			imageFileId = 23.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 400.toUUID(),
			botWalletId = 200.toUUID(),
			tokenAddress = tokenAddress1,
			profitTargetFraction = BigDecimal("20.0"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234567,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "50".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 401.toUUID(),
			botWalletId = 201.toUUID(),
			tokenAddress = tokenAddress2,
			profitTargetFraction = BigDecimal("31.83"),
			stopLossFraction = BigDecimal("21.22"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234568,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "15".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 402.toUUID(),
			botWalletId = 202.toUUID(),
			tokenAddress = tokenAddress3,
			profitTargetFraction = BigDecimal("23.3"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234569,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "25".toBigInteger(),
					amountIn = "20".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		// Quick-fix for clock not being used for createdAt/updatedAt timestamp on JPA entities
		dslContext.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.CREATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))
			.set(BOT_MARKET_POSITION.UPDATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))
			.execute()

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress1,
			chain = Chain.SOLANA,
			priceWei = BigInteger("16"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress2,
			chain = Chain.SOLANA,
			priceWei = BigInteger("26"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress3,
			chain = Chain.SOLANA,
			priceWei = BigInteger("36"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		Chain.entries.forEach { chain ->
			every { coinbaseConnector.getUsdExchangeRate(chain.currency) } returns BigDecimal("100.0")
		}

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(tokenAddress1.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("100")))
					put(tokenAddress2.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("200")))
					put(tokenAddress3.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("300")))
				},
			)

		val resultByProfitUsd = queryBus(
			BotsLeaderboardSearchQuery(
				userId = 1.toUUID(),
				filter = BotsLeaderboardSearchQuery.Filter(
					timeRange = TimeRange.WEEK,
					searchString = null,
					allBots = true,
					sortParameter = BotSortParameter.PROFIT_USD,
				),
				infiniteScroll = InfiniteScrollDesc.BigDecimal(),
			),
		)

		resultByProfitUsd.content.map { it.botName } shouldBe listOf("Bot2", "Bot1", "Bot3")

		val resultByProfitPercentage = queryBus(
			BotsLeaderboardSearchQuery(
				userId = 1.toUUID(),
				filter = BotsLeaderboardSearchQuery.Filter(
					timeRange = TimeRange.WEEK,
					searchString = null,
					allBots = true,
					sortParameter = BotSortParameter.PROFIT_PERCENTAGE,
				),
				infiniteScroll = InfiniteScrollDesc.BigDecimal(),
			),
		)
		resultByProfitPercentage.content.map { it.botName } shouldBe listOf("Bot3", "Bot2", "Bot1")

		val resultByWalletBalance = queryBus(
			BotsLeaderboardSearchQuery(
				userId = 1.toUUID(),
				filter = BotsLeaderboardSearchQuery.Filter(
					timeRange = TimeRange.WEEK,
					searchString = null,
					allBots = true,
					sortParameter = BotSortParameter.WALLET_BALANCE,
				),
				infiniteScroll = InfiniteScrollDesc.BigDecimal(),
			),
		)
		resultByWalletBalance.content.map { it.botName } shouldBe listOf("Bot1", "Bot3", "Bot2")
	}

	@Test
	fun `should calculate changes in wallet balance, profit USD, and profit percentage`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getFile(id = 31.toUUID())
		integrationTestHelper.getFile(id = 32.toUUID())
		integrationTestHelper.getFile(id = 33.toUUID())

		integrationTestHelper.getBot(
			id = 100.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 31.toUUID(),
			name = "Bot1",
		)
		integrationTestHelper.getBot(
			id = 101.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 32.toUUID(),
			name = "Bot2",
		)
		integrationTestHelper.getBot(
			id = 102.toUUID(),
			userId = 1.toUUID(),
			avatarFileId = 33.toUUID(),
			name = "Bot3",
		)

		integrationTestHelper.getBotWallet(
			id = 200.toUUID(),
			botId = 100.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("100000000")) // 0.1 SOL
				increaseAcquisitionValue(BigDecimal("10.0")) // at 100$/SOL
			},
		)
		integrationTestHelper.getBotWallet(
			id = 201.toUUID(),
			botId = 101.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("200000000")) // 0.2 SOL
				increaseAcquisitionValue(BigDecimal("20.0")) // at 100$/SOL
			},
		)
		integrationTestHelper.getBotWallet(
			id = 202.toUUID(),
			botId = 102.toUUID(),
			beforeSave = {
				increaseBalance(BigInteger("300000000")) // 0.3 SOL
				increaseAcquisitionValue(BigDecimal("30.0")) // at 100$/SOL
			},
		)

		val now = Instant.now(clock)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 100.toUUID(),
			profitValueUsd = BigDecimal("100.0"),
			profitValueFraction = BigDecimal("0.5"),
			balanceUsd = BigDecimal("200.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 101.toUUID(),
			profitValueUsd = BigDecimal("200.0"),
			profitValueFraction = BigDecimal("0.8"),
			balanceUsd = BigDecimal("300.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		integrationTestHelper.getBotLeaderboardSnapshot(
			botId = 102.toUUID(),
			profitValueUsd = BigDecimal("150.0"),
			profitValueFraction = BigDecimal("0.6"),
			balanceUsd = BigDecimal("250.0"),
			snapshotMadeAt = now.minus(1, ChronoUnit.HOURS),
		)

		integrationTestHelper.getFile(id = 21.toUUID())
		integrationTestHelper.getFile(id = 22.toUUID())
		integrationTestHelper.getFile(id = 23.toUUID())

		val tokenAddress1 = AddressWrapper("FYxc7ntfTjV2c86oF4dDp8yYiyNLAR5rdhFhNGSMpump")
		val tokenAddress2 = AddressWrapper("2xmhwZrE91rKbwNDHiQQi4WcnCTsx5EAaBUaHgWCpump")
		val tokenAddress3 = AddressWrapper("HTYLJ5QTnq28HFUWQuBPKdc4RJu6JGb5ucesqAt3xTt8")

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress1,
			name = "ADHD",
			symbol = "ADHD",
			imageFileId = 21.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress2,
			name = "yolo",
			symbol = "yolo",
			imageFileId = 22.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = tokenAddress3,
			name = "PumpFunAss",
			symbol = "PumpFunAss",
			imageFileId = 23.toUUID(),
			chain = Chain.SOLANA,
			tokenDecimals = 6.toBigInteger(),
		)

		integrationTestHelper.getBotMarketPosition(
			id = 400.toUUID(),
			botWalletId = 200.toUUID(),
			tokenAddress = tokenAddress1,
			profitTargetFraction = BigDecimal("20.0"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234567,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "50".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 401.toUUID(),
			botWalletId = 201.toUUID(),
			tokenAddress = tokenAddress2,
			profitTargetFraction = BigDecimal("31.83"),
			stopLossFraction = BigDecimal("21.22"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234568,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "15".toBigInteger(),
					amountIn = "10".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		integrationTestHelper.getBotMarketPosition(
			id = 402.toUUID(),
			botWalletId = 202.toUUID(),
			tokenAddress = tokenAddress3,
			profitTargetFraction = BigDecimal("23.3"),
			stopLossFraction = BigDecimal("8.0"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234569,
			beforeSave = {
				setBuyDataFromChain(
					amountOut = "25".toBigInteger(),
					amountIn = "20".toBigInteger(),
					fee = "1".toBigInteger(),
					exchangeRate = BigDecimal("3.5"),
				)
			},
		)

		// Quick-fix for clock not being used for createdAt/updatedAt timestamp on JPA entities
		dslContext.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.CREATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))
			.set(BOT_MARKET_POSITION.UPDATED_AT, Instant.now(clock).minus(1, ChronoUnit.HOURS))
			.execute()

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress1,
			chain = Chain.SOLANA,
			priceWei = BigInteger("16"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress2,
			chain = Chain.SOLANA,
			priceWei = BigInteger("26"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		integrationTestHelper.getTokenPriceSnapshot(
			tokenAddress = tokenAddress3,
			chain = Chain.SOLANA,
			priceWei = BigInteger("36"),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
			exchangeRateUsd = BigDecimal("3.5"),
		)

		Chain.entries.forEach { chain ->
			every { coinbaseConnector.getUsdExchangeRate(chain.currency) } returns BigDecimal("100.0")
		}

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<ChainAddress, BaseAmount>>("cache")!!
			.synchronous()
			.putAll(
				buildMap {
					put(tokenAddress1.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("100")))
					put(tokenAddress2.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("200")))
					put(tokenAddress3.toChainAddress(chain = Chain.SOLANA), BaseAmount(BigInteger("300")))
				},
			)

		// when
		val result = queryBus(
			BotsLeaderboardSearchQuery(
				userId = 1.toUUID(),
				filter = BotsLeaderboardSearchQuery.Filter(
					timeRange = TimeRange.WEEK,
					searchString = null,
					allBots = true,
					sortParameter = BotSortParameter.PROFIT_USD,
				),
				infiniteScroll = InfiniteScrollDesc.BigDecimal(),
			),
		)

		// then
		result.content.size shouldBe 3

		val bot1Result = result.content.find { it.botName == "Bot1" }!!
		val bot2Result = result.content.find { it.botName == "Bot2" }!!
		val bot3Result = result.content.find { it.botName == "Bot3" }!!

		result.content.size shouldBe 3
		result.content.map { it.botName } shouldContainExactlyInAnyOrder listOf("Bot1", "Bot2", "Bot3")

		bot1Result.profitUsd shouldBeEqualComparingTo BigDecimal("-99.7960719995")
		bot1Result.profitPercentage shouldBeEqualComparingTo BigDecimal("-0.47960719995")
		bot1Result.walletBalance shouldBeEqualComparingTo BigDecimal("-189.7960719995")

		bot2Result.profitUsd shouldBeEqualComparingTo BigDecimal("-199.7960719997")
		bot2Result.profitPercentage shouldBeEqualComparingTo BigDecimal("-0.789803599985")
		bot2Result.walletBalance shouldBeEqualComparingTo BigDecimal("-279.7960719997")

		bot3Result.profitUsd shouldBeEqualComparingTo BigDecimal("-149.79607199925")
		bot3Result.profitPercentage shouldBeEqualComparingTo BigDecimal("-0.593202399975")
		bot3Result.walletBalance shouldBeEqualComparingTo BigDecimal("-219.79607199925")
	}
}
