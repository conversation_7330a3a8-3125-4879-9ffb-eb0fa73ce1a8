package com.cleevio.fatbot.application.module.userfattycard

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.userfattycard.query.GetUserFattyCardsOverviewQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class GetUserFattyCardsOverviewQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should calculate available cards based on traded amount`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 1.toUUID(),
			beforeSave = { updateTradedAmount(25000.32.toBigDecimal()) },
		)
		// three cards earned today
		integrationTestHelper.getFile(id = 3.toUUID())
		integrationTestHelper.getFattyCard(id = 3.toUUID(), avatarFileId = 3.toUUID())
		integrationTestHelper.getUserFattyCard(
			id = 1.toUUID(),
			fattyCardId = 3.toUUID(),
			userId = 1.toUUID(),
		)
		integrationTestHelper.getUserFattyCard(
			id = 2.toUUID(),
			fattyCardId = 3.toUUID(),
			userId = 1.toUUID(),
		)
		integrationTestHelper.getUserFattyCard(
			id = 3.toUUID(),
			fattyCardId = 3.toUUID(),
			userId = 1.toUUID(),
		)

		val result = queryBus(
			GetUserFattyCardsOverviewQuery(
				userId = 1.toUUID(),
			),
		)

		result.userId shouldBe 1.toUUID()
		result.amountNeededToNextFattyCard shouldBeEqualComparingTo 999.68.toBigDecimal()
		result.amountOfFattyCardsEarnedToday shouldBe 3
	}

	@Test
	fun `should respect daily card limit`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 1.toUUID(),
			beforeSave = { updateTradedAmount(25000.32.toBigDecimal()) }, // Would be 5 cards without limit
		)
		// Maximum cards earned today
		integrationTestHelper.getFile(id = 3.toUUID())
		integrationTestHelper.getFattyCard(id = 3.toUUID(), avatarFileId = 3.toUUID())
		(0..9).forEach {
			integrationTestHelper.getUserFattyCard(
				id = it.toUUID(),
				fattyCardId = 3.toUUID(),
				userId = 1.toUUID(),
			)
		}

		val result = queryBus(
			GetUserFattyCardsOverviewQuery(
				userId = 1.toUUID(),
			),
		)

		result.amountOfFattyCardsEarnedToday shouldBe 10
		result.amountNeededToNextFattyCard shouldBe BigDecimal.ZERO
	}
}
