package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.user.command.UpdateSelectedChainsCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.user.FirebaseUserRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldNotContain
import io.kotest.matchers.shouldBe
import org.springframework.beans.factory.annotation.Autowired
import kotlin.test.Test

class UpdateSelectedChainsCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val firebaseUserRepository: FirebaseUserRepository,
) : IntegrationTest() {

	@Test
	fun `should update user selected chains`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		// when
		commandBus(UpdateSelectedChainsCommand(userId = 1.toUUID(), selectedChains = setOf(Chain.SOLANA)))

		// then
		val updatedUser = firebaseUserRepository.findAll().first()

		updatedUser.id shouldBe 1.toUUID()

		updatedUser.selectedChains shouldContain Chain.SOLANA

		updatedUser.selectedChains shouldNotContain Chain.EVM_MAINNET
		updatedUser.selectedChains shouldNotContain Chain.EVM_BASE
	}
}
