package com.cleevio.fatbot.application.module.userstatistics

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.module.userstatistics.service.UpdateDonutsGainedUserStatisticsService
import com.cleevio.fatbot.domain.userstatistics.UserStatisticsRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class UpdateDonutsGainedUserStatisticsServiceTest(
	@Autowired private val underTest: UpdateDonutsGainedUserStatisticsService,
	@Autowired private val userStatisticsRepository: UserStatisticsRepository,
) : IntegrationTest() {

	@Test
	fun `should update user statistics with default multiplier when user not on leaderboard`() {
		val userId = 1.toUUID()
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(userId = 1.toUUID())

		// when
		underTest(userId, BigDecimal.valueOf(100))

		// then
		val updatedStatistics = userStatisticsRepository.findByUserId(userId)
		updatedStatistics!!.leaderboardDonuts shouldBeEqualComparingTo BigDecimal(100)
	}

	@Test
	fun `should update user statistics with custom multiplier from leaderboard`() {
		integrationTestHelper.getFirebaseUser(id = 2.toUUID())
		integrationTestHelper.getUserStatistics(userId = 2.toUUID())
		integrationTestHelper.getUserLeaderboard(
			userId = 2.toUUID(),
			rank = 2,
			donutMultiplier = 2.toBigDecimal(),
		)

		underTest(2.toUUID(), BigDecimal.valueOf(100))

		val updatedStatistics = userStatisticsRepository.findByUserId(2.toUUID())
		updatedStatistics!!.leagueDonuts shouldBeEqualComparingTo BigDecimal(200.00) // 100 * 2 = 200
	}
}
