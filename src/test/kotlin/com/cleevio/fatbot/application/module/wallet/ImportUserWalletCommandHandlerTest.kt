package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.command.ImportUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.exception.FailedToGetAddressFromPrivateKeyException
import com.cleevio.fatbot.application.module.wallet.exception.MaxWalletCountExceededException
import com.cleevio.fatbot.application.module.wallet.exception.WalletAlreadyImportedException
import com.cleevio.fatbot.domain.wallet.WalletRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import java.util.stream.Stream

class ImportUserWalletCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val walletRepository: WalletRepository,
) : IntegrationTest() {

	@ParameterizedTest
	@MethodSource("addressAndPrivateKeyAndExpectedProvider")
	fun `should import new user wallet when correct private key`(
		inputPrivateKey: String,
		inputChain: Chain,
		expectedAddress: String,
		expectedPrivateKey: String,
	) {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		every {
			web3jWrapper.getWalletBalance(AddressWrapper("******************************************"))
		} returns BigInteger.ZERO
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns BigDecimal("3000")
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("200")

		every { walletCurrencyPositionEventListener.onNewWalletImportedEvent(any()) } just Runs

		commandBus(
			ImportUserWalletCommand(
				userId = 1.toUUID(),
				privateKey = inputPrivateKey,
				chain = inputChain,
			),
		)

		walletRepository.findAll().first().run {
			userId shouldBe 1.toUUID()
			address.getAddressString() shouldBe expectedAddress
			privateKey shouldBe expectedPrivateKey
		}

		verify(exactly = 1) {
			walletCurrencyPositionEventListener.onNewWalletImportedEvent(any())
		}
	}

	@ParameterizedTest
	@MethodSource("addressPartAndPrivateKeyProvider")
	fun `should throw when importing wallet would exceed limit`(
		addressPart: String,
		privateKey: String,
		inputChain: Chain,
	) {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "${addressPart}A"),
			chain = inputChain,
		)
		integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "${addressPart}B"),
			chain = inputChain,
		)
		integrationTestHelper.getWallet(
			userId = 1.toUUID(),
			address = AddressWrapper(addressString = "${addressPart}C"),
			chain = inputChain,
		)

		shouldThrow<MaxWalletCountExceededException> {
			commandBus(
				ImportUserWalletCommand(
					userId = 1.toUUID(),
					privateKey = privateKey,
					chain = inputChain,
				),
			)
		}
	}

	@ParameterizedTest
	@MethodSource("addressAndPrivateKeyProvider")
	fun `should throw when importing same wallet second time`(privateKey: String, chain: Chain) {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		every {
			web3jWrapper.getWalletBalance(AddressWrapper("******************************************"))
		} returns BigInteger.ZERO
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns BigDecimal("3000")
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("200")

		every { walletCurrencyPositionEventListener.onNewWalletImportedEvent(any()) } just Runs

		commandBus(
			ImportUserWalletCommand(
				userId = 1.toUUID(),
				privateKey = privateKey,
				chain = chain,
			),
		)

		shouldThrow<WalletAlreadyImportedException> {
			commandBus(
				ImportUserWalletCommand(
					userId = 1.toUUID(),
					privateKey = privateKey,
					chain = chain,
				),
			)
		}
	}

	@ParameterizedTest
	@MethodSource("addressAndPrivateKeyProvider")
	fun `should throw when importing wallet with invalid private key`(privateKey: String, chain: Chain) {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		shouldThrow<ConstraintViolationException> {
			commandBus(
				ImportUserWalletCommand(
					userId = 1.toUUID(),
					privateKey = "${privateKey}0", // one char too long
					chain = chain,
				),
			)
		}
	}

	@ParameterizedTest
	@MethodSource("privateKeyAndInvalidChainTypeProvider")
	fun `should throw when importing wallet with private key not matching the chain type`(
		privateKey: String,
		chain: Chain,
	) {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		shouldThrow<FailedToGetAddressFromPrivateKeyException> {
			commandBus(
				ImportUserWalletCommand(
					userId = 1.toUUID(),
					privateKey = privateKey,
					chain = chain,
				),
			)
		}
	}

	companion object {
		@JvmStatic
		fun addressAndPrivateKeyAndExpectedProvider(): Stream<Arguments> {
			return Stream.of(
				// should import new EVM user wallet when correct private key with 0x
				Arguments.of(
					"0x547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
					Chain.EVM_MAINNET,
					"******************************************",
					"547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
				),
				// should import new EVM user wallet when correct private key without 0x
				Arguments.of(
					"547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
					Chain.EVM_MAINNET,
					"******************************************",
					"547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
				),
				// should import new EVM user wallet when correct private key in upper case
				Arguments.of(
					"547D11A39047CB34981A9BFD2EC26BADCDE36015726FC2663F688CC9CBF83AA6",
					Chain.EVM_MAINNET,
					"******************************************",
					"547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
				),
				// should import new SOLANA user wallet when correct private key
				Arguments.of(
					"5mj2b1gv9Jq5VroebGTGD8K4NsaT2dP3yyZwMdyjTKTnanXaG2a6otg2FAYrLHPpiUmsUj2CRYEuVk5KB7uiyhdL",
					Chain.SOLANA,
					"GYCiYAtstSznrYaTi2hMkKJzqRhPBcfeRUqWJWtabULJ",
					"5mj2b1gv9Jq5VroebGTGD8K4NsaT2dP3yyZwMdyjTKTnanXaG2a6otg2FAYrLHPpiUmsUj2CRYEuVk5KB7uiyhdL",
				),
			)
		}

		@JvmStatic
		fun addressPartAndPrivateKeyProvider(): Stream<Arguments> {
			// address part - addresses without last character
			return Stream.of(
				Arguments.of(
					"0xd356504Bbf10204Ade9Ab6aFE8Ca8C7252f47Cc",
					"0x547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83ba6",
					Chain.EVM_MAINNET,
				),
				Arguments.of(
					"GYCiYAtstSznrYaTi2hMkKJzqRhPBcfeRUqWJWtabUL",
					"5mj2b1gv9Jq5VroebGTGD8K4NsaT2dP3yyZwMdyjTKTnanXaG2a6otg2FAYrLHPpiUmsUj2CRYEuVk5KB7uiyhdL",
					Chain.SOLANA,
				),
			)
		}

		@JvmStatic
		fun addressAndPrivateKeyProvider(): Stream<Arguments> {
			return Stream.of(
				Arguments.of(
					"0x547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
					Chain.EVM_MAINNET,
				),
				Arguments.of(
					"5mj2b1gv9Jq5VroebGTGD8K4NsaT2dP3yyZwMdyjTKTnanXaG2a6otg2FAYrLHPpiUmsUj2CRYEuVk5KB7uiyhdL",
					Chain.SOLANA,
				),
			)
		}

		@JvmStatic
		fun privateKeyAndInvalidChainTypeProvider(): Stream<Arguments> {
			return Stream.of(
				Arguments.of(
					"0x547d11a39047cb34981a9bfd2ec26badcde36015726fc2663f688cc9cbf83aa6",
					Chain.SOLANA,
				),
				Arguments.of(
					"5mj2b1gv9Jq5VroebGTGD8K4NsaT2dP3yyZwMdyjTKTnanXaG2a6otg2FAYrLHPpiUmsUj2CRYEuVk5KB7uiyhdL",
					Chain.EVM_MAINNET,
				),
			)
		}
	}
}
