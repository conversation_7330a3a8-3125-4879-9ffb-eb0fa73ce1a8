package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bot.command.DeleteBotCommand
import com.cleevio.fatbot.domain.bot.BotRepository
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionRepository
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateRepository
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateType
import com.cleevio.fatbot.domain.botwallet.BotWalletRepository
import com.cleevio.fatbot.setAndReturnPrivateProperty
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger

class DeleteBotCommandHandlerTest(
	@Autowired private val botRepository: BotRepository,
	@Autowired private val botWalletRepository: BotWalletRepository,
	@Autowired private val botMarketPositionRepository: BotMarketPositionRepository,
	@Autowired private val botMarketPositionTradeStateRepository: BotMarketPositionTradeStateRepository,
	@Autowired private val commandBus: CommandBus,
) : IntegrationTest() {

	@Test
	fun `should delete bot`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getFile(id = 10.toUUID())
		integrationTestHelper.getBot(id = 2.toUUID(), userId = 1.toUUID(), avatarFileId = 10.toUUID(), isActive = false)
		integrationTestHelper.getBotWallet(
			id = 3.toUUID(),
			botId = 2.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", BigInteger.ZERO) },
		)

		integrationTestHelper.getBotMarketPosition(
			id = 401.toUUID(),
			botWalletId = 3.toUUID(),
			tokenAddress = AddressWrapper("6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump"),
			profitTargetFraction = BigDecimal("31.83"),
			stopLossFraction = BigDecimal("21.22"),
			assumedBuyPrice = BigDecimal("10.0"),
			blockSlot = 1234568,
		)

		integrationTestHelper.getBotMarketPositionTradeState(
			id = 9.toUUID(),
			botMarketPositionId = 401.toUUID(),
			type = BotMarketPositionTradeStateType.BUY,
			marketCapUsd = BigDecimal("4.5"),
			liquidityUsd = BigDecimal("6.6"),
			volumeUsd = BigDecimal("4.3"),
			numOfAccountHolders = 15L,
			buyVolume = BigDecimal("0.5"),
			sellVolume = BigDecimal("0.5"),
			fractionOfSellTransactions = BigDecimal("1"),
		)
		integrationTestHelper.getBotMarketPositionTradeState(
			id = 10.toUUID(),
			botMarketPositionId = 401.toUUID(),
			type = BotMarketPositionTradeStateType.SELL,
			marketCapUsd = BigDecimal("4.5"),
			liquidityUsd = BigDecimal("6.6"),
			volumeUsd = BigDecimal("4.3"),
			numOfAccountHolders = 15L,
			buyVolume = BigDecimal("0.5"),
			sellVolume = BigDecimal("0.5"),
			fractionOfSellTransactions = BigDecimal("1"),
		)

		botRepository.count() shouldBe 1
		botWalletRepository.count() shouldBe 1
		botMarketPositionRepository.count() shouldBe 1
		botMarketPositionTradeStateRepository.count() shouldBe 2

		commandBus(DeleteBotCommand(userId = 1.toUUID(), botId = 2.toUUID()))

		botRepository.count() shouldBe 0
		botWalletRepository.count() shouldBe 0
		botMarketPositionRepository.count() shouldBe 0
		botMarketPositionTradeStateRepository.count() shouldBe 0
	}

	@Test
	fun `should throw when trying to delete an active bot`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getFile(id = 10.toUUID())
		integrationTestHelper.getBot(id = 2.toUUID(), userId = 1.toUUID(), avatarFileId = 10.toUUID(), isActive = true)
		integrationTestHelper.getBotWallet(id = 3.toUUID(), botId = 2.toUUID())

		botRepository.count() shouldBe 1
		botWalletRepository.count() shouldBe 1

		shouldThrow<IllegalStateException> {
			commandBus(DeleteBotCommand(userId = 1.toUUID(), botId = 2.toUUID()))
		}

		botRepository.count() shouldBe 1
		botWalletRepository.count() shouldBe 1
	}

	@Test
	fun `should throw when bot has a wallet with non-zero balance`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getFile(id = 10.toUUID())
		integrationTestHelper.getBot(id = 2.toUUID(), userId = 1.toUUID(), avatarFileId = 10.toUUID())
		integrationTestHelper.getBotWallet(
			id = 3.toUUID(),
			botId = 2.toUUID(),
			beforeSave = { setAndReturnPrivateProperty("balance", 10.toBigInteger()) },
		)

		botRepository.count() shouldBe 1

		shouldThrow<IllegalStateException> {
			commandBus(DeleteBotCommand(userId = 1.toUUID(), botId = 2.toUUID()))
		}

		botRepository.count() shouldBe 1
	}
}
