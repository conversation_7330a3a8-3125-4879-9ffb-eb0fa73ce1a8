package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.limitorder.command.CreateLimitOrderCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderRepository
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class CreateLimitOrderCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val limitOrderRepository: LimitOrderRepository,
) : IntegrationTest() {

	@Test
	fun `should create limit order`() {
		// given
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2.toUUID(), userId = 1.toUUID())

		// when
		commandBus(
			CreateLimitOrderCommand(
				userId = 1.toUUID(),
				walletId = 2.toUUID(),
				tokenAddress = AddressWrapper("******************************************"),
				chain = Chain.SOLANA,
				limitPrice = NativeAmount(BigDecimal("0.001")),
				initialAmount = NativeAmount(BigDecimal("1.0")),
				type = LimitOrderType.BUY,
			),
		)

		// then
		val limitOrder = limitOrderRepository.findAll().first()
		limitOrder.userId shouldBe 1.toUUID()
		limitOrder.walletId shouldBe 2.toUUID()
		limitOrder.tokenAddress shouldBe AddressWrapper("******************************************")
		limitOrder.chain shouldBe Chain.SOLANA
		limitOrder.type shouldBe LimitOrderType.BUY
		limitOrder.limitPrice shouldBe NativeAmount(BigDecimal("0.001")).toBase(Chain.SOLANA).amount
		limitOrder.remainingAmount shouldBe NativeAmount(BigDecimal("1.0")).toBase(Chain.SOLANA).amount
	}
}
