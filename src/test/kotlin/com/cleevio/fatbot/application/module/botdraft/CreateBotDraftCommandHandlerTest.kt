package com.cleevio.fatbot.application.module.botdraft

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.botdraft.command.CreateBotDraftCommand
import com.cleevio.fatbot.domain.botdraft.BotDraftRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.equals.shouldBeEqual
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class CreateBotDraftCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val botDraftRepository: BotDraftRepository,
) : IntegrationTest() {

	@Test
	fun `should create new bot draft`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		val result = commandBus(CreateBotDraftCommand(userId = 1.toUUID()))

		val botDrafts = botDraftRepository.findAll()
		botDrafts shouldHaveSize 1

		botDrafts.single().id shouldBeEqual result.botDraftId
	}

	@Test
	fun `should allow creation of maximum 15 drafts per user`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))
		commandBus(CreateBotDraftCommand(userId = 1.toUUID()))

		shouldThrow<IllegalStateException> { commandBus(CreateBotDraftCommand(userId = 1.toUUID())) }

		// Different userId does not throw
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")
		commandBus(CreateBotDraftCommand(userId = 2.toUUID()))
	}
}
