package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.market.command.UpdateUserPositionsCommand
import com.cleevio.fatbot.domain.market.MarketPositionRefreshRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.util.concurrent.CompletableFuture

class UpdateUserPositionsCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val marketPositionRefreshRepository: MarketPositionRefreshRepository,
) : IntegrationTest() {

	@Test
	fun `should run when user has no market position refresh entity`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		commandBus(UpdateUserPositionsCommand(1.toUUID()))

		val marketPositionRefresh = marketPositionRefreshRepository.findAll().first()
		with(marketPositionRefresh) {
			userId shouldBe 1.toUUID()
			marketLastViewedAt shouldBe Instant.parse("2024-08-30T10:00:00Z")
		}
	}

	@Test
	fun `should not update when market position refresh is still fresh`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getMarketPositionRefresh(
			userId = 1.toUUID(),
			lastViewedAt = Instant.parse("2024-08-30T09:59:41Z"),
		)

		commandBus(UpdateUserPositionsCommand(1.toUUID()))

		val marketPositionRefresh = marketPositionRefreshRepository.findAll().first()
		with(marketPositionRefresh) {
			userId shouldBe 1.toUUID()
			marketLastViewedAt shouldBe Instant.parse("2024-08-30T09:59:41Z")
			version shouldBe 0 // no update happened
		}
	}

	@Test
	fun `should update only once when market position refresh is stale and multiple requests come`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getMarketPositionRefresh(
			userId = 1.toUUID(),
			lastViewedAt = Instant.parse("2024-08-30T09:58:59Z"),
		)

		(0..100)
			.map { CompletableFuture.runAsync { commandBus(UpdateUserPositionsCommand(1.toUUID())) } }
			.forEach { it.join() }

		val marketPositionRefresh = marketPositionRefreshRepository.findAll().first()
		with(marketPositionRefresh) {
			userId shouldBe 1.toUUID()
			marketLastViewedAt shouldBe Instant.parse("2024-08-30T10:00:00Z")
			version shouldBe 1 // update happened
		}
	}
}
