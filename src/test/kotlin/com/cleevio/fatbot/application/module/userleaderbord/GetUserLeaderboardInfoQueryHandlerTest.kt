package com.cleevio.fatbot.application.module.userleaderbord

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.userleaderbord.query.GetUserLeaderboardInfoQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class GetUserLeaderboardInfoQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should return user on leaderboard`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 1.toUUID(),
			beforeSave = { updateDonuts(500.toBigDecimal()) },
		)
		integrationTestHelper.getUserLeaderboard(
			userId = 1.toUUID(),
			rank = 5,
			donutMultiplier = 1.5.toBigDecimal(),
			donutGainedSnapshot = 500.toBigDecimal(),
		)

		val result = queryBus(GetUserLeaderboardInfoQuery(userId = 1.toUUID()))

		result.run {
			multiplier shouldBeEqualComparingTo 1.5.toBigDecimal()
			rank shouldBe 5
			donutsGained shouldBeEqualComparingTo 500.toBigDecimal()
		}
	}

	@Test
	fun `should handle first ranked user`() {
		integrationTestHelper.getFirebaseUser(id = 3.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 3.toUUID(),
			beforeSave = { updateDonuts(10000.toBigDecimal()) },
		)
		integrationTestHelper.getUserLeaderboard(
			userId = 3.toUUID(),
			rank = 1, // Top position
			donutMultiplier = 2.0.toBigDecimal(),
			donutGainedSnapshot = 10000.toBigDecimal(),
		)

		val result = queryBus(GetUserLeaderboardInfoQuery(userId = 3.toUUID()))

		result.run {
			multiplier shouldBeEqualComparingTo 2.0.toBigDecimal()
			rank shouldBe 1
			donutsGained shouldBeEqualComparingTo 10000.toBigDecimal()
			donutsNeededForNextThreshold shouldBeEqualComparingTo BigDecimal.ZERO
		}
	}

	@Test
	fun `should return correct donutsNeeded for user close to making the leaderboard`() {
		integrationTestHelper.getFirebaseUser(id = 4.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 4.toUUID(),
			beforeSave = { updateDonuts(195.toBigDecimal()) },
		)
		// last user on leaderboard
		integrationTestHelper.getFirebaseUser(id = 1001.toUUID(), email = "<EMAIL>")
		integrationTestHelper.getUserStatistics(
			userId = 1001.toUUID(),
			beforeSave = { updateDonuts(200.toBigDecimal()) },
		)
		integrationTestHelper.getUserLeaderboard(
			userId = 1001.toUUID(),
			rank = 1000,
			donutGainedSnapshot = 200.toBigDecimal(),
		)

		val result = queryBus(GetUserLeaderboardInfoQuery(userId = 4.toUUID()))

		result.run {
			donutsGained shouldBeEqualComparingTo 195.toBigDecimal()
			donutsNeededForNextThreshold shouldBeEqualComparingTo 5.toBigDecimal() // 200-195
		}
	}
}
