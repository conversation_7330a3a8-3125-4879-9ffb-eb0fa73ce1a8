package com.cleevio.fatbot.application.module.userfattycard

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.userfattycard.command.ClaimUserFattyCardCommand
import com.cleevio.fatbot.application.module.userfattycard.exceptions.UserFattyCarAlreadyClaimedException
import com.cleevio.fatbot.domain.userfattycard.UserFattyCardRepository
import com.cleevio.fatbot.domain.userstatistics.UserStatisticsRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class ClaimUserFattyCardCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val userStatisticsRepository: UserStatisticsRepository,
	@Autowired private val userFattyCardRepository: UserFattyCardRepository,
) : IntegrationTest() {

	@Test
	fun `should claim fatty card and update user statistics with default multiplier`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(userId = 1.toUUID())
		integrationTestHelper.getFile(id = 3.toUUID())
		integrationTestHelper.getFattyCard(
			id = 5.toUUID(),
			donutReward = 100.toBigDecimal(),
			avatarFileId = 3.toUUID(),
		)
		integrationTestHelper.getUserFattyCard(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyCardId = 5.toUUID(),
		)

		commandBus(ClaimUserFattyCardCommand(userId = 1.toUUID(), userFattyCardId = 1.toUUID()))

		val updatedStatistics = userStatisticsRepository.findByUserId(1.toUUID())
		updatedStatistics!!.leaderboardDonuts shouldBeEqualComparingTo 100.toBigDecimal()
		userFattyCardRepository.findById(1.toUUID()).get().isClaimed shouldBe true
	}

	@Test
	fun `should claim fatty card and update user statistics with leaderboard multiplier`() {
		integrationTestHelper.getFirebaseUser(id = 0.toUUID())
		integrationTestHelper.getUserStatistics(userId = 0.toUUID())
		integrationTestHelper.getFile(id = 3.toUUID())
		integrationTestHelper.getFattyCard(
			id = 10.toUUID(),
			donutReward = BigDecimal(100),
			avatarFileId = 3.toUUID(),
		)
		integrationTestHelper.getUserFattyCard(
			id = 0.toUUID(),
			userId = 0.toUUID(),
			fattyCardId = 10.toUUID(),
		)
		integrationTestHelper.getUserLeaderboard(
			userId = 0.toUUID(),
			rank = 2,
			donutMultiplier = 2.0.toBigDecimal(),
		)

		commandBus(ClaimUserFattyCardCommand(userId = 0.toUUID(), userFattyCardId = 0.toUUID()))

		val updatedStatistics = userStatisticsRepository.findByUserId(0.toUUID())
		updatedStatistics!!.leaderboardDonuts shouldBeEqualComparingTo
			100.toBigDecimal().multiply(2.0.toBigDecimal())
		userFattyCardRepository.findById(0.toUUID()).get().isClaimed shouldBe true
	}

	@Test
	fun `should not allow claiming same fatty card twice`() {
		integrationTestHelper.getFirebaseUser(id = 0.toUUID())
		integrationTestHelper.getUserStatistics(userId = 0.toUUID())
		integrationTestHelper.getFile(id = 3.toUUID())
		integrationTestHelper.getFattyCard(
			id = 15.toUUID(),
			donutReward = 100.toBigDecimal(),
			avatarFileId = 3.toUUID(),
		)
		integrationTestHelper.getUserFattyCard(
			id = 1.toUUID(),
			userId = 0.toUUID(),
			fattyCardId = 15.toUUID(),
			claimed = true, // Already claimed
		)

		assertThrows<UserFattyCarAlreadyClaimedException> {
			commandBus(ClaimUserFattyCardCommand(userId = 0.toUUID(), userFattyCardId = 1.toUUID()))
		}
	}
}
