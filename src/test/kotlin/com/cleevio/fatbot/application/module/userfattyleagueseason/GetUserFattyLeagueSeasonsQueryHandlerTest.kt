package com.cleevio.fatbot.application.module.userfattyleagueseason

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.userfattyleagueseason.query.GetUserFattyLeagueSeasonsQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit

class GetUserFattyLeagueSeasonsQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val integrationTestClock: IntegrationTestClock,
) : IntegrationTest() {

	@Test
	fun `should get user fatty league seasons`() {
		// given
		val now = Instant.now()
		integrationTestClock.setTo(now)
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		// Create two fatty league seasons
		val fattyLeague1 = integrationTestHelper.createFattyLeagueSeason(
			id = 1.toUUID(),
			from = now.minus(2, ChronoUnit.DAYS),
			name = "Fatty League 1",
			seasonNumber = 1,
			claimCooldownPeriodInMinutes = 10L,
			donutsToFattyTokensRatio = BigDecimal.TEN,
			initialReleasePercentage = BigDecimal.TEN,
			remainingUnlockPeriodInMonths = 3L,
			remainingUnlockPeriodParts = 3,

		)

		val fattyLeague2 = integrationTestHelper.createFattyLeagueSeason(
			id = 2.toUUID(),
			from = now.minus(1, ChronoUnit.DAYS),
			name = "Fatty League 2",
			seasonNumber = 1,
			claimCooldownPeriodInMinutes = 10L,
			donutsToFattyTokensRatio = BigDecimal.TEN,
			initialReleasePercentage = BigDecimal.TEN,
			remainingUnlockPeriodInMonths = 3L,
			remainingUnlockPeriodParts = 3,
			beforeSave = { closeSeason(now) },
		)

		// Create user fatty league seasons
		integrationTestHelper.createUserFattyLeagueSeason(
			id = 1.toUUID(),
			userId = 1.toUUID(),
			fattyLeagueSeasonId = 1.toUUID(),
			totalTokens = 1000.0.toBigDecimal(),
			lastClaimedAt = now.minus(1, ChronoUnit.HOURS),
			beforeSave = {
				val claimableTokens = fattyLeague1.computeClaimableTokens(lastClaimedAt, totalTokens, getRemainingTokens(), now)

				claimTokens(now, claimableTokens)
			},
		)
		integrationTestHelper.createUserFattyLeagueSeason(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			fattyLeagueSeasonId = 2.toUUID(),
			totalTokens = 2000.0.toBigDecimal(),
			lastClaimedAt = null,
			beforeSave = {
				val claimableTokens = fattyLeague2.computeClaimableTokens(lastClaimedAt, totalTokens, getRemainingTokens(), now)

				claimTokens(now, claimableTokens)
			},
		)

		val result = queryBus(GetUserFattyLeagueSeasonsQuery(userId = 1.toUUID()))

		result.userFattyLeagueSeasons shouldHaveSize 2
		result.totalClaimedTokens shouldBeEqualComparingTo 200.toBigDecimal()
		result.totalClaimableTokens shouldBeEqualComparingTo 2800.toBigDecimal()

		result.userFattyLeagueSeasons.find { it.id == 1.toUUID() }!!.run {
			seasonName shouldBe "Fatty League 1"
			claimableTokens
		}
		result.userFattyLeagueSeasons.find { it.id == 2.toUUID() }!!.run {
			seasonName shouldBe "Fatty League 2"
		}
	}
}
