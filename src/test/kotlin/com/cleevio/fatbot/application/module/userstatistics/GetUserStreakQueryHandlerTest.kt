package com.cleevio.fatbot.application.module.userstatistics

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.userstatistics.query.GetUserStreakQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.equals.shouldBeEqual
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal

class GetUserStreakQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
) : IntegrationTest() {

	@Test
	fun `should return correct streak info for new user without transactions`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(userId = 1.toUUID())

		val result = queryBus(GetUserStreakQuery(userId = 1.toUUID()))

		result.userId shouldBe 1.toUUID()
		result.daysInStreak shouldBe 0
		result.currentMultiplier shouldBe BigDecimal.ONE
		result.daysToNextStreak shouldBe 3
		result.nextMultiplier!! shouldBeEqual 1.2.toBigDecimal()
	}

	@Test
	fun `should return correct streak info for user with 3 day streak`() {
		val userId = 1.toUUID()
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 1.toUUID(),
			daysInStreak = 3,
		)

		val result = queryBus(GetUserStreakQuery(userId))

		result.userId shouldBe userId
		result.daysInStreak shouldBe 3
		result.currentMultiplier shouldBe 1.2.toBigDecimal()
		result.daysToNextStreak shouldBe 4 // 7 - 3
		result.nextMultiplier!! shouldBeEqualComparingTo 1.5.toBigDecimal()
	}

	@Test
	fun `should return correct streak info for user with 7 day streak`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 1.toUUID(),
			daysInStreak = 7,
		)

		val result = queryBus(GetUserStreakQuery(userId = 1.toUUID()))

		result.userId shouldBe 1.toUUID()
		result.daysInStreak shouldBe 7
		result.currentMultiplier shouldBe 1.5.toBigDecimal()
		result.daysToNextStreak shouldBe 23 // 30 - 7
		result.nextMultiplier!! shouldBeEqual 2.toBigDecimal()
	}

	@Test
	fun `should return correct streak info for user with transaction`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getWallet(id = 2.toUUID(), userId = 1.toUUID())
		integrationTestHelper.getTransaction(
			walletId = 2.toUUID(),
			type = TransactionType.BUY,
			status = TransactionStatus.SUCCESS,
		)
		integrationTestHelper.getUserStatistics(
			userId = 1.toUUID(),
			daysInStreak = 1,
		)

		val result = queryBus(GetUserStreakQuery(1.toUUID()))

		result.userId shouldBe 1.toUUID()
		result.daysInStreak shouldBe 1
		result.currentMultiplier shouldBe BigDecimal.ONE
		result.daysToNextStreak shouldBe 2 // 3 - 1
		result.nextMultiplier!! shouldBeEqual 1.2.toBigDecimal()
	}

	@Test
	fun `should return correct streak info for user with more than max day streak`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = 1.toUUID(),
			daysInStreak = 777,
		)

		val result = queryBus(GetUserStreakQuery(userId = 1.toUUID()))

		result.userId shouldBe 1.toUUID()
		result.daysInStreak shouldBe 777
		result.currentMultiplier shouldBe 3.toBigDecimal()
		result.daysToNextStreak shouldBe null
		result.nextMultiplier shouldBe null
	}
}
