package com.cleevio.fatbot.application.module.referral

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.evm.model.FeesPerGas
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.module.referral.command.ClaimReferralRewardsCommand
import com.cleevio.fatbot.application.module.referral.exception.ReferralRewardBelowThresholdException
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.referral.ReferralRewardRepository
import com.cleevio.fatbot.domain.transaction.TransactionRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigInteger

@Suppress("ktlint:standard:max-line-length")
class ClaimReferralRewardsCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val referralRewardRepository: ReferralRewardRepository,
	@Autowired private val transactionRepository: TransactionRepository,
) : IntegrationTest() {

	@Test
	fun `should throw error when claiming less then threshold`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)
		integrationTestHelper.getReferralReward(
			id = 1.toUUID(),
			userId = user.id,
			baseAmount = BigInteger("10"),
		)

		val unclaimedRewardsBefore = referralRewardRepository.findAll()
		unclaimedRewardsBefore.size shouldBe 1
		unclaimedRewardsBefore.first().claimed shouldBe false

		shouldThrow<ReferralRewardBelowThresholdException> {
			commandBus(ClaimReferralRewardsCommand(userId = user.id, chain = Chain.EVM_MAINNET))
		}

		val unclaimedRewardsAfter = referralRewardRepository.findAll()
		unclaimedRewardsAfter.size shouldBe 1
		unclaimedRewardsAfter.first().claimed shouldBe false
	}

	@Test
	fun `should claim unclaimed reward`() {
		transactionRepository.findAll().size shouldBe 0

		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)
		integrationTestHelper.getReferralReward(
			id = 1.toUUID(),
			userId = user.id,
			txHash = TxHash("0x3ed334ccc21e23af2b86221b2d9d7377b8fe27e4d3136de5df06ce4b1d7ca848"),
			baseAmount = 1000.toBigInteger(),
		)

		val unclaimedRewards = referralRewardRepository.findAll()
		unclaimedRewards.size shouldBe 1
		unclaimedRewards.first().claimed shouldBe false

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = 1000000000.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()

		/*
		Decompiled Tx:
		{
		  "hash": "0xae9a0ae1187d46a99c89c75a93b2b8460c8a26ce06940987f91d4fda26bc427e",
		  "gasLimit": "28635",
		  "maxFeePerGas": "1000001500",
		  "maxPriorityFeePerGas": "1000000000",
		  "from": "******************************************",
		  "to": "******************************************",
		  "publicKey": "0x04c5acb98881856838d58e269cd902748cb10e8f862f1410e404921e6e3ffbe635dee71b76ee6603d6144092eeb63c14dc056c587bc71bb273e4eb05096c806573",
		  "v": "00",
		  "r": "cfff3a73b574b92a90d8692c676daa2b61c2628ef9f5a5f646362ac033db4ce7",
		  "s": "7d26d3b72695504189378de6532445338bde715faa1e4a05cfd544a50e918031",
		  "value": "1000"
		}
		 */
		every {
			web3jWrapper.sendRawTransaction(
				"0x02f86c0101843b9aca00843b9acfdc826fdb94d356504bbf10204ade9ab6afe8ca8c7252f47ccb8203e880c080a0cfff3a73b574b92a90d8692c676daa2b61c2628ef9f5a5f646362ac033db4ce7a07d26d3b72695504189378de6532445338bde715faa1e4a05cfd544a50e918031",
			)
		} just Runs

		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()

		val result = commandBus(ClaimReferralRewardsCommand(userId = user.id, chain = Chain.EVM_MAINNET))

		result.txHash.txHash shouldBe "0xae9a0ae1187d46a99c89c75a93b2b8460c8a26ce06940987f91d4fda26bc427e"

		val transactions = transactionRepository.findAll()
		transactions.size shouldBe 1
		with(transactions.first()) {
			type shouldBe TransactionType.CLAIM_REFERRAL_REWARD
			baseValue shouldBe 1000.toBigInteger()
			chain shouldBe Chain.EVM_MAINNET
		}

		val claimedRewards = referralRewardRepository.findAll()
		claimedRewards.size shouldBe 1
		claimedRewards.first().claimed shouldBe true
	}

	@Test
	fun `should claim multiple rewards`() {
		transactionRepository.findAll().size shouldBe 0

		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)
		integrationTestHelper.getReferralReward(
			id = 1.toUUID(),
			userId = user.id,
			txHash = TxHash("0x3ed334ccc21e23af2b86221b2d9d7377b8fe27e4d3136de5df06ce4b1d7ca848"),
			baseAmount = BigInteger.valueOf(1000),
		)
		integrationTestHelper.getReferralReward(
			id = 2.toUUID(),
			userId = user.id,
			txHash = TxHash("0xce44b57a9936d229351ec3cdcef3c43376f3a97552064fa8c936c949885da10e"),
			baseAmount = BigInteger.valueOf(500),
		)
		integrationTestHelper.getReferralReward(
			id = 3.toUUID(),
			userId = user.id,
			txHash = TxHash("0x43f3fe5a8da9ac9260f2b592cf207176477cfcdc6ce903b415b0a59f2a50afb0"),
			baseAmount = BigInteger.valueOf(2000),
		)

		val unclaimedRewardsBefore = referralRewardRepository.findAll()
		unclaimedRewardsBefore.size shouldBe 3
		unclaimedRewardsBefore.forEach { it.claimed shouldBe false }

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = 1000000000.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every {
			web3jWrapper.sendRawTransaction(
				"0x02f86c0101843b9aca00843b9acfdc826fdb94d356504bbf10204ade9ab6afe8ca8c7252f47ccb820dac80c080a0b94a48d09ca8d3089e6e7a28effd76f34d587d8e07c0b4f21ea60e27f762d77ea07b9c5e53ee066f8f203db9a41a836a885f123397d39d8e00c855c174f61cdb8d",
			)
		} just Runs

		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()

		val result = commandBus(ClaimReferralRewardsCommand(userId = user.id, chain = Chain.EVM_MAINNET))

		result.txHash.txHash shouldBe "0xb7a5bc4a85d7ba3457a5f9dcebaf3c2a0e1aaa0883227bb4d03b5419a9eca748"

		val transactions = transactionRepository.findAll()
		transactions.size shouldBe 1
		with(transactions.first()) {
			type shouldBe TransactionType.CLAIM_REFERRAL_REWARD
			baseValue shouldBe 3500.toBigInteger()
			chain shouldBe Chain.EVM_MAINNET
		}

		val claimedRewards = referralRewardRepository.findAll()
		claimedRewards.size shouldBe 3
		claimedRewards.forEach { it.claimed shouldBe true }
	}

	@Test
	fun `should throw threshold error when no rewards`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)

		referralRewardRepository.findAll().size shouldBe 0

		shouldThrow<ReferralRewardBelowThresholdException> {
			commandBus(ClaimReferralRewardsCommand(userId = user.id, chain = Chain.EVM_MAINNET))
		}
	}

	@Test
	fun `should throw threshold error when only claimed rewards`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)

		integrationTestHelper.getReferralReward(
			id = 1.toUUID(),
			userId = user.id,
			txHash = TxHash("0x3ed334ccc21e23af2b86221b2d9d7377b8fe27e4d3136de5df06ce4b1d7ca848"),
			baseAmount = 10000.toBigInteger(),
			beforeSave = { claim() },
		)

		val claimedRewards = referralRewardRepository.findAll()
		claimedRewards.size shouldBe 1
		claimedRewards.first().claimed shouldBe true

		shouldThrow<ReferralRewardBelowThresholdException> {
			commandBus(ClaimReferralRewardsCommand(userId = user.id, chain = Chain.EVM_MAINNET))
		}
	}

	@Test
	fun `should return correct result when both claimed and unclaimed rewards exist`() {
		val user = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		val wallet = integrationTestHelper.getWallet(id = 1.toUUID(), userId = user.id)

		integrationTestHelper.getReferralReward(
			id = 1.toUUID(),
			userId = user.id,
			txHash = TxHash("0x3ed334ccc21e23af2b86221b2d9d7377b8fe27e4d3136de5df06ce4b1d7ca848"),
			baseAmount = BigInteger.valueOf(1000),
		)
		integrationTestHelper.getReferralReward(
			id = 2.toUUID(),
			userId = user.id,
			txHash = TxHash("0xce44b57a9936d229351ec3cdcef3c43376f3a97552064fa8c936c949885da10e"),
			baseAmount = BigInteger.valueOf(2000),
			beforeSave = { claim() },
		)

		referralRewardRepository.findAll().size shouldBe 2

		every { web3jWrapper.getFeesPerGas() } returns FeesPerGas(
			baseFee = 1000.toBigInteger(),
			maxPriorityFee = 1000000000.toBigInteger(), // 1 Gwei
		)
		every { web3jWrapper.getNonce(any()) } returns 1.toBigInteger()
		every {
			web3jWrapper.sendRawTransaction(
				"0x02f86c0101843b9aca00843b9acfdc826fdb94d356504bbf10204ade9ab6afe8ca8c7252f47ccb8203e880c080a0cfff3a73b574b92a90d8692c676daa2b61c2628ef9f5a5f646362ac033db4ce7a07d26d3b72695504189378de6532445338bde715faa1e4a05cfd544a50e918031",
			)
		} just Runs

		every { web3jWrapper.estimateGas(any()) } returns 19090.toBigInteger()

		val result = commandBus(ClaimReferralRewardsCommand(userId = user.id, chain = Chain.EVM_MAINNET))

		result.txHash.txHash shouldBe "0xae9a0ae1187d46a99c89c75a93b2b8460c8a26ce06940987f91d4fda26bc427e"

		val transactions = transactionRepository.findAll()
		transactions.size shouldBe 1
		with(transactions.first()) {
			type shouldBe TransactionType.CLAIM_REFERRAL_REWARD
			baseValue shouldBe 1000.toBigInteger()
			chain shouldBe Chain.EVM_MAINNET
		}

		val claimedRewards = referralRewardRepository.findAll()
		claimedRewards.size shouldBe 2
		claimedRewards.forEach { it.claimed shouldBe true }
	}
}
