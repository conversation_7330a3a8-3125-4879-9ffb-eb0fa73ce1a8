package com.cleevio.fatbot.application.module.referral

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.module.user.exception.UserNotFoundException
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.referral.ReferralRewardRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrowAny
import io.kotest.assertions.throwables.shouldThrowExactly
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ReferralRewardServiceTest(
	@Autowired private val underTest: ReferralRewardService,
	@Autowired private val referralRewardRepository: ReferralRewardRepository,
) : IntegrationTest() {

	@Test
	fun `should save referral reward`() {
		referralRewardRepository.findAll().size shouldBe 0

		integrationTestHelper.getFirebaseUser(1.toUUID())

		underTest.saveReferralReward(
			userId = 1.toUUID(),
			txHash = TxHash("0x8a0fd1dec6c9580a281c9ae0f657f5004e96c6102560ca1dde85fe6013bfba64"),
			amount = 1000.toBigInteger(),
			chain = Chain.EVM_MAINNET,
		)

		val referralRewards = referralRewardRepository.findAll()
		referralRewards.size shouldBe 1
		with(referralRewards.first()) {
			userId shouldBe 1.toUUID()
			txHash shouldBe TxHash("0x8a0fd1dec6c9580a281c9ae0f657f5004e96c6102560ca1dde85fe6013bfba64")
			baseAmount shouldBe 1000.toBigInteger()
			claimed shouldBe false
		}
	}

	@Test
	fun `should fail saving second reward with same txHash`() {
		referralRewardRepository.findAll().size shouldBe 0

		integrationTestHelper.getFirebaseUser(1.toUUID())

		underTest.saveReferralReward(
			userId = 1.toUUID(),
			txHash = TxHash("0x8a0fd1dec6c9580a281c9ae0f657f5004e96c6102560ca1dde85fe6013bfba64"),
			amount = 1000.toBigInteger(),
			chain = Chain.EVM_MAINNET,
		)

		shouldThrowAny {
			underTest.saveReferralReward(
				userId = 1.toUUID(),
				txHash = TxHash("0x8a0fd1dec6c9580a281c9ae0f657f5004e96c6102560ca1dde85fe6013bfba64"),
				amount = 1000.toBigInteger(),
				chain = Chain.EVM_MAINNET,
			)
		}
	}

	@Test
	fun `should save multiple rewards for same user`() {
		referralRewardRepository.findAll().size shouldBe 0

		integrationTestHelper.getFirebaseUser(1.toUUID())

		underTest.saveReferralReward(
			userId = 1.toUUID(),
			txHash = TxHash("0x7a0fd1dec6c9580a281c9ae0f657f5004e96c6102560ca1dde85fe6013bfba64"),
			amount = 1000.toBigInteger(),
			chain = Chain.EVM_MAINNET,
		)

		underTest.saveReferralReward(
			userId = 1.toUUID(),
			txHash = TxHash("0x8a0fd1dec6c9580a281c9ae0f657f5004e96c6102560ca1dde85fe6013bfba64"),
			amount = 2000.toBigInteger(),
			chain = Chain.EVM_MAINNET,
		)

		val referralRewards = referralRewardRepository.findAll()
		referralRewards.size shouldBe 2
	}

	@Test
	fun `should throw error on missing user`() {
		shouldThrowExactly<UserNotFoundException> {
			underTest.saveReferralReward(
				userId = 123456.toUUID(),
				txHash = TxHash("0x8a0fd1dec6c9580a281c9ae0f657f5004e96c6102560ca1dde85fe6013bfba64"),
				amount = 1000.toBigInteger(),
				chain = Chain.EVM_MAINNET,
			)
		}
	}
}
