package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.walletposition.query.SearchUserWalletCurrencyPositionsQuery
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import java.time.temporal.ChronoUnit

class SearchUserWalletPositionsQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val clock: IntegrationTestClock,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		// All chains selected by default
		integrationTestHelper.getFirebaseUser(id = 1.toUUID(), selectedChains = Chain.entries.toSet())
		integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "<EMAIL>")

		integrationTestHelper.getWallet(
			id = 2.toUUID(),
			userId = 1.toUUID(),
			chain = Chain.EVM_MAINNET,
		)

		integrationTestHelper.getWallet(
			id = 3.toUUID(),
			userId = 1.toUUID(),
			chain = Chain.SOLANA,
			address = AddressWrapper("9nUvZhdxaWLDUTRTXfGmQc8caUFq9yXqVe3CWwG7GhWM"),
		)

		integrationTestHelper.getWallet(
			id = 4.toUUID(),
			userId = 1.toUUID(),
			chain = Chain.SOLANA,
			address = AddressWrapper("9nUvZhdxaWLDUTRTXfGmQc8caUFq9yXqVe3CWwG7GhWA"),
		)

		// Mainnet
		integrationTestHelper.getWalletCurrencyPosition(
			walletId = 2.toUUID(),
			totalBought = "1000".nativeToWei(),
			totalSold = "300".nativeToWei(),
			totalAcquisitionCostUsd = "2000.1".toBigDecimal(), // average rate = 2.0001
		)

		// Have 2 positions for the same wallet in the DB to make sure we correctly use the last one
		integrationTestHelper.getWalletCurrencyPosition(
			walletId = 2.toUUID(),
			totalBought = "1000".nativeToWei(),
			totalSold = "500".nativeToWei(),
			totalAcquisitionCostUsd = "2000.1".toBigDecimal(), // average rate = 2.0001
		)

		// Solana
		integrationTestHelper.getWalletCurrencyPosition(
			walletId = 3.toUUID(),
			totalBought = "500".nativeToLamport(),
			totalSold = "250".nativeToLamport(),
			totalAcquisitionCostUsd = "650.21".toBigDecimal(), // average rate = 1.30042
		)

		integrationTestHelper.getWalletCurrencyPosition(
			walletId = 4.toUUID(),
			totalBought = "100".nativeToLamport(),
			totalSold = "0".nativeToLamport(),
			totalAcquisitionCostUsd = "150".toBigDecimal(), // average rate = 1.5
		)
	}

	@Test
	fun `no positions return no positions`() {
		val result = queryBus(
			SearchUserWalletCurrencyPositionsQuery(
				// No positions for this userId
				userId = 2.toUUID(),
				useSelectedChains = true,
				filter = SearchUserWalletCurrencyPositionsQuery.Filter(walletId = null),
			),
		)

		result shouldHaveSize 0
	}

	@Test
	fun `returns wallet positions for all wallets`() {
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns "2.0001".toBigDecimal()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "1.30074".toBigDecimal()

		integrationTestHelper.setSelectedChains(userId = 1.toUUID(), selectedChains = emptySet())

		val result = queryBus(
			SearchUserWalletCurrencyPositionsQuery(
				userId = 1.toUUID(),
				useSelectedChains = false,
				filter = SearchUserWalletCurrencyPositionsQuery.Filter(walletId = null),
			),
		)

		result shouldHaveSize 2
	}

	@Test
	fun `returns wallet positions for all wallets on selected chains`() {
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns "2.0001".toBigDecimal()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "1.30074".toBigDecimal()

		integrationTestHelper.setSelectedChains(userId = 1.toUUID(), selectedChains = setOf(Chain.SOLANA))

		val result = queryBus(
			SearchUserWalletCurrencyPositionsQuery(
				userId = 1.toUUID(),
				useSelectedChains = true,
				filter = SearchUserWalletCurrencyPositionsQuery.Filter(walletId = null),
			),
		)

		result shouldHaveSize 1
		result.single().chain shouldBe Chain.SOLANA
	}

	@Test
	fun `returns no wallet positions for no wallets on selected chains`() {
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns "2.0001".toBigDecimal()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "1.30074".toBigDecimal()

		integrationTestHelper.setSelectedChains(userId = 1.toUUID(), selectedChains = setOf(Chain.EVM_ARBITRUM_ONE))

		val result = queryBus(
			SearchUserWalletCurrencyPositionsQuery(
				userId = 1.toUUID(),
				useSelectedChains = true,
				filter = SearchUserWalletCurrencyPositionsQuery.Filter(walletId = null),
			),
		)

		result shouldHaveSize 0
	}

	@Test
	fun `returns wallet positions for a single wallet`() {
		// 10% change to average
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns "2.20011".toBigDecimal()

		val result = queryBus(
			SearchUserWalletCurrencyPositionsQuery(
				userId = 1.toUUID(),
				useSelectedChains = true,
				filter = SearchUserWalletCurrencyPositionsQuery.Filter(
					walletId = 2.toUUID(),
				),
			),
		)

		result shouldHaveSize 1

		result.single().run {
			chain shouldBe Chain.EVM_MAINNET
			pnlFraction shouldBeEqualComparingTo "0.1".toBigDecimal()
			pnlUsd shouldBeEqualComparingTo "100.005".toBigDecimal()
			currentValueNative shouldBe NativeAmount(BigDecimal("500.000000000000000000"))
			oneDayChangeFraction shouldBe null
			oneHourChangeFraction shouldBe null
		}
	}

	@Test
	fun `returns wallet positions aggregated by chain type`() {
		// Current prices
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns "2.20011".toBigDecimal()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "1.1".toBigDecimal()

		// to produce +10% change
		integrationTestHelper.getExchangeRateSnapshot(
			currency = CryptoCurrency.SOL,
			exchangeRateUsd = "1.00".toBigDecimal(),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
		)

		val result = queryBus(
			SearchUserWalletCurrencyPositionsQuery(
				userId = 1.toUUID(),
				useSelectedChains = true,
				filter = SearchUserWalletCurrencyPositionsQuery.Filter(walletId = null),
			),
		)

		result shouldHaveSize 2

		result.single { it.chain == Chain.SOLANA }.run {
			pnlFraction shouldBe "-0.1896528135885751570705423011755295".toBigDecimal()
			pnlUsd shouldBeEqualComparingTo "-90.105".toBigDecimal()
			currentValueUsd shouldBeEqualComparingTo "385".toBigDecimal()
			currentValueNative shouldBe NativeAmount(BigDecimal("350.000000000"))
			oneHourChangeFraction shouldBe "0.1".toBigDecimal()
			oneDayChangeFraction shouldBe null
		}
	}

	@Test
	fun `should correctly calculate 1h and 1d change`() {
		// Current prices
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns "2.20011".toBigDecimal()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "1.1".toBigDecimal()

		val initialResult = queryBus(
			SearchUserWalletCurrencyPositionsQuery(
				userId = 1.toUUID(),
				useSelectedChains = true,
				filter = SearchUserWalletCurrencyPositionsQuery.Filter(walletId = null),
			),
		)

		initialResult shouldHaveSize 2

		initialResult.single { it.chain == Chain.EVM_MAINNET }.run {
			oneDayChangeFraction shouldBe null
			oneHourChangeFraction shouldBe null
		}

		initialResult.single { it.chain == Chain.SOLANA }.run {
			oneDayChangeFraction shouldBe null
			oneHourChangeFraction shouldBe null
		}

		// to produce +10% change
		integrationTestHelper.getExchangeRateSnapshot(
			currency = CryptoCurrency.ETH,
			exchangeRateUsd = "2.0001".toBigDecimal(),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
		)

		// to produce ~-10% change
		integrationTestHelper.getExchangeRateSnapshot(
			currency = CryptoCurrency.SOL,
			exchangeRateUsd = "1.222222222222222".toBigDecimal(),
			validAt = clock.currentTime().minus(1, ChronoUnit.HOURS),
		)

		// to produce ~-10% change
		integrationTestHelper.getExchangeRateSnapshot(
			currency = CryptoCurrency.ETH,
			exchangeRateUsd = "2.444566666666667".toBigDecimal(),
			validAt = clock.currentTime().minus(24, ChronoUnit.HOURS),
		)

		// to produce -10% change
		integrationTestHelper.getExchangeRateSnapshot(
			currency = CryptoCurrency.SOL,
			exchangeRateUsd = "1.00".toBigDecimal(),
			validAt = clock.currentTime().minus(24, ChronoUnit.HOURS),
		)

		val resultWithChanges = queryBus(
			SearchUserWalletCurrencyPositionsQuery(
				userId = 1.toUUID(),
				useSelectedChains = true,
				filter = SearchUserWalletCurrencyPositionsQuery.Filter(walletId = null),
			),
		)
		resultWithChanges shouldHaveSize 2

		resultWithChanges.single { it.chain == Chain.EVM_MAINNET }.run {
			oneHourChangeFraction shouldBe "0.1".toBigDecimal()
			oneDayChangeFraction shouldBe "-0.1000000000000001227211366704391886".toBigDecimal()
		}

		resultWithChanges.single { it.chain == Chain.SOLANA }.run {
			oneHourChangeFraction shouldBe "-0.0999999999999998363636363636363339".toBigDecimal()
			oneDayChangeFraction shouldBe "0.1".toBigDecimal()
		}
	}

	private fun String.nativeToWei(): BigInteger = toBigDecimal().scaleByPowerOfTen(18).toBigInteger()

	private fun String.nativeToLamport(): BigInteger = toBigDecimal().scaleByPowerOfTen(9).toBigInteger()
}
