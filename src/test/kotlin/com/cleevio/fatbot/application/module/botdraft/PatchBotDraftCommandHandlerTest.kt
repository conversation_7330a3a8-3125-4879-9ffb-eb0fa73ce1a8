package com.cleevio.fatbot.application.module.botdraft

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.`in`.rest.dto.PatchBotSettingsRequest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.botdraft.command.PatchBotDraftCommand
import com.cleevio.fatbot.domain.botdraft.BotDraftRepository
import com.cleevio.fatbot.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.util.Optional

class PatchBotDraftCommandHandlerTest(
	@Autowired private val commandBus: CommandBus,
	@Autowired private val botDraftRepository: BotDraftRepository,
) : IntegrationTest() {

	@Test
	fun `should update non-nullable properties`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 20.toUUID())
		integrationTestHelper.getBotDraft(id = 2.toUUID(), userId = 1.toUUID())

		commandBus(
			PatchBotDraftCommand(
				botDraftId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					name = "testName",
					profitTargetFraction = BigDecimal("0.25"),
					stopLossFraction = null,
					avatarFileId = 20.toUUID(),
					tradeAmount = BigDecimal("400.1"),
					buyFrequency = null,
				),
			),
		)

		botDraftRepository.findByIdOrNull(2.toUUID())!!.run {
			name shouldBe "testName"
			profitTargetFraction shouldBe BigDecimal("0.25")
			stopLossFraction shouldBe null
			avatarFileId shouldBe 20.toUUID()
			tradeAmount shouldBe BigDecimal("400.1")
			buyFrequency shouldBe null
		}

		commandBus(
			PatchBotDraftCommand(
				botDraftId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					name = null,
					profitTargetFraction = BigDecimal("0.30"),
					stopLossFraction = null,
					buyFrequency = 20.toBigInteger(),
				),
			),
		)

		botDraftRepository.findByIdOrNull(2.toUUID())!!.run {
			name shouldBe "testName"
			profitTargetFraction shouldBe BigDecimal("0.30")
			stopLossFraction shouldBe null
			buyFrequency shouldBe 20
		}
	}

	@Test
	fun `should update nullable properties`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getBotDraft(id = 2.toUUID(), userId = 1.toUUID())

		commandBus(
			PatchBotDraftCommand(
				botDraftId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					numberOfHoldersFrom = Optional.empty(), // Update to null
					numberOfHoldersTo = Optional.of(20.toBigInteger()), // Update to 20
					dailyVolumeFromUsd = Optional.of(BigDecimal("12345")), // Update to 12345
					dailyVolumeToUsd = Optional.of(BigDecimal("45678")), // Update to 12345
					buyVolume = Optional.of(BigDecimal("0.5")),
					sellVolume = Optional.of(BigDecimal("0.5")),
					liquidityFromUsd = null, // No Update
					liquidityToUsd = Optional.of(BigDecimal("30")), // Update to 30
					marketCapFromUsd = Optional.empty(), // Update to null
					marketCapToUsd = Optional.of(BigDecimal("500")), // // Update to 500
				),
			),
		)

		botDraftRepository.findByIdOrNull(2.toUUID())!!.run {
			numberOfHoldersFrom shouldBe null
			numberOfHoldersTo shouldBe 20
			dailyVolumeFromUsd shouldBe BigDecimal("12345")
			dailyVolumeToUsd shouldBe BigDecimal("45678")
			buyVolume shouldBe BigDecimal("0.5")
			sellVolume shouldBe BigDecimal("0.5")
			liquidityFromUsd shouldBe null
			liquidityToUsd shouldBe BigDecimal("30")
			marketCapFromUsd shouldBe null
			marketCapToUsd shouldBe BigDecimal("500")
		}

		commandBus(
			PatchBotDraftCommand(
				botDraftId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					buyVolume = null,
					sellVolume = null,
					numberOfHoldersFrom = Optional.of(15.toBigInteger()), // Update to 15
					numberOfHoldersTo = null, //  "undefined" value - No update
					dailyVolumeFromUsd = Optional.empty(), // Update to null
					liquidityFromUsd = Optional.of(BigDecimal("20")),
				),
			),
		)

		botDraftRepository.findByIdOrNull(2.toUUID())!!.run {
			numberOfHoldersFrom shouldBe 15
			numberOfHoldersTo shouldBe 20
			dailyVolumeFromUsd shouldBe null
			dailyVolumeToUsd shouldBe BigDecimal("45678")
			buyVolume shouldBe BigDecimal("0.5")
			sellVolume shouldBe BigDecimal("0.5")
			liquidityFromUsd shouldBe BigDecimal("20")
			liquidityToUsd shouldBe BigDecimal("30")
			marketCapFromUsd shouldBe null
			marketCapToUsd shouldBe BigDecimal("500")
		}
	}

	@Test
	fun `should throw on invalid input ranges`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getBotDraft(id = 2.toUUID(), userId = 1.toUUID())

		shouldThrow<ConstraintViolationException> {
			commandBus(
				PatchBotDraftCommand(
					botDraftId = 2.toUUID(),
					userId = 1.toUUID(),
					patchRequest = PatchBotSettingsRequest(
						buyVolume = Optional.of(BigDecimal("0.4")),
						sellVolume = Optional.of(BigDecimal("0.6")),
					),
				),
			)
		}

		shouldThrow<IllegalArgumentException> {
			commandBus(
				PatchBotDraftCommand(
					botDraftId = 2.toUUID(),
					userId = 1.toUUID(),
					patchRequest = PatchBotSettingsRequest(
						buyVolume = Optional.of(BigDecimal("0.7")),
						sellVolume = Optional.of(BigDecimal("0.4")),
					),
				),
			)
		}

		val invalidRangeRequests = listOf(
			PatchBotSettingsRequest(
				buyVolume = Optional.of(BigDecimal("0.8")),
				sellVolume = Optional.of(BigDecimal("0.3")),
			),
			PatchBotSettingsRequest(
				dailyVolumeFromUsd = Optional.of(BigDecimal("123")),
				dailyVolumeToUsd = Optional.of(BigDecimal("122")),
			),
			PatchBotSettingsRequest(
				marketCapFromUsd = Optional.of(BigDecimal("1")),
				marketCapToUsd = Optional.of(BigDecimal("0")),
			),
			PatchBotSettingsRequest(
				liquidityFromUsd = Optional.of(BigDecimal("999")),
				liquidityToUsd = Optional.of(BigDecimal("1")),
			),
			PatchBotSettingsRequest(
				numberOfHoldersFrom = Optional.of(1.toBigInteger()),
				numberOfHoldersTo = Optional.of(1.toBigInteger()),
			),
		)

		invalidRangeRequests.forEach {
			shouldThrow<IllegalArgumentException> {
				commandBus(
					PatchBotDraftCommand(
						botDraftId = 2.toUUID(),
						userId = 1.toUUID(),
						patchRequest = it,
					),
				)
			}
		}

		// Test that present data in the DB are taken into account
		commandBus(
			PatchBotDraftCommand(
				botDraftId = 2.toUUID(),
				userId = 1.toUUID(),
				patchRequest = PatchBotSettingsRequest(
					liquidityFromUsd = Optional.of(BigDecimal("100")),
					buyVolume = Optional.of(BigDecimal("0.5")),
					sellVolume = Optional.of(BigDecimal("0.5")),
					marketCapToUsd = Optional.of(BigDecimal("500")),
				),
			),
		)

		val invalidRangeRequests2 = listOf(
			PatchBotSettingsRequest(
				// buyVolume is set to 0.5,
				sellVolume = Optional.of(BigDecimal("0.4")),
			),
			PatchBotSettingsRequest(
				// liquidityFrom set to 100,
				liquidityToUsd = Optional.of(BigDecimal("50")),
			),
			PatchBotSettingsRequest(
				marketCapFromUsd = Optional.of(BigDecimal("300")),
				marketCapToUsd = Optional.of(BigDecimal("200")),
			),
		)

		invalidRangeRequests2.forEach {
			shouldThrow<IllegalArgumentException> {
				commandBus(
					PatchBotDraftCommand(
						botDraftId = 2.toUUID(),
						userId = 1.toUUID(),
						patchRequest = it,
					),
				)
			}
		}
	}
}
