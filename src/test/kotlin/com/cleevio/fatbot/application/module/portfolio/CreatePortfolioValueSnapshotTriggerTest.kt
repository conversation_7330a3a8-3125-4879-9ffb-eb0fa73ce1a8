package com.cleevio.fatbot.application.module.portfolio

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.portfolio.PortfolioValueSnapshotRepository
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.bigdecimal.shouldBeEqualIgnoringScale
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.web3j.protocol.core.DefaultBlockParameterName
import java.math.BigDecimal
import java.math.BigInteger
import java.time.LocalDate

@Suppress("ktlint:standard:max-line-length")
class CreatePortfolioValueSnapshotTriggerTest(
	@Autowired private val underTest: CreatePortfolioValueSnapshotTrigger,
	@Autowired private val portfolioValueSnapshotRepository: PortfolioValueSnapshotRepository,
) : IntegrationTest() {

	@Test
	fun `should create portfolio snapshot`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())

		integrationTestHelper.getWallet(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			address = 10.toAddress(),
			chain = Chain.EVM_MAINNET,
		)
		integrationTestHelper.getWallet(
			id = 20.toUUID(),
			userId = 1.toUUID(),
			address = 10.toAddress(),
			chain = Chain.EVM_BASE,
		)

		// And one buy on mainnet
		integrationTestHelper.getMarketPosition(
			walletId = 10.toUUID(),
			chain = Chain.EVM_MAINNET,
			tokenAddress = "aaa".toAddress(),
		) {
			newBuy(
				amountOfTokensReceived = BigInteger("10000000000000000000"), // 10 tokens
				amountOfBaseCurrencyPaid = BigInteger("1000000000000000000"), // 1 ETH
				exchangeRate = BigDecimal("1.5"),
			)
		}

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_MAINNET,
			tokenAddress = "aaa".toAddress(),
			tokenDecimals = BigInteger("18"),
		)

		// And one buy on base chain
		integrationTestHelper.getMarketPosition(
			walletId = 20.toUUID(),
			chain = Chain.EVM_BASE,
			tokenAddress = "bbb".toAddress(),
		) {
			newBuy(
				amountOfTokensReceived = BigInteger("10000000000000000000"), // 10 tokens
				amountOfBaseCurrencyPaid = BigInteger("100000000000000000"), // 0.1 ETH
				exchangeRate = BigDecimal("1.5"),
			)
		}

		integrationTestHelper.getEvmTokenInfo(
			chain = Chain.EVM_BASE,
			tokenAddress = "bbb".toAddress(),
			tokenDecimals = BigInteger("18"),
		)

		// and mock to get token prices

		// on chain 1
		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************"]
				"0xe016d3ab00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000001000000000000000000000000aaa0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000aaa0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000000DE0B6B3A7640000" // 1 ETH

		every {
			readOnlyTransactionManager.sendCall(
				"******************************************",
				// encoded input of ["******************************************"]
				"0xe016d3ab00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000001000000000000000000000000bbb0000000000000000000000000000000000000",
				DefaultBlockParameterName.LATEST,
			)
		} returns "0x" +
			"0000000000000000000000000000000000000000000000000000000000000040" +
			"0000000000000000000000000000000000000000000000000000000000000080" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"000000000000000000000000bbb0000000000000000000000000000000000000" +
			"0000000000000000000000000000000000000000000000000000000000000001" +
			"0000000000000000000000000000000000000000000000001BC16D674EC80000" // 2 ETH

		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.ETH) } returns "3000".toBigDecimal()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns "5000".toBigDecimal()
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.BNB) } returns "7000".toBigDecimal()

		every { web3jWrapper.getWalletBalances(walletAddresses = listOf(10.toAddress())) } returns
			listOf(BigInteger("1000000000000000000")) // 1 ETH

		underTest.trigger()

		val snapshots = portfolioValueSnapshotRepository.findAll()

		snapshots.size shouldBe 5
		with(snapshots.first { it.chain == Chain.EVM_MAINNET }) {
			this.date shouldBe LocalDate.of(2024, 8, 29)

			// 1 ETH on wallet main net -> 3000 USD
			// 10 tokens on main net, each cost 1 ETH -> 30000 USD
			this.valueUsd shouldBeEqualIgnoringScale BigDecimal("33000")
		}
		with(snapshots.first { it.chain == Chain.EVM_BASE }) {
			this.date shouldBe LocalDate.of(2024, 8, 29)

			// 1 ETH on wallet base chain -> 3000 USD
			// 10 tokens on base chain, each cost 2 ETH -> 60000 USD
			this.valueUsd shouldBeEqualIgnoringScale BigDecimal("63000")
		}
		with(snapshots.first { it.chain == Chain.SOLANA }) {
			this.date shouldBe LocalDate.of(2024, 8, 29)
			this.valueUsd shouldBeEqualIgnoringScale BigDecimal("0")
		}
		with(snapshots.first { it.chain == Chain.EVM_BSC }) {
			this.date shouldBe LocalDate.of(2024, 8, 29)
			this.valueUsd shouldBeEqualIgnoringScale BigDecimal("0")
		}
		with(snapshots.first { it.chain == Chain.EVM_ARBITRUM_ONE }) {
			this.date shouldBe LocalDate.of(2024, 8, 29)
			this.valueUsd shouldBeEqualIgnoringScale BigDecimal("0")
		}
	}
}
