package com.cleevio.fatbot.application.module.leaderboard

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.application.module.userleaderbord.service.RefreshUserLeaderboardService
import com.cleevio.fatbot.domain.userleaderboad.UserLeaderBoardRepository
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class RefreshUserLeaderboardServiceTest(
	@Autowired private val underTest: RefreshUserLeaderboardService,
	@Autowired private val userLeaderBoardRepository: UserLeaderBoardRepository,
) : IntegrationTest() {

	@Test
	fun `should refresh leaderboard with correct rankings`() {
		// given
		val user1 = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = user1.id,
			beforeSave = { updateDonuts(1000.toBigDecimal()) },
		)
		val user2 = integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "test2@test")
		integrationTestHelper.getUserStatistics(
			userId = user2.id,
			beforeSave = { updateDonuts(500.toBigDecimal()) },
		)
		val user3 = integrationTestHelper.getFirebaseUser(id = 3.toUUID(), email = "test3@test")
		integrationTestHelper.getUserStatistics(
			userId = user3.id,
			beforeSave = { updateDonuts(250.toBigDecimal()) },
		)
		val user4 = integrationTestHelper.getFirebaseUser(id = 4.toUUID(), email = "test4@test")
		integrationTestHelper.getUserStatistics(
			userId = user4.id,
			beforeSave = { updateDonuts(250.toBigDecimal()) },
		)

		underTest()

		val leaderboardEntries = userLeaderBoardRepository.findAll()
		leaderboardEntries.size shouldBe 4
		leaderboardEntries.find { it.userId == 1.toUUID() }!!.run {
			rank shouldBe 1
			donutGainedSnapshot shouldBeEqualComparingTo 1000.toBigDecimal()
		}
		leaderboardEntries.find { it.userId == 2.toUUID() }!!.run {
			rank shouldBe 2
			donutGainedSnapshot shouldBeEqualComparingTo 500.toBigDecimal()
		}
		leaderboardEntries.find { it.userId == 3.toUUID() }!!.run {
			rank shouldBe 3
			donutGainedSnapshot shouldBeEqualComparingTo 250.toBigDecimal()
		}
		leaderboardEntries.find { it.userId == 4.toUUID() }!!.run {
			rank shouldBe 4
			donutGainedSnapshot shouldBeEqualComparingTo 250.toBigDecimal()
		}
	}

	@Test
	fun `should refresh leaderboard with existing users`() {
		// actual user in statistics
		val user1 = integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getUserStatistics(
			userId = user1.id,
			beforeSave = { updateDonuts(1000.toBigDecimal()) },
		)
		val user2 = integrationTestHelper.getFirebaseUser(id = 2.toUUID(), email = "test2@test")
		integrationTestHelper.getUserStatistics(
			userId = user2.id,
			beforeSave = { updateDonuts(500.toBigDecimal()) },
		)

		// actual user leaderboard
		integrationTestHelper.getUserLeaderboard(
			userId = user2.id,
			rank = 2,
		)
		val user3 = integrationTestHelper.getFirebaseUser(id = 3.toUUID(), email = "test4@test")
		integrationTestHelper.getUserLeaderboard(
			userId = user3.id,
			rank = 1,
		)

		underTest()

		val leaderboardEntries = userLeaderBoardRepository.findAll()
		leaderboardEntries.size shouldBe 2
		leaderboardEntries.find { it.userId == 1.toUUID() }!!.run {
			rank shouldBe 1
			donutGainedSnapshot shouldBe 1000.toBigDecimal()
		}
		leaderboardEntries.find { it.userId == 2.toUUID() }!!.run {
			rank shouldBe 2
			donutGainedSnapshot shouldBe 500.toBigDecimal()
		}
	}
}
