package com.cleevio.fatbot.application.module.bottransaction

import com.cleevio.fatbot.IntegrationTest
import com.cleevio.fatbot.IntegrationTestClock
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.bottransaction.query.SearchUserBotsTransactionsQuery
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.toAddress
import com.cleevio.fatbot.toUUID
import io.kotest.matchers.collections.shouldNotContain
import io.kotest.matchers.equals.shouldBeEqual
import io.kotest.matchers.shouldBe
import io.mockk.every
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.math.BigInteger
import java.net.URI
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

class SearchUserBotsTransactionsQueryHandlerTest(
	@Autowired private val queryBus: QueryBus,
	@Autowired private val clock: IntegrationTestClock,
) : IntegrationTest() {

	@BeforeEach
	fun setUp() {
		every { coinbaseConnector.getUsdExchangeRate(CryptoCurrency.SOL) } returns BigDecimal("100.001")
	}

	@Test
	fun `should search all user bot transactions`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())
		integrationTestHelper.getFile(id = 3.toUUID())
		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			name = "testBot1",
			avatarFileId = 2.toUUID(),
		)
		integrationTestHelper.getBot(
			id = 20.toUUID(),
			userId = 1.toUUID(),
			name = "testBot2",
			avatarFileId = 3.toUUID(),
		)

		integrationTestHelper.getBotWallet(
			id = 11.toUUID(),
			botId = 10.toUUID(),
			address = 11.toAddress(),
		)
		integrationTestHelper.getBotWallet(
			id = 21.toUUID(),
			botId = 20.toUUID(),
			address = 21.toAddress(),
		)

		integrationTestHelper.getBotTokenInfo(
			tokenAddress = 666.toAddress(),
			name = "Hot token",
			symbol = "HOTT",
			tokenDecimals = BigInteger.valueOf(9),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = 999.toAddress(),
			name = "Cold token",
			symbol = "BRRR",
			tokenDecimals = BigInteger.valueOf(6),
		)

		val tx0 = integrationTestHelper.getBotTransaction(
			id = 100.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.DEPOSIT,
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.01e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("150.001"),
			amountIn = BigInteger("100000000"),
			amountOut = null,
			createdAt = Instant.now(clock).minus(1, ChronoUnit.DAYS),
		)

		val tx1 = integrationTestHelper.getBotTransaction(
			id = 101.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.DEPOSIT,
			txHash = TxHash("txHash1"),
			status = TransactionStatus.FAILED,
			tokenAddress = 666.toAddress(),
			baseValue = 0.08e9.toLong().toBigInteger(),
			amountIn = BigInteger("80000000"),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		integrationTestHelper.getBotTransaction(
			id = 102.toUUID(),
			botWalletId = 21.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash2"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.03e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
			amountIn = BigInteger.valueOf(300),
			amountOut = BigInteger.valueOf(300_000),
		)

		integrationTestHelper.getBotTransaction(
			id = 103.toUUID(),
			botWalletId = 21.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash3"),
			status = TransactionStatus.PENDING,
			tokenAddress = 666.toAddress(),
			baseValue = 0.04e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		val tx4 = integrationTestHelper.getBotTransaction(
			id = 104.toUUID(),
			botWalletId = 21.toUUID(),
			type = BotTransactionType.APPROVE,
			txHash = TxHash("txHash4"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.05e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		val tx5 = integrationTestHelper.getBotTransaction(
			id = 105.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash5"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.06e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("150.001"),
			amountIn = null,
			amountOut = BigInteger("60000000"),
		)

		val tx6 = integrationTestHelper.getBotTransaction(
			id = 106.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash6"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 999.toAddress(),
			baseValue = 0.07e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("160.001"),
			amountIn = BigInteger.valueOf(800),
			amountOut = BigInteger("70000000"),
		)

		// Should not be included in the result
		integrationTestHelper.getBotTransaction(
			id = 107.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.DEPOSIT,
			txHash = TxHash("txHash7"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.01e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
			amountIn = BaseAmount.ONE.amount,
			amountOut = null,
			createdAt = Instant.now(clock).minus(1, ChronoUnit.DAYS),
		)

		val resultFirstSlice = queryBus(
			SearchUserBotsTransactionsQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotsTransactionsQuery.Filter(
					botId = 10.toUUID(),
					searchString = null,
				),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 2, lastId = null),
			),
		)

		val resultNextSlice = queryBus(
			SearchUserBotsTransactionsQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotsTransactionsQuery.Filter(
					botId = 10.toUUID(),
					searchString = null,
				),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = resultFirstSlice.lastId),
			),
		)

		val resultLastSlice = queryBus(
			SearchUserBotsTransactionsQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotsTransactionsQuery.Filter(
					botId = 10.toUUID(),
					searchString = null,
				),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = resultNextSlice.lastId),
			),
		)

		resultFirstSlice.content.size shouldBe 2
		resultFirstSlice.lastId shouldBe tx5.id
		resultFirstSlice.hasMore shouldBe true

		with(resultFirstSlice.content[0]) {
			walletId shouldBe 11.toUUID()
			txId shouldBe tx6.id
			txHash shouldBe tx6.txHash
			txDetailUrl shouldBe URI.create("https://solscan.io/tx/${tx6.txHash.txHash}")
			txType shouldBe BotTransactionType.PORTFOLIO_WITHDRAW
			txStatus shouldBe TransactionStatus.SUCCESS
			botId shouldBe 10.toUUID()
			botName shouldBe "testBot1"
			tokenAddress shouldBe 999.toAddress()
			tokenName shouldBe "Cold token"
			tokenSymbol shouldBe "BRRR"
			nativeAmount shouldBe NativeAmount(BigDecimal("0.070000000"))
			amountUsd shouldBe BigDecimal("11.************")
			txChain shouldBe Chain.SOLANA
			percentageOf shouldBe null
		}

		with(resultFirstSlice.content[1]) {
			walletId shouldBe 11.toUUID()
			txId shouldBe tx5.id
			txHash shouldBe tx5.txHash
			txDetailUrl shouldBe URI.create("https://solscan.io/tx/${tx5.txHash.txHash}")
			txType shouldBe BotTransactionType.PORTFOLIO_WITHDRAW
			txStatus shouldBe TransactionStatus.SUCCESS
			botId shouldBe 10.toUUID()
			botName shouldBe "testBot1"
			tokenAddress shouldBe 666.toAddress()
			tokenName shouldBe "Hot token"
			tokenSymbol shouldBe "HOTT"
			nativeAmount shouldBe NativeAmount(BigDecimal("0.060000000"))
			amountUsd?.stripTrailingZeros() shouldBe BigDecimal("9.00006")
			txChain shouldBe Chain.SOLANA
			percentageOf shouldBe null
		}

		resultNextSlice.content.size shouldBe 2
		resultNextSlice.lastId shouldBe tx0.id
		resultNextSlice.hasMore shouldBe false

		// tx4 is of type approve, that should be filtered out
		resultNextSlice.content.map { it.txId } shouldNotContain tx4.id

		resultNextSlice.content[0].txId shouldBe tx1.id
		resultNextSlice.content[1].txId shouldBe tx0.id

		resultLastSlice.content.size shouldBe 0
		resultLastSlice.lastId shouldBe null
		resultLastSlice.hasMore shouldBe false
	}

	@Test
	fun `should search user bot transactions based on search string`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())
		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			name = "testBot1",
			avatarFileId = 2.toUUID(),
		)
		integrationTestHelper.getBotWallet(
			id = 11.toUUID(),
			botId = 10.toUUID(),
			address = 11.toAddress(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = 666.toAddress(),
			name = "Hot token",
			symbol = "HOTT",
			tokenDecimals = BigInteger.valueOf(9),
		)

		val tx0 = integrationTestHelper.getBotTransaction(
			id = 100.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.DEPOSIT,
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.1e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
			amountIn = BigInteger("10000000"),
			amountOut = BigInteger.valueOf(100_000),
		)

		val tx1 = integrationTestHelper.getBotTransaction(
			id = 101.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.DEPOSIT,
			txHash = TxHash("txHash1"),
			status = TransactionStatus.FAILED,
			tokenAddress = 666.toAddress(),
			baseValue = 0.2e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		val tx2 = integrationTestHelper.getBotTransaction(
			id = 102.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash2"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.3e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
			amountIn = BigInteger.valueOf(300),
			amountOut = BigInteger("30000000"),
		)

		val tx3 = integrationTestHelper.getBotTransaction(
			id = 103.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash3"),
			status = TransactionStatus.PENDING,
			tokenAddress = 666.toAddress(),
			baseValue = 0.4e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		integrationTestHelper.getBotTransaction(
			id = 104.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.APPROVE,
			txHash = TxHash("txHash4"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.51e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		val tx5 = integrationTestHelper.getBotTransaction(
			id = 105.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash5"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.6e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
			amountIn = null,
			amountOut = BigInteger("50000000"),
		)

		val result = queryBus(
			SearchUserBotsTransactionsQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotsTransactionsQuery.Filter(
					botId = 10.toUUID(),
					searchString = "oT", // searching for HOTT
				),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 2, lastId = null),
			),
		)

		result.content.size shouldBe 2
		result.lastId shouldBe tx3.id
		result.hasMore shouldBe true

		result.content[0].txId shouldBe tx5.id
		result.content[1].txId shouldBe tx3.id

		val nextSliceResult = queryBus(
			SearchUserBotsTransactionsQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotsTransactionsQuery.Filter(
					botId = 10.toUUID(),
					searchString = "oT", // searching for HOTT
				),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 2, lastId = result.lastId),
			),
		)

		nextSliceResult.content.size shouldBe 2
		nextSliceResult.lastId shouldBe tx1.id
		nextSliceResult.hasMore shouldBe true

		nextSliceResult.content[0].txId shouldBe tx2.id
		nextSliceResult.content[1].txId shouldBe tx1.id

		val nextSliceResult2 = queryBus(
			SearchUserBotsTransactionsQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotsTransactionsQuery.Filter(
					botId = 10.toUUID(),
					searchString = "oT", // searching for HOTT
				),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 2, lastId = nextSliceResult.lastId),
			),
		)

		nextSliceResult2.content.size shouldBe 1
		nextSliceResult2.lastId shouldBe tx0.id
		nextSliceResult2.hasMore shouldBe false
		nextSliceResult2.content[0].txId shouldBe tx0.id
	}

	@Test
	fun `should search user bot transactions by bot id`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())
		integrationTestHelper.getFile(id = 3.toUUID())
		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			name = "testBot1",
			avatarFileId = 2.toUUID(),
		)
		integrationTestHelper.getBot(
			id = 20.toUUID(),
			userId = 1.toUUID(),
			name = "testBot2",
			avatarFileId = 3.toUUID(),
		)
		integrationTestHelper.getBotWallet(
			id = 11.toUUID(),
			botId = 10.toUUID(),
			address = 11.toAddress(),
		)
		integrationTestHelper.getBotWallet(
			id = 21.toUUID(),
			botId = 20.toUUID(),
			address = 21.toAddress(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = 666.toAddress(),
			name = "Hot token",
			symbol = "HOTT",
			tokenDecimals = BigInteger.valueOf(9),
		)

		integrationTestHelper.getBotTransaction(
			id = 30.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.DEPOSIT,
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.1e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
			amountIn = BigInteger("100000000"),
			amountOut = null,
		)

		integrationTestHelper.getBotTransaction(
			id = 31.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.DEPOSIT,
			txHash = TxHash("txHash1"),
			status = TransactionStatus.FAILED,
			tokenAddress = 666.toAddress(),
			baseValue = 0.2e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		integrationTestHelper.getBotTransaction(
			id = 32.toUUID(),
			botWalletId = 21.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash2"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.3e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
			amountIn = BigInteger.valueOf(300),
			amountOut = BigInteger.valueOf(300_000),
		)

		integrationTestHelper.getBotTransaction(
			id = 33.toUUID(),
			botWalletId = 21.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash3"),
			status = TransactionStatus.PENDING,
			tokenAddress = 666.toAddress(),
			baseValue = 0.4e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		integrationTestHelper.getBotTransaction(
			id = 34.toUUID(),
			botWalletId = 21.toUUID(),
			type = BotTransactionType.APPROVE,
			txHash = TxHash("txHash4"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.5e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		integrationTestHelper.getBotTransaction(
			id = 35.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash5"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.6e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
			amountIn = BigInteger("60000000"),
			amountOut = BigInteger("50000000"),
		)

		val result = queryBus(
			SearchUserBotsTransactionsQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotsTransactionsQuery.Filter(
					botId = 10.toUUID(),
					searchString = null,
				),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
			),
		)

		result.content.size shouldBe 3
		result.lastId shouldBe 30.toUUID()
		result.hasMore shouldBe false

		// Verify all transactions belong to bot1
		result.content.forEach { transaction ->
			transaction.botId shouldBe 10.toUUID()
			transaction.botName shouldBe "testBot1"
		}

		// Verify transactions are ordered by creation date (newest first)
		result.content[0].txId shouldBe 35.toUUID()
		result.content[1].txId shouldBe 31.toUUID()
		result.content[2].txId shouldBe 30.toUUID()

		// Verify APPROVE transactions are filtered out
		result.content.map { it.txId } shouldNotContain 34.toUUID()
	}

	@Test
	fun `should ignore transactions with small usd value`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())
		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			name = "testBot1",
			avatarFileId = 2.toUUID(),
		)
		integrationTestHelper.getBotWallet(
			id = 11.toUUID(),
			botId = 10.toUUID(),
			address = 11.toAddress(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = 666.toAddress(),
			name = "Hot token",
			symbol = "HOTT",
			tokenDecimals = BigInteger.valueOf(9),
		)

		// valueUsd = 0.50005 -> Should be included
		integrationTestHelper.getBotTransaction(
			id = 100.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.DEPOSIT,
			txHash = TxHash("txHash1"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.005e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
			amountIn = BigInteger("10000000"),
			amountOut = null,
		)

		// valueUsd = 0.49994999 -> Should NOT be included
		integrationTestHelper.getBotTransaction(
			id = 101.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.DEPOSIT,
			txHash = TxHash("txHash2"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.004999e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
			amountIn = BigInteger("10000000"),
			amountOut = null,
		)

		// valueUsd = 40.004 -> Should be included
		integrationTestHelper.getBotTransaction(
			id = 102.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash3"),
			status = TransactionStatus.PENDING,
			tokenAddress = 666.toAddress(),
			baseValue = 0.4e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		// valueUsd = 0,00010001 -> Should NOT be included
		integrationTestHelper.getBotTransaction(
			id = 103.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash4"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.000001e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		val result = queryBus(
			SearchUserBotsTransactionsQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotsTransactionsQuery.Filter(
					botId = 10.toUUID(),
					searchString = null,
				),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 10, lastId = null),
			),
		)

		result.content.size shouldBe 2
		result.lastId shouldBe 100.toUUID()
		result.hasMore shouldBe false
		result.content[0].txId shouldBe 102.toUUID()
		result.content[1].txId shouldBe 100.toUUID()
	}

	@Test
	fun `should search user bot transactions by type based on search string`() {
		integrationTestHelper.getFirebaseUser(id = 1.toUUID())
		integrationTestHelper.getFile(id = 2.toUUID())
		integrationTestHelper.getBot(
			id = 10.toUUID(),
			userId = 1.toUUID(),
			name = "testBot1",
			avatarFileId = 2.toUUID(),
		)
		integrationTestHelper.getBotWallet(
			id = 11.toUUID(),
			botId = 10.toUUID(),
			address = 11.toAddress(),
		)
		integrationTestHelper.getBotTokenInfo(
			tokenAddress = 666.toAddress(),
			name = "Hot token",
			symbol = "HOTT",
			tokenDecimals = BigInteger.valueOf(9),
		)

		integrationTestHelper.getBotTransaction(
			id = 100.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.DEPOSIT,
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.1e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
			amountIn = BigInteger("10000000"),
			amountOut = BigInteger.valueOf(100_000),
		)

		integrationTestHelper.getBotTransaction(
			id = 101.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.DEPOSIT,
			txHash = TxHash("txHash1"),
			status = TransactionStatus.FAILED,
			tokenAddress = 666.toAddress(),
			baseValue = 0.2e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		integrationTestHelper.getBotTransaction(
			id = 102.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash2"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.3e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
			amountIn = BigInteger.valueOf(300),
			amountOut = BigInteger("30000000"),
		)

		integrationTestHelper.getBotTransaction(
			id = 103.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash3"),
			status = TransactionStatus.PENDING,
			tokenAddress = 666.toAddress(),
			baseValue = 0.4e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		integrationTestHelper.getBotTransaction(
			id = 104.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.APPROVE,
			txHash = TxHash("txHash4"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.5e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
		)

		integrationTestHelper.getBotTransaction(
			id = 105.toUUID(),
			botWalletId = 11.toUUID(),
			type = BotTransactionType.PORTFOLIO_WITHDRAW,
			txHash = TxHash("txHash5"),
			status = TransactionStatus.SUCCESS,
			tokenAddress = 666.toAddress(),
			baseValue = 0.6e9.toLong().toBigInteger(),
			exchangeRateUsd = BigDecimal("100.001"),
			amountIn = null,
			amountOut = BigInteger("50000000"),
		)

		// searching for 'Deposit' type
		testSearch(searchString = "DeP", expectedTxIds = listOf(101.toUUID(), 100.toUUID()))

		// searching for 'Deposit' type
		testSearch(searchString = "deposit", expectedTxIds = listOf(101.toUUID(), 100.toUUID()))

		// searching for 'portfolio_withdraw' type
		testSearch(searchString = "withd", expectedTxIds = listOf(105.toUUID(), 103.toUUID(), 102.toUUID()))

		// searching for 'portfolio_withdraw' type
		testSearch(searchString = "portf", expectedTxIds = listOf(105.toUUID(), 103.toUUID(), 102.toUUID()))

		// searching for 'portfolio_withdraw' type
		testSearch(searchString = "portfolio_withd", expectedTxIds = listOf(105.toUUID(), 103.toUUID(), 102.toUUID()))

		// searching for 'approval' type
		testSearch(searchString = "approv", expectedTxIds = emptyList())

		// Searching for value usd 60.0006
		testSearch(searchString = "60.00", expectedTxIds = listOf(105.toUUID()))

		// Searching for value usd 60.0006
		testSearch(searchString = ".0006", expectedTxIds = listOf(105.toUUID()))

		// Searching for value usd 40.0004
		testSearch(searchString = "40.0004", expectedTxIds = listOf(103.toUUID()))
	}

	fun testSearch(searchString: String, expectedTxIds: List<UUID>) {
		val result = queryBus(
			SearchUserBotsTransactionsQuery(
				userId = 1.toUUID(),
				filter = SearchUserBotsTransactionsQuery.Filter(
					botId = 10.toUUID(),
					searchString = searchString,
				),
				infiniteScroll = InfiniteScrollDesc.UUID(size = 100, lastId = null),
			),
		)

		result.hasMore shouldBe false
		result.content.map { it.txId } shouldBeEqual expectedTxIds
	}
}
