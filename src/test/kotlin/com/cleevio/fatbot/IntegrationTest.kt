package com.cleevio.fatbot

import com.cleevio.fatbot.adapter.out.bitquery.BitqueryApiV2Connector
import com.cleevio.fatbot.adapter.out.bitquery.BitqueryEapConnector
import com.cleevio.fatbot.adapter.out.bitquery.GetIntervalTokenPricesBitquery
import com.cleevio.fatbot.adapter.out.bitquery.GetSingleIntervalTokenPriceBitquery
import com.cleevio.fatbot.adapter.out.bitquery.TokenTradeProvider
import com.cleevio.fatbot.adapter.out.coinbase.CoinbaseConnector
import com.cleevio.fatbot.adapter.out.coinbase.GetExchangeRateCoinbase
import com.cleevio.fatbot.adapter.out.dexscreener.DexScreenerConnector
import com.cleevio.fatbot.adapter.out.dexscreener.GetTokenDetailDexScreener
import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector
import com.cleevio.fatbot.adapter.out.evm.GetTokenPricesEVM
import com.cleevio.fatbot.adapter.out.evm.GetTransactionResultsEVM
import com.cleevio.fatbot.adapter.out.evm.builder.beaverbuild.BeaverbuildConnector
import com.cleevio.fatbot.adapter.out.evm.builder.titanbuilder.TitanbuilderConnector
import com.cleevio.fatbot.adapter.out.evm.context.EvmWeb3jWrapperProvider
import com.cleevio.fatbot.adapter.out.evm.context.Web3jWrapper
import com.cleevio.fatbot.adapter.out.firebase.FirebaseService
import com.cleevio.fatbot.adapter.out.openai.OpenAIConnector
import com.cleevio.fatbot.application.common.port.out.FirebaseAuth
import com.cleevio.fatbot.application.module.file.client.FileDownloadClient
import com.cleevio.fatbot.application.module.limitorder.service.LimitOrderTradesProcessingService
import com.cleevio.fatbot.application.module.token.port.out.RealtimeTokenTradePublisher
import com.cleevio.fatbot.application.module.transaction.port.out.PublishUserTransactionStatusChanged
import com.cleevio.fatbot.application.module.walletposition.event.listener.WalletCurrencyPositionEventListener
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import com.github.benmanes.caffeine.cache.AsyncCache
import com.github.benmanes.caffeine.cache.Cache
import com.ninjasquad.springmockk.MockkBean
import com.ninjasquad.springmockk.SpykBean
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.spyk
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.p2p.solanaj.rpc.RpcApi
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.event.ApplicationEventMulticaster
import org.springframework.context.support.AbstractApplicationContext
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.util.AopTestUtils
import org.web3j.protocol.Web3j
import org.web3j.tx.ReadonlyTransactionManager
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible

@SpringBootTest
@EnableAsync
@ActiveProfiles("test")
@ContextConfiguration(
	classes = [
		IntegrationTestAsyncConfig::class,
		IntegrationTestClockConfig::class,
		IntegrationTestFirebaseConfig::class,
	],
)
class IntegrationTest {

	@Autowired
	private lateinit var jdbcTemplate: JdbcTemplate

	@Autowired
	internal lateinit var integrationTestHelper: IntegrationTestHelper

	@Autowired
	private lateinit var countingAsyncUncaughtExceptionHandler: CountingAsyncUncaughtExceptionHandler

	@MockkBean
	internal lateinit var firebaseService: FirebaseService

	@MockkBean
	internal lateinit var evmWeb3JWrapperProvider: EvmWeb3jWrapperProvider

	@MockkBean
	internal lateinit var readOnlyTransactionManager: ReadonlyTransactionManager

	@MockkBean
	internal lateinit var web3jWrapper: Web3jWrapper

	@MockkBean
	internal lateinit var web3j: Web3j

	@MockkBean
	internal lateinit var getTransactionResultsEVM: GetTransactionResultsEVM

	@MockkBean
	internal lateinit var publishUserTransactionStatusChanged: PublishUserTransactionStatusChanged

	@MockkBean
	internal lateinit var walletCurrencyPositionEventListener: WalletCurrencyPositionEventListener

	@MockkBean
	internal lateinit var beaverbuildConnector: BeaverbuildConnector

	@MockkBean
	internal lateinit var titanbuilderConnector: TitanbuilderConnector

	@MockkBean
	internal lateinit var dexScreenerConnector: DexScreenerConnector

	@MockkBean
	internal lateinit var openAIConnector: OpenAIConnector

	@MockkBean
	internal lateinit var coinbaseConnector: CoinbaseConnector

	@MockkBean
	internal lateinit var bitqueryApiV2Connector: BitqueryApiV2Connector

	@MockkBean
	internal lateinit var bitqueryEapConnector: BitqueryEapConnector

	@MockkBean
	internal lateinit var tokenTradeProvider: TokenTradeProvider

	@MockkBean
	internal lateinit var realtimeTokenTradePublisher: RealtimeTokenTradePublisher

	@MockkBean
	internal lateinit var limitOrderTradesProcessingService: LimitOrderTradesProcessingService

	@MockkBean
	internal lateinit var rpcClient: RpcClient

	@MockkBean(name = "stakedRpcClient")
	internal lateinit var stakedClient: RpcClient

	@MockkBean
	internal lateinit var rpcApi: RpcApi

	@MockkBean
	internal lateinit var etherscanConnector: EtherscanConnector

	@SpykBean
	internal lateinit var fileDownloadClient: FileDownloadClient

	@SpykBean
	internal lateinit var chainProperties: ChainProperties

	@SpykBean
	internal lateinit var getTokenDetailDexScreener: GetTokenDetailDexScreener

	@Autowired
	internal lateinit var getTokenPricesEVM: GetTokenPricesEVM

	@Autowired
	private lateinit var applicationContext: AbstractApplicationContext

	@Autowired
	private lateinit var getExchangeRateCoinbase: GetExchangeRateCoinbase

	@Autowired
	private lateinit var getTokenPriceChartBitquery: GetIntervalTokenPricesBitquery

	@Autowired
	private lateinit var getSingleIntervalTokenPriceBitquery: GetSingleIntervalTokenPriceBitquery

	@Suppress("SpringJavaInjectionPointsAutowiringInspection") // ignore this error
	@Autowired
	private lateinit var integrationTestClock: IntegrationTestClock

	@MockkBean
	private lateinit var firebaseAuth: FirebaseAuth

	@Autowired
	private lateinit var realApplicationEventMulticaster: ApplicationEventMulticaster

	/**
	 * A Spy placed directly in [AbstractApplicationContext] that allows us to monitor / control
	 * all events that are passed in the context
	 *
	 * Note: Spring calls the `ApplicationEventMulticaster.multicastEvent(event, null)` variation.
	 * Meaning that `every { multicaster.multicastEvent(any(), any()) }` needs to be used for the mocking to take effect.
	 */
	internal lateinit var applicationEventMulticaster: ApplicationEventMulticaster

	@BeforeEach
	fun beforeEach() {
		every { firebaseAuth.verifyPassword(any(), any()) } just Runs
		integrationTestClock.reset()
		countingAsyncUncaughtExceptionHandler.reset()
		jdbcTemplate.execute(TRUNCATE_ALL_TABLES_SQL)

		getTokenDetailDexScreener.getPrivatePropertyOfProxy<GetTokenDetailDexScreener, AsyncCache<*, *>>("cache")!!
			.synchronous()
			.invalidateAll()

		getTokenPricesEVM.getPrivatePropertyOfProxy<GetTokenPricesEVM, AsyncCache<*, *>>("cache")!!
			.synchronous()
			.invalidateAll()

		getExchangeRateCoinbase.getPrivateProperty<GetExchangeRateCoinbase, AsyncCache<*, *>>("cache")!!
			.synchronous()
			.invalidateAll()

		getTokenPriceChartBitquery.getPrivatePropertyOfProxy<GetIntervalTokenPricesBitquery, Cache<*, *>>("cache")!!
			.invalidateAll()

		getSingleIntervalTokenPriceBitquery
			.getPrivatePropertyOfProxy<GetSingleIntervalTokenPriceBitquery, Cache<*, *>>("cache")!!
			.invalidateAll()

		// Inject the realApplicationEventMulticaster spy into applicationContext to spy on events
		val spyEventMulticaster = applicationContext.setAndReturnPrivateProperty(
			"applicationEventMulticaster",
			spyk(objToCopy = realApplicationEventMulticaster),
		) as ApplicationEventMulticaster

		applicationEventMulticaster = spyEventMulticaster

		every { evmWeb3JWrapperProvider.ofChainId(any()) } returns web3jWrapper

		every { web3jWrapper.getWeb3j() } returns web3j
		every { web3jWrapper.getReadOnlyTransactionManager() } returns readOnlyTransactionManager

		every { rpcClient.api } returns rpcApi
	}

	@AfterEach
	fun afterEach() {
		countingAsyncUncaughtExceptionHandler.assertNoExceptionsThrownInAsyncThreads()
	}
}

inline fun <reified T : Any, R> T.getPrivatePropertyOfProxy(name: String): R? =
	AopTestUtils.getTargetObject<T>(this).getPrivateProperty(name)

inline fun <reified T : Any, R> T.getPrivateProperty(name: String): R? = T::class
	.memberProperties
	.firstOrNull { it.name == name }
	?.apply { isAccessible = true }
	?.get(this) as? R

private const val TRUNCATE_ALL_TABLES_SQL = """
DO
${'$'}do${'$'}
	BEGIN
		EXECUTE
			(SELECT 'TRUNCATE TABLE ' || string_agg(oid::regclass::text, ', ') || ' CASCADE'
			 FROM   pg_class
			 WHERE  relkind = 'r'  -- only tables
			   AND    relnamespace = 'public'::regnamespace
			   AND relname NOT IN ('databasechangelog', 'databasechangeloglock')
			);
	END
${'$'}do${'$'};
"""
