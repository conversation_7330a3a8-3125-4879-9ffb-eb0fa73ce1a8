sentry:
  dsn:

spring:
  datasource:
    url: jdbc:tc:postgresql:15:///fatbot_api?serverTimezone=UTC
    driver-class-name: org.testcontainers.jdbc.ContainerDatabaseDriver
    hikari:
      maximum-pool-size: 10

fatbot:
  file-storage-url: http://localhost:8080
  storage:
    local-path: ${java.io.tmpdir}/fatbot-api
  fee:
    platform-fee-percentage: 1
    referrer-fee-reward-percentage: 30
    # referred users should still pay 0.9% platform fee (90.1% of 1% = 0.901 ~ 0.90%)
    referee-manual-trading-discount-percentage: 9.9
    referee-automatic-trading-discount-percentage: 10
  evm:
    - chain: EVM_MAINNET
      node: https://virtual.mainnet.rpc.tenderly.co/random-uuid
      tx-explorer-url: https://etherscan.io/tx/
      weth-address: '******************************************'
      fatbot-router: '******************************************'
      fatbot-util: '******************************************'
      referral-wallet-private-key: '0x86356e9f41f72b07059198d25cc69bffb0b498cbfd62cf0e34eee1e515e2a2c5'
      referral-claim-threshold-native-amount: 0.0000000000000005
      priority-fee-buffer-basis-point: 0
      buy-sell-slippage-basis-point: 400
    - chain: EVM_BASE
      node: https://virtual.mainnet.rpc.tenderly.co/random-uuid
      tx-explorer-url: https://basescan.org/tx/
      weth-address: '******************************************'
      fatbot-router: '******************************************'
      fatbot-util: '******************************************'
      referral-wallet-private-key: '0x86356e9f41f72b07059198d25cc69bffb0b498cbfd62cf0e34eee1e515e2a2c5'
      referral-claim-threshold-native-amount: 0.0000000000000006
      priority-fee-buffer-basis-point: 0
      buy-sell-slippage-basis-point: 400
    - chain: EVM_BSC
      node: https://virtual.mainnet.rpc.tenderly.co/random-uuid
      tx-explorer-url: https://basescan.org/tx/
      weth-address: '******************************************'
      fatbot-router: '******************************************'
      fatbot-util: '******************************************'
      referral-wallet-private-key: '0x86356e9f41f72b07059198d25cc69bffb0b498cbfd62cf0e34eee1e515e2a2c5'
      referral-claim-threshold-native-amount: 0.0000000000000006
      priority-fee-buffer-basis-point: 0
      buy-sell-slippage-basis-point: 400
    - chain: EVM_ARBITRUM_ONE
      node: https://virtual.mainnet.rpc.tenderly.co/random-uuid
      tx-explorer-url: https://arbiscan.io/
      weth-address: '******************************************'
      fatbot-router: '******************************************'
      fatbot-util: '******************************************'
      referral-wallet-private-key: '0x86356e9f41f72b07059198d25cc69bffb0b498cbfd62cf0e34eee1e515e2a2c5'
      referral-claim-threshold-native-amount: 0.0000000000000006
      priority-fee-buffer-basis-point: 0
      buy-sell-slippage-basis-point: 400
  svm:
    solana:
      node: "https://api.devnet.solana.com"
      fast-transaction-node: "https://api.devnet.solana.com"
      tx-explorer-url: https://solscan.io/tx/
      treasury-wallet: "Yq4ELHNNcVtbKxiqx7o4T37Fs4nu54HJPPRbSCz6ndH"
      referral-wallet-private-key: '2S5gZyFusNTyGSd4JBvMjLgSaoomCm12gUwkBWxLB3fNtGhUoDFKEPyLeXwDWXcYmb6pKbvZSNqAXE2s4bcuNUrU'
      referral-claim-threshold-native-amount: 0.5
  proxy:
    api-key: 00000000-0000-0000-0000-000000000000
    base-url: http://fake.url
temper:
  base-url: mock

integration:
  coinbase:
    base-url: https://api.coinbase.com
  titanbuilder:
    api-key: abcdef
    base-url: https://rpc.titanbuilder.xyz
  beaverbuild:
    base-url: https://rpc.beaverbuild.org/
  bitquery:
    graphql-endpoint: https://graphql.bitquery.io
  fatbot-api:
    api-key: 00000000-0000-0000-0000-000000000000
  etherscan:
    api-key: 00000000-0000-0000-0000-000000000000
  openai:
    api-key: 00000000-0000-0000-0000-000000000000
    base-url: https://api.openai.com
    model: gpt-4.1-nano-2025-04-14

logging:
  level:
    root: INFO
  config: classpath:logback-test.xml

springdoc:
  swagger-server:

management:
  metrics:
    enabled: false
  observations:
    enabled: false

grafana:
  otlp:
    enabled: false

security:
  wallet-encryption-key: KmxvbmdsaXZlZmF0Ym95Kg==

streak-system:
  multiplier:
    "3": 1.2
    "7": 1.5
    "30": 2
    "90": 3
  reset:
    cron: "-"
    lock-for: "-"

processing:
  bot-transactions:
    enabled: false
  token-trade-publishing:
    enabled: false
