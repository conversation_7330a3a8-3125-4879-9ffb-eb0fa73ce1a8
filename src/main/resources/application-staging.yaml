sentry:
  environment: staging
  traces-sample-rate: 0.2

springdoc:
  swagger-server: https://api.fatbot.staging.cleevio.dev

fatbot:
  proxy:
    base-url: http://fatbot-bitquery-proxy-api-cluster-ip-service.fatbot-bitquery-proxy-api-devel.svc.cluster.local:8080
  anti-mev:
    force-use: true
  file-storage-url: https://cdn.fatbot.staging.cleevio.dev
  svm:
    solana:
      warm-up-helius-staked-node-cache: false # delete this when we enable bot trading on staging

integration:
  fatbot-token-aggregator:
    base-url: http://fatbot-token-aggregator-staging-cluster-ip-service.fatbot-token-aggregator-staging.svc.cluster.local:8080
  fatbot-messaging-proxy:
    base-url: http://fatbot-messaging-proxy-api-cluster-ip-service.fatbot-messaging-proxy-api-staging.svc.cluster.local:8080
