query EVMTokenHistoricalData(
    $network: evm_network!
    $currencyAddress: String!
    $tokenAddress: String!
    $timeInterval: OLAP_DateTimeIntervalUnits!
    $intervalCount: Int!
    $limit: Int!
    $after: DateTime
    $afterDate: String
    $before: DateTime
    $beforeDate: String
) {
    EVM(network: $network, dataset: combined) {
        DEXTradeByTokens(
            orderBy: { descendingByField: "Block_Time" }
            where: {
                Trade: {
                    Side: {
                        Currency: { SmartContract: { is: $currencyAddress } }
                        AmountInUSD: { ge: "0.5" }
                    }
                    Currency: { SmartContract: { is: $tokenAddress } }
                    Success: true
                    PriceAsymmetry: { le: 0.1 }
                }
                TransactionStatus: { Success: true }
                Block: {
                    Time: { after: $after, before: $before }
                    Date: { before: $beforeDate, after: $afterDate }
                }
            }
            limit: { count: $limit }
        ) {
            Block {
                Time(interval: { in: $timeInterval, count: $intervalCount })
            }
            volume: sum(of: Trade_Side_AmountInUSD)
            high: quantile(of: Trade_PriceInUSD, level: 0.90)
            low: quantile(of: Trade_PriceInUSD, level: 0.10)
            Trade {
                open: PriceInUSD(minimum: Block_Number)
                openNative: Price(minimum: Block_Number)
                close: PriceInUSD(maximum: Block_Number)
                closeNative: Price(maximum: Block_Number)
            }
        }
    }
}
