query EVMTokenPrice(
    $network: evm_network!,
    $tokenAddress: String!,
    $currencyAddress: String!,
    $pairAddress: String!
    $timeInterval: OLAP_DateTimeIntervalUnits!
    $intervalCount: Int!
    $limit: Int!
    $afterDate: String
    $afterDateTime: DateTime
) {
    EVM(dataset: combined, network: $network) {
        DEXTradeByTokens(
            orderBy: { descendingByField: "Block_Time"}
            limit: { count: $limit }
            where: {
                Block: {
                    Date: { after: $afterDate }
                    Time: { after: $afterDateTime }
                }
                Trade: {
                    Currency: {
                        SmartContract: { is: $tokenAddress }
                    },
                    Side: {
                        Currency: {
                            SmartContract: { is: $currencyAddress }
                        }
                        AmountInUSD: { gt: "0.25" }
                    }
                    Dex: {
                        SmartContract: { is: $pairAddress }
                    }
                    Success: true
                    PriceAsymmetry: { le: 0.1 }
                }
                TransactionStatus: {Success: true}
            }
        ) {
            Block {
                Time(interval: {in: $timeInterval, count: $intervalCount })
            }
            volume: sum(of: Trade_Side_AmountInUSD)
            price: median(of: Trade_PriceInUSD)
            priceNative: median(of: Trade_Price)
        }
    }
}
