query SolanaTokenHistoricalData(
    $tokenAddress: String!
    $quoteTokenAddresses: [String!]!
    $timeInterval: OLAP_DateTimeIntervalUnits!
    $intervalCount: Int!
    $limit: Int!
    $after: DateTime
    $afterDate: String
    $before: DateTime
    $beforeDate: String
) {
    Solana(dataset: combined) {
        DEXTradeByTokens(
            orderBy: { descendingByField: "Block_Time" }
            where: {
                Block: {
                    Time: { after: $after, before: $before }
                    Date: { before: $beforeDate, after: $afterDate }
                }
                Trade: {
                    Currency: { MintAddress: { is: $tokenAddress } }
                    Side: {
                        Currency: { MintAddress: { in: $quoteTokenAddresses } }
                        AmountInUSD: { ge: "0.5" }
                    }
                    PriceAsymmetry: { le: 0.1 }
                }
                Transaction: { Result: { Success: true } }
            }
            limit: { count: $limit }
        ) {
            Block {
                Time(interval: { count: $intervalCount, in: $timeInterval })
            }
            volume: sum(of: Trade_Side_AmountInUSD)
            high: quantile(of: Trade_PriceInUSD, level: 0.90)
            low: quantile(of: Trade_PriceInUSD, level: 0.10)
            Trade {
                open: PriceInUSD(minimum: Block_Slot)
                openNative: Price(minimum: Block_Slot)
                close: PriceInUSD(maximum: Block_Slot)
                closeNative: Price(maximum: Block_Slot)
            }
        }
    }
}
