<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="sebastian.jakabcin">
        <createTable tableName="token_price_snapshot">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="token_address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="chain_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="price_wei" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="exchange_rate_usd" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="token_price_snapshot" indexName="19bcaf0338ce4bb7b907_ix">
            <column name="token_address"/>
            <column name="created_at"/>
        </createIndex>
    </changeSet>
    
    <changeSet id="1" author="sebastian.jakabcin">
        <addColumn tableName="token_price_snapshot">
            <column name="valid_at" type="timestamptz"/>
        </addColumn>

        <update tableName="token_price_snapshot">
            <column name="valid_at" valueComputed="token_price_snapshot.created_at"/>
        </update>

        <addNotNullConstraint tableName="token_price_snapshot" columnName="valid_at" />

        <dropIndex tableName="token_price_snapshot" indexName="19bcaf0338ce4bb7b907_ix" />

        <createIndex tableName="token_price_snapshot" indexName="19bcaf0338ce4bb7b907_ix">
            <column name="token_address"/>
            <column name="valid_at"/>
        </createIndex>
    </changeSet>

    <changeSet id="2" author="tomas.krason">
        <dropIndex tableName="token_price_snapshot" indexName="19bcaf0338ce4bb7b907_ix"/>

        <createIndex tableName="token_price_snapshot" indexName="19bcaf0338ce4bb7b907_ix">
            <column name="token_address"/>
            <column name="valid_at" descending="true"/>
        </createIndex>
    </changeSet>
    
    <changeSet id="3" author="tomas.krason">
        <createIndex tableName="token_price_snapshot" indexName="804ff87cb7ce48c18d37_ix">
            <column name="valid_at"/>
        </createIndex>
    </changeSet>

    <changeSet id="4" author="sebastian.jakabcin">
        <addColumn tableName="token_price_snapshot">
            <column name="chain" type="text" />
        </addColumn>

        <sql>
            UPDATE token_price_snapshot
            SET chain = 'EVM_MAINNET'
            WHERE chain_id = 1
        </sql>
        <sql>
            UPDATE token_price_snapshot
            SET chain = 'EVM_BASE'
            WHERE chain_id = 8453
        </sql>

        <addNotNullConstraint tableName="token_price_snapshot" columnName="chain" />
        <dropColumn tableName="token_price_snapshot" columnName="chain_id" />
    </changeSet>
</databaseChangeLog>