<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="sebastian.jakabcin">
        <createTable tableName="bot_wallet">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="bot_id" type="uuid">
                <constraints nullable="false" />
            </column>
            <column name="address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="private_key" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="chain_type" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="chain_id" type="bigint" />

            <column name="balance" type="numeric">
                <constraints nullable="false" />
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <createIndex tableName="bot_wallet" indexName="d206185ecbd64357_ui" unique="true">
            <column name="bot_id" />
        </createIndex>
    </changeSet>

    <changeSet id="1" author="sebastian.jakabcin">
        <addColumn tableName="bot_wallet">
            <column name="chain" type="text" />
        </addColumn>

        <sql>
            UPDATE bot_wallet
            SET chain = 'SOLANA'
            WHERE chain_type = 'SOLANA'
        </sql>

        <addNotNullConstraint tableName="bot_wallet" columnName="chain" />

        <dropColumn tableName="bot_wallet" columnName="chain_type" />
        <dropColumn tableName="bot_wallet" columnName="chain_id" />
    </changeSet>

    <changeSet id="2" author="sebastian.jakabcin">
        <addColumn tableName="bot_wallet">
            <column name="latest_signature" type="text" />
        </addColumn>
    </changeSet>

    <changeSet id="3" author="anna.navratilova">
        <addColumn tableName="bot_wallet">
            <column name="acquisition_value_usd" type="numeric" defaultValue="0.0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>