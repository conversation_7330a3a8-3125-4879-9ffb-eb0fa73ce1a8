<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="sebastian.jakabcin">
        <createTable tableName="hot_token">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="token_address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="token_name" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="token_symbol" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="token_decimals" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="token_image_file_id" type="uuid">
                <constraints nullable="true"/>
            </column>
            <column name="token_image_file_extension" type="text">
                <constraints nullable="true"/>
            </column>
            <column name="chain_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="change24h" type="numeric">
                <constraints nullable="true"/>
            </column>
            <column name="volume1h_usd" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="price_usd" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="1" author="tomas.krason">
        <renameColumn tableName="hot_token" oldColumnName="volume1h_usd" newColumnName="volume24h_usd"/>
    </changeSet>

    <changeSet id="2" author="sebastian.jakabcin">
        <dropColumn tableName="hot_token" columnName="price_usd" />
        <dropColumn tableName="hot_token" columnName="change24h" />
    </changeSet>
    
    <changeSet id="3" author="sebastian.jakabcin">
        <addColumn tableName="hot_token">
            <column name="chain" type="text" />
        </addColumn>

        <sql>
            UPDATE hot_token
            SET chain = 'EVM_MAINNET'
            WHERE chain_id = 1
        </sql>
        <sql>
            UPDATE hot_token
            SET chain = 'EVM_BASE'
            WHERE chain_id = 8453
        </sql>

        <addNotNullConstraint tableName="hot_token" columnName="chain" />
        <dropColumn tableName="hot_token" columnName="chain_id" />
    </changeSet>
</databaseChangeLog>