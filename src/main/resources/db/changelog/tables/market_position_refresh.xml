<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="tomas.krason">
        <createTable tableName="market_position_refresh">

            <column name="id" type="UUID">
                <constraints primaryKey="true"/>
            </column>
            <column name="user_id" type="UUID">
                <constraints nullable="false" />
            </column>
            <column name="last_viewed_at" type="timestamptz">
                <constraints nullable="false" />
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false" />
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false" />
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <createIndex tableName="market_position_refresh" indexName="9a69be11d5d94e53a27f_ui" unique="true">
            <column name="user_id"/>
        </createIndex>
        
    </changeSet>

    <changeSet id="1" author="sebastian.jakabcin">
        <renameColumn tableName="market_position_refresh"
                      oldColumnName="last_viewed_at"
                      newColumnName="market_last_viewed_at" />

        <addColumn tableName="market_position_refresh">
            <column name="wallet_last_viewed_at" type="timestamptz" defaultValue="1970-01-01T00:00:00Z">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>