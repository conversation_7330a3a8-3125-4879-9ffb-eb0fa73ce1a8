<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="jana-farova">
        <createTable tableName="user_statistics">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="user_id" type="uuid">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="donuts_gained" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="donuts_gained_cumulative" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz"/>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <createIndex tableName="user_statistics" indexName="6d1e30d1678f49b4b99b_ix">
            <column name="user_id" />
        </createIndex>
    </changeSet>

    <changeSet id="1" author="jana-farova">
        <addColumn tableName="user_statistics">
            <column name="traded_amount_in_day" type="numeric" defaultValue="0"/>
        </addColumn>
        <dropDefaultValue tableName="user_statistics" columnName="traded_amount_in_day"/>
    </changeSet>

    <changeSet id="2" author="jan.farova">
        <addColumn tableName="user_statistics">
            <column name="days_in_streak" type="int" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="last_manual_trade_at" type="date"/>
        </addColumn>
    </changeSet>

    <changeSet id="3" author="jana-farova">
        <renameColumn tableName="user_statistics" oldColumnName="donuts_gained" newColumnName="leaderboard_donuts"/>
        <addColumn tableName="user_statistics">
            <column name="league_donuts" type="numeric" defaultValue="0"/>
        </addColumn>

        <dropDefaultValue tableName="user_statistics" columnName="league_donuts"/>
    </changeSet>
</databaseChangeLog>
