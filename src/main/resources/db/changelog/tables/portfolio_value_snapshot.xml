<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="norbert-bodnar">
        <createTable tableName="portfolio_value_snapshot">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="date" type="date">
                <constraints nullable="false"/>
            </column>
            <column name="value_usd" type="numeric">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="portfolio_value_snapshot" indexName="01939bc10d8f72888ffe_ix" unique="true">
            <column name="user_id"/>
            <column name="date"/>
        </createIndex>
    </changeSet>

    <changeSet id="1" author="tomas.krason">
        <dropIndex tableName="portfolio_value_snapshot" indexName="01939bc10d8f72888ffe_ix"/>

        <addColumn tableName="portfolio_value_snapshot">
            <column name="chain" type="text" defaultValue="ETH_MAINNET">
                <constraints nullable="false"/>
            </column>
        </addColumn>

        <createIndex tableName="portfolio_value_snapshot" indexName="01939bc10d8f72888ffe_ix" unique="true">
            <column name="user_id"/>
            <column name="date"/>
            <column name="chain"/>
        </createIndex>
    </changeSet>

    <changeSet id="2" author="tomas.krason">
        <sql>
            -- Fixes mistake in changeset 1 ETH_MAINNET
            UPDATE portfolio_value_snapshot SET chain = 'EVM_MAINNET' WHERE chain = 'ETH_MAINNET'
        </sql>
    </changeSet>

</databaseChangeLog>