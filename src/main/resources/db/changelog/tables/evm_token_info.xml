<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="tomas-krason">
        <createTable tableName="evm_token_info">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="chain_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="decimals" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="symbol" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="1" author="tomas-krason">
        <createIndex tableName="evm_token_info" indexName="790a093b86e642d18bcf_ui" unique="true">
            <column name="address"/>
            <column name="chain_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="2" author="tomas-krason">
        <addColumn tableName="evm_token_info">
            <column name="image_file_id" type="uuid"/>
        </addColumn>
    </changeSet>

    <changeSet id="3" author="sebastian.jakabcin">
        <addColumn tableName="evm_token_info">
            <column name="chain" type="text" />
        </addColumn>

        <sql>
            UPDATE evm_token_info
            SET chain = 'EVM_MAINNET'
            WHERE chain_id = 1
        </sql>
        <sql>
            UPDATE evm_token_info
            SET chain = 'EVM_BASE'
            WHERE chain_id = 8453
        </sql>

        <addNotNullConstraint tableName="evm_token_info" columnName="chain" />

        <dropIndex tableName="evm_token_info" indexName="790a093b86e642d18bcf_ui" />
        <createIndex tableName="evm_token_info" indexName="790a093b86e642d18bcf_ui" unique="true">
            <column name="address"/>
            <column name="chain"/>
        </createIndex>

        <dropColumn tableName="evm_token_info" columnName="chain_id" />
    </changeSet>

    <changeSet id="4" author="sebastian.jakabcin">
        <delete tableName="evm_token_info">
            <where>chain = 'SOLANA'</where>
        </delete>
    </changeSet>

    <changeSet id="5" author="sebastian.jakabcin">
        <addColumn tableName="evm_token_info">
            <column name="is_token2022" type="bool" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="6" author="sebastian.jakabcin">
        <addColumn tableName="evm_token_info">
            <column name="is_verified" type="bool" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
