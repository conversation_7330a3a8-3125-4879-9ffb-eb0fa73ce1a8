<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="tomas-krason">
        <createTable tableName="market_position">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="token_address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="chain_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="wallet_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="total_token_amount_bought" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="total_token_acquisition_cost_in_wei" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="total_token_amount_sold" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1" author="tomas-krason">
        <createIndex tableName="market_position" indexName="89df1d9ca0094f5ab377_ui" unique="true">
            <column name="token_address"/>
            <column name="wallet_id"/>
            <column name="chain_id"/>
        </createIndex>
        <createIndex tableName="market_position" indexName="2da5ff4943c941ae9b24_ui">
            <column name="wallet_id"/>
            <column name="chain_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="1" author="sebastian.jakabcin">
        <addColumn tableName="market_position">
            <column name="total_token_disposition_cost_in_wei" type="numeric" defaultValue="0" />
        </addColumn>

        <update tableName="market_position">
            <column name="total_token_disposition_cost_in_wei" valueComputed="(total_token_acquisition_cost_in_wei / total_token_amount_bought) * total_token_amount_sold" />
            <where>total_token_amount_bought != 0</where>
        </update>
    </changeSet>

    <changeSet id="2" author="sebastian.jakabcin">
        <update tableName="market_position">
            <column name="total_token_disposition_cost_in_wei" valueComputed="round(total_token_disposition_cost_in_wei)" />
        </update>
    </changeSet>

    <changeSet id="3" author="sebastian.jakabcin">
        <addColumn tableName="market_position">
            <column name="chain" type="text" />
        </addColumn>

        <sql>
            UPDATE market_position
            SET chain = 'EVM_MAINNET'
            WHERE chain_id = 1
        </sql>
        <sql>
            UPDATE market_position
            SET chain = 'EVM_BASE'
            WHERE chain_id = 8453
        </sql>

        <addNotNullConstraint tableName="market_position" columnName="chain" />

        <dropIndex tableName="market_position" indexName="89df1d9ca0094f5ab377_ui" />

        <createIndex tableName="market_position" indexName="89df1d9ca0094f5ab377_ui" unique="true">
            <column name="token_address"/>
            <column name="wallet_id"/>
            <column name="chain"/>
        </createIndex>

        <dropIndex tableName="market_position" indexName="2da5ff4943c941ae9b24_ui" />
        <createIndex tableName="market_position" indexName="2da5ff4943c941ae9b24_ix">
            <column name="wallet_id"/>
            <column name="chain"/>
        </createIndex>

        <dropColumn tableName="market_position" columnName="chain_id" />
    </changeSet>
    <changeSet id="4" author="norbert-bodnar">
        <renameColumn tableName="market_position" oldColumnName="total_token_acquisition_cost_in_wei" newColumnName="total_token_acquisition_cost_base_amount"/>
        <renameColumn tableName="market_position" oldColumnName="total_token_disposition_cost_in_wei" newColumnName="total_token_disposition_cost_base_amount"/>
    </changeSet>

    <changeSet id="5" author="tomas.krason">
        <addColumn tableName="bot_market_position">
            <column name="profit_target_fraction" type="numeric" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="stop_loss_fraction" type="numeric" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="assumed_buy_price" type="numeric" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="real_buy_price" type="numeric" defaultValue="null"/>
            <column name="assumed_sell_price" type="numeric" defaultValue="null"/>
            <column name="real_sell_price" type="numeric" defaultValue="null"/>
        </addColumn>
    </changeSet>

    <changeSet id="6" author="anna.navratilova">
        <addColumn tableName="market_position">
            <column name="total_token_acquisition_cost_usd" type="numeric" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <sql>
            UPDATE
                market_position
            SET
                total_token_acquisition_cost_usd = (total_token_acquisition_cost_base_amount / POWER(10, 18)) * 2520.59
            WHERE chain = 'EVM_MAINNET'
        </sql>
        <sql>
            UPDATE
                market_position
            SET
                total_token_acquisition_cost_usd = (total_token_acquisition_cost_base_amount / POWER(10, 18)) * 2151.79
            WHERE chain = 'EVM_BASE' OR chain = 'EVM_ARBITRUM_ONE'
        </sql>
        <sql>
            UPDATE
                market_position
            SET
                total_token_acquisition_cost_usd = (total_token_acquisition_cost_base_amount / POWER(10, 18)) * 625.54
            WHERE chain = 'EVM_BSC'
        </sql>
        <sql>
            UPDATE
                market_position
            SET
                total_token_acquisition_cost_usd = (total_token_acquisition_cost_base_amount / POWER(10, 9)) * 139.40
            WHERE chain = 'SOLANA'
        </sql>
        <dropDefaultValue tableName="market_position" columnName="total_token_acquisition_cost_usd"/>
    </changeSet>

</databaseChangeLog>
