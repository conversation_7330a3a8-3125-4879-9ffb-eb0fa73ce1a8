<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="anna-navratilova">
        <createTable tableName="bot_leaderboard_snapshot">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="bot_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="profit_value_usd" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="profit_value_fraction" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="balance_usd" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="snapshot_made_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>
