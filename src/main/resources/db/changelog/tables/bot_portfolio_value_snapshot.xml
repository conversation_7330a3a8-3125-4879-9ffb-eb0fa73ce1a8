<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="0" author="anna-navratilova">
        <createTable tableName="bot_portfolio_value_snapshot">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="bot_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="snapshot_made_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="portfolio_value_usd" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="chain" type="text">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="bot_portfolio_value_snapshot" indexName="60c9a0c158594e628ce8_ix" unique="true">
            <column name="bot_id"/>
            <column name="snapshot_made_at"/>
        </createIndex>
    </changeSet>
    <changeSet id="1" author="anna-navratilova">
        <addColumn tableName="bot_portfolio_value_snapshot">
            <column name="acquisition_value_usd" type="numeric" defaultValueNumeric="0.0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>
