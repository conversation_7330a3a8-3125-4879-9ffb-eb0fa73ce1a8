<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1" author="tomas.schmidl">
        <createTable tableName="transaction">
            <column name="id" type="UUID">
                <constraints primaryKey="true"/>
            </column>
            <column name="wallet_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="nonce" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="gas_limit" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="recipient" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="sender" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="eth_amount" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="signed_tx" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="tx_hash" type="text">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="token_address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="max_priority_fee_per_gas" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="max_fee_per_gas" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="chain_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="verification_count" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="transaction" indexName="daa0a0bd8a064b8a9a08_ix">
            <column name="wallet_id"/>
        </createIndex>
        <createIndex tableName="transaction" indexName="9215e9399f0340349aea_ui" unique="true">
            <column name="tx_hash"/>
        </createIndex>
    </changeSet>
    <changeSet id="2" author="tomas.schmidl">
        <createIndex tableName="transaction" indexName="d520085e37694575a061_ix">
            <column name="status"/>
        </createIndex>
    </changeSet>

    <changeSet id="3" author="tomas-krason">
        <addColumn tableName="transaction">
            <column name="amount_in" type="numeric"/>
            <column name="amount_out" type="numeric"/>
            <column name="fatbot_fee" type="numeric"/>
        </addColumn>
    </changeSet>

    <changeSet id="4" author="tomas-krason">
        <addColumn tableName="transaction">
            <column name="exchange_rate_usd" type="numeric"/>
        </addColumn>
    </changeSet>

    <changeSet id="5" author="tomas-schmidl">
        <dropNotNullConstraint tableName="transaction" columnName="token_address"/>
    </changeSet>
    
    <changeSet id="6" author="tomas-krason">
        <createIndex tableName="transaction" indexName="26b09d6930344890821c_ix">
            <column name="token_address"/>
        </createIndex>
    </changeSet>

    <changeSet id="7" author="tomas-schmidl">
        <dropNotNullConstraint tableName="transaction" columnName="nonce"/>
        <dropNotNullConstraint tableName="transaction" columnName="gas_limit"/>
        <dropNotNullConstraint tableName="transaction" columnName="recipient"/>
        <dropNotNullConstraint tableName="transaction" columnName="sender"/>
        <dropNotNullConstraint tableName="transaction" columnName="signed_tx"/>
        <dropNotNullConstraint tableName="transaction" columnName="tx_hash"/>
        <dropNotNullConstraint tableName="transaction" columnName="max_priority_fee_per_gas"/>
        <dropNotNullConstraint tableName="transaction" columnName="max_fee_per_gas"/>

        <dropIndex tableName="transaction" indexName="d520085e37694575a061_ix"/>
        <sql>
            CREATE INDEX "3506e14428c44ce2a4b8_ix" ON transaction (status) WHERE signed_tx IS NOT NULL;
        </sql>
    </changeSet>
    <changeSet id="8" author="norbert-bodnar">
        <sql>
            UPDATE TRANSACTION SET amount_out=eth_amount WHERE type='TRANSFER_ETH' and status='SUCCESS';
        </sql>
        <sql>
            UPDATE TRANSACTION SET exchange_rate_usd = 3110.50 WHERE type='TRANSFER_ETH' and status='SUCCESS' and exchange_rate_usd is null;
        </sql>
    </changeSet>

    <changeSet id="9" author="sebastian.jakabcin">
        <addColumn tableName="transaction">
            <column name="chain" type="text"/>
        </addColumn>

        <sql>
            UPDATE transaction
            SET chain = 'EVM_MAINNET'
            WHERE chain_id = 1
        </sql>

        <sql>
            UPDATE transaction
            SET chain = 'EVM_BASE'
            WHERE chain_id = 8453
        </sql>

        <addNotNullConstraint tableName="transaction" columnName="chain" />
        <dropColumn tableName="transaction" columnName="chain_id" />
    </changeSet>
    <changeSet id="10" author="norbert-bodnar">
        <sql>
            UPDATE transaction SET amount_in=amount_out, amount_out=amount_in WHERE signed_tx is null;
        </sql>

        <renameColumn tableName="transaction" oldColumnName="eth_amount" newColumnName="base_value" />

        <sql>
            UPDATE transaction
            SET amount_out = base_value
            WHERE signed_tx is null and type = 'SELL' and status = 'SUCCESS';
        </sql>
        <sql>
            UPDATE transaction
            SET amount_in = base_value
            WHERE signed_tx is null and type = 'BUY' and status = 'SUCCESS';
        </sql>
    </changeSet>
    <changeSet id="11" author="norbert-bodnar">
        <dropColumn tableName="transaction" columnName="gas_limit"/>
        <dropColumn tableName="transaction" columnName="recipient"/>
        <dropColumn tableName="transaction" columnName="sender"/>
        <dropColumn tableName="transaction" columnName="max_priority_fee_per_gas"/>
        <dropColumn tableName="transaction" columnName="max_fee_per_gas"/>
    </changeSet>
    <changeSet id="12" author="tomas.krason">
        <!-- Used by hot tokens to filter transactions that are in some time range-->
        <createIndex tableName="transaction" indexName="10d25591c838493c9e53_ix">
            <column name="updated_at"/>
        </createIndex>
    </changeSet>
    <changeSet id="13" author="tomas.krason">
        <sql>
            UPDATE transaction
            SET type = 'TRANSFER_CURRENCY'
            WHERE type = 'TRANSFER_ETH'
        </sql>
    </changeSet>
    <changeSet id="14" author="anna.navratilova">
        <addColumn tableName="transaction">
            <column name="fail_reason" type="text"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
