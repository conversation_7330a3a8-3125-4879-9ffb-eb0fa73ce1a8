<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="sebastian.jakabcin">
        <createTable tableName="bot_transaction">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="bot_wallet_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="user_wallet_id" type="uuid" />
            <column name="type" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="signed_tx" type="text" />
            <column name="tx_hash" type="text">
                <constraints nullable="false" />
            </column>
            <column name="nonce" type="bigint" />
            <column name="token_address" type="bytea" />
            <column name="status" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="verification_count" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="exchange_rate_usd" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="base_value" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="amount_in" type="numeric"/>
            <column name="amount_out" type="numeric"/>
            <column name="fatbot_fee" type="numeric"/>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="bot_transaction" indexName="130b7d14183a429cb375_ix">
            <column name="bot_wallet_id" />
        </createIndex>

        <createIndex tableName="bot_transaction" indexName="d39e84ff82ee4f9fa399_ix">
            <column name="user_wallet_id" />
        </createIndex>

    </changeSet>
    <changeSet id="1" author="anna-navratilova">
        <dropNotNullConstraint tableName="bot_transaction" columnName="exchange_rate_usd"/>
    </changeSet>
    <changeSet id="2" author="anna-navratilova">
        <addColumn tableName="bot_transaction">
            <column name="percentage_of" type="numeric"/>
        </addColumn>
    </changeSet>

    <changeSet id="3" author="tomas.krason">
        <createIndex tableName="bot_transaction" indexName="f3213318529b41eea15d_ui" unique="true">
            <column name="tx_hash"/>
        </createIndex>
    </changeSet>

    <changeSet id="4" author="sebastian.jakabcin">
        <dropIndex tableName="bot_transaction" indexName="d39e84ff82ee4f9fa399_ix" />
        <!-- column user_wallet_id dropped in bot_transaction_fk.xml -->
    </changeSet>

    <changeSet id="5" author="tomas.krason">
        <sql>
            CREATE UNIQUE INDEX "7501523ec3ed42bbbf88_ui" ON bot_transaction (id) WHERE status = 'PENDING'
        </sql>
    </changeSet>

</databaseChangeLog>