<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="sebastian.jakabcin">
        <createTable tableName="limit_order">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>

            <column name="user_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="wallet_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="token_address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="chain" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="limit_price" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="remaining_amount" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="filled_amount" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="is_locked" type="bool">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="limit_order" indexName="a6badb8cd87940ff855e_ix">
            <column name="token_address" />
            <column name="chain" />
            <column name="limit_price" />
            <column name="remaining_amount" />
        </createIndex>
    </changeSet>
</databaseChangeLog>
