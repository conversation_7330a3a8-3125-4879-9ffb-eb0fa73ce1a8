<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">


    <changeSet id="0" author="sebastian.jakabcin">
        <createTable tableName="bot">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="user_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="is_active" type="bool">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="avatar_file_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="trade_amount" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="buy_frequency" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="profit_target_fraction" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="stop_loss_fraction" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="market_cap_from_usd" type="numeric"/>
            <column name="market_cap_to_usd" type="numeric"/>
            <column name="liquidity_from_usd" type="numeric"/>
            <column name="liquidity_to_usd" type="numeric"/>
            <column name="daily_volume_from_usd" type="numeric"/>
            <column name="daily_volume_to_usd" type="numeric"/>
            <column name="number_of_holders_from" type="bigint"/>
            <column name="number_of_holders_to" type="bigint"/>
            <column name="buy_count_from" type="bigint"/>
            <column name="buy_count_to" type="bigint"/>
            <column name="sell_count_from" type="bigint"/>
            <column name="sell_count_to" type="bigint"/>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="bot" indexName="7f863c3a004741598cd0_ix">
            <column name="user_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1" author="anna-navratilova">
        <addColumn tableName="bot">
            <column name="remaining_buy_frequency" type="bigint"/>
        </addColumn>
        <addColumn tableName="bot">
            <column name="buy_frequency_last_reset_at" type="timestamptz"/>
        </addColumn>
        <sql>
            UPDATE
                bot
            SET remaining_buy_frequency     = buy_frequency,
                buy_frequency_last_reset_at = created_at
            WHERE remaining_buy_frequency IS NULL
               OR buy_frequency_last_reset_at IS NULL
        </sql>
        <addNotNullConstraint tableName="bot" columnName="remaining_buy_frequency"/>
        <addNotNullConstraint tableName="bot" columnName="buy_frequency_last_reset_at"/>
    </changeSet>
    <changeSet id="2" author="anna-navratilova">
        <dropColumn tableName="bot" columnName="buy_count_from"/>
        <dropColumn tableName="bot" columnName="buy_count_to"/>
        <dropColumn tableName="bot" columnName="sell_count_from"/>
        <dropColumn tableName="bot" columnName="sell_count_to"/>
        <addColumn tableName="bot">
            <column name="buy_volume" type="numeric"/>
        </addColumn>
        <addColumn tableName="bot">
            <column name="sell_volume" type="numeric"/>
        </addColumn>
    </changeSet>

    <changeSet id="3" author="tomas.krason">
        <addColumn tableName="bot">
            <column name="buy_tokens_alive_at_least_for" type="interval"/>
        </addColumn>
    </changeSet>

    <changeSet id="4" author="tomas.krason">
        <addColumn tableName="bot">
            <column name="sell_transaction_fraction_threshold" type="numeric"/>
        </addColumn>
    </changeSet>

    <changeSet id="5" author="anna.navratilova">
        <addColumn tableName="bot">
            <column name="token_ticker_copy_is_checked" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot">
            <column name="creator_high_buy_is_checked" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot">
            <column name="bundled_buys_detected_is_checked" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot">
            <column name="suspicious_wallets_detected_is_checked" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot">
            <column name="should_auto_sell_after_hold_time" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <dropDefaultValue tableName="bot" columnName="token_ticker_copy_is_checked"/>
        <dropDefaultValue tableName="bot" columnName="creator_high_buy_is_checked"/>
        <dropDefaultValue tableName="bot" columnName="bundled_buys_detected_is_checked"/>
        <dropDefaultValue tableName="bot" columnName="suspicious_wallets_detected_is_checked"/>
        <dropDefaultValue tableName="bot" columnName="should_auto_sell_after_hold_time"/>
    </changeSet>

    <changeSet id="6" author="tomas.krason">
        <dropColumn tableName="bot">
            <column name="sell_transaction_fraction_threshold"/>
        </dropColumn>

        <addColumn tableName="bot">
            <column name="sell_transaction_fraction" type="numeric"/>
            <column name="buy_transaction_fraction" type="numeric"/>
            <column name="sell_to_buy_transaction_ratio" type="numeric"/>
        </addColumn>
    </changeSet>

    <changeSet id="7" author="tomas.krason">
        <addColumn tableName="bot">
            <column name="single_high_buy_is_checked" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <dropDefaultValue tableName="bot" columnName="single_high_buy_is_checked"/>
    </changeSet>

    <changeSet id="8" author="tomas.krason">
        <addColumn tableName="bot">
            <column name="follower_bot_id" type="uuid"/>
        </addColumn>
    </changeSet>

    <changeSet id="9" author="anna.navratilova">
        <addColumn tableName="bot">
            <column name="number_of_active_days" type="bigint" defaultValue="1">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="10" author="tomas.krason">
        <addColumn tableName="bot">
            <column name="creator_graduation_success_rate_fraction" type="numeric"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
