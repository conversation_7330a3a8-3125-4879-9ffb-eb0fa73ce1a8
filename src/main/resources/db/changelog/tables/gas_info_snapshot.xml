<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="sebastian.jakabcin">
        <createTable tableName="gas_info_snapshot">
            <column name="id" type="UUID">
                <constraints primaryKey="true"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false" />
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false" />
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="chain_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="max_priority_fee_per_gas" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="max_fee_per_gas" type="numeric">
                <constraints nullable="false"/>
            </column>

        </createTable>

        <createIndex tableName="gas_info_snapshot" indexName="348cbdb99755414d9ac2_ui" unique="true">
            <column name="chain_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>