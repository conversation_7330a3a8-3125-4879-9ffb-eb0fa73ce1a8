<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="tomas-krason">
        <createTable tableName="firebase_user">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="email" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1" author="tomas-krason">
        <createIndex tableName="firebase_user" indexName="8640efc68a9a4c1399d4_ix" unique="true">
            <column name="email"/>
        </createIndex>
    </changeSet>
    <changeSet id="2" author="norbert-bodnar">
        <addColumn tableName="firebase_user">
            <column name="referral_code" type="text">
                <constraints nullable="true"/>
            </column>
            <column name="referred_by_user_id" type="uuid">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="3" author="norbert-bodnar">
        <sql>
            CREATE UNIQUE INDEX "6fd3576ebff04dbf9fa2_ix" ON firebase_user (referral_code) WHERE referral_code IS NOT NULL;
        </sql>
    </changeSet>
    <changeSet id="4" author="norbert-bodnar">
        <addColumn tableName="firebase_user">
            <column name="quick_buy_amount_usd" type="numeric" defaultValue="10">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="5" author="anna-navratilova">
        <addColumn tableName="firebase_user">
            <column name="selected_chains" type="text[]" defaultValueComputed="'{EVM_MAINNET}'::text[]">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>
