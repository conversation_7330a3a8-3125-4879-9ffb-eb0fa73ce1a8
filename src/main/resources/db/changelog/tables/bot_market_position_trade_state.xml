<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="anna-navratilova">
        <createTable tableName="bot_market_position_trade_state">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="bot_market_position_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="market_cap_usd" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="liquidity_usd" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="volume_usd" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="num_of_account_holders" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="buy_volume" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="sell_volume" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="fraction_of_sell_transactions" type="numeric">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="bot_market_position_trade_state" indexName="2a4a9bb90fcb4ad98913_ux" unique="true">
            <column name="bot_market_position_id"/>
            <column name="type"/>
        </createIndex>
        <createIndex tableName="bot_market_position_trade_state" indexName="e89d1a3b90ec4415a137_ix">
            <column name="bot_market_position_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
