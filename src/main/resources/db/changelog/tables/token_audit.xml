<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="norbert-bodnar">
        <createTable tableName="token_audit">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="token_address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="chain" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="audit_result" type="json"/>
            <column name="processing_started_at" type="timestamptz"/>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="state" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="token_audit" indexName="475f9ba4d24a439cab62_ui" unique="true">
            <column name="token_address"/>
            <column name="chain"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>