<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="sebastian.jakabcin">
        <createTable tableName="simulated_token">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="chain_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="token_address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="wallet_address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="simulated_token" indexName="2b7517e5592a47a19701_ui" unique="true">
            <column name="chain_id"/>
            <column name="token_address"/>
            <column name="wallet_address"/>
        </createIndex>
    </changeSet>

    <changeSet id="1" author="sebastian.jakabcin">
        <addColumn tableName="simulated_token">
            <column name="token_amount" type="numeric" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="2" author="sebastian.jakabcin">
        <delete tableName="simulated_token" />
    </changeSet>
</databaseChangeLog>