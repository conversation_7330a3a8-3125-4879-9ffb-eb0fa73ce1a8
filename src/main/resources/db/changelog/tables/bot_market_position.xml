<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="anna-navratilova">
        <createTable tableName="bot_market_position">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="token_address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="chain" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="bot_wallet_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="total_token_amount_bought" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="total_token_amount_sold" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="total_token_acquisition_cost_base_amount" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="total_token_disposition_cost_base_amount" type="numeric">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1" author="anna-navratilova">
        <createIndex tableName="bot_market_position" indexName="f85fd2cf00164d28aa4c_ui" unique="true">
            <column name="token_address"/>
            <column name="bot_wallet_id"/>
            <column name="chain"/>
        </createIndex>
        <createIndex tableName="bot_market_position" indexName="ac0b84873c2b4dad8b41_ix">
            <column name="bot_wallet_id"/>
            <column name="chain"/>
        </createIndex>
    </changeSet>
    <changeSet id="2" author="anna-navratilova">
        <addColumn tableName="bot_market_position">
            <column name="sell_profit_price" type="numeric">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot_market_position">
            <column name="stop_loss_price" type="numeric">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot_market_position">
            <column name="state" type="text" defaultValue="'OPENED'">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <dropDefaultValue tableName="bot_market_position" columnName="state"/>
    </changeSet>
    <changeSet id="3" author="anna-navratilova">
        <addColumn tableName="bot_market_position">
            <column name="block_height" type="int">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="4" author="tomas.krason">
        <renameColumn tableName="bot_market_position" oldColumnName="block_height" newColumnName="block_slot"/>
    </changeSet>

    <changeSet id="5" author="tomas.krason">
        <addColumn tableName="bot_market_position">
            <column name="buy_amount_frozen" type="numeric" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="fee_to_sell_frozen" type="numeric" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="6" author="tomas.krason">
        <addColumn tableName="bot_market_position">
            <column name="dex_screener_debug_chart_url" type="text" defaultValue="">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="7" author="anna-navratilova">
        <addColumn tableName="bot_market_position">
            <column name="position_closed_at" type="timestamptz"/>
        </addColumn>
        <addColumn tableName="bot_market_position">
            <column name="buy_exchange_rate" type="numeric"/>
        </addColumn>
        <addColumn tableName="bot_market_position">
            <column name="sell_exchange_rate" type="numeric"/>
        </addColumn>
    </changeSet>

    <changeSet id="7" author="tomas.krason">
        <sql>
            CREATE INDEX "3eed520ed96d46f89eda_ix" ON bot_market_position (created_at)
        </sql>
    </changeSet>

    <changeSet id="8" author="anna.navratilova">
        <addColumn tableName="bot_market_position">
            <column name="retry_count" type="int" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <dropDefaultValue tableName="bot_market_position" columnName="retry_count"/>
    </changeSet>

    <changeSet id="9" author="tomas.krason">
        <addColumn tableName="bot_market_position">
            <column name="sell_reason" type="text"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
