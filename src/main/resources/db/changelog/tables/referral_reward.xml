<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="norbert-bodnar">
        <createTable tableName="referral_reward">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="tx_hash" type="text">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="wei_amount" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="claimed" type="boolean">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="referral_reward" indexName="5a54ca22201c45569e3b_ix">
            <column name="user_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1" author="norbert-bodnar">
        <addColumn tableName="referral_reward">
            <column name="chain_type" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="chain_id" type="bigint"/>
        </addColumn>
        <dropIndex tableName="referral_reward" indexName="5a54ca22201c45569e3b_ix"/>
        <createIndex tableName="referral_reward" indexName="85b6626ffff74d828c5f_ix">
            <column name="user_id"/>
            <column name="chain_type"/>
            <column name="chain_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="2" author="sebastian.jakabcin">
        <addColumn tableName="referral_reward">
            <column name="chain" type="text"/>
        </addColumn>

        <sql>
            UPDATE referral_reward
            SET chain = 'EVM_MAINNET'
            WHERE chain_type = 'EVM' and chain_id = 1
        </sql>

        <addNotNullConstraint tableName="referral_reward" columnName="chain" />

        <dropIndex tableName="referral_reward" indexName="85b6626ffff74d828c5f_ix" />
        <createIndex tableName="referral_reward" indexName="85b6626ffff74d828c5f_ix">
            <column name="user_id"/>
            <column name="chain"/>
        </createIndex>

        <dropColumn tableName="referral_reward" columnName="chain_id" />
        <dropColumn tableName="referral_reward" columnName="chain_type" />
    </changeSet>

    <changeSet id="3" author="tomas.krason">
        <renameColumn tableName="referral_reward" oldColumnName="wei_amount" newColumnName="base_amount"/>
    </changeSet>

</databaseChangeLog>