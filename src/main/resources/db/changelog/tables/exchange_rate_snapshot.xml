<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="sebastian.jakabcin">
        <createTable tableName="exchange_rate_snapshot">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="currency" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="exchange_rate_usd" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="valid_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex tableName="exchange_rate_snapshot" indexName="c1b6606ab5964c26ba45_ix">
            <column name="valid_at"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>