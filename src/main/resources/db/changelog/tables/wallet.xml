<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="tomas-krason">
        <createTable tableName="wallet">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="user_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="private_key" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="chain_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="wallet" indexName="a3b81e09fe0b4fc0ad5a_ix">
            <column name="user_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1" author="tomas-krason">
        <createIndex tableName="wallet" indexName="13860742f4364e5391f7_ui" unique="true">
            <column name="user_id"/>
            <column name="address"/>
        </createIndex>
    </changeSet>
    <changeSet id="2" author="tomas-schmidl">
        <addColumn tableName="wallet">
            <column name="chain_type" type="text" defaultValue="'EVM'">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <createIndex tableName="wallet" indexName="41814641a84c4880a98e_ui" unique="true">
            <column name="chain_id"/>
            <column name="address"/>
        </createIndex>
        <createIndex tableName="wallet" indexName="be764830e65a4a208982_ix">
            <column name="chain_type"/>
            <column name="address"/>
        </createIndex>
    </changeSet>
    <changeSet id="3" author="tomas-schmidl">
        <dropDefaultValue tableName="wallet" columnName="chain_type"/>
    </changeSet>
    <changeSet id="4" author="tomas-krason">
        <dropColumn tableName="wallet">
            <column name="chain_id"/>
        </dropColumn>
    </changeSet>
    <changeSet id="5" author="tomas-schmidl">
        <addColumn tableName="wallet">
            <column name="custom_name" type="text"/>
            <column name="anti_mev" type="boolean"/>
            <column name="anti_rug" type="boolean"/>
        </addColumn>
    </changeSet>
    <changeSet id="6" author="tomas-krason">
        <addColumn tableName="wallet">
            <column name="sell_anti_mev_protection" type="boolean"/>
        </addColumn>
        <renameColumn tableName="wallet" oldColumnName="anti_mev" newColumnName="buy_anti_mev_protection"/>
    </changeSet>
    <changeSet id="7" author="norbert-bodnar">
        <addColumn tableName="wallet">
            <column name="chain_id" type="int"/>
        </addColumn>
    </changeSet>
    <changeSet id="8" author="norbert-bodnar">
        <modifyDataType tableName="wallet" columnName="chain_id" newDataType="bigint"/>
    </changeSet>
    <changeSet id="8" author="tomas-krason">
        <addColumn tableName="wallet">
            <column name="total_native_coin_bought" type="numeric" defaultValue="0"/>
            <column name="total_native_coin_acquisition_price_usd" type="numeric" defaultValue="0"/>
            <column name="total_native_coin_sold" type="numeric" defaultValue="0"/>
        </addColumn>
    </changeSet>

    <changeSet id="9" author="sebastian.jakabcin">
        <dropColumn tableName="wallet">
            <column name="total_native_coin_bought" />
            <column name="total_native_coin_acquisition_price_usd" />
            <column name="total_native_coin_sold"/>
        </dropColumn>
    </changeSet>

    <changeSet id="10" author="tomas.krason">
        <sql>
            UPDATE wallet
            SET buy_anti_mev_protection = true, sell_anti_mev_protection = true, anti_rug = true
            WHERE chain_type = 'EVM'
        </sql>
    </changeSet>

    <changeSet id="11" author="tomas.krason">
        <dropIndex tableName="wallet" indexName="13860742f4364e5391f7_ui"/>
        <createIndex tableName="wallet" indexName="13860742f4364e5391f7_ui" unique="true">
            <column name="user_id"/>
            <column name="address"/>
            <column name="chain_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="12" author="sebastian.jakabcin">
        <addColumn tableName="wallet">
            <column name="chain" type="text" />
        </addColumn>
        <sql>
            UPDATE wallet
            SET chain = 'SOLANA'
            WHERE chain_type = 'SOLANA'
        </sql>
        <sql>
            UPDATE wallet
            SET chain = 'EVM_MAINNET'
            WHERE chain_type = 'EVM' and chain_id = 1
        </sql>
        <sql>
            UPDATE wallet
            SET chain = 'EVM_BASE'
            WHERE chain_type = 'EVM' and chain_id = 8453
        </sql>

        <addNotNullConstraint tableName="wallet" columnName="chain" />

        <dropIndex tableName="wallet" indexName="13860742f4364e5391f7_ui"/>
        <createIndex tableName="wallet" indexName="13860742f4364e5391f7_ui" unique="true">
            <column name="user_id"/>
            <column name="address"/>
            <column name="chain"/>
        </createIndex>


        <dropIndex tableName="wallet" indexName="be764830e65a4a208982_ix" />
        <dropColumn tableName="wallet" columnName="chain_id" />
        <dropColumn tableName="wallet" columnName="chain_type" />
    </changeSet>

    <changeSet id="13" author="tomas.krason">
        <addColumn tableName="wallet">
            <column name="is_default" type="boolean" defaultValue="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>

        <sql>
            UPDATE wallet
            SET is_default = true
            WHERE id IN (SELECT min(id::text)::uuid as oldest_wallet_id
                         FROM wallet
                         GROUP BY user_id, chain)
        </sql>
    </changeSet>

    <changeSet id="14" author="tomas.krason">
        <sql>
            UPDATE wallet
            SET buy_anti_mev_protection = false, sell_anti_mev_protection = false, anti_rug = false
            WHERE chain = 'SOLANA'
        </sql>

        <dropColumn tableName="wallet">
            <column name="anti_rug"/>
        </dropColumn>
    </changeSet>

</databaseChangeLog>
