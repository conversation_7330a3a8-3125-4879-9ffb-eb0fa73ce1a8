<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="0" author="anna-navratilova">
        <createTable tableName="bot_market_position_snapshot">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="bot_wallet_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="chain" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="token_address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="total_token_amount_bought" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="total_token_amount_sold" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="total_token_acquisition_cost_base_amount" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="total_token_disposition_cost_base_amount" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="snapshot_made_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1" author="anna-navratilova">
        <createIndex tableName="bot_market_position_snapshot" indexName="31d1cce4f2964e728cbb_ix">
            <column name="snapshot_made_at"/>
            <column name="bot_wallet_id"/>
            <column name="chain"/>
        </createIndex>
    </changeSet>
    <changeSet id="2" author="anna-navratilova">
        <addColumn tableName="bot_market_position_snapshot">
            <column name="sell_profit_price" type="numeric">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot_market_position_snapshot">
            <column name="stop_loss_price" type="numeric">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addColumn tableName="bot_market_position_snapshot">
            <column name="state" type="text" defaultValue="'OPENED'">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <dropDefaultValue tableName="bot_market_position_snapshot" columnName="state"/>
    </changeSet>
    <changeSet id="3" author="anna-navratilova">
        <addColumn tableName="bot_market_position_snapshot">
            <column name="block_height" type="int">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="4" author="tomas.krason">
        <renameColumn tableName="bot_market_position_snapshot" oldColumnName="block_height" newColumnName="block_slot"/>
    </changeSet>

    <!-- THIS TABLE IS DELETED! -->

</databaseChangeLog>
