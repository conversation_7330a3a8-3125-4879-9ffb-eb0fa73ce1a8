<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="0" author="anna-navratilova">
        <createTable tableName="bot_token_info">
            <column name="id" type="uuid">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="bytea">
                <constraints nullable="false"/>
            </column>
            <column name="chain" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="decimals" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="symbol" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="image_file_id" type="uuid">
                <constraints nullable="true"/>
            </column>
            <column name="is_token2022" type="boolean">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="bot_token_info" unique="true" indexName="68938bf5cc094be58d3f_ui">
            <column name="address"/>
            <column name="chain"/>
        </createIndex>
    </changeSet>

    <changeSet id="1" author="sebastian.jakabcin">
        <addColumn tableName="bot_token_info">
            <column name="graduated_to_pair_address" type="bytea" />
        </addColumn>
    </changeSet>

    <changeSet id="2" author="tomas.krason">
        <addColumn tableName="bot_token_info">
            <!-- Default is here, so we don't have to deal with nulls. All positions will be closed in DB-->
            <!-- manually, so no position will be actually requesting this default value -->
            <column name="creator_address" type="bytea" defaultValue="\x11111111111111111111111111111111"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
