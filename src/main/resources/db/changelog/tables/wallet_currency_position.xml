<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="0" author="sebastian.jakabcin">
        <createTable tableName="wallet_currency_position">
            <column name="id" type="uuid">
                <constraints primaryKey="true"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>

            <column name="wallet_id" type="uuid">
                <constraints nullable="false"/>
            </column>
            <column name="total_bought" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="total_acquisition_cost_usd" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="total_sold" type="numeric">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <createIndex tableName="wallet_currency_position" indexName="2accd219dcc9413b8b07_ix">
            <column name="wallet_id" />
        </createIndex>
    </changeSet>
</databaseChangeLog>