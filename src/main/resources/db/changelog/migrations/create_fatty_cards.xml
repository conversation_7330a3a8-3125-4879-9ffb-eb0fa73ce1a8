<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="create-user-statistics-for-existing-users" author="jana-farova" runOnChange="false" runAlways="false">
        <comment>Create default Fatty cards records</comment>

        <!-- Create file for avatar id -->
        <insert tableName="file">
            <column name="id" value="0195faac-29de-76d2-bd41-f4ab4d4693ed"/>
            <column name="extension" value="png"/>
            <column name="created_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="updated_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="version" value="0"/>
        </insert>

        <!-- Create fatty cards-->
        <insert tableName="fatty_card">
            <column name="id" value="0195faad-0260-7ec0-b31f-560947d94ee0"/>
            <column name="avatar_file_id" value="0195faac-29de-76d2-bd41-f4ab4d4693ed"/>
            <column name="rarity" value="Common"/>
            <column name="probability" value="50"/>
            <column name="donut_reward" value="100"/>
            <column name="created_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="updated_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="version" value="0"/>
        </insert>

        <insert tableName="fatty_card">
            <column name="id" value="0195faac-6ca4-7439-9377-e4107341e4f6"/>
            <column name="avatar_file_id" value="0195faac-29de-76d2-bd41-f4ab4d4693ed"/>
            <column name="rarity" value="Uncommon"/>
            <column name="probability" value="35"/>
            <column name="donut_reward" value="300"/>
            <column name="created_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="updated_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="version" value="0"/>
        </insert>

        <insert tableName="fatty_card">
            <column name="id" value="0195faac-8a2e-752a-9061-874bb0d4334e"/>
            <column name="avatar_file_id" value="0195faac-29de-76d2-bd41-f4ab4d4693ed"/>
            <column name="rarity" value="Rare"/>
            <column name="probability" value="12"/>
            <column name="donut_reward" value="1000"/>
            <column name="created_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="updated_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="version" value="0"/>
        </insert>

        <insert tableName="fatty_card">
            <column name="id" value="0195faac-a8f1-7447-9366-3c23a163d971"/>
            <column name="avatar_file_id" value="0195faac-29de-76d2-bd41-f4ab4d4693ed"/>
            <column name="rarity" value="Legendary"/>
            <column name="probability" value="2.7"/>
            <column name="donut_reward" value="5000"/>
            <column name="created_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="updated_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="version" value="0"/>
        </insert>

        <insert tableName="fatty_card">
            <column name="id" value="0195faac-d910-7833-a0de-80b3ddc1167b"/>
            <column name="avatar_file_id" value="0195faac-29de-76d2-bd41-f4ab4d4693ed"/>
            <column name="rarity" value="Mythical"/>
            <column name="probability" value="0.29"/>
            <column name="donut_reward" value="20000"/>
            <column name="created_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="updated_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="version" value="0"/>
        </insert>

        <insert tableName="fatty_card">
            <column name="id" value="0195fb34-251c-7127-990c-d90372a55db4"/>
            <column name="avatar_file_id" value="0195faac-29de-76d2-bd41-f4ab4d4693ed"/>
            <column name="rarity" value="Epic"/>
            <column name="probability" value="0.01"/>
            <column name="donut_reward" value="200000"/>
            <column name="created_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="updated_at" valueComputed="CURRENT_TIMESTAMP"/>
            <column name="version" value="0"/>
        </insert>
    </changeSet>
</databaseChangeLog>
