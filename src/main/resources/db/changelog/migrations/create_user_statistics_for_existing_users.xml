<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="create-user-statistics-for-existing-users" author="jana-farova" runOnChange="true" runAlways="false">
        <comment>
            Create UserStatistics records for all existing FirebaseUser records that don't have one yet

            NOTE: This migration is commented out as it was run once on every environment and after that it got changed
            breaking the checksum. It's no longer needed.
        </comment>
        <!-- Use a custom SQL query to insert records -->
<!--        <sql>-->
<!--            &#45;&#45; Insert UserStatistics for each FirebaseUser that doesn't already have a record-->
<!--            INSERT INTO user_statistics (-->
<!--                id, -->
<!--                user_id, -->
<!--                leaderboard_donuts,-->
<!--                league_donuts,-->
<!--                donuts_gained_cumulative, -->
<!--                created_at, -->
<!--                updated_at, -->
<!--                version-->
<!--            )-->
<!--            SELECT-->
<!--                gen_random_uuid(),-->
<!--                fu.id as user_id,-->
<!--                0 as leaderboard_donuts,-->
<!--                0 as league_donuts,-->
<!--                0 as donuts_gained_cumulative,-->
<!--                now() as created_at,-->
<!--                now() as updated_at,-->
<!--                0 as version-->
<!--            FROM -->
<!--                firebase_user fu-->
<!--        </sql>-->
    </changeSet>
</databaseChangeLog>
