sentry:
  environment: devel
  traces-sample-rate: 0.1

springdoc:
  swagger-server: https://api.fatbot.devel.cleevio.dev

fatbot:
  proxy:
    base-url: http://fatbot-bitquery-proxy-api-cluster-ip-service.fatbot-bitquery-proxy-api-devel.svc.cluster.local:8080
  anti-mev:
    force-use: false
  file-storage-url: https://cdn.fatbot.devel.cleevio.dev
  svm:
    solana:
      warm-up-helius-staked-node-cache: true

integration:
  titanbuilder:
    api-key: definitely-real-api-key # we use fake api key here, as titanbuilder is on main net anyway
  fatbot-token-aggregator:
    base-url: http://fatbot-token-aggregator-devel-cluster-ip-service.fatbot-token-aggregator-devel.svc.cluster.local:8080
  fatbot-messaging-proxy:
    base-url: http://fatbot-messaging-proxy-api-cluster-ip-service.fatbot-messaging-proxy-api-devel.svc.cluster.local:8080
