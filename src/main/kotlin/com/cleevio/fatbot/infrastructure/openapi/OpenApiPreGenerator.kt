package com.cleevio.fatbot.infrastructure.openapi

import com.cleevio.fatbot.infrastructure.config.logger
import org.springdoc.webmvc.api.MultipleOpenApiWebMvcResource
import org.springdoc.webmvc.api.OpenApiResource
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.ApplicationListener
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component

@Component
@Profile("!test")
class OpenApiPreGenerator(
	private val multipleOpenApiWebMvcResource: MultipleOpenApiWebMvcResource,
) : ApplicationListener<ApplicationReadyEvent> {

	private val logger = logger()

	/**
	 * Pre generates OpenAPI docs after application startup, so they are already cached when API call comes.
	 *
	 * Using reflection to force-call the same methods, that are being called when API calls are resolved.
	 */
	@Async
	@Suppress("UNCHECKED_CAST")
	override fun onApplicationEvent(event: ApplicationReadyEvent) {
		val field = multipleOpenApiWebMvcResource.javaClass.superclass.getDeclaredField("groupedOpenApiResources")
		field.isAccessible = true
		val apis = field.get(multipleOpenApiWebMvcResource) as Map<String, OpenApiResource>

		apis.forEach { (name, resource) ->
			logger.info("Going to pre-generate openapi $name")
			val method = resource.javaClass.superclass.superclass.getDeclaredMethod("getOpenApi")
			method.isAccessible = true
			method.invoke(resource)
		}
	}
}
