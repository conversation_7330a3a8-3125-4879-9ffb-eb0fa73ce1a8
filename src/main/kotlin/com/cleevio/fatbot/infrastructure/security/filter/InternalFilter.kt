package com.cleevio.fatbot.infrastructure.security.filter

import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.http.HttpHeaders
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.web.util.matcher.AntPathRequestMatcher
import org.springframework.security.web.util.matcher.RequestMatcher
import org.springframework.web.filter.OncePerRequestFilter
import java.util.UUID

class InternalFilter(
	pattern: String,
	private val apiKey: UUID,
) : OncePerRequestFilter() {

	private val matcher: RequestMatcher = AntPathRequestMatcher(pattern)

	// do not apply filter on non-matching requests
	override fun shouldNotFilter(request: HttpServletRequest): Boolean = !matcher.matches(request)

	override fun doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, filterChain: FilterChain) {
		val authHeaderValue = request.getHeader(HttpHeaders.AUTHORIZATION)
		when (authHeaderValue) {
			null -> {
				response.sendError(HttpServletResponse.SC_FORBIDDEN)
				return
			}
			apiKey.toString() -> {
				SecurityContextHolder.getContext().authentication = UsernamePasswordAuthenticationToken("internal", null, null)
				filterChain.doFilter(request, response)
			}
			else -> {
				response.sendError(HttpServletResponse.SC_FORBIDDEN)
				return
			}
		}
	}
}
