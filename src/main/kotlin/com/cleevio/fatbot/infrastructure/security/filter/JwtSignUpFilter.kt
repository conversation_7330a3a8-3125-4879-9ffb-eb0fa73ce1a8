package com.cleevio.fatbot.infrastructure.security.filter

import com.cleevio.fatbot.application.module.access.port.out.ParseToken
import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.http.HttpStatus
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.web.util.matcher.AntPathRequestMatcher
import org.springframework.security.web.util.matcher.RequestMatcher
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.filter.OncePerRequestFilter

class JwtSignUpFilter(
	private val parseToken: ParseToken,
	pattern: String,
) : OncePerRequestFilter() {
	private val matcher: RequestMatcher = AntPathRequestMatcher(pattern)

	override fun shouldNotFilter(request: HttpServletRequest): Boolean = !matcher.matches(request)

	override fun doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, filterChain: FilterChain) =
		getAuthentication(request)
			?.let { SecurityContextHolder.getContext().authentication = it }
			.run { filterChain.doFilter(request, response) }

	private fun getAuthentication(request: HttpServletRequest) = runCatching {
		getTokenFromRequest(request)
			.let { token -> parseToken(token = token).email }
			.let { email -> UsernamePasswordAuthenticationToken(email, null, null) }
	}.getOrNull()
}

internal fun getTokenFromRequest(request: HttpServletRequest): String {
	val tokenPrefix = "Bearer "

	return request.getHeader("Authorization")?.let {
		if (it.startsWith(tokenPrefix)) it else null
	}?.removePrefix(tokenPrefix) ?: throw InvalidTokenException()
}

@ResponseStatus(HttpStatus.UNAUTHORIZED)
class InvalidTokenException : FatbotApiException(
	reason = ExtendedErrorReasonType.INVALID_TOKEN,
	message = "Invalid or missing Authorization header with proper Bearer token",
)
