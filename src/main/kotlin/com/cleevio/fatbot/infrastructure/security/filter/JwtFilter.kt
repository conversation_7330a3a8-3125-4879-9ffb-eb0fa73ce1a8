package com.cleevio.fatbot.infrastructure.security.filter

import com.cleevio.fatbot.application.module.access.port.out.ParseToken
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.filter.OncePerRequestFilter

class JwtFilter(
	private val parseToken: ParseToken,
	private val firebaseUserFinderService: FirebaseUserFinderService,
) : OncePerRequestFilter() {
	// we do not need this filter if auth is set from JwtSignUpFilter or Internal filter
	override fun shouldNotFilter(request: HttpServletRequest): Bo<PERSON>an =
		SecurityContextHolder.getContext().authentication != null

	override fun doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, filterChain: Filter<PERSON>hain) =
		getAuthentication(request)
			?.let { SecurityContextHolder.getContext().authentication = it }
			.run { filterChain.doFilter(request, response) }

	private fun getAuthentication(request: HttpServletRequest) = runCatching {
		getTokenFromRequest(request)
			.let { token -> parseToken(token = token).email }
			.let { email -> firebaseUserFinderService.getByEmailIgnoringCase(email) }
			.let { user -> UsernamePasswordAuthenticationToken(user.id, null, null) }
	}.getOrNull()
}
