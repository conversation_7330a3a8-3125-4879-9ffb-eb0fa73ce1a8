package com.cleevio.fatbot.infrastructure.middleware

import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.query.QueryHandler
import jakarta.validation.Valid
import org.springframework.stereotype.Component
import org.springframework.validation.annotation.Validated
import kotlin.reflect.KClass

@Component
@Validated
class QueryBusMiddleware(queryHandlers: List<QueryHandler<*, *>>) : QueryBus {
	private val handlers: MutableMap<String, QueryHandler<*, Query<*>>> = mutableMapOf()

	init {
		queryHandlers.forEach {
			if (handlers.containsKey(it.query.queryName())) {
				throw Exception("Multiple handlers for single query ${it.query.queryName()}")
			}
			@Suppress("UNCHECKED_CAST")
			handlers[it.query.queryName()] = it as Query<PERSON>andler<*, Query<*>>
		}
	}

	override fun <R> invoke(@Valid query: Query<R>): R {
		val queryName = query::class.queryName()
		if (!handlers.containsKey(queryName)) throw Exception("No handler for query $queryName")
		@Suppress("UNCHECKED_CAST")
		return handlers[queryName]!!.handle(query) as R
	}
}

private fun <T : Any> KClass<T>.queryName(): String = toString()
