package com.cleevio.fatbot.infrastructure.config

import io.sentry.Sentry
import io.sentry.spring.jakarta.SentryTaskDecorator
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler
import org.springframework.aop.interceptor.SimpleAsyncUncaughtExceptionHandler
import org.springframework.boot.autoconfigure.task.TaskExecutionProperties
import org.springframework.boot.task.ThreadPoolTaskExecutorCustomizer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.AsyncConfigurer
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import java.lang.reflect.Method

@Configuration
@EnableAsync
@Profile("!test")
class AsyncConfig(
	private val taskExecutionProperties: TaskExecutionProperties,
) : AsyncConfigurer {

	@Bean
	override fun getAsyncExecutor() = ThreadPoolTaskExecutor().apply {
		corePoolSize = taskExecutionProperties.pool.coreSize
		maxPoolSize = taskExecutionProperties.pool.maxSize
		queueCapacity = taskExecutionProperties.pool.queueCapacity
		setThreadNamePrefix(taskExecutionProperties.threadNamePrefix)
	}

	@Bean
	fun customizeTaskExecutor() = ThreadPoolTaskExecutorCustomizer {
		it.setTaskDecorator(SentryTaskDecorator())
	}

	override fun getAsyncUncaughtExceptionHandler(): AsyncUncaughtExceptionHandler =
		SentrySimpleAsyncUncaughtExceptionHandler
}

private object SentrySimpleAsyncUncaughtExceptionHandler : SimpleAsyncUncaughtExceptionHandler() {
	override fun handleUncaughtException(ex: Throwable, method: Method, vararg params: Any?) {
		super.handleUncaughtException(ex, method, *params)
		Sentry.captureException(ex)
	}
}
