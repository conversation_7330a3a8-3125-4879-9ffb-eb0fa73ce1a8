package com.cleevio.fatbot.infrastructure.config

import com.fasterxml.jackson.databind.ObjectMapper
import org.hibernate.cfg.AvailableSettings
import org.hibernate.type.format.jackson.JacksonJsonFormatMapper
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class HibernateConfig {

	@Bean
	fun jsonFormatMapperCustomizer(objectMapper: ObjectMapper): HibernatePropertiesCustomizer {
		return HibernatePropertiesCustomizer { properties ->
			properties[AvailableSettings.JSON_FORMAT_MAPPER] = Jackson<PERSON>sonFormatMapper(objectMapper)
		}
	}
}
