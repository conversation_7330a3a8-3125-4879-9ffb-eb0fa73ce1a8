package com.cleevio.fatbot.infrastructure.config.properties

import jakarta.validation.constraints.NotBlank
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.validation.annotation.Validated

@ConfigurationProperties(prefix = "integration.titanbuilder")
@Validated
data class TitanbuilderProperties(
	@field:NotBlank val baseUrl: String,
	@field:NotBlank val apiKey: String,
)
