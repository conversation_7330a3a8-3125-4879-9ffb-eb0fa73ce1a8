package com.cleevio.fatbot.infrastructure.config.graphql
import com.apollographql.apollo3.api.Adapter
import com.apollographql.apollo3.api.CustomScalarAdapters
import com.apollographql.apollo3.api.json.JsonReader
import com.apollographql.apollo3.api.json.JsonWriter
import java.time.Instant

class InstantGraphQLAdapter : Adapter<Instant> {

	override fun from<PERSON><PERSON>(reader: JsonReader, customScalarAdapters: CustomScalarAdapters): Instant {
		val dateString = reader.nextString() ?: error("Date string was null")

		return Instant.parse(dateString)
	}

	override fun toJ<PERSON>(writer: <PERSON><PERSON><PERSON>rite<PERSON>, customScalarAdapters: CustomScalarAdapters, value: Instant) {
		writer.value(value.toString())
	}
}
