package com.cleevio.fatbot.infrastructure.config

import io.sentry.Sentry
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.provider.jdbctemplate.JdbcTemplateLockProvider
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock
import org.springframework.boot.task.ThreadPoolTaskSchedulerBuilder
import org.springframework.boot.task.ThreadPoolTaskSchedulerCustomizer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler
import javax.sql.DataSource

@Configuration
@EnableScheduling
/*
`defaultLockAtMostFor` is just a safety net in case that the node executing the task dies.
It should ideally be set on each scheduled task to significantly larger than the maximum estimated execution time.
 */
@EnableSchedulerLock(defaultLockAtMostFor = "1h")
@Profile("!test")
class SchedulingConfig {

	private val logger = logger()

	@Bean
	fun taskScheduler(builder: ThreadPoolTaskSchedulerBuilder): ThreadPoolTaskScheduler =
		builder.customizers(customizer()).build()

	fun customizer() = ThreadPoolTaskSchedulerCustomizer {
		it.setErrorHandler { ex ->
			Sentry.captureException(ex)
			logger.error("Exception in scheduled task: ", ex)
		}
		it.setThreadNamePrefix("scheduler-")
	}

	@Bean
	fun lockProvider(dataSource: DataSource): LockProvider {
		val lockProviderConfig = JdbcTemplateLockProvider.Configuration
			.builder()
			.withJdbcTemplate(JdbcTemplate(dataSource))
			.usingDbTime()
			.build()

		return JdbcTemplateLockProvider(lockProviderConfig)
	}
}
