package com.cleevio.fatbot.infrastructure.config

import com.cleevio.fatbot.application.module.access.port.out.ParseToken
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.infrastructure.config.properties.FatbotApiProperties
import com.cleevio.fatbot.infrastructure.security.filter.InternalFilter
import com.cleevio.fatbot.infrastructure.security.filter.JwtFilter
import com.cleevio.fatbot.infrastructure.security.filter.JwtSignUpFilter
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpStatus
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.HttpStatusEntryPoint
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.CorsConfigurationSource
import org.springframework.web.cors.UrlBasedCorsConfigurationSource

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true, jsr250Enabled = true)
class SecurityConfig(
	private val parseToken: ParseToken,
	private val firebaseUserFinderService: FirebaseUserFinderService,
	private val fatbotApiProperties: FatbotApiProperties,
) {
	@Bean
	fun corsFilter(): CorsConfigurationSource {
		val config = CorsConfiguration().apply {
			allowCredentials = true
			allowedOriginPatterns = listOf(CorsConfiguration.ALL)
			allowedHeaders = listOf(CorsConfiguration.ALL)
			allowedMethods = listOf(CorsConfiguration.ALL)
		}

		return UrlBasedCorsConfigurationSource().apply {
			registerCorsConfiguration("/**", config)
		}
	}

	@Bean
	fun filterChain(http: HttpSecurity): SecurityFilterChain = http
		.csrf { it.disable() }
		.cors { it.configurationSource(corsFilter()) }
		.authorizeHttpRequests {
			it.requestMatchers(ANONYMOUS_PATH).permitAll()
			it.requestMatchers(API_DOCS_PATH).permitAll()
			it.requestMatchers(ACTUATOR_PATH).permitAll()
			it.requestMatchers("/ws/**").permitAll()
			it.anyRequest().authenticated()
		}
		.addFilterAfter(
			JwtSignUpFilter(parseToken, "/users/sign-up"),
			BasicAuthenticationFilter::class.java,
		)
		.addFilterAfter(
			InternalFilter(pattern = "/internal/**", apiKey = fatbotApiProperties.apiKey),
			BasicAuthenticationFilter::class.java,
		)
		.addFilterAfter(
			JwtFilter(parseToken, firebaseUserFinderService),
			JwtSignUpFilter::class.java,
		)
		.exceptionHandling { it.authenticationEntryPoint(HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)) }
		.sessionManagement { it.sessionCreationPolicy(SessionCreationPolicy.STATELESS) }
		.build()
}

private const val ANONYMOUS_PATH = "/public/**"
private const val API_DOCS_PATH = "/api-docs/**"
private const val ACTUATOR_PATH = "/actuator/**"
