package com.cleevio.fatbot.infrastructure.config

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.messaging.converter.DefaultContentTypeResolver
import org.springframework.messaging.converter.MappingJackson2MessageConverter
import org.springframework.util.MimeTypeUtils

@Configuration
class MessageConverterConfig {

	@Bean
	fun mappingJackson2MessageConverter(objectMapper: ObjectMapper): MappingJackson2MessageConverter =
		MappingJackson2MessageConverter().also { converter ->
			converter.objectMapper = objectMapper
			converter.contentTypeResolver = DefaultContentTypeResolver().also { resolver ->
				resolver.defaultMimeType = MimeTypeUtils.APPLICATION_JSON
			}
		}
}
