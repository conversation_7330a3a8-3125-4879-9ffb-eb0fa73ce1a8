package com.cleevio.fatbot.infrastructure.config.properties

import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.validation.annotation.Validated
import java.math.BigDecimal

@ConfigurationProperties(prefix = "fatbot.fee")
@Validated
class FeeProperties(
	@field:Min(0) @field:Max(100) val platformFeePercentage: BigDecimal,
	@field:Min(0) @field:Max(100) val referrerFeeRewardPercentage: BigDecimal,
	@field:Min(0) @field:Max(100) val refereeManualTradingDiscountPercentage: BigDecimal,
	@field:Min(0) @field:Max(100) val refereeAutomaticTradingDiscountPercentage: BigDecimal,
)
