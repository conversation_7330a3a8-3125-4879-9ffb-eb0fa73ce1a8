package com.cleevio.fatbot.infrastructure.config.properties

import jakarta.validation.constraints.NotBlank
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.validation.annotation.Validated

@ConfigurationProperties(prefix = "integration.bitquery")
@Validated
data class BitqueryProperties(
	@field:NotBlank val graphqlApiV2Endpoint: String,
	@field:NotBlank val graphqlEapEndpoint: String,
	@field:NotBlank val accessToken: String,
)
