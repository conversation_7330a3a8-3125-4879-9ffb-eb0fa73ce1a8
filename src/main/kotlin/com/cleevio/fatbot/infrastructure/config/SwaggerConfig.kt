package com.cleevio.fatbot.infrastructure.config

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.type.SimpleType
import io.swagger.v3.core.jackson.ModelResolver
import io.swagger.v3.oas.models.Components
import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.info.Info
import io.swagger.v3.oas.models.media.Schema
import io.swagger.v3.oas.models.media.StringSchema
import io.swagger.v3.oas.models.security.SecurityScheme
import io.swagger.v3.oas.models.servers.Server
import org.springdoc.core.customizers.OperationCustomizer
import org.springdoc.core.customizers.ParameterCustomizer
import org.springdoc.core.customizers.PropertyCustomizer
import org.springdoc.core.models.GroupedOpenApi
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpHeaders
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Duration

@Configuration
class SwaggerConfig(
	@Value("\${springdoc.swagger-server}")
	private val server: String,

	@Value("\${git.commit.id.abbrev:local}")
	private val commitId: String,

	@Value("\${git.build.time:unknown}")
	private val lastBuild: String,

) {

	init {
		ModelResolver.enumsAsRef = true
	}

	@Bean
	fun openApiConfiguration(): OpenAPI {
		return OpenAPI()
			.info(
				Info()
					.title("Fatbot API")
					.version("v4.0.0+$commitId")
					.description("Fatbot documentation. Last built at: $lastBuild"),
			)
			.addServersItem(
				Server()
					.url(server)
					.description("Server"),
			)
			.components(
				Components()
					.addSecuritySchemes(
						"bearer",
						SecurityScheme().type(SecurityScheme.Type.HTTP).scheme("bearer").description("general")
							.bearerFormat("JWT"),
					)
					.addSecuritySchemes(
						"api-key",
						SecurityScheme().type(SecurityScheme.Type.APIKEY).`in`(SecurityScheme.In.HEADER).name(HttpHeaders.AUTHORIZATION),
					),
			)
	}

	/**
	 * Map custom mapper to support snake/camel case
	 *
	 * @param objectMapper Project object mapper
	 * @return Model resolver
	 */
	@Bean
	fun modelResolver(objectMapper: ObjectMapper): ModelResolver {
		return ModelResolver(objectMapper)
	}

	/**
	 * Customizes parameters picked up by SpringDoc i.e., handler method parameters
	 */
	@Bean
	fun parametersCustomizer() = ParameterCustomizer { parameterModel, methodParameter ->
		if (parameterModel == null) return@ParameterCustomizer null

		parameterModel.apply {
			val modifiedSchema = schema.modifiedByType(methodParameter.parameterType)

			schema = modifiedSchema
		}
	}

	/**
	 * Customizes [Schema] properties picked up by SpringDoc i.e., requests classes
	 */
	@Bean
	fun propertyCustomizer() = PropertyCustomizer { propertySchema, type ->
		val propertyType = type.type
		if (propertyType !is SimpleType) return@PropertyCustomizer propertySchema

		propertySchema.modifiedByType(propertyType.rawClass)
	}

	/**
	 * Maps different [Schema] modifications by type [klazz]
	 *
	 * This approach (as opposed to replacing the Schema for the entire type [klazz] in `SpringDocUtils.getConfig()`)
	 * facilitates higher flexibility by allowing us to see the original [Schema] i.e.,
	 * retaining a [Schema.nullable] property
	 */
	private fun Schema<*>.modifiedByType(klazz: Class<*>) = when (klazz.kotlin) {
		BigInteger::class -> {
			StringSchema().also {
				it.format = "integer"
				it.example = "88280773986419849064045"
				it.nullable = this.nullable
			}
		}
		BigDecimal::class -> {
			StringSchema().also {
				it.format = "decimal"
				it.example = "49.3256365129677938130087978045577872595529886403429871938170"
				it.nullable = this.nullable
			}
		}
		AddressWrapper::class -> {
			StringSchema().also {
				it.example = "******************************************"
				it.nullable = this.nullable
			}
		}
		Duration::class -> {
			StringSchema().also {
				it.format = "duration"
				it.nullable = this.nullable
			}
		}
		else -> this
	}

	@Bean
	fun operationNotImplementedCustomizer() = OperationCustomizer { operation, handlerMethod ->
		if (handlerMethod.getMethodAnnotation(NotImplemented::class.java) != null) {
			operation.summary("Endpoint has not yet been implemented. Will return 500 when called.")
		}

		operation
	}

	@Bean
	fun commonApi(): GroupedOpenApi = GroupedOpenApi.builder()
		.group("Common API")
		.pathsToMatch("/**")
		.pathsToExclude("/public/**", "/internal/**")
		.addOperationCustomizer(operationNotImplementedCustomizer())
		.build()

	@Bean
	fun anonymousApi(): GroupedOpenApi = GroupedOpenApi.builder()
		.group("Public API")
		.pathsToMatch("/public/**")
		.addOperationCustomizer(operationNotImplementedCustomizer())
		.build()

	@Bean
	fun internalApi(): GroupedOpenApi = GroupedOpenApi.builder()
		.group("Internal API")
		.pathsToMatch("/internal/**")
		.addOperationCustomizer(operationNotImplementedCustomizer())
		.build()
}

annotation class NotImplemented
