package com.cleevio.fatbot.infrastructure.config

import com.cleevio.library.exceptionhandler.service.model.ApiException
import io.sentry.Hint
import io.sentry.SentryEvent
import io.sentry.SentryOptions
import org.springframework.stereotype.Component

@Component
class SentryConfig : SentryOptions.BeforeSendCallback {
	override fun execute(event: SentryEvent, hint: Hint) = event.takeUnless { it.throwable is ApiException }
}
