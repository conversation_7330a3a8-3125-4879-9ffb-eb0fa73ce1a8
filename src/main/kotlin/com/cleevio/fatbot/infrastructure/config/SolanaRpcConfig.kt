package com.cleevio.fatbot.infrastructure.config

import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import io.sentry.Sentry
import io.sentry.spring.jakarta.tracing.SentrySpan
import io.sentry.spring.jakarta.tracing.SentrySpanAdvice
import okhttp3.OkHttpClient
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.RpcRequest
import org.springframework.aop.framework.ProxyFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.util.ReflectionUtils
import java.util.concurrent.TimeUnit

@Configuration
class SolanaRpcConfig {

	@Bean
	fun okHttpClient() = OkHttpClient().newBuilder()
		.readTimeout(20, TimeUnit.SECONDS)
		.build()

	@Bean
	@Primary
	fun rpcClient(chainProperties: ChainProperties): RpcClient {
		val client = SentryObservableRpcClient(chainProperties.svm.solana.node, okHttpClient())
		val proxiedClient = client.proxiedWithSentrySpan()
		client.api.setPrivateProperty("client", proxiedClient)
		return client
	}

	@Bean
	@Qualifier(value = "stakedRpcClient")
	fun stakedRpcClient(chainProperties: ChainProperties): RpcClient {
		val client = SentryObservableRpcClient(chainProperties.svm.solana.stakedNode, okHttpClient())
		val proxiedClient = client.proxiedWithSentrySpan()
		client.api.setPrivateProperty("client", proxiedClient)
		return client
	}

	@Bean
	@Qualifier(value = "jitoRpcClient")
	fun jitoRpcClient(chainProperties: ChainProperties): RpcClient {
		val client = SentryObservableRpcClient(chainProperties.svm.solana.jitoNode, okHttpClient())
		val proxiedClient = client.proxiedWithSentrySpan()
		client.api.setPrivateProperty("client", proxiedClient)
		return client
	}

	@Bean
	@Qualifier(value = "alchemyRpcClient")
	fun alchemyRpcClient(chainProperties: ChainProperties): RpcClient {
		val client = SentryObservableRpcClient(chainProperties.svm.solana.alchemyRepeaterNode, okHttpClient())
		val proxiedClient = client.proxiedWithSentrySpan()
		client.api.setPrivateProperty("client", proxiedClient)
		return client
	}

	private fun SentryObservableRpcClient.proxiedWithSentrySpan() = ProxyFactory(this).apply {
		addAdvice(SentrySpanAdvice())
		isProxyTargetClass = true
	}.proxy as RpcClient

	fun <T : Any> T.setPrivateProperty(variableName: String, data: Any?): Any? {
		val field = ReflectionUtils.findField(javaClass, variableName) ?: throw Exception("No such field $variableName")
		return field.let {
			it.isAccessible = true
			it.set(this, data)
		}
	}

	open class SentryObservableRpcClient(val node: String, client: OkHttpClient) : RpcClient(node, client) {
		@SentrySpan
		override fun <T : Any?> call(method: String?, params: Any?, clazz: Class<T>?): T {
			Sentry.getSpan()?.operation = "RpcClient::$method"
			return super.call(method, params, clazz)
		}

		@SentrySpan
		override fun <T : Any?> callBatch(rpcRequests: MutableList<RpcRequest>?, clazz: Class<T>?): MutableList<T> {
			Sentry.getSpan()?.operation = "RpcClient::callBatch"
			return super.callBatch(rpcRequests, clazz)
		}
	}
}
