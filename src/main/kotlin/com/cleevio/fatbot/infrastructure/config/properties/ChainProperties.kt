package com.cleevio.fatbot.infrastructure.config.properties

import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.module.access.port.out.GetEnabledChains
import com.cleevio.fatbot.application.module.market.exception.UnsupportedChainIdException
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties.EthereumVirtualMachine
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties.SolanaVirtualMachine
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.math.BigDecimal

@Configuration
class ChainPropertiesConfig(
	val chainProps: Properties,
	val getEnabledChains: GetEnabledChains,
) {

	@ConfigurationProperties(prefix = "fatbot")
	data class Properties(
		val evm: List<EthereumVirtualMachine>,
		val svm: SolanaVirtualMachine,
	)

	@Bean
	fun createChainProperties(): ChainProperties = ChainProperties(
		evm = chainProps.evm,
		svm = chainProps.svm,
		enabledChains = getEnabledChains.fetch(),
	)
}

data class ChainProperties(
	private val evm: List<EthereumVirtualMachine>,
	val svm: SolanaVirtualMachine,
	val enabledChains: Set<Chain>,
) {

	private val enabledEvmChainToProperties: Map<Chain, EthereumVirtualMachine> = evm
		.filter { it.chain in enabledChains }
		.associateBy { it.chain }

	val supportedEvmChains = evm.mapToSet { it.chain }
	val enabledEvmChains = enabledEvmChainToProperties.keys
	val enabledEvmProperties = enabledEvmChainToProperties.values

	fun ofEvmChainId(chainId: Long): EthereumVirtualMachine {
		val chain = Chain.ofEVM(chainId)
		return enabledEvmChainToProperties[chain]
			?: throw UnsupportedChainIdException("ChainId $chainId is not supported")
	}

	fun ofEvmChain(chain: Chain): EthereumVirtualMachine {
		return enabledEvmChainToProperties[chain]
			?: throw UnsupportedChainIdException("Chain ${chain.evmId} is not supported")
	}

	data class EthereumVirtualMachine(
		val chain: Chain,
		val node: String,
		val txExplorerUrl: String,
		val wethAddress: AddressWrapper,
		val fatbotRouter: AddressWrapper,
		val fatbotUtil: AddressWrapper,
		val referralWalletPrivateKey: String,
		val referralClaimThresholdNativeAmount: BigDecimal,
		private val priorityFeeBufferBasisPoint: Int,
		private val buySellSlippageBasisPoint: Int,
	) {
		val priorityFeeBufferBP = BasisPoint.of(priorityFeeBufferBasisPoint)
		val buySellSlippageBp = BasisPoint.of(buySellSlippageBasisPoint)
	}

	data class SolanaVirtualMachine(
		val solana: Solana,
	) {
		data class Solana(
			val node: String,
			val stakedNode: String,
			val alchemyRepeaterNode: String,
			val warmUpHeliusStakedNodeCache: Boolean,
			val jitoNode: String,
			val txExplorerUrl: String,
			val treasuryWallet: AddressWrapper,
			val referralWalletPrivateKey: String,
			val referralClaimThresholdNativeAmount: BigDecimal,
		)
	}
}
