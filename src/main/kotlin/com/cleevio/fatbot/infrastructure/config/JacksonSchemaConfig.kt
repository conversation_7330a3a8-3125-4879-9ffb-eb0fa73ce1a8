package com.cleevio.fatbot.infrastructure.config

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.introspect.AnnotatedConstructor
import com.fasterxml.jackson.databind.introspect.AnnotatedParameter
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.github.victools.jsonschema.generator.FieldScope
import com.github.victools.jsonschema.generator.SchemaGeneratorConfigBuilder
import com.github.victools.jsonschema.module.jackson.JacksonModule
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type
import java.time.Instant
import kotlin.reflect.KParameter
import kotlin.reflect.KProperty
import kotlin.reflect.full.primaryConstructor
import kotlin.reflect.jvm.javaType
import kotlin.reflect.jvm.kotlinProperty

/**
 * Code taken from https://github.com/stromland/jsonschema-module-jackson-kotlin
 */

data class KotlinJacksonModuleConfig(
	val disableRequired: Boolean = false,
	val disableDefaultValue: Boolean = false,
	val disableNullable: Boolean = false,
)

/**
 * Module for jsonschema-generator. Takes advantage of Kotlin data classes.
 * Given a data class this module can add the following information to json schema:
 *  - Default values.
 *  - Nullable type.
 *  - Required properties. Required when a field is not nullable or doesn't have a default value.
 *
 * @param typeMetadataRegister register for resolving types.
 */
class KotlinJacksonModule(
	val config: KotlinJacksonModuleConfig = KotlinJacksonModuleConfig(),
	val typeMetadataRegister: TypeMetadataRegister = TypeMetadataRegister(),
) : JacksonModule() {
	private var mapper = jacksonObjectMapper()

	override fun applyToConfigBuilder(builder: SchemaGeneratorConfigBuilder) {
		super.applyToConfigBuilder(builder)
		mapper = builder.objectMapper
			.registerKotlinModule()
			.enable(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES)

		builder.forFields().also {
			if (!config.disableRequired) it.withRequiredCheck(this::resolveRequiredCheck)
			if (!config.disableDefaultValue) it.withDefaultResolver(this::resolveDefault)
			if (!config.disableNullable) it.withNullableCheck(this::resolveNullable)
		}
	}

	private fun resolveRequiredCheck(field: FieldScope): Boolean {
		return field.isRequired()
	}

	private fun resolveDefault(field: FieldScope): Any? {
		val kProperty = field.rawMember.kotlinProperty ?: return null
		return typeMetadataRegister.getDefaultValue(field.member.declaringType.erasedType, kProperty)
	}

	private fun resolveNullable(field: FieldScope): Boolean {
		val type = field.rawMember.kotlinProperty
		return type?.returnType?.isMarkedNullable ?: false
	}

	/**
	 * EXTENSION FUNCTIONS
	 */

	private fun FieldScope.getAnnotatedParameter(): AnnotatedParameter? {
		val constructor = this.getAnnotatedConstructor()

		val index = constructor?.annotated?.parameters?.indexOfFirst {
			it.name == this.name
		} ?: return null

		if (index == -1) {
			return null
		}

		return constructor.getParameter(index)
	}

	private fun FieldScope.getAnnotatedConstructor(): AnnotatedConstructor? {
		val bean = getBeanDescriptionForClass(this.member.declaringType)
		return bean.constructors.takeIf { it.size > 0 }?.first()
	}

	private fun FieldScope.isRequired(): Boolean {
		val annotatedParameter = this.getAnnotatedParameter() ?: return false
		return mapper.serializationConfig.annotationIntrospector.hasRequiredMarker(annotatedParameter)
	}
}

/**
 * Register for creating an instance of a class with required parameters. And a cache for resolved types.
 * @param typeToMetadataMap additional types.
 */
data class TypeMetadataRegister(private val typeToMetadataMap: Map<Type, TypeMetadata> = mapOf()) {
	private val defaultTypeValues = mutableMapOf(
		Int::class.java.toTypedPair(0),
		Integer::class.java.toTypedPair(0),
		Boolean::class.java.toTypedPair(false),
		String::class.java.toTypedPair(""),
		Long::class.java.toTypedPair(0L),
		Map::class.java.toTypedPair(mapOf<Any, Any>()),
		List::class.java.toTypedPair(listOf<Any>()),
		Instant::class.java.toTypedPair(Instant.EPOCH),
	)

	init {
		defaultTypeValues.putAll(typeToMetadataMap)
	}

	fun resolveType(type: Type): TypeMetadata? {
		return getTypeMetadata(type) ?: registerType(type)
	}

	fun getDefaultValue(type: Type, prop: KProperty<*>? = null): Any? {
		val metadata = resolveType(type) ?: return null
		return metadata.getDefaultValue(prop)
	}

	private fun registerType(clazz: Type): TypeMetadata? {
		fun getDefaultRequiredValue(param: KParameter): Any? {
			val type = when (val javaType = param.type.javaType) {
				is ParameterizedType -> javaType.rawType
				else -> javaType
			}
			val typeMetadata = defaultTypeValues[type]
			if (typeMetadata != null) {
				return typeMetadata.instance
			}
			return registerType(type)?.instance
		}

		if (clazz !is Class<*>) {
			return null
		}

		if (clazz.isEnum) {
			val value = Class.forName(clazz.name).enumConstants.firstOrNull() ?: return null
			val typeMetadata = clazz.toTypeMetadata(value, mapOf())
			defaultTypeValues[clazz] = typeMetadata
			return typeMetadata
		}

		val constructor = clazz.kotlin.primaryConstructor ?: return null

		val requiredParameters: Map<KParameter, Any?> = constructor.parameters
			.filter { !it.isOptional }
			.associateWith { getDefaultRequiredValue(it) }

		// TODO: Error handling
		val instant = constructor.callBy(requiredParameters)
		val metadata = clazz.toTypeMetadata(instant, requiredParameters)
		defaultTypeValues[clazz] = metadata
		return metadata
	}

	private fun getTypeMetadata(type: Type): TypeMetadata? {
		return defaultTypeValues[type]
	}
}

/**
 * Model for storing information about a type.
 * @property type The Java type of the class.
 * @property instance An instance of the class.
 * @property requiredParameters A map of requiredParameters to instantiate an instance of the class.
 */
data class TypeMetadata(val type: Type, val instance: Any, val requiredParameters: Map<KParameter, Any?> = mapOf()) {
	private val parameterNames: Set<String> = requiredParameters.mapNotNull { it.key.name }.toSet()

	/**
	 * Get default value for the given type or for a property of the given type.
	 * @param prop a property of a class.
	 * @return return null if property is required, else the instance.
	 */
	fun getDefaultValue(prop: KProperty<*>? = null): Any? {
		if (prop == null) {
			return instance
		}
		return if (prop.isRequired()) null else prop.call(instance)
	}

	/**
	 * Determine if a property has a default value or not.
	 * @param prop The property for validation.
	 * @return True if property has a default value, false if not.
	 */
	fun hasPropertyADefaultValue(prop: KProperty<*>): Boolean {
		return !prop.isRequired()
	}

	private fun KProperty<*>.isRequired() = parameterNames.contains(this.name)
}

fun Type.toTypedPair(value: Any): Pair<Type, TypeMetadata> {
	return this to this.toTypeMetadata(value)
}

fun Type.toTypeMetadata(defaultValue: Any, requiredParameters: Map<KParameter, Any?> = mapOf()): TypeMetadata {
	return TypeMetadata(this, defaultValue, requiredParameters)
}
