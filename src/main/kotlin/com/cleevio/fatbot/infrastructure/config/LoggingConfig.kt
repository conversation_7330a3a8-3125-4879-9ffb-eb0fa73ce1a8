package com.cleevio.fatbot.infrastructure.config

import jakarta.servlet.http.HttpServletRequest
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.web.util.matcher.AntPathRequestMatcher
import org.springframework.security.web.util.matcher.RequestMatcher
import org.springframework.web.filter.CommonsRequestLoggingFilter

inline fun <reified T> T.logger(): Logger {
	return LoggerFactory.getLogger(T::class.java)
}

@Configuration
class RequestLoggingFilterConfig {
	@Bean
	fun logFilter(): CommonsRequestLoggingFilter {
		return PatternIgnoringRequestLoggingFilter(pathToIgnore = "/internal/**").apply {
			setIncludeQueryString(true)
			setIncludePayload(true)
			setMaxPayloadLength(10000)
			setIncludeHeaders(false)
		}
	}
}

class PatternIgnoringRequestLoggingFilter(
	pathToIgnore: String,
) : CommonsRequestLoggingFilter() {

	// everything matched will not be logged
	private val matcher: RequestMatcher = AntPathRequestMatcher(pathToIgnore)

	override fun shouldLog(request: HttpServletRequest): Boolean {
		if (matcher.matches(request)) return false
		return super.shouldLog(request)
	}
}
