package com.cleevio.fatbot.infrastructure.config

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.kotlin.addDeserializer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.math.BigDecimal
import java.math.BigInteger

@Configuration
class ObjectMapperConfig {

	@Bean
	fun prepareObjectMapper(): ObjectMapper {
		return ObjectMapper()
			.registerModule(KotlinModule.Builder().build())
			.registerModule(JavaTimeModule())
			.registerModule(Jdk8Module())
			.registerModule(
				SimpleModule().apply {
					addSerializer(AddressWrapperSerializer)
					addSerializer(BigIntegerSerializer)
					addSerializer(BigDecimalSerializer)
					addDeserializer(AddressWrapper::class, AddressWrapperDeserializer)
				},
			)
			.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
			.configure(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN, true)
	}
}

object AddressWrapperSerializer : JsonSerializer<AddressWrapper>() {
	override fun serialize(value: AddressWrapper?, gen: JsonGenerator?, serializers: SerializerProvider?) {
		gen?.writeString(value?.getAddressString())
	}

	override fun handledType(): Class<AddressWrapper> {
		return AddressWrapper::class.java
	}
}

object AddressWrapperDeserializer : JsonDeserializer<AddressWrapper>() {
	override fun deserialize(p: JsonParser?, ctxt: DeserializationContext?): AddressWrapper {
		return AddressWrapper(addressString = p!!.text)
	}

	override fun handledType(): Class<*> {
		return AddressWrapper::class.java
	}
}

object BigIntegerSerializer : JsonSerializer<BigInteger>() {
	override fun serialize(value: BigInteger?, gen: JsonGenerator?, serializers: SerializerProvider?) {
		gen?.writeString(value?.toString())
	}

	override fun handledType(): Class<BigInteger> {
		return BigInteger::class.java
	}
}

object BigDecimalSerializer : JsonSerializer<BigDecimal>() {
	override fun serialize(value: BigDecimal?, gen: JsonGenerator?, serializers: SerializerProvider?) {
		gen?.writeString(value?.toPlainString())
	}

	override fun handledType(): Class<BigDecimal> {
		return BigDecimal::class.java
	}
}
