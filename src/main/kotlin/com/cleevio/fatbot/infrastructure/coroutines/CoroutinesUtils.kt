package com.cleevio.fatbot.infrastructure.coroutines

import io.sentry.kotlin.SentryContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.slf4j.MDCContext

private val dispatcher = Dispatchers.IO

// Use this when exception in one of the scope Jobs should NOT cancel all other jobs
fun createSupervisorJobScope() = CoroutineScope(dispatcher + SupervisorJob() + MDCContext() + SentryContext())

// Use this when exception in one of the scope jobs should cancel all other jobs
fun createJobScope() = CoroutineScope(dispatcher + Job() + MDCContext() + SentryContext())
