package com.cleevio.fatbot.infrastructure.migration.migrations

import com.cleevio.fatbot.adapter.out.solana.pumpfun.GetPumpfunCurveState
import com.cleevio.fatbot.adapter.out.solana.pumpswap.GetPumpswapPool
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.util.runWithRetry
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.fatbot.tables.references.TOKEN_PAIR_INFO
import com.cleevio.library.lockinghandler.service.Lock
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.toJavaDuration

@Component
class UpdateCreatorAddressForTokenPairsMigration(
	private val dslContext: DSLContext,
	private val getPumpswapPool: GetPumpswapPool,
	private val getPumpfunCurveState: GetPumpfunCurveState,
) : Migration {

	override val migrationId: UUID = UUID.fromString("734848d4-3b8f-4160-aab2-b6273532ed0d")

	private val logger = logger()

	@Transactional
	@Lock(module = "MIGRATION", lockName = "UPDATE_CREATOR")
	override fun migrate() {
		updatePumpfun()
		updatePumpswap()
	}

	private fun updatePumpfun() {
		val pumpfunPairs = dslContext
			.select(TOKEN_PAIR_INFO.PAIR_ADDRESS)
			.from(TOKEN_PAIR_INFO)
			.where(TOKEN_PAIR_INFO.DEX_TYPE.eq(GetDex.Dex.PUMP_FUN))
			.and(TOKEN_PAIR_INFO.CREATOR_ADDRESS.isNull)
			.fetch()
			.mapToSet { it[TOKEN_PAIR_INFO.PAIR_ADDRESS]!! }
			.filter { it.isSolana() }

		pumpfunPairs.chunked(100) { pairs ->
			val curveStates = runWithRetry(
				retries = 3,
				retryDelay = 250.milliseconds.toJavaDuration(),
				onError = { logger.warn("Error in getPumpfunCurveState: ", it) },
				block = { runCatching { getPumpfunCurveState.getMany(pairs) } },
			).getOrThrow()

			val updates = curveStates
				.mapValues { (_, curveState) -> curveState?.creator }
				.filterValues { it != null }
				.map { (address, creator) ->
					dslContext.update(TOKEN_PAIR_INFO)
						.set(TOKEN_PAIR_INFO.CREATOR_ADDRESS, creator)
						.where(TOKEN_PAIR_INFO.PAIR_ADDRESS.eq(address))
				}

			dslContext.batch(updates).execute()
		}
	}

	private fun updatePumpswap() {
		val pumpswapPairs = dslContext
			.select(TOKEN_PAIR_INFO.PAIR_ADDRESS)
			.from(TOKEN_PAIR_INFO)
			.where(TOKEN_PAIR_INFO.DEX_TYPE.eq(GetDex.Dex.PUMP_SWAP))
			.and(TOKEN_PAIR_INFO.CREATOR_ADDRESS.isNull)
			.fetch()
			.mapToSet { it[TOKEN_PAIR_INFO.PAIR_ADDRESS]!! }
			.filter { it.isSolana() }

		pumpswapPairs.chunked(100) { pairs ->
			val curveStates = runWithRetry(
				retries = 3,
				retryDelay = 250.milliseconds.toJavaDuration(),
				onError = { logger.warn("Error in getPumpswapPool: ", it) },
				block = { runCatching { getPumpswapPool.getMany(pairs) } },
			).getOrThrow()

			val updates = curveStates
				.mapValues { (_, curveState) -> curveState?.coinCreator }
				.filterValues { it != null }
				.map { (address, creator) ->
					dslContext.update(TOKEN_PAIR_INFO)
						.set(TOKEN_PAIR_INFO.CREATOR_ADDRESS, creator)
						.where(TOKEN_PAIR_INFO.PAIR_ADDRESS.eq(address))
				}

			dslContext.batch(updates).execute()
		}
	}
}
