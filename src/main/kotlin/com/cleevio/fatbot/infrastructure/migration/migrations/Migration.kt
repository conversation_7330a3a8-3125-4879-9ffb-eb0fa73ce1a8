package com.cleevio.fatbot.infrastructure.migration.migrations

import java.util.UUID

sealed interface Migration {
	/**
	 * This should be stable UUID.fromString() in order to run this migration only once.
	 *
	 * Note: In rare case when this migration should be run on every startup, you can choose to use random UUID
	 */
	val migrationId: UUID

	/**
	 * The actual migration script that will be run
	 */
	fun migrate()

	/**
	 * Override this if you need two migrations happening at the same time and one is dependent on the other
	 *
	 * Lower means will happen sooner
	 */
	fun order(): Int {
		return Int.MAX_VALUE
	}
}
