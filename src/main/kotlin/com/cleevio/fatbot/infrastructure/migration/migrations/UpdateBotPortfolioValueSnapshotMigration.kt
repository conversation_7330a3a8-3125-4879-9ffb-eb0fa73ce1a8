package com.cleevio.fatbot.infrastructure.migration.migrations

import com.cleevio.fatbot.tables.references.BOT_PORTFOLIO_VALUE_SNAPSHOT
import com.cleevio.fatbot.tables.references.BOT_WALLET
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
class UpdateBotPortfolioValueSnapshotMigration(
	private val dslContext: DSLContext,
) : Migration {

	override val migrationId: UUID = UUID.fromString("51136bdf-c0e9-4e4d-a704-57e908e90d9d")

	@Transactional
	override fun migrate() {
		dslContext.update(BOT_PORTFOLIO_VALUE_SNAPSHOT)
			.set(BOT_PORTFOLIO_VALUE_SNAPSHOT.ACQUISITION_VALUE_USD, BOT_WALLET.ACQUISITION_VALUE_USD)
			.from(BOT_WALLET)
			.where(BOT_PORTFOLIO_VALUE_SNAPSHOT.BOT_ID.eq(BOT_WALLET.BOT_ID))
			.execute()
	}
}
