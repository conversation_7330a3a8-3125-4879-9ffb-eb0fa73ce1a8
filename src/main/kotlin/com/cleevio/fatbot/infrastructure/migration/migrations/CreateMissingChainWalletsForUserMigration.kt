package com.cleevio.fatbot.infrastructure.migration.migrations

import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.wallet.command.CreateNewUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.FIREBASE_USER
import com.cleevio.fatbot.tables.references.WALLET
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class CreateMissingChainWalletsForUserMigration(
	private val dslContext: DSLContext,
	private val commandBus: CommandBus,
) {

	@Transactional
	operator fun invoke(chain: Chain) {
		val userIdsWithoutEvmArbitrumOneWallet = dslContext
			.select(FIREBASE_USER.ID)
			.from(FIREBASE_USER)
			.leftJoin(WALLET)
			.on(FIREBASE_USER.ID.eq(WALLET.USER_ID))
			.and(WALLET.CHAIN.eq(chain))
			.where(WALLET.ID.isNull)
			.fetch()
			.map { it[FIREBASE_USER.ID]!! }

		userIdsWithoutEvmArbitrumOneWallet.forEach {
			commandBus(CreateNewUserWalletCommand(userId = it, chain = chain))
		}
	}
}
