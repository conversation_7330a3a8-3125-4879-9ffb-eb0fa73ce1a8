package com.cleevio.fatbot.infrastructure.migration.migrations

import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
class CreateMissingEvmBaseWalletsForUsersMigration(
	private val createMissingChainWalletsForUserMigration: CreateMissingChainWalletsForUserMigration,
) : Migration {

	// Changed ID to allow re-migration on 2025-04-22 (oldId: fa76d9ef-722c-4fb0-8740-979474bf0712)
	override val migrationId: UUID = UUID.fromString("fa76d9ef-722c-4fb0-8740-979474bf0711")

	@Transactional
	override fun migrate() {
		createMissingChainWalletsForUserMigration(chain = Chain.EVM_BASE)
	}
}
