package com.cleevio.fatbot.infrastructure.migration.migrations

import com.cleevio.fatbot.adapter.out.solana.pumpswap.GetPumpswapPool
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.util.runWithRetry
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.fatbot.tables.references.TOKEN_PAIR_INFO
import com.cleevio.library.lockinghandler.service.Lock
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.toJavaDuration

@Component
class UpdateCreatorAddressForPumpswapTokenPairsMigration(
	private val dslContext: DSLContext,
	private val getPumpswapPool: GetPumpswapPool,
) : Migration {

	override val migrationId: UUID = UUID.fromString("*************-42f4-b7ed-344bde544567")

	private val logger = logger()

	@Transactional
	@Lock(module = "MIGRATION", lockName = "UPDATE_PUMPSWAP_CREATOR")
	override fun migrate() {
		updatePumpswap()
	}

	private fun updatePumpswap() {
		val pumpswapPairs = dslContext
			.select(TOKEN_PAIR_INFO.PAIR_ADDRESS)
			.from(TOKEN_PAIR_INFO)
			.where(TOKEN_PAIR_INFO.DEX_TYPE.eq(GetDex.Dex.PUMP_SWAP))
			.and(TOKEN_PAIR_INFO.CREATOR_ADDRESS.isNotNull)
			.fetch()
			.mapToSet { it[TOKEN_PAIR_INFO.PAIR_ADDRESS]!! }
			.filter { it.isSolana() }

		pumpswapPairs.chunked(100) { pairs ->
			val curveStates = runWithRetry(
				retries = 3,
				retryDelay = 250.milliseconds.toJavaDuration(),
				onError = { logger.warn("Error in getPumpswapPool: ", it) },
				block = { runCatching { getPumpswapPool.getMany(pairs) } },
			).getOrThrow()

			val updates = curveStates
				.mapValues { (_, curveState) -> curveState?.coinCreator }
				.filterValues { it != null }
				.map { (address, creator) ->
					dslContext.update(TOKEN_PAIR_INFO)
						.set(TOKEN_PAIR_INFO.CREATOR_ADDRESS, creator)
						.where(TOKEN_PAIR_INFO.PAIR_ADDRESS.eq(address))
				}

			dslContext.batch(updates).execute()
		}
	}
}
