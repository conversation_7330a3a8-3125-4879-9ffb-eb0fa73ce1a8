package com.cleevio.fatbot.infrastructure.migration.migrations

import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
class CreateMissingEvmBSCWalletsForUsersMigration(
	private val createMissingChainWalletsForUserMigration: CreateMissingChainWalletsForUserMigration,
) : Migration {

	override val migrationId: UUID = UUID.fromString("255b806c-ca5a-4c99-8590-ca455941d3db")

	@Transactional
	override fun migrate() {
		createMissingChainWalletsForUserMigration(chain = Chain.EVM_BSC)
	}
}
