package com.cleevio.fatbot.infrastructure.migration.migrations

import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
class CreateMissingSolanaWalletsForUsersMigration(
	private val createMissingChainWalletsForUserMigration: CreateMissingChainWalletsForUserMigration,
) : Migration {

	override val migrationId: UUID = UUID.fromString("0194ac44-e5f8-70c7-a340-01ef028c38be")

	@Transactional
	override fun migrate() {
		createMissingChainWalletsForUserMigration(chain = Chain.SOLANA)
	}
}
