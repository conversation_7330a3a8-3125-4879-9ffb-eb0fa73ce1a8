package com.cleevio.fatbot.infrastructure.migration.entity

import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Version
import org.hibernate.annotations.CreationTimestamp
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.Instant
import java.util.UUID

@Entity
class MigrationInfo(
	@Id val id: UUID,
	@CreationTimestamp var createdAt: Instant = Instant.now(),
	@Version val version: Int? = null,
)

@Repository
interface MigrationInfoRepository : JpaRepository<MigrationInfo, UUID>
