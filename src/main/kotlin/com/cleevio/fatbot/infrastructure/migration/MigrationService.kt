package com.cleevio.fatbot.infrastructure.migration

import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.fatbot.infrastructure.migration.entity.MigrationInfo
import com.cleevio.fatbot.infrastructure.migration.entity.MigrationInfoRepository
import com.cleevio.fatbot.infrastructure.migration.exception.MigrationException
import com.cleevio.fatbot.infrastructure.migration.migrations.Migration
import jakarta.annotation.PostConstruct
import org.springframework.stereotype.Component

@Component
class MigrationService(
	val migrationInfoRepository: MigrationInfoRepository,
	val migrations: List<Migration>,
) {

	private val logger = logger()

	@PostConstruct
	fun migrate() {
		val migrationIds = migrations.map { it.migrationId }

		val alreadyMigratedIds = migrationInfoRepository.findAllById(migrationIds).mapToSet { it.id }
		val (alreadyMigrated, toMigrate) = migrations.partition { it.migrationId in alreadyMigratedIds }

		logger.info("=== Code migration summary ===")
		logger.info("------------------------------")
		logger.info("Already migrated: ${alreadyMigrated.size}")
		logger.info("Awaiting migration: ${toMigrate.size}")

		val toMigrateOrdered = toMigrate.sortedBy { it.order() }

		toMigrateOrdered.forEach {
			logger.info("Running migration ${it.migrationId}")
			runCatching { it.migrate() }.onFailure { ex -> throw MigrationException(it.migrationId, ex) }
			migrationInfoRepository.save(MigrationInfo(id = it.migrationId))
		}
	}
}
