package com.cleevio.fatbot.infrastructure.migration.migrations

import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector.Companion.ETHERSCAN_RATE_LIMIT_COOLDOWN
import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector.Companion.ETHERSCAN_RATE_LIMIT_SIZE
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.token.port.out.GetIsTokenContractVerified
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.fatbot.infrastructure.coroutines.createSupervisorJobScope
import com.cleevio.fatbot.tables.references.EVM_TOKEN_INFO
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

private const val UPDATE_BATCH_SIZE = 1000
private const val RETRY_LIMIT = 5

@Component
class UpdateIsVerifiedOnTokenInfoMigration(
	private val dslContext: DSLContext,
	private val getIsTokenContractVerified: GetIsTokenContractVerified,
) : Migration {

	override val migrationId: UUID = UUID.fromString("55365268-141c-44d4-8c3e-972ba742c701")

	private val logger = logger()

	private data class VerificationResults(
		val verified: List<ChainAddress>,
		val failed: List<ChainAddress>,
	)

	@Transactional
	override fun migrate() {
		val initial = Triple(0, 0, emptyList<ChainAddress>())

		val updateSequence = generateSequence(initial) { (offset, updatedCountTotal, failedAddresses) ->
			val chainAddresses = dslContext
				.select(EVM_TOKEN_INFO.ADDRESS, EVM_TOKEN_INFO.CHAIN)
				.from(EVM_TOKEN_INFO)
				.where(EVM_TOKEN_INFO.CHAIN.`in`(Chain.allEvm()))
				.orderBy(EVM_TOKEN_INFO.ID)
				.limit(UPDATE_BATCH_SIZE)
				.offset(offset)
				.map { it[EVM_TOKEN_INFO.ADDRESS]!!.toChainAddress(it[EVM_TOKEN_INFO.CHAIN]!!) }

			if (chainAddresses.isEmpty()) return@generateSequence null

			val (verifiedAddresses, newFailedAddresses) = getTokenVerificationResults(chainAddresses)

			val updatedCount = updateVerified(verifiedAddresses)

			Triple(offset + UPDATE_BATCH_SIZE, updatedCountTotal + updatedCount, failedAddresses + newFailedAddresses)
		}

		val (_, totalUpdates, failed) = updateSequence.last()

		logger.info("Updated total of $totalUpdates tokens. ${failed.size} failed.")

		if (failed.isNotEmpty()) {
			val initialAddressToRetryCount = failed.associateWith { 0 }

			logger.info("Going to retry fetch for ${failed.size} failed tokens with max $RETRY_LIMIT retries.")

			val retryFailedSeq = generateSequence(initialAddressToRetryCount to 0) { (addressToRetryCount, totalUpdateCount) ->
				if (addressToRetryCount.isEmpty()) return@generateSequence null

				val verificationResults = getTokenVerificationResults(addressToRetryCount.keys.toList())
				val updatedCount = updateVerified(verificationResults.verified)

				val failedToVerify = verificationResults.failed.toSet()

				val newAddressToRetryCount = addressToRetryCount
					.filterKeys { it in failedToVerify }
					.mapValues { (_, retryCount) -> retryCount + 1 }

				val reachedRetryLimit = newAddressToRetryCount.filterValues { it > RETRY_LIMIT }

				if (reachedRetryLimit.isNotEmpty()) {
					logger.warn(
						"${reachedRetryLimit.size} token addresses reached a retry limit of 5." +
							"They will continue to be marked as not verified in the token info" +
							"Token addresses: ${reachedRetryLimit.keys}",
					)
				}

				newAddressToRetryCount - reachedRetryLimit.keys to totalUpdateCount + updatedCount
			}

			val (_, totalRetryUpdates) = retryFailedSeq.last()

			logger.info("Updated additional $totalRetryUpdates out of ${failed.size}")
		}

		val updatedNonEvmRecordsCount = dslContext.update(EVM_TOKEN_INFO)
			.set(EVM_TOKEN_INFO.IS_VERIFIED, true)
			.where(EVM_TOKEN_INFO.CHAIN.notIn(Chain.allEvm()))
			.execute()

		logger.info("Updated $updatedNonEvmRecordsCount non-EVM Token info records to isVerified = true.")
	}

	private fun getTokenVerificationResults(addresses: List<ChainAddress>): VerificationResults {
		val scope = createSupervisorJobScope()

		val addressToIsVerified = addresses.chunked(ETHERSCAN_RATE_LIMIT_SIZE) { chunk ->
			val requests = chunk.map {
				scope.async {
					val isVerifiedResult = runCatching {
						getIsTokenContractVerified(tokenAddress = it.address, chainId = it.chain.evmId)
					}

					it to isVerifiedResult
				}
			}

			val responses = runBlocking { requests.awaitAll() }

			Thread.sleep(ETHERSCAN_RATE_LIMIT_COOLDOWN)

			responses.map { (chainAddress, result) -> chainAddress to result.getOrNull() }
		}.flatten()

		val verifiedAddresses = addressToIsVerified.mapNotNull { (address, isVerified) ->
			address.takeIf { isVerified == true }
		}

		val failedAddresses = addressToIsVerified.mapNotNull { (address, isVerified) ->
			address.takeIf { isVerified == null }
		}

		return VerificationResults(verified = verifiedAddresses, failed = failedAddresses)
	}

	private fun updateVerified(verifiedAddresses: List<ChainAddress>): Int {
		val chainToVerifiedAddress = verifiedAddresses.groupBy({ it.chain }, { it.address })

		val updatedCount = chainToVerifiedAddress.entries.sumOf { (chain, verifiedAddresses) ->
			val updatedCount = dslContext.update(EVM_TOKEN_INFO)
				.set(EVM_TOKEN_INFO.IS_VERIFIED, true)
				.where(EVM_TOKEN_INFO.ADDRESS.`in`(verifiedAddresses))
				.and(EVM_TOKEN_INFO.CHAIN.eq(chain))
				.execute()

			updatedCount
		}

		return updatedCount
	}
}
