package com.cleevio.fatbot.infrastructure.migration.migrations

import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.fatbot.tables.references.EVM_TOKEN_INFO
import org.jooq.DSLContext
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

private const val UPDATE_BATCH_SIZE = 1000

@Component
class UpdateIsToken2022OnTokenInfoMigration(
	private val dslContext: DSLContext,
	private val rpcClient: RpcClient,
) : Migration {

	override val migrationId: UUID = UUID.fromString("9b6c678e-7655-42de-b782-0133630f8580")

	@Transactional
	override fun migrate() {
		val updateSequence = generateSequence(0 to 0) { (offset, updatedCountTotal) ->
			val addresses = dslContext
				.select(EVM_TOKEN_INFO.ADDRESS)
				.from(EVM_TOKEN_INFO)
				.where(EVM_TOKEN_INFO.CHAIN.eq(Chain.SOLANA))
				.orderBy(EVM_TOKEN_INFO.ID)
				.limit(UPDATE_BATCH_SIZE)
				.offset(offset)
				.map { it[EVM_TOKEN_INFO.ADDRESS]!! }

			if (addresses.isEmpty()) return@generateSequence null

			val token2022Addresses = addresses.chunked(100) { chunk ->
				val accounts = rpcClient.api.getMultipleAccounts(chunk.map { it.getSolanaPublicKey() })

				chunk.zip(accounts).mapNotNull { (address, account) ->
					address.takeIf { PublicKey(account.owner) == SolanaConstants.TOKEN_2022_PROGRAM_ID }
				}
			}.flatten()

			val updatedCount = dslContext.update(EVM_TOKEN_INFO)
				.set(EVM_TOKEN_INFO.IS_TOKEN2022, true)
				.where(EVM_TOKEN_INFO.ADDRESS.`in`(token2022Addresses))
				.and(EVM_TOKEN_INFO.CHAIN.eq(Chain.SOLANA))
				.execute()

			offset + UPDATE_BATCH_SIZE to updatedCountTotal + updatedCount
		}

		val (_, updatedCountTotal) = updateSequence.last()

		logger().info("Finished updating isToken2022 flag for TokenInfo. Updated $updatedCountTotal records.")
	}
}
