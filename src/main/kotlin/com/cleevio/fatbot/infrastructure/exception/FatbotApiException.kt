package com.cleevio.fatbot.infrastructure.exception

import com.cleevio.library.exceptionhandler.service.model.ApiException
import com.cleevio.library.exceptionhandler.service.model.ErrorCode
import com.cleevio.library.exceptionhandler.service.model.ErrorReason
import com.cleevio.library.exceptionhandler.service.model.Source

abstract class FatbotApiException(
	reason: ErrorReason,
	message: String,
	shouldBeLogged: Boolean = true,
) : ApiException(
	source = ErrorSource,
	reason = reason,
	message = message,
	shouldBeLogged = shouldBeLogged,
)

private object ErrorSource : Source {
	override val errorCode: ErrorCode
		get() = ErrorCode("FATBOT")
}
