package com.cleevio.fatbot.application.common.util

import java.math.BigDecimal
import java.math.MathContext

fun calculateFractionDifference(a: BigDecimal, b: BigDecimal): BigDecimal {
	val difference = (a - b).abs()
	val average = (a + b).divide(BigDecimal.TWO, MathContext.DECIMAL128)
	val differenceFraction = difference.divide(average, MathContext.DECIMAL128)

	return differenceFraction
}

fun calculateFractionChange(a: BigDecimal, b: BigDecimal): BigDecimal {
	if (b.compareTo(BigDecimal.ZERO) == 0) return BigDecimal.ZERO

	return a.divide(b, MathContext.DECIMAL128).dec()
}
