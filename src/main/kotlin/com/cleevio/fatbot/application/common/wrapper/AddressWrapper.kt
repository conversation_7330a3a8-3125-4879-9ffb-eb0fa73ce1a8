package com.cleevio.fatbot.application.common.wrapper

import com.cleevio.fatbot.application.common.crypto.isValidEthereumAddress
import com.cleevio.fatbot.application.common.crypto.isValidSolanaAddress
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.constant.O_X
import com.fasterxml.jackson.annotation.JsonValue
import io.swagger.v3.oas.annotations.media.Schema
import org.bitcoinj.core.Base58
import org.p2p.solanaj.core.PublicKey
import org.web3j.crypto.Keys

@Schema(type = "string")
class AddressWrapper {
	private val addressBytes: ByteArray
	val addressType: ChainType

	companion object {
		val EVM_NULL_ADDRESS = AddressWrapper("******************************************")
	}

	constructor(addressString: String) {
		val trimmed = addressString.trim()
		when {
			trimmed.isValidEthereumAddress() -> {
				addressBytes = hexStringToByteArray(trimmed.substring(2))
				addressType = ChainType.EVM
			}
			trimmed.isValidSolanaAddress() -> {
				addressBytes = Base58.decode(trimmed)
				addressType = ChainType.SOLANA
			}
			else -> throw IllegalArgumentException("Invalid address format of $addressString.")
		}
	}

	constructor(addressBytes: ByteArray) {
		addressType = when (addressBytes.size) {
			20 -> ChainType.EVM
			32 -> ChainType.SOLANA
			else -> throw IllegalArgumentException("Invalid byte array size.")
		}
		this.addressBytes = addressBytes
	}

	fun getAddressBytes(): ByteArray = addressBytes.clone()

	fun getSolanaPublicKey(): PublicKey {
		check(addressType == ChainType.SOLANA) { "Cannot get solana publicKey for EVM address!" }

		return PublicKey(getAddressBytes())
	}

	@JsonValue
	fun getAddressString(): String {
		return when (addressType) {
			ChainType.EVM -> Keys.toChecksumAddress("$O_X${byteArrayToHexString(addressBytes)}")
			ChainType.SOLANA -> Base58.encode(addressBytes)
		}
	}

	fun getLinkToExplorer(chain: Chain): String {
		return when (addressType) {
			ChainType.EVM -> when (chain) {
				Chain.EVM_MAINNET -> "$ETHERSCAN${getAddressString()}"
				Chain.EVM_BSC -> "$BSCSCAN${getAddressString()}"
				Chain.EVM_BASE -> "$BASESCAN${getAddressString()}"
				Chain.EVM_ARBITRUM_ONE -> "$ARBITRUMSCAN${getAddressString()}"
				else -> error("Invalid chain: $chain for chain type: $addressType")
			}
			ChainType.SOLANA -> "$SOLSCAN${getAddressString()}"
		}
	}

	fun isEvmNullAddress(): Boolean = this == EVM_NULL_ADDRESS
	fun isEvm(): Boolean = addressType == ChainType.EVM
	fun isSolana(): Boolean = addressType == ChainType.SOLANA

	override fun equals(other: Any?): Boolean {
		if (this === other) return true
		if (other !is AddressWrapper) return false
		return addressBytes.contentEquals(other.addressBytes) && addressType == other.addressType
	}

	override fun hashCode(): Int {
		var result = addressBytes.contentHashCode()
		result = 31 * result + addressType.hashCode()
		return result
	}

	override fun toString(): String = getAddressString()

	private fun hexStringToByteArray(s: String): ByteArray {
		val len = s.length
		return ByteArray(len / 2).also { data ->
			for (i in 0 until len step 2) {
				data[i / 2] = ((Character.digit(s[i], 16) shl 4) + Character.digit(s[i + 1], 16)).toByte()
			}
		}
	}

	private fun byteArrayToHexString(bytes: ByteArray): String {
		val hexString = StringBuilder()
		for (b in bytes) {
			val hex = Integer.toHexString(0xFF and b.toInt())
			if (hex.length == 1) {
				hexString.append('0')
			}
			hexString.append(hex)
		}
		return hexString.toString()
	}
}

private const val ETHERSCAN = "https://etherscan.io/address/"
private const val BSCSCAN = "https://bscscan.com/address/"
private const val BASESCAN = "https://basescan.org/address/"
private const val ARBITRUMSCAN = "https://arbiscan.io/address/"
private const val SOLSCAN = "https://solscan.io/address/"
