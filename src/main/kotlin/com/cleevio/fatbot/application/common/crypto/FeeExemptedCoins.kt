package com.cleevio.fatbot.application.common.crypto

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.wallet.constant.Chain

val COINS_EXEMPTED_FROM_FATBOT_FEE = listOf(
	// exempted due to ticket CXFB-1809
	ChainAddress(
		chain = Chain.EVM_ARBITRUM_ONE,
		address = AddressWrapper("******************************************"),
	),
)

fun ChainAddress.isExemptedFromFatbotFee() = this in COINS_EXEMPTED_FROM_FATBOT_FEE
