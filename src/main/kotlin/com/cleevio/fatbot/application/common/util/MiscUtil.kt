package com.cleevio.fatbot.application.common.util

import java.time.Duration
import kotlin.random.Random
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration as KotlinDuration

/**
 * Runs [block] with additional [retries] amount of times until [Result.success] is encountered.
 *
 * When retry count is exhausted, a [Result.failure] with [IllegalStateException] is returned.
 *
 * [retryDelay] is invoked (as [Thread.sleep]) after before each retry
 *
 * I.e.
 * ```
 * val isVerified = runWithRetry(
 * 	retries = 3,
 * 	retryDelay = 100.milliseconds.toJavaDuration(), // Wait 100ms before each retry
 * 	block = { runCatching { foo(...) } },
 * )
 * ```
 * - If `foo` always throws -> calls `foo` exactly 4 times (1 initial call + 3 retries), while waiting 100ms before each of the 3 retries
 * - If `foo` returns [Result.success] immediately -> calls `foo` exactly once with no additional delay
 */
tailrec fun <T> runWithRetry(
	retries: Int,
	retryDelay: Duration,
	onError: (Throwable) -> Unit = {},
	block: () -> Result<T>,
): Result<T> {
	if (retries < 0) return Result.failure(IllegalStateException("Ran out of retries"))

	val result = block()

	return if (result.isSuccess) {
		result
	} else {
		result.onFailure(onError)

		if (retries > 0) Thread.sleep(retryDelay)
		runWithRetry(retries - 1, retryDelay, onError, block)
	}
}

/**
 * Calculates backoff time with exponential increase and jitter
 */
private fun calculateBackoffTime(
	retryCount: Int,
	initialBackoffMs: Long,
	maxBackoffMs: Long,
	jitterFactor: Double,
): Long {
	val exponentialBackoff = (1L shl retryCount.coerceAtMost(10)) * initialBackoffMs
	val jitter = (exponentialBackoff * jitterFactor * (Random.nextDouble() * 2 - 1)).toLong()
	return (exponentialBackoff + jitter).coerceAtMost(maxBackoffMs)
}

/**
 * Runs [block] with exponential backoff retry strategy
 *
 * @param maxRetries Maximum number of retries
 * @param initialBackoffMs Initial backoff time in milliseconds
 * @param maxBackoffMs Maximum backoff time in milliseconds
 * @param jitterFactor Random jitter factor (0.0-1.0) to add to backoff time
 * @param errorParser Optional function to extract wait time from error messages
 */
tailrec fun <T> runWithExponentialBackoff(
	retryCount: Int = 0,
	maxRetries: Int,
	initialBackoffMs: Long = 1000,
	maxBackoffMs: Long = 60000,
	jitterFactor: Double = 0.15,
	errorParser: (Throwable) -> KotlinDuration? = { null },
	block: () -> Result<T>,
): Result<T> {
	if (retryCount > maxRetries) return block()

	val result = block()

	return if (result.isSuccess) {
		result
	} else {
		if (retryCount < maxRetries) {
			var backoffMs = calculateBackoffTime(retryCount, initialBackoffMs, maxBackoffMs, jitterFactor)

			// Try to extract wait time from error message if available
			result.exceptionOrNull()?.let { error ->
				errorParser(error)?.let { parsedDuration ->
					backoffMs = parsedDuration.inWholeMilliseconds
				}
			}

			Thread.sleep(backoffMs)

			runWithExponentialBackoff(
				retryCount + 1,
				maxRetries,
				initialBackoffMs,
				maxBackoffMs,
				jitterFactor,
				errorParser,
				block,
			)
		} else {
			result
		}
	}
}

/**
 * Suspending version of [runWithExponentialBackoff] for use in coroutines
 */
tailrec suspend fun <T> runWithExponentialBackoffSuspend(
	retryCount: Int = 0,
	maxRetries: Int,
	initialBackoffMs: Long = 1000,
	maxBackoffMs: Long = 60000,
	jitterFactor: Double = 0.15,
	errorParser: (Throwable) -> KotlinDuration? = { null },
	block: suspend () -> Result<T>,
): Result<T> {
	if (retryCount > maxRetries) return block()

	val result = block()

	return if (result.isSuccess) {
		result
	} else {
		if (retryCount < maxRetries) {
			var backoffMs = calculateBackoffTime(retryCount, initialBackoffMs, maxBackoffMs, jitterFactor)

			// Try to extract wait time from error message if available
			result.exceptionOrNull()?.let { error ->
				errorParser(error)?.let { parsedDuration ->
					backoffMs = parsedDuration.inWholeMilliseconds
				}
			}

			kotlinx.coroutines.delay(backoffMs)

			runWithExponentialBackoffSuspend(
				retryCount + 1,
				maxRetries,
				initialBackoffMs,
				maxBackoffMs,
				jitterFactor,
				errorParser,
				block,
			)
		} else {
			result
		}
	}
}

/**
 * Try to extract wait time from OpenAI rate limit error message
 * Example: "Please try again in 8.378s"
 */
fun extractOpenAIWaitTime(error: Throwable): KotlinDuration? {
	val errorMessage = error.message ?: return null

	val regex = """Please try again in (\d+\.?\d*)s""".toRegex()
	val matchResult = regex.find(errorMessage) ?: return null

	return try {
		val seconds = matchResult.groupValues[1].toDouble()
		// Add 500ms buffer to be safe
		(seconds * 1000 + 500).milliseconds
	} catch (e: Exception) {
		null
	}
}
