package com.cleevio.fatbot.application.common.validation

import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import org.springframework.util.StringUtils
import org.springframework.web.multipart.MultipartFile
import kotlin.reflect.KClass

@Constraint(validatedBy = [ValidFileValidator::class])
@Target(AnnotationTarget.FIELD, AnnotationTarget.TYPE, AnnotationTarget.ANNOTATION_CLASS)
@Retention(AnnotationRetention.RUNTIME)
annotation class ValidFile(
	val extensions: Array<String>,
	val message: String = "must be a valid file",
	val groups: Array<KClass<*>> = [],
	val payload: Array<KClass<out Payload>> = [],
)

@Constraint(validatedBy = [])
@Target(AnnotationTarget.FIELD, AnnotationTarget.TYPE)
@Retention(AnnotationRetention.RUNTIME)
@ValidFile(extensions = ["jpg", "jpeg", "png", "svg"], message = "must be a valid image")
annotation class ValidImage(
	val message: String = "must be a valid image",
	val groups: Array<KClass<*>> = [],
	val payload: Array<KClass<out Payload>> = [],
)

class ValidFileValidator : ConstraintValidator<ValidFile, MultipartFile> {
	private lateinit var validExtensions: List<String>

	override fun initialize(constraintAnnotation: ValidFile) {
		validExtensions = constraintAnnotation.extensions.toList()
		super.initialize(constraintAnnotation)
	}

	override fun isValid(value: MultipartFile?, context: ConstraintValidatorContext?): Boolean {
		if (value == null) return false
		val extension = StringUtils.getFilenameExtension(value.originalFilename) ?: return false
		return validExtensions.contains(extension)
	}
}
