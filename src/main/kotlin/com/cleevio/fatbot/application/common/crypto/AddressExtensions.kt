package com.cleevio.fatbot.application.common.crypto

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.O_X
import org.p2p.solanaj.core.PublicKey
import java.math.BigInteger

fun String.fromHexStringGetEvmAddressWrapper(): AddressWrapper {
	val removeZeroes = this.remove0x().trimStart('0')
	if (removeZeroes.isEmpty()) return AddressWrapper.EVM_NULL_ADDRESS

	val addressString = "$O_X${removeZeroes.padStart(40, '0')}"
	return AddressWrapper(addressString = addressString)
}

fun String.fromHexStringGetBigInteger(): BigInteger {
	val removeZeroes = this.remove0x().trimStart('0')
	if (removeZeroes.isEmpty()) return BigInteger.ZERO

	return BigInteger(removeZeroes, 16)
}

private const val CHARACTERS_COUNT_32_BYTES = 64

@OptIn(ExperimentalStdlibApi::class)
fun String.fromHexStringGetString(): String {
	// for now works only for return values with one value
	val chunks = this.remove0xAndSplitParameters()

	// val chunkWithOffset = chunks[0] <-- not used for now
	val chunkWithSize = chunks[1]
	val chunkWithValue = chunks[2]

	val outputSize = chunkWithSize.fromHexStringGetBigInteger().toInt() * 2
	return String(chunkWithValue.take(outputSize).hexToByteArray())
}

fun String.remove0xAndSplitParameters() = this.remove0x().chunked(CHARACTERS_COUNT_32_BYTES)
fun String.remove0x() = removePrefix(O_X)
fun String.padToDataPayloadLength() = padStart(64, '0')

fun PublicKey.toAddress() = AddressWrapper(this.toByteArray())
