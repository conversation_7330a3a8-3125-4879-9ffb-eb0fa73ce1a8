package com.cleevio.fatbot.application.common.crypto

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import org.p2p.solanaj.core.PublicKey
import java.math.BigDecimal
import java.math.BigInteger

object SolanaConstants {

	val WSOL_MINT = PublicKey("So********************************111111112")
	val WSOL_MINT_ADDRESS = AddressWrapper(WSOL_MINT.toByteArray())

	val TOKEN_PROGRAM_ID = PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA")
	val ASSOCIATED_TOKEN_PROGRAM_ID = PublicKey("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL")
	val SYSTEM_PROGRAM_ID = PublicKey("********************************")
	val TOKEN_2022_PROGRAM_ID = PublicKey("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb")
	val MEMO_RPGORAM = PublicKey("MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr")

	val PUMPFUN_PROGRAM = PublicKey("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P")
	val PUMPFUN_GLOBAL = PublicKey("4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf")
	val PUMPFUN_EVENT_AUTHORITY = PublicKey("Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1")
	val PUMPFUN_FEE_ACCOUNT = PublicKey("62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV")

	val PUMPFUN_GLOBAL_FEE_BP = BasisPoint.of(95)
	val PUMPFUN_GLOBAL_CREATOR_FEE_BP = BasisPoint.of(5)
	val PUMPFUN_GLOBAL_TOTAL_FEE_BP = PUMPFUN_GLOBAL_FEE_BP + PUMPFUN_GLOBAL_CREATOR_FEE_BP

	val PUMPSWAP_PROGRAM = PublicKey("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA")
	val PUMPSWAP_GLOBAL_CONFIG = PublicKey("ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw")
	val PUMPSWAP_FEE_ACCOUNT = PublicKey("AVmoTthdrX6tKt4nDjco2D775W2YK3sDhxPcMmzUAmTY")
	val PUMPSWAP_EVENT_AUTHORITY = PublicKey("GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR")

	val PUMPSWAP_LP_FEE_BP = BasisPoint.of(20)
	val PUMPSWAP_PROTOCOL_FEE_BP = BasisPoint.of(5)
	val PUMPSWAP_CREATOR_FEE_BP = BasisPoint.of(5)
	val PUMPSWAP_TOTAL_FEE_BP = PUMPSWAP_LP_FEE_BP + PUMPSWAP_PROTOCOL_FEE_BP + PUMPSWAP_CREATOR_FEE_BP

	val RAYDIUM_AMM_PROGRAM_ID = PublicKey("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8")
	val RAYDIUM_AMM_AUTHORITY = PublicKey("5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1")
	val RAYDIUM_AMM_LP_FEE_BP = BasisPoint.of(25)

	val RAYDIUM_CLMM_PROGRAM_ID = PublicKey("CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK")

	val RAYDIUM_CPMM_PROGRAM_ID = PublicKey("CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C")
	val RAYDIUM_CPMM_AUTHORITY = PublicKey("GpMZbSM2GgvTKHJirzeGfMFoaZ8UR2X7F4v8vHTvxFbL")

	// TODO: Extract to env variables
	val FATBOT_ROUTER_PROGRAM_ID = PublicKey("fat2dUTkypDNDT86LtLGmzJDK11FSJ72gfUW35igk7u")

	// Jito
	val JITO_TIP_1_ACCOUNT = PublicKey("96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5")
	val JITO_MIN_TIP = BaseAmount(BigInteger("1000"))

	val MINIMUM_RENT_THRESHOLD = BaseAmount(BigInteger("890880"))

	// CU Limits
	const val CU_LIMIT_CURRENCY_TRANSFER = 10_000
	const val CU_LIMIT_TOKEN_TRANSFER = 35_000

	const val CU_LIMIT_BUY_PUMPFUN = 175_000
	const val CU_LIMIT_BUY_PUMPSWAP = 250_000
	const val CU_LIMIT_BUY_RAYDIUM = 250_000

	const val CU_LIMIT_SELL_PUMPFUN = 110_000
	const val CU_LIMIT_SELL_PUMPSWAP = 250_000
	const val CU_LIMIT_SELL_RAYDIUM = 250_000

	const val STAKED_ENDPOINT_BUY_CU_PRICE_MULTIPLIER = 10
	const val STAKED_ENDPOINT_SELL_CU_PRICE_MULTIPLIER = 5

	const val AMOUNT_OF_LAMPORTS_PER_SIGNATURE = 5_000L

	/*
		Computed as signature price + priority fee price

		Signature price: 5000 lamports
		Priority fee price: CU_limit * CU_price (10_000) in micro lamports * bot multiplier * safety overhead
	 */
	val BOT_BUY_FEE_FREEZE_BASE_AMOUNT = (
		BigInteger.valueOf(AMOUNT_OF_LAMPORTS_PER_SIGNATURE) +
			(
				BigDecimal.valueOf(CU_LIMIT_BUY_PUMPFUN.toLong()) *
					BigDecimal.valueOf(STAKED_ENDPOINT_BUY_CU_PRICE_MULTIPLIER.toLong()) *
					BigDecimal.valueOf(0.01) * // 10_000 in microlamports
					BigDecimal.valueOf(2) // safety overhead
				).toBigInteger()
		).asBaseAmount()

	val BOT_BUY_RENT_BASE_AMOUNT = BaseAmount(BigInteger("2039280"))

	val BOT_SELL_FEE_FREEZE_BASE_AMOUNT = (
		BigInteger.valueOf(AMOUNT_OF_LAMPORTS_PER_SIGNATURE) +
			(
				BigDecimal.valueOf(CU_LIMIT_SELL_PUMPFUN.toLong()) *
					BigDecimal.valueOf(STAKED_ENDPOINT_SELL_CU_PRICE_MULTIPLIER.toLong()) *
					BigDecimal.valueOf(0.01) * // 10_000 in microlamports
					BigDecimal.valueOf(2) // safety overhead
				).toBigInteger()
		).asBaseAmount()
}
