package com.cleevio.fatbot.application.common.util

import com.cleevio.fatbot.application.common.crypto.SolanaConstants.ASSOCIATED_TOKEN_PROGRAM_ID
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.TOKEN_2022_PROGRAM_ID
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.TOKEN_PROGRAM_ID
import org.bouncycastle.jcajce.provider.digest.SHA256
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.types.AccountInfo
import java.util.Base64

fun getFunctionDiscriminator(functionName: String): ByteArray {
	val sha256 = SHA256.Digest()
	val hash = sha256.digest(functionName.toByteArray())

	return hash.sliceArray(0..7) // First 8 bytes
}

fun getAssociatedTokenAddress(
	mint: <PERSON><PERSON><PERSON>,
	owner: <PERSON><PERSON><PERSON>,
	programId: PublicKey = TOKEN_PROGRAM_ID,
	associatedTokenProgramId: PublicKey = ASSOCIATED_TOKEN_PROGRAM_ID,
): PublicKey {
	val pda = PublicKey.findProgramAddress(
		listOf(owner.toByteArray(), programId.toByteArray(), mint.toByteArray()),
		associatedTokenProgramId,
	)

	return pda.address
}

data class AssociatedTokenAddressPair(
	val tokenAddress: PublicKey,
	val token2022Address: PublicKey,
)

/**
 * Returns the address of associated token account for both [TOKEN_PROGRAM_ID] and [TOKEN_2022_PROGRAM_ID]
 */
fun getAssociatedTokenAddressPair(
	mint: PublicKey,
	owner: PublicKey,
	associatedTokenProgramId: PublicKey = ASSOCIATED_TOKEN_PROGRAM_ID,
): AssociatedTokenAddressPair {
	val tokenPda = getAssociatedTokenAddress(
		mint = mint,
		owner = owner,
		programId = TOKEN_PROGRAM_ID,
		associatedTokenProgramId = associatedTokenProgramId,
	)

	val token2022Pda = getAssociatedTokenAddress(
		mint = mint,
		owner = owner,
		programId = TOKEN_2022_PROGRAM_ID,
		associatedTokenProgramId = associatedTokenProgramId,
	)

	return AssociatedTokenAddressPair(tokenAddress = tokenPda, token2022Address = token2022Pda)
}

fun AccountInfo.Value.decodeData(): ByteArray {
	check(data[1] == "base64") { "Invalid encoding scheme ${data[1]}, expected base64!" }

	return Base64.getDecoder().decode(data.first())
}
