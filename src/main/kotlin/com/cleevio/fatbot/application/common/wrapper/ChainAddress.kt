package com.cleevio.fatbot.application.common.wrapper

import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.wallet.constant.Chain

data class ChainAddress(
	val chain: Chain,
	val address: AddressWrapper,
)

fun AddressWrapper.toChainAddress(chain: Chain) = ChainAddress(chain = chain, address = this)

fun Set<ChainAddress>.assertAllChainsSupported(activeChains: Set<Chain>) {
	val presentChains = mapToSet { it.chain }
	val unsupportedChains = presentChains - activeChains
	if (unsupportedChains.isNotEmpty()) error("Detected unsupported chains $unsupportedChains")
}
