package com.cleevio.fatbot.application.common.converter

import jakarta.persistence.AttributeConverter
import jakarta.persistence.Converter
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec

@Converter
@Component
class PrivateKeyConverter(
	@Value("\${security.wallet-encryption-key}") private val key: String,
) : AttributeConverter<String, String> {

	private val algorithm = "AES"

	override fun convertToDatabaseColumn(attribute: String): String {
		val cipher = Cipher.getInstance(algorithm)
		cipher.init(Cipher.ENCRYPT_MODE, getKeyFromString(key))
		val encryptedBytes = cipher.doFinal(attribute.toByteArray())
		return Base64.getEncoder().encodeToString(encryptedBytes)
	}

	override fun convertToEntityAttribute(dbData: String): String {
		val cipher = Cipher.getInstance(algorithm)
		cipher.init(Cipher.DECRYPT_MODE, getKeyFromString(key))
		val decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(dbData))
		return String(decryptedBytes)
	}

	// Convert a string to a SecretKey
	private fun getKeyFromString(keyStr: String): SecretKey {
		val decodedKey: ByteArray = Base64.getDecoder().decode(keyStr)
		return SecretKeySpec(decodedKey, 0, decodedKey.size, algorithm)
	}
}
