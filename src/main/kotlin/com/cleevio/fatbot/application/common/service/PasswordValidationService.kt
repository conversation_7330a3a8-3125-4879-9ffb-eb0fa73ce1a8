package com.cleevio.fatbot.application.common.service

import com.cleevio.fatbot.application.common.port.out.FirebaseAuth
import com.cleevio.fatbot.application.common.service.command.VerifyPasswordCommand
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import org.springframework.stereotype.Service

@Service
class PasswordValidationService(
	private val firebaseUserFinderService: FirebaseUserFinderService,
	private val firebaseAuth: FirebaseAuth,
) {

	operator fun invoke(command: VerifyPasswordCommand) {
		val userEmail = firebaseUserFinderService.getById(command.userId).email
		firebaseAuth.verifyPassword(email = userEmail, password = command.password)
	}
}
