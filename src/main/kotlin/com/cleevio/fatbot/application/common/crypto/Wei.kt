package com.cleevio.fatbot.application.common.crypto

import org.web3j.utils.Convert
import java.math.BigInteger

@JvmInline
value class Wei(val amount: BigInteger)

fun BigInteger.asWei() = Wei(this)
fun BigInteger.toEth() = Convert.fromWei(
	this.toBigDecimal(),
	Convert.Unit.ETHER,
)
fun Long.asWei() = Wei(this.toBigInteger())
fun Int.asWei() = Wei(this.toBigInteger())

fun Wei.toEth() = Convert.fromWei(
	this.amount.toBigDecimal(),
	Convert.Unit.ETHER,
)
