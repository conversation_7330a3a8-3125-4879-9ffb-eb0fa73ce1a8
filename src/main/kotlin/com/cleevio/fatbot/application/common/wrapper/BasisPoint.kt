package com.cleevio.fatbot.application.common.wrapper

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import java.math.BigDecimal
import java.math.BigInteger
import java.math.RoundingMode

/**
 * A wrapper class for representing percentages as **basis points (bps)**.
 *
 * One basis point is equal to 0.01% (1/100th of a percentage point). This class provides
 * a type-safe way to represent and manipulate percentage values without ambiguity.
 * It uses integer values internally to maintain precision, where `100 bps` equals `1%`.
 */
@JvmInline
value class BasisPoint private constructor(val value: Int) {
	companion object {
		private const val MIN_VALUE = 0
		const val MAX_VALUE = 10_000 // 100% in basis points
		val ZERO = this.of(0)

		/**
		 * Factory method to create a BasisPoint, validating the input.
		 */
		fun of(value: Int): BasisPoint {
			require(value in MIN_VALUE..MAX_VALUE) {
				"BasisPoint value must be between $MIN_VALUE and $MAX_VALUE (inclusive), but was $value"
			}
			return BasisPoint(value)
		}

		/**
		 * Factory method to create a BasisPoint from decimal by flooring the value.
		 */
		fun of(value: BigDecimal): BasisPoint {
			val intValue = value.setScale(0, RoundingMode.FLOOR).toInt()

			return of(intValue)
		}

		/**
		 * Factory method to create a BasisPoint from percentage value.
		 *
		 * E.g.:
		 * ```
		 * 	1% 		 => BasisPoint.ofPercentage(1) = BasisPoint(100)
		 * 	2.5% 	 => BasisPoint.ofPercentage(2.5) = BasisPoint(250)
		 * 	3.1159% => BasisPoint.ofPercentage(3.1159) = BasisPoint(311)
		 * 	```
		 */
		fun ofPercentage(value: BigDecimal): BasisPoint {
			return of(value * BigDecimal(100))
		}

		fun ofPercentage(value: Double): BasisPoint {
			return ofPercentage(value.toBigDecimal())
		}

		/**
		 * Factory method to create a BasisPoint from fraction value.
		 *
		 * E.g.:
		 * ```
		 * 	0.01 (1%)		  => BasisPoint(100)
		 * 	0.025 (2.5%) 	  => BasisPoint(250)
		 * 	0.031159 (3.1159%) => BasisPoint(311)
		 * 	```
		 */
		fun ofFraction(value: BigDecimal): BasisPoint {
			return of(value * MAX_VALUE.toBigDecimal())
		}
	}

	val fraction: BigDecimal
		get() = value.toBigDecimal().divide(MAX_VALUE.toBigDecimal())

	/**
	 * Calculates proportional value of [value] in respect to the basis points stored.
	 */
	fun applyOn(value: BigInteger): BigInteger {
		val basisPoints = this.toBigInteger()
		val proportionalValue = value.times(basisPoints).div(MAX_VALUE.toBigInteger())

		return proportionalValue
	}

	/**
	 * Calculates proportional value of [value] in respect to the basis points stored using ceilDiv.
	 */
	fun applyOnCeilDiv(value: BigInteger): BigInteger {
		val basisPoints = this.toBigInteger()

		val proportionalValue = value.times(basisPoints).ceilDiv(MAX_VALUE.toBigInteger())

		return proportionalValue
	}

	fun applyOn(value: BaseAmount): BaseAmount = applyOn(value.amount).asBaseAmount()

	fun applyOn(value: Int): Int = applyOn(value.toBigInteger()).toInt()

	fun toBigInteger(): BigInteger = value.toBigInteger()
	fun toLong(): Long = value.toLong()

	operator fun plus(other: BasisPoint) = of(this.value + other.value)
}

private fun BigInteger.ceilDiv(other: BigInteger) = this.add(other.dec()).divide(other)
