package com.cleevio.fatbot.application.common.util

import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.SharedFlow

/**
 * Facilitates [SharedFlow.collect] by wrapping [collector] calls with a `try-catch` block
 */
suspend fun <T> SharedFlow<T>.collectCatching(exceptionHandler: (Throwable) -> Unit, collector: (T) -> Unit) {
	collect {
		try {
			collector(it)
		} catch (e: Throwable) {
			exceptionHandler(e)
		}
	}
}

/**
 * Receives up to `maxSize` items from channel. Might return list with fewer elements than `maxSize` or empty list
 * if there is not enough items in channel.
 */
fun <T : Any> Channel<T>.receiveChunk(maxSize: Int): List<T> {
	val sequence = generateSequence { this.tryReceive().getOrNull() }
	return sequence.take(maxSize).toList()
}
