package com.cleevio.fatbot.application.common.validation

import com.cleevio.fatbot.application.common.crypto.isValidEthereumPrivateKey
import com.cleevio.fatbot.application.common.crypto.isValidSolanaPrivateKey
import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import kotlin.reflect.KClass

@Constraint(validatedBy = [ValidPrivateKeyValidator::class])
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class ValidPrivateKey(
	val message: String = "must be a valid Ethereum private key",
	// TODO: for POC purposes, display ETH private key message
	// val message: String = "must be a valid Ethereum or Solana private key",
	val groups: Array<KClass<*>> = [],
	val payload: Array<KClass<out Payload>> = [],
)

class ValidPrivateKeyValidator : ConstraintValidator<ValidPrivateKey?, String?> {
	override fun isValid(privateKey: String?, context: ConstraintValidatorContext): Boolean {
		if (privateKey.isNullOrBlank()) return false

		return privateKey.isValidEthereumPrivateKey() || privateKey.isValidSolanaPrivateKey()
	}
}
