package com.cleevio.fatbot.application.common.validation

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import kotlin.reflect.KClass

@Constraint(validatedBy = [ValidSolanaAddressValidator::class])
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class ValidSolanaAddress(
	val message: String = "is not an Solana compatible wallet address, you can lose your funds!",
	val groups: Array<KClass<*>> = [],
	val payload: Array<KClass<out Payload>> = [],
)

class ValidSolanaAddressValidator : ConstraintValidator<ValidSolanaAddress?, AddressWrapper?> {
	override fun isValid(value: AddressWrapper?, context: ConstraintValidatorContext?): Boolean {
		return value?.isSolana() ?: false
	}
}
