package com.cleevio.fatbot.application.common.validation

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import kotlin.reflect.KClass

@Constraint(validatedBy = [ValidEvmAddressValidator::class])
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class ValidEvmAddress(
	val message: String = "is not an EVM compatible wallet address, you can lose your funds!",
	val groups: Array<KClass<*>> = [],
	val payload: Array<KClass<out Payload>> = [],
)

@Constraint(validatedBy = [ValidEvmAddressOrNullValidator::class])
@Target(AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
annotation class ValidEvmAddressOrNull(
	val message: String = "is not an EVM compatible wallet address (or null), you can lose your funds!",
	val groups: Array<KClass<*>> = [],
	val payload: Array<KClass<out Payload>> = [],
)

class ValidEvmAddressValidator : ConstraintValidator<ValidEvmAddress?, AddressWrapper?> {
	override fun isValid(value: AddressWrapper?, context: ConstraintValidatorContext?): Boolean {
		return value?.isEvm() ?: false
	}
}

class ValidEvmAddressOrNullValidator : ConstraintValidator<ValidEvmAddressOrNull?, AddressWrapper?> {
	override fun isValid(value: AddressWrapper?, context: ConstraintValidatorContext?): Boolean {
		return value?.isEvm() ?: true
	}
}
