package com.cleevio.fatbot.application.common.service

import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

/**
 * To be used when a manual trigger of [Valid] is needed.
 */
@Service
@Validated
class ValidationService {

	/**
	 * Receiving [target] as a [Map] allows us to express the name of the object as a key:
	 *
	 * `validate.target[{key}].{property}...` instead of `validate.target.{property}...`.
	 *
	 * I.e.: `validate.target[botUpdateRequest].sellCountTo...` instead of `validate.target.sellCountTo...`
	 */
	fun <T : Any> validate(@Valid target: Map<String, T>) {}
}
