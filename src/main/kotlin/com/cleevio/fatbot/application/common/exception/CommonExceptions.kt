package com.cleevio.fatbot.application.common.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.FORBIDDEN)
class InvalidPasswordException : FatbotApiException(
	reason = ExtendedErrorReasonType.INVALID_PASSWORD,
	message = "Invalid password.",
)

@ResponseStatus(HttpStatus.TOO_MANY_REQUESTS)
class TooManyAttemptsException : FatbotApiException(
	reason = ExtendedErrorReasonType.TOO_MANY_ATTEMPTS,
	message = "Too many attempts.",
)
