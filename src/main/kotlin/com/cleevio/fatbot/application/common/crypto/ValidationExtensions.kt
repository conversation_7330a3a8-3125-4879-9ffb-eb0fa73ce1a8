package com.cleevio.fatbot.application.common.crypto

import com.cleevio.fatbot.application.module.wallet.constant.O_X
import org.bitcoinj.core.Base58
import org.web3j.crypto.WalletUtils

fun String.isValidEthereumAddress(): Boolean = this.matches(ETHEREUM_ADDRESS_REGEX) && WalletUtils.isValidAddress(this)

fun String.isValidEthereumPrivateKey(): Boolean =
	this.removePrefix(O_X).matches(ETHEREUM_PRIVATE_KEY_REGEX) && WalletUtils.isValidPrivateKey(this)

fun String.isValidSolanaAddress(): Boolean {
	if (!BASE58_REGEX.matches(this)) return false

	return try {
		Base58.decode(this).size == 32
	} catch (ex: Exception) {
		false
	}
}

fun String.isValidSolanaPrivateKey(): Boolean {
	if (!BASE58_REGEX.matches(this)) return false

	return try {
		Base58.decode(this).size == 64
	} catch (ex: Exception) {
		false
	}
}

private val ETHEREUM_ADDRESS_REGEX = "^0x[a-fA-F0-9]{40}$".toRegex()
private val ETHEREUM_PRIVATE_KEY_REGEX = "^[0-9a-fA-F]{64}$".toRegex()
private val BASE58_REGEX = "^[**********************************************************]+$".toRegex()
