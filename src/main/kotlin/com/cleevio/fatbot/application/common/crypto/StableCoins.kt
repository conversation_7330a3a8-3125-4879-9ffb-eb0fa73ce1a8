package com.cleevio.fatbot.application.common.crypto

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.wallet.constant.Chain

data class StableCoin(
	val token: ChainAddress,
	val pairAddress: AddressWrapper,
	val shouldSwapQuoteAndBaseToken: <PERSON><PERSON><PERSON>,
)

val STABLE_USD_PEGGED_TOKENS = listOf(
	StableCoin(
		token = ChainAddress(
			chain = Chain.SOLANA,
			address = AddressWrapper("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"),
		),
		pairAddress = AddressWrapper("CYbD9RaToYMtWKA7QZyoLahnHdWq553Vm62Lh6qWtuxq"),
		shouldSwapQuoteAndBaseToken = true,
	),
	StableCoin(
		token = ChainAddress(
			chain = Chain.EVM_MAINNET,
			address = AddressWrapper("******************************************"),
		),
		pairAddress = AddressWrapper("******************************************"),
		shouldSwapQuoteAndBaseToken = true,
	),
	StableCoin(
		token = ChainAddress(
			chain = Chain.EVM_BSC,
			address = AddressWrapper("******************************************"),
		),
		pairAddress = AddressWrapper("******************************************"),
		shouldSwapQuoteAndBaseToken = true,
	),
	StableCoin(
		token = ChainAddress(
			chain = Chain.EVM_BASE,
			address = AddressWrapper("0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913"),
		),
		pairAddress = AddressWrapper("0xd0b53D9277642d899DF5C87A3966A349A798F224"),
		shouldSwapQuoteAndBaseToken = true,
	),
	StableCoin(
		token = ChainAddress(
			chain = Chain.EVM_ARBITRUM_ONE,
			address = AddressWrapper("0xaf88d065e77c8cC2239327C5EDb3A432268e5831"),
		),
		pairAddress = AddressWrapper("0xC6962004f452bE9203591991D15f6b388e09E8D0"),
		shouldSwapQuoteAndBaseToken = false,
	),
)

val stableUsdPeggedTokens = STABLE_USD_PEGGED_TOKENS.map { it.token }
fun ChainAddress.isStableCoin() = this in stableUsdPeggedTokens
