package com.cleevio.fatbot.application.common.util

import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters

/**
 * Perform truncation by [seconds]
 *
 * When [seconds] is equivalent to some [java.time.temporal.ChronoUnit] (i.e. [java.time.temporal.ChronoUnit.MINUTES])
 * this behaves same as [Instant.truncatedTo] of that unit
 *
 * ```
 * 2025-01-09T16:11:03Z by 5 seconds -> 2025-01-09T16:11:00Z
 * 2025-01-09T16:11:07Z by 5 seconds -> 2025-01-09T16:11:05Z
 * 2025-01-09T16:11:07Z by 60 seconds -> 2025-01-09T16:11:00Z
 * 2025-01-09T16:22:07Z by 900 seconds (15 minutes) -> 2025-01-09T16:15:00Z
 * ```
 */
fun Instant.truncateBySeconds(seconds: Long): Instant = Instant.ofEpochSecond(epochSecond - (epochSecond % (seconds)))

/**
 * Returns the Monday and Sunday of the week containing the provided date
 * @param date The reference date
 * @return Pair of dates where first is Monday and second is Sunday
 */
fun LocalDate.getWeekBoundaries(): Pair<LocalDate, LocalDate> {
	val monday = this.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
	val sunday = this.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY))
	return Pair(monday, sunday)
}

fun Instant.plusDays(days: Long): Instant = this.plusSeconds(days * 24 * 60 * 60)

fun Instant.plusMinutes(minutes: Long): Instant = this.plusSeconds(minutes * 60)

fun Instant.plusMonths(months: Long): Instant = this.atZone(ZoneOffset.UTC)
	.plusMonths(months)
	.toInstant()

fun Instant.getMinutesInNextMonths(months: Long): Long {
	return ChronoUnit.MINUTES.between(this, this.plusMonths(months))
}

fun LocalDate.atStartOfNextDay() = this.plusDays(1).atStartOfDay().toInstant(ZoneOffset.UTC)
