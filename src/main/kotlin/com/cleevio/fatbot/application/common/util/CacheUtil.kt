package com.cleevio.fatbot.application.common.util

import com.cleevio.fatbot.application.module.token.constant.TimeInterval
import com.github.benmanes.caffeine.cache.Ticker
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.nanoseconds
import kotlin.time.Duration.Companion.seconds

class EpochClockTicker(private val clock: Clock) : Ticker {
	override fun read(): Long {
		val instant = clock.instant()

		return instant.epochSecond.seconds.inWholeNanoseconds + instant.nano
	}
}

fun calculateNumberOfNanosUntilEndOfMinute(currentTime: Long): Long {
	val now = Instant.ofEpochMilli(currentTime.nanoseconds.inWholeMilliseconds)
	val expiresAt = now.truncatedTo(ChronoUnit.MINUTES).plus(1, ChronoUnit.MINUTES)
	return expiresAt.toEpochMilli().milliseconds.inWholeNanoseconds - currentTime
}

fun calculateExpiresAtInNanos(nowInNanos: Long, timeInterval: TimeInterval, intervalCount: Int): Long {
	val now = Instant.ofEpochMilli(nowInNanos.nanoseconds.inWholeMilliseconds)

	val intervalPeriodSeconds = timeInterval.unit.duration.seconds * intervalCount
	val subtractMillis = if (timeInterval == TimeInterval.DAY) 1L else 0L

	val expiresAt = now
		.truncateBySeconds(intervalPeriodSeconds)
		.plusSeconds(intervalPeriodSeconds)
		.minusMillis(subtractMillis)

	return expiresAt.toEpochMilli().milliseconds.inWholeNanoseconds - nowInNanos
}
