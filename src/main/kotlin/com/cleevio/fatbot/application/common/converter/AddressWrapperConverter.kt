package com.cleevio.fatbot.application.common.converter

import com.cleevio.fatbot.application.common.converter.AddressWrapperHibernateConverter.convertToDatabaseColumn
import com.cleevio.fatbot.application.common.converter.AddressWrapperHibernateConverter.convertToEntityAttribute
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import jakarta.persistence.AttributeConverter
import jakarta.persistence.Converter
import org.jooq.Converter as JooqConverter

@Converter
object AddressWrapperHibernateConverter : AttributeConverter<AddressWrapper?, ByteArray?> {

	override fun convertToDatabaseColumn(attribute: AddressWrapper?): ByteArray? {
		return attribute?.getAddressBytes()
	}

	override fun convertToEntityAttribute(dbData: ByteArray?): AddressWrapper? {
		return dbData?.let { AddressWrapper(addressBytes = it) }
	}
}

class AddressWrapperJooqConverter : JooqConverter<ByteArray, AddressWrapper> {

	override fun from(bytes: ByteArray?): AddressWrapper? {
		return convertToEntityAttribute(bytes)
	}

	override fun to(addressWrapper: AddressWrapper?): ByteArray? {
		return convertToDatabaseColumn(addressWrapper)
	}

	override fun fromType() = ByteArray::class.java
	override fun toType() = AddressWrapper::class.java
}
