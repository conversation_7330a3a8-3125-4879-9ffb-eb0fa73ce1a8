package com.cleevio.fatbot.application.common.crypto

import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.math.BigInteger
import java.math.MathContext
import java.math.RoundingMode

@JvmInline
value class BaseAmount(val amount: BigInteger) {
	companion object {
		val ZERO = BaseAmount(BigInteger.ZERO)
		val ONE = BaseAmount(BigInteger.ONE)
		val TEN = BaseAmount(BigInteger.TEN)
	}
}

fun BigInteger.asBaseAmount() = BaseAmount(this)

fun BaseAmount.toNative(chain: Chain): NativeAmount = toNative(chain.currency.decimals)
fun BaseAmount.toNative(decimals: Int): NativeAmount {
	val native = BigDecimal(amount, decimals)
	return NativeAmount(native)
}

operator fun BaseAmount.plus(other: BaseAmount) = BaseAmount(this.amount + other.amount)
operator fun BaseAmount.minus(other: BaseAmount) = BaseAmount(this.amount - other.amount)
operator fun BaseAmount.times(other: BaseAmount) = this.amount.multiply(other.amount).asBaseAmount()
operator fun BaseAmount.compareTo(other: BaseAmount) = this.amount.compareTo(other.amount)
operator fun BaseAmount.compareTo(other: BigInteger) = this.amount.compareTo(other)

/**
 * Subtracts [basisPoints] proportional value from the receiver [this].
 *
 * I.e.
 * ```
 * val amount = BaseAmount(100)
 * val bp = BasisPoint(1000) // 10%
 * val result = amount.minusBasisPoints(bp)
 * assert(result == BaseAmount(90))
 * ```
 */
fun BaseAmount.minusBasisPoints(basisPoints: BasisPoint) = this - basisPoints.applyOn(this)

fun Iterable<BaseAmount>.sum() = this.sumOf { it.amount }.asBaseAmount()

@JvmInline
value class NativeAmount(val amount: BigDecimal) {
	companion object {
		val ZERO = NativeAmount(BigDecimal.ZERO)
		val ONE = NativeAmount(BigDecimal.ONE)
	}
}

fun BigDecimal.asNativeAmount() = NativeAmount(this)

fun NativeAmount.toBase(chain: Chain): BaseAmount = toBase(chain.currency.decimals)
fun NativeAmount.toBase(decimals: Int): BaseAmount {
	val base = this
		.amount
		.setScale(decimals, RoundingMode.FLOOR) // Drop any decimals beyond the decimals needed
		.scaleByPowerOfTen(decimals)

	return BaseAmount(base.toBigInteger())
}

operator fun NativeAmount.plus(other: NativeAmount) = NativeAmount(this.amount + other.amount)
operator fun NativeAmount.minus(other: NativeAmount) = NativeAmount(this.amount - other.amount)
operator fun NativeAmount.compareTo(other: NativeAmount) = this.amount.compareTo(other.amount)

operator fun NativeAmount.times(other: NativeAmount) =
	this.amount.multiply(other.amount, MathContext.DECIMAL128).asNativeAmount()
operator fun NativeAmount.times(other: BigDecimal) =
	this.amount.multiply(other, MathContext.DECIMAL128).asNativeAmount()

operator fun NativeAmount.div(other: NativeAmount) =
	this.amount.divide(other.amount, MathContext.DECIMAL128).asNativeAmount()
operator fun NativeAmount.div(other: BigDecimal) = this.amount.divide(other, MathContext.DECIMAL128).asNativeAmount()
