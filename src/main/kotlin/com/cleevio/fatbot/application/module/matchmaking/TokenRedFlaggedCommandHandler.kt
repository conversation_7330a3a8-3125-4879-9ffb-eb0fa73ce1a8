package com.cleevio.fatbot.application.module.matchmaking

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.bot.BotFinderService
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.botmarket.finder.BotMarketPositionFinderService
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.matchmaking.command.TokenRedFlaggedCommand
import com.cleevio.fatbot.application.module.matchmaking.event.BotSellRedFlaggedTokenRequest
import com.cleevio.fatbot.application.module.matchmaking.event.TradedBotMarketPositionsEvent
import com.cleevio.fatbot.application.module.matchmaking.locks.MATCH_BOTS
import com.cleevio.fatbot.application.module.matchmaking.locks.MATCH_MAKING_MODULE
import com.cleevio.fatbot.application.module.matchmaking.port.out.FindAllRedFlaggedBotMarketPositionIds
import com.cleevio.fatbot.application.module.matchmaking.port.out.SendBotTransaction
import com.cleevio.fatbot.application.module.matchmaking.port.out.UpsertBotMarketPositionTradeState
import com.cleevio.fatbot.application.module.token.finder.BotTokenInfoFinderService
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateType
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
class TokenRedFlaggedCommandHandler(
	private val findAllRedFlaggedBotMarketPositionIds: FindAllRedFlaggedBotMarketPositionIds,
	private val botTokenInfoFinderService: BotTokenInfoFinderService,
	private val botMarketPositionFinderService: BotMarketPositionFinderService,
	private val botWalletFinderService: BotWalletFinderService,
	private val botFinderService: BotFinderService,
	private val sendBotTransaction: SendBotTransaction,
	private val upsertBotMarketPositionTradeState: UpsertBotMarketPositionTradeState,
) : CommandHandler<Unit, TokenRedFlaggedCommand> {

	private val logger = logger()
	override val command = TokenRedFlaggedCommand::class

	@Autowired
	@Lazy
	private lateinit var self: TokenRedFlaggedCommandHandler

	@SentrySpan
	override fun handle(command: TokenRedFlaggedCommand) {
		logger.info("Token ${command.tokenAddress} received redFlag: ${command.type} on block: ${command.blockSlot}")
		val marketPositionIdsHoldingRedFlagToken =
			findAllRedFlaggedBotMarketPositionIds(tokenAddress = command.tokenAddress, type = command.type)

		if (marketPositionIdsHoldingRedFlagToken.isEmpty()) return

		self.sellRedFlaggedToken(
			toSellMarketPositionIds = marketPositionIdsHoldingRedFlagToken,
			tokenTradeState = command,
		)
	}

	// Warning: This function acquires lock that blocks whole bot trading to not create conflict, so make sure it's fast
	@SentrySpan
	@Transactional(transactionManager = "matchMakingTransactionManager")
	@Lock(module = MATCH_MAKING_MODULE, lockName = MATCH_BOTS)
	fun sellRedFlaggedToken(toSellMarketPositionIds: List<UUID>, tokenTradeState: TokenRedFlaggedCommand) {
		val positionsToSell = botMarketPositionFinderService.getAllByIds(ids = toSellMarketPositionIds.toSet())

		// we have to recheck under lock if they are still open
		val openPositions = positionsToSell
			.filter { it.state == BotMarketPositionState.OPENED }
			// sorted so leader bots will sell before followers (their positions are created first when buying)
			.sortedBy { it.id }

		val botWalletIds = openPositions.mapToSet { it.botWalletId }
		val botWalletIdToWallet = botWalletFinderService.getAllByIds(ids = botWalletIds).associateBy { it.id }

		val botIds = botWalletIdToWallet.values.mapToSet { it.botId }
		val botIdToBot = botFinderService.getAllByIds(ids = botIds).associateBy { it.id }

		val tradedPositions = openPositions.map { position ->
			position.closePositionAsRedFlagged()

			val botWallet = botWalletIdToWallet.getValue(position.botWalletId)
			val bot = botIdToBot.getValue(botWallet.botId)

			sendBotTransaction(
				BotSellRedFlaggedTokenRequest(
					blockSlot = tokenTradeState.blockSlot,
					botWalletId = botWallet.id,
					botId = botWallet.botId,
					userId = bot.userId,
					tokenAddress = position.tokenAddress,
					creator = tokenTradeState.creator,
					privateKey = botWallet.privateKey,
					chain = botWallet.chain,
					amountToSell = position.totalTokenAmountBought,
				),
			)

			TradedBotMarketPositionsEvent.TradedPosition(
				botMarketPositionId = position.id,
				type = BotMarketPositionTradeStateType.SELL,
				marketCapUsd = tokenTradeState.marketCapUsd,
				liquidityUsd = tokenTradeState.liquidityUsd,
				volumeUsd = tokenTradeState.volumeUsd,
				numOfAccountHolders = tokenTradeState.numOfAccountHolders,
				buyVolume = tokenTradeState.buyVolume,
				sellVolume = tokenTradeState.sellVolume,
				fractionOfSellTransactions = tokenTradeState.fractionOfSellTransactions,
			)
		}

		upsertBotMarketPositionTradeState(tradedPositions = TradedBotMarketPositionsEvent(positions = tradedPositions))
	}
}
