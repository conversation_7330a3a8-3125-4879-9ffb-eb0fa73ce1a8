package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import java.math.BigInteger

interface GetTokenDecimals {
	operator fun invoke(token: ChainAddress): BigInteger
	fun supports(): ChainType
}

// Helper function so we can call invoke on multiple GetTokenDecimals instances, and find
// which one to use based on chain on which token is
operator fun List<GetTokenDecimals>.invoke(token: ChainAddress) = this
	.find { it.supports() == token.chain.type }
	?.invoke(token)
	?: error("Getting of token decimals is not supported for chainType ${token.chain.type}")
