package com.cleevio.fatbot.application.module.wallet.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.domain.walletposition.WalletCurrencyPosition
import com.cleevio.fatbot.domain.walletposition.WalletCurrencyPositionRepository
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class WalletCurrencyPositionFinderService(
	private val walletCurrencyPositionRepository: WalletCurrencyPositionRepository,
) : BaseFinderService<WalletCurrencyPosition>(walletCurrencyPositionRepository) {
	override fun errorBlock(message: String) = error(message)

	override fun getEntityType() = WalletCurrencyPosition::class

	fun findLatestPositionByWalletId(walletId: UUID): WalletCurrencyPosition? =
		walletCurrencyPositionRepository.findFirstByWalletIdOrderByCreatedAtDesc(walletId)
}
