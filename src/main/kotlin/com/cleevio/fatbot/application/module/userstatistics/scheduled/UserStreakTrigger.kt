package com.cleevio.fatbot.application.module.userstatistics.scheduled

import com.cleevio.fatbot.application.module.userstatistics.locks.RESET_USER_DAYS_IN_STREAK
import com.cleevio.fatbot.domain.userstatistics.UserStatisticUpdateService
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Clock
import java.time.LocalDate

@Component
class UserStreakTrigger(
	private val userStatisticUpdateService: UserStatisticUpdateService,
	private val clock: Clock,
) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.reset-user-streak")
	@Scheduled(cron = "\${streak-system.reset.cron}")
	@SchedulerLock(
		name = RESET_USER_DAYS_IN_STREAK,
		lockAtMostFor = "\${streak-system.reset.lock-for}",
		lockAtLeastFor = "\${streak-system.reset.lock-for}",
	)
	fun updateDaysInStreak() {
		logger.info("$RESET_USER_DAYS_IN_STREAK cron started")
		// cron runs at midnight - we need to check if conditions for streak were accomplished previous day
		userStatisticUpdateService.updateDaysInStreakForAll(LocalDate.now(clock).minusDays(1))
		logger.info("$RESET_USER_DAYS_IN_STREAK cron ended")
	}
}
