package com.cleevio.fatbot.application.module.bottransaction.event.listener

import com.cleevio.fatbot.application.module.matchmaking.event.BotTransactionSentEvent
import com.cleevio.fatbot.domain.bottransaction.BotTransactionCreateService
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class MatchMakingBotTransactionEventListener(
	private val botTransactionCreateService: BotTransactionCreateService,
) {

	@EventListener
	fun onBotTransactionSentEvent(event: BotTransactionSentEvent) {
		botTransactionCreateService.createFromBotEvent(event)
	}
}
