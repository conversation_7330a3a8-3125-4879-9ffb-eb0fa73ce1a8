package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.adapter.out.evm.simulation.GetTokenTaxEVM
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.EpochClockTicker
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.port.out.toDexPairInfo
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.token.port.out.GetTokenTransferFee
import com.cleevio.fatbot.application.module.token.query.GetTokenTaxQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.fatbot.infrastructure.config.properties.HoneypotWhitelistProperties
import com.fasterxml.jackson.databind.ObjectMapper
import com.github.benmanes.caffeine.cache.Caffeine
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Clock
import java.time.Duration
import java.time.temporal.ChronoUnit
import java.util.concurrent.CompletableFuture

private val HONEYPOT_THRESHOLD = BigDecimal("0.7")

@Component
class GetTokenTaxQueryHandler(
	private val getTokenTaxEVM: GetTokenTaxEVM,
	private val evmTokenInfoFinderService: EvmTokenInfoFinderService,
	private val getTokenTransferFee: GetTokenTransferFee,
	private val objectMapper: ObjectMapper,
	honeypotWhitelistProperties: HoneypotWhitelistProperties,
	clock: Clock,
) : QueryHandler<GetTokenTaxQuery.Result, GetTokenTaxQuery> {

	override val query = GetTokenTaxQuery::class

	private val logger = logger()

	private val whitelistContracts = honeypotWhitelistProperties.contracts

	/*
	Note: The cache here is temporary and will be removed / refactored with Tenderly removal

	Its primary reason is to lower the load on the RPC provider, which is used very heavily with every get~Tax call.
	 */
	private val cache = Caffeine
		.newBuilder()
		.maximumSize(200)
		.refreshAfterWrite(Duration.of(60, ChronoUnit.SECONDS))
		.expireAfterWrite(Duration.of(120, ChronoUnit.SECONDS))
		.ticker(EpochClockTicker(clock))
		.buildAsync<GetTokenTaxQuery, GetTokenTaxQuery.Result> { key, executor ->
			CompletableFuture.supplyAsync(
				{ getResult(tokenAddress = key.tokenAddress, chain = key.chain, dexPairInfo = key.dexPairInfo.toDexPairInfo()) },
				executor,
			)
		}

	override fun handle(query: GetTokenTaxQuery): GetTokenTaxQuery.Result {
		return cache.get(query).join()
	}

	private fun getResult(
		tokenAddress: AddressWrapper,
		chain: Chain,
		dexPairInfo: GetDex.Result,
	): GetTokenTaxQuery.Result {
		return when (dexPairInfo) {
			is GetDex.Evm -> handleEvm(
				tokenAddress = tokenAddress,
				chainId = chain.evmId,
				dexPairInfo = dexPairInfo,
			)
			is GetDex.Solana -> handleSolana(tokenAddress = tokenAddress)
		}
	}

	private fun handleEvm(tokenAddress: AddressWrapper, chainId: Long, dexPairInfo: GetDex.Evm): GetTokenTaxQuery.Result {
		val result = runCatching {
			getTokenTaxEVM(
				tokenAddress = tokenAddress,
				chainId = chainId,
				dexPairInfo = dexPairInfo,
			)
		}.getOrElse {
			logger.warn(
				"""
				Failed to estimate tax!
				tokenAddress: $tokenAddress
				chainId: $chainId
				dexPairInfo: ${objectMapper.writeValueAsString(dexPairInfo)}
				------
				Error:
				$it
				""".trimIndent(),
			)

			return GetTokenTaxQuery.Result(
				buyTax = null,
				sellTax = null,
				isHoneypot = tokenAddress !in whitelistContracts,
			)
		}

		val isHoneypot = tokenAddress !in whitelistContracts && result.sellTax > HONEYPOT_THRESHOLD

		return GetTokenTaxQuery.Result(
			buyTax = result.buyTax,
			sellTax = result.sellTax,
			isHoneypot = isHoneypot,
		)
	}

	private fun handleSolana(tokenAddress: AddressWrapper): GetTokenTaxQuery.Result {
		val tokenInfo = evmTokenInfoFinderService.getByTokenAddressAndChain(tokenAddress, Chain.SOLANA)

		val transferFeeBp = if (tokenInfo.isToken2022) getTokenTransferFee(tokenAddress) else BasisPoint.ZERO
		val transferFee = transferFeeBp.fraction

		val isHoneypot = transferFee > HONEYPOT_THRESHOLD

		return GetTokenTaxQuery.Result(
			buyTax = transferFee,
			sellTax = transferFee,
			isHoneypot = isHoneypot,
		)
	}
}
