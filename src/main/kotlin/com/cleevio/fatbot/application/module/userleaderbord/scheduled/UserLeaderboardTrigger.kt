package com.cleevio.fatbot.application.module.userleaderbord.scheduled

import com.cleevio.fatbot.application.module.userleaderbord.service.RefreshUserLeaderboardService
import com.cleevio.fatbot.domain.userstatistics.UserStatisticUpdateService
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class UserLeaderboardTrigger(
	private val userStatisticUpdateService: UserStatisticUpdateService,
	private val refreshUserLeaderboardService: RefreshUserLeaderboardService,
) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.reset-user-leaderboard")
	@Scheduled(cron = "\${leaderboard.reset.cron}")
	@SchedulerLock(
		name = "RESET_USER_LEADERBOARD",
		lockAtMostFor = "\${leaderboard.reset.lock-for}",
		lockAtLeastFor = "\${leaderboard.reset.lock-for}",
	)
	fun resetUserLeaderBoard() {
		logger.info("RESET_USER_LEADERBOARD cron started")
		userStatisticUpdateService.resetLeaderboardDonutsForAll()
		refreshUserLeaderboardService()
		logger.info("RESET_USER_LEADERBOARD cron ended")
	}

	@SentryTransaction(operation = "scheduled.refresh-user-leaderboard")
	@Scheduled(cron = "\${leaderboard.refresh.cron}")
	@SchedulerLock(
		name = "REFRESH_USER_LEADERBOARD",
		lockAtMostFor = "\${leaderboard.refresh.lock-for}",
		lockAtLeastFor = "\${leaderboard.refresh.lock-for}",
	)
	fun refreshUserLeaderBoard() {
		logger.info("REFRESH_USER_LEADERBOARD cron started")
		refreshUserLeaderboardService()
		logger.info("REFRESH_USER_LEADERBOARD cron ended")
	}
}
