package com.cleevio.fatbot.application.module.exchangerate

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.domain.exchangerate.ExchangeRateSnapshotCreateService
import com.cleevio.fatbot.domain.exchangerate.ExchangeRateSnapshotRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.Clock
import java.time.Duration
import java.time.Instant

private val SNAPSHOT_AGE_THRESHOLD = Duration.ofHours(36)

@Service
class ExchangeRateSnapshotService(
	private val exchangeRateSnapshotCreateService: ExchangeRateSnapshotCreateService,
	private val exchangeRateSnapshotRepository: ExchangeRateSnapshotRepository,
	private val clock: Clock,
) {

	@Transactional
	fun createNewAndDeleteOld(newSnapshots: Map<CryptoCurrency, BigDecimal>) {
		val now = Instant.now(clock)

		newSnapshots.forEach { (currency, rate) ->
			exchangeRateSnapshotCreateService.create(currency, rate, now)
		}

		exchangeRateSnapshotRepository.deleteSnapshotsBefore(now.minus(SNAPSHOT_AGE_THRESHOLD))
	}
}
