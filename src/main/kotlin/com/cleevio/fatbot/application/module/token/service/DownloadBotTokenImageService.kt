package com.cleevio.fatbot.application.module.token.service

import com.cleevio.fatbot.adapter.out.ipfs.IpfsConnector
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.file.client.FileDownloadClient
import com.cleevio.fatbot.application.module.matchmaking.event.BoughtTokensEvent
import com.cleevio.fatbot.application.module.token.finder.BotTokenInfoFinderService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.token.BotTokenInfoRepository
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.annotation.Lazy
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Service

@Service
class DownloadBotTokenImageService(
	private val ipfsConnector: IpfsConnector,
	private val botTokenInfoFinderService: BotTokenInfoFinderService,
	private val fileDownloadClient: FileDownloadClient,
	private val botTokenInfoRepository: BotTokenInfoRepository,
) {

	@Lazy
	@Autowired
	private lateinit var self: DownloadBotTokenImageService

	private val logger = logger()
	private val scope = CoroutineScope(
		Dispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	private val downloadChannel: Channel<BoughtTokensEvent.BoughtToken> = Channel(capacity = Channel.UNLIMITED)

	@EventListener(ApplicationReadyEvent::class)
	fun onStartup() {
		scope.launch { downloadImageProcessor() }
	}

	fun processBoughtTokens(boughtTokens: List<BoughtTokensEvent.BoughtToken>) {
		runBlocking {
			boughtTokens.forEach {
				downloadChannel.send(it)
			}
		}
	}

	suspend fun downloadImageProcessor() {
		for (token in downloadChannel) {
			runCatching {
				token.infoUrl?.let { self.processBotTokenImage(token.tokenAddress, token.chain, it) }
			}.onFailure { logger.error("Failed to process token images.", it) }
		}
	}

	@SentryTransaction(operation = "async.download-bot-token-image")
	fun processBotTokenImage(tokenAddress: AddressWrapper, chain: Chain, infoUrl: String) {
		val botToken = botTokenInfoFinderService.getByTokenAddressAndChain(tokenAddress = tokenAddress, chain = chain)

		// Do not download the token image twice.
		if (botToken.imageFileId != null) return
		val tokenInfo = ipfsConnector.downloadTokenInfo(infoUrl = infoUrl)

		val imageFile = runCatching { fileDownloadClient.downloadAndSaveFile(tokenInfo.imageUrl) }.onFailure {
			logger.warn("Failed to download file ${tokenInfo.imageUrl} for token $tokenAddress")
		}.getOrNull()

		imageFile?.let { botToken.setImageFileId(it.id) }
		botTokenInfoRepository.save(botToken)
	}
}
