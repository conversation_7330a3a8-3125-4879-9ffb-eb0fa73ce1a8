package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.wallet.command.ImportUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.exception.WalletAlreadyImportedException
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.application.module.wallet.locks.CREATE_OR_UPDATE_USER_WALLETS
import com.cleevio.fatbot.application.module.wallet.locks.WALLET_MODULE
import com.cleevio.fatbot.application.module.wallet.util.getAddressFromPrivateKey
import com.cleevio.fatbot.application.module.wallet.util.validateWalletCount
import com.cleevio.fatbot.application.module.walletposition.event.NewWalletImportedEvent
import com.cleevio.fatbot.domain.wallet.WalletCreateService
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class ImportUserWalletCommandHandler(
	private val walletFinderService: WalletFinderService,
	private val walletCreateService: WalletCreateService,
	private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<ImportUserWalletCommand.Result, ImportUserWalletCommand> {

	override val command = ImportUserWalletCommand::class

	private val logger = logger()

	@Transactional
	@Lock(module = WALLET_MODULE, lockName = CREATE_OR_UPDATE_USER_WALLETS)
	override fun handle(@LockFieldParameter("userId") command: ImportUserWalletCommand): ImportUserWalletCommand.Result {
		val walletAddresses = walletFinderService.findAllByUserIdAndChain(
			userId = command.userId,
			chain = command.chain,
		).map { it.address }

		val address = getAddressFromPrivateKey(privateKey = command.privateKey, chainType = command.chain.type)
		if (address in walletAddresses) {
			throw WalletAlreadyImportedException("Wallet with address $address is already imported.")
		}

		validateWalletCount(walletCount = walletAddresses.size, chain = command.chain)

		val importedWallet = walletCreateService.create(
			userId = command.userId,
			addressWrapper = address,
			chain = command.chain,
			privateKey = command.privateKey,
			walletCount = walletAddresses.size,
		)
		logger.info("User ${command.userId} imported new wallet $address.")

		applicationEventPublisher.publishEvent(NewWalletImportedEvent(walletId = importedWallet.id))

		return ImportUserWalletCommand.Result(
			walletId = importedWallet.id,
			walletAddress = importedWallet.address,
			privateKey = importedWallet.privateKey,
		)
	}
}
