package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotMarketPosition
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotPortfolioValueSnapshot
import com.cleevio.fatbot.application.module.bot.query.BotsOverviewQuery
import com.cleevio.fatbot.application.module.bot.query.CompareBotsQuery
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator.lossPositionsCount
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator.profitablePositionsCount
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator.totalClosedVolume
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator.totalOpenVolume
import com.cleevio.fatbot.application.module.botwallet.port.out.GetBotWallets
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.token.constant.toInstant
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Clock
import java.time.Instant

@Component
class BotsOverviewQueryHandler(
	private val clock: Clock,
	private val findAllBotMarketPosition: FindAllBotMarketPosition,
	private val fileUrlMapper: FileUrlMapper,
	private val getTokenPrices: GetTokenPrices,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val usdConverter: UsdConverter,
	private val getBotWallets: GetBotWallets,
	private val findAllBotPortfolioValueSnapshot: FindAllBotPortfolioValueSnapshot,
) : QueryHandler<BotsOverviewQuery.Result, BotsOverviewQuery> {

	override val query = BotsOverviewQuery::class

	override fun handle(query: BotsOverviewQuery): BotsOverviewQuery.Result {
		val allBotMarketPositions = findAllBotMarketPosition(
			userId = query.userId,
			botIds = null,
			isBotMarketPositionActive = null,
			searchString = null,
			fileToUrlMapper = fileUrlMapper::map,
		)

		// Fetch token prices for all open bot market positions
		val openPositions = allBotMarketPositions.filter { it.positionClosedAt == null }
		val openPositionTokens = openPositions.mapToSet { it.tokenAddress.toChainAddress(it.chain) }
		val tokenAddressToTokenPrice = getTokenPrices(tokens = openPositionTokens)

		val chainToExchangeRate = Chain.entries.associateWith { getUsdExchangeRate(it.currency) }

		val allBotWallets = getBotWallets(userId = query.userId, botId = null)

		val botIdToMarketPositionValues = BotMarketPositionCalculator.botToMarketPositionsResults(
			botMarketPositions = openPositions,
			tokenAddressToTokenPrice = tokenAddressToTokenPrice,
			chainToExchangeRate = chainToExchangeRate,
		)

		val botsWalletBalanceUsd = allBotWallets.sumOf {
			usdConverter.baseToUsd(it.balance, it.chain, chainToExchangeRate.getValue(it.chain))
		}
		val botsMarketPositionsValueUsd = botIdToMarketPositionValues.values.sumOf { it.currentValueUsd + it.rentUsd }
		val botsTotalValueUsd = botsMarketPositionsValueUsd + botsWalletBalanceUsd

		val now = Instant.now(clock)
		val from = query.filter.timeRange.toInstant(now)

		val botsMarketPositions = allBotMarketPositions.filter { it.positionOpenedAt in from..now }

		// Fetch all and filter by time range programmatically - might be a performance issue in future
		val portfolioPastValues = findAllBotPortfolioValueSnapshot(
			botIds = allBotWallets.mapToSet { it.botId },
			from = from,
			to = now,
		)

		val portfolioSnapshotToValueUsd = portfolioPastValues
			.groupBy { it.snapshotMadeAt }
			.mapValues { it.value.sumOf { pastValue -> pastValue.portfolioValueUsd } }

		val firstSnapshotTimeInTimeRange = portfolioSnapshotToValueUsd
			.keys
			.filter { it in from..now }
			.minOrNull()

		val botsTotalAcquisitionValueUsd = allBotWallets.sumOf { it.acquisitionValueUsd }

		val portfolioValueTimeRangeAgoUsd = portfolioSnapshotToValueUsd[firstSnapshotTimeInTimeRange] ?: BigDecimal.ZERO

		val timeRangeChangeUsd = botsTotalValueUsd - portfolioValueTimeRangeAgoUsd
		val timeRangeChangeFraction = calculateFractionChange(botsTotalValueUsd, portfolioValueTimeRangeAgoUsd)

		val totalPnlAmountUsd = botsTotalValueUsd - botsTotalAcquisitionValueUsd
		val totalPnlAmountFraction = calculateFractionChange(botsTotalValueUsd, botsTotalAcquisitionValueUsd)

		val (botMarketOpenPositions, botMarketClosedPositions) = botsMarketPositions.partition { it.positionClosedAt == null }

		val openPositionsValues = botMarketOpenPositions.map {
			BotMarketPositionCalculator.toBotMarketPositionResult(
				botMarketPosition = it,
				tokenAddressToTokenPrice = tokenAddressToTokenPrice,
				currentExchangeRate = chainToExchangeRate.getValue(it.chain),
			)
		}

		val closedPositionsValues = botMarketClosedPositions.map {
			BotMarketPositionCalculator.toBotMarketPositionResult(
				botMarketPosition = it,
				tokenAddressToTokenPrice = tokenAddressToTokenPrice,
				currentExchangeRate = chainToExchangeRate.getValue(it.chain),
			)
		}

		val allPositionsValues = openPositionsValues + closedPositionsValues

		val botsPortfolioValueSumPastValues = portfolioSnapshotToValueUsd.map {
			CompareBotsQuery.BotPortfolioPastValue(
				portfolioValueUsd = it.value,
				createdAt = it.key,
			)
		} + CompareBotsQuery.BotPortfolioPastValue(
			portfolioValueUsd = botsTotalValueUsd,
			createdAt = now,
		)

		val botsTransactionsCount = openPositionsValues.size + (closedPositionsValues.size * 2)
		val botsProfitableTransactionsCount = closedPositionsValues.profitablePositionsCount()
		val botsLossTransactionsCount = closedPositionsValues.lossPositionsCount()
		val botsTotalTransactionVolume = openPositionsValues.totalOpenVolume() + closedPositionsValues.totalClosedVolume()

		return BotsOverviewQuery.Result(
			userId = query.userId,
			botsTotalValueAmountUsd = botsTotalValueUsd,
			botsTotalPnlAmountUsd = totalPnlAmountUsd,
			botsTotalPnlAmountFraction = totalPnlAmountFraction,
			botsOneDayChangeAmountUsd = timeRangeChangeUsd,
			botsOneDayChangeFraction = timeRangeChangeFraction,
			botsPortfolioValueSumPastValues = botsPortfolioValueSumPastValues,
			botsTransactionsCount = botsTransactionsCount,
			botsBuyTransactionsCount = allPositionsValues.size,
			botsProfitableTransactionsCount = botsProfitableTransactionsCount,
			botsLossTransactionsCount = botsLossTransactionsCount,
			botsTransactionsVolumeSum = botsTotalTransactionVolume,
		)
	}
}
