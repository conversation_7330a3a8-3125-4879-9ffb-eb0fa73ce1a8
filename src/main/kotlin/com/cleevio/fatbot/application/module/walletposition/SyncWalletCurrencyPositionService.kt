package com.cleevio.fatbot.application.module.walletposition

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.finder.WalletCurrencyPositionFinderService
import com.cleevio.fatbot.domain.walletposition.WalletCurrencyPosition
import com.cleevio.fatbot.domain.walletposition.WalletCurrencyPositionCreateService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.BigInteger
import java.util.UUID

@Service
class SyncWalletCurrencyPositionService(
	private val walletCurrencyPositionFinderService: WalletCurrencyPositionFinderService,
	private val walletCurrencyPositionCreateService: WalletCurrencyPositionCreateService,
	private val usdConverter: UsdConverter,
) {

	@Transactional
	fun syncWallets(walletIdToBalance: Map<UUID, Pair<BigInteger, Chain>>) {
		walletIdToBalance.forEach { (walletId, balanceToCurrency) ->
			syncWallet(walletId, balanceToCurrency.first, balanceToCurrency.second)
		}
	}

	@Transactional
	fun syncWallet(walletId: UUID, chainBalance: BigInteger, chain: Chain) {
		val latestPosition = walletCurrencyPositionFinderService.findLatestPositionByWalletId(walletId = walletId)

		if (latestPosition == null) {
			createInitialPosition(
				walletId = walletId,
				initialBalance = chainBalance,
				initialAcquisitionCostUsd = usdConverter.baseToUsd(chainBalance.asBaseAmount(), chain),
			)

			return
		}

		val storedBalance = latestPosition.totalBought - latestPosition.totalSold

		when {
			// do nothing, as the value in DB is exactly the same as value on chain
			chainBalance == storedBalance -> Unit // do nothing

			// on chain there is more tokens than in DB, so user got some which is essentially same as buy
			chainBalance > storedBalance -> {
				val amountBought = chainBalance - storedBalance
				val acquisitionCostUsd = usdConverter.baseToUsd(amountBought.asBaseAmount(), chain)

				createBuyPosition(
					currentPosition = latestPosition,
					amountBought = amountBought,
					acquisitionCostUsd = acquisitionCostUsd,
				)
			}

			// on chain there is less tokens than in DB, so user lost some tokens which is essentially same as sell
			chainBalance < storedBalance -> createSellPosition(
				currentPosition = latestPosition,
				amountSold = storedBalance - chainBalance,
			)
		}
	}

	private fun createSellPosition(currentPosition: WalletCurrencyPosition, amountSold: BigInteger) {
		walletCurrencyPositionCreateService.create(
			walletId = currentPosition.walletId,
			totalBought = currentPosition.totalBought,
			totalSold = currentPosition.totalSold + amountSold,
			totalAcquisitionCostUsd = currentPosition.totalAcquisitionCostUsd,
		)
	}

	private fun createBuyPosition(
		currentPosition: WalletCurrencyPosition,
		amountBought: BigInteger,
		acquisitionCostUsd: BigDecimal,
	) {
		walletCurrencyPositionCreateService.create(
			walletId = currentPosition.walletId,
			totalBought = currentPosition.totalBought + amountBought,
			totalSold = currentPosition.totalSold,
			totalAcquisitionCostUsd = currentPosition.totalAcquisitionCostUsd + acquisitionCostUsd,
		)
	}

	private fun createInitialPosition(walletId: UUID, initialBalance: BigInteger, initialAcquisitionCostUsd: BigDecimal) {
		walletCurrencyPositionCreateService.create(
			walletId = walletId,
			totalBought = initialBalance,
			totalSold = BigInteger.ZERO,
			totalAcquisitionCostUsd = initialAcquisitionCostUsd,
		)
	}
}
