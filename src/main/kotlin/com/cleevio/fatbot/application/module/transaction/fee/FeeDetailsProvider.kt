package com.cleevio.fatbot.application.module.transaction.fee

import com.cleevio.fatbot.application.common.crypto.isExemptedFromFatbotFee
import com.cleevio.fatbot.application.common.crypto.isStableCoin
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.domain.user.FirebaseUser
import com.cleevio.fatbot.infrastructure.config.properties.FeeProperties
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.util.UUID

@Component
class FeeDetailsProvider(
	private val userFinderService: FirebaseUserFinderService,
	private val feeProperties: FeeProperties,
) {

	data class FeeDetails(
		val referrerId: UUID?,
		val referrerFeeBps: BasisPoint,
		val platformFeeBps: BasisPoint,
	)

	fun getFeeDetails(transactingUserId: UUID, token: ChainAddress): FeeDetails {
		if (token.isStableCoin() || token.isExemptedFromFatbotFee()) {
			// Stable coins are exempted from the fees same as specially selected coins
			return FeeDetails(
				referrerId = null,
				referrerFeeBps = BasisPoint.ZERO,
				platformFeeBps = BasisPoint.ZERO,
			)
		}

		val transactingUser = userFinderService.getById(transactingUserId)

		return FeeDetails(
			referrerId = transactingUser.referredByUserId,
			platformFeeBps = getPlatformFee(
				transactingUser,
				feeProperties.platformFeePercentage,
				feeProperties.refereeManualTradingDiscountPercentage,
			),
			referrerFeeBps = getReferrerRewardFee(
				transactingUser,
				feeProperties.referrerFeeRewardPercentage,
			),
		)
	}

	private fun getPlatformFee(
		transactingUser: FirebaseUser,
		platformFeePercentage: BigDecimal,
		refereeFeeDiscountPercentage: BigDecimal,
	): BasisPoint {
		val complementaryRefereeFeePercentage = BigDecimal(100) - refereeFeeDiscountPercentage

		val isUserReferred = transactingUser.referredByUserId != null

		val platformFee = if (isUserReferred) {
			(platformFeePercentage * complementaryRefereeFeePercentage).divide(BigDecimal("100"))
		} else {
			platformFeePercentage
		}

		return BasisPoint.ofPercentage(platformFee)
	}

	private fun getReferrerRewardFee(transactingUser: FirebaseUser, referralFeePercentage: BigDecimal): BasisPoint =
		transactingUser.referredByUserId?.let { BasisPoint.ofPercentage(referralFeePercentage) }
			?: BasisPoint.ZERO
}
