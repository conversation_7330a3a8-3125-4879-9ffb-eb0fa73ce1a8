package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import java.math.BigInteger

interface GetTokenBalancesSolana {

	/**
	 * Returns balance of token account associated with [ownerAddress] and [tokenAddress].
	 * Note: if the associated token account does not exist the result is [BaseAmount.ZERO]
	 */
	fun single(ownerAddress: AddressWrapper, tokenAddress: AddressWrapper): BaseAmount

	fun single(tokenAccountAddress: AddressWrapper): BaseAmount

	fun ofAll(ownerAddress: AddressWrapper, tokenAddresses: List<AddressWrapper>): Map<AddressWrapper, BigInteger>
}
