package com.cleevio.fatbot.application.module.file.client

import com.cleevio.fatbot.application.module.file.FileService
import com.cleevio.fatbot.domain.file.DownloadFailedException
import com.cleevio.fatbot.domain.file.File
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Service
import org.springframework.web.client.RestClient
import java.net.URI

@Service
class FileDownloadClient(
	private val fileService: FileService,
) {

	private val logger = logger()

	private val client = RestClient.builder().build()

	@SentrySpan
	fun downloadAndSaveFile(downloadUrl: String): File {
		logger.info("Going to try to download file at: $downloadUrl")
		val fileBytes = client
			.get()
			.uri(downloadUrl)
			.retrieve()
			.body(ByteArray::class.java)
			?: throw DownloadFailedException()

		return fileService.upload(byteArray = fileBytes, downloadUrl.getFileNameFromDownloadLink())
	}

	private fun String.getFileNameFromDownloadLink() = URI
		.create(this)
		.path
}
