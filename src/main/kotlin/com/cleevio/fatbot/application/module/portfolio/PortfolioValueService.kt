package com.cleevio.fatbot.application.module.portfolio

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.sum
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.bot.BotFinderService
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotPortfolioValueSnapshot
import com.cleevio.fatbot.application.module.botportfolio.BotPortfolioValueService
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.exchangerate.port.out.GetLatestExchangeRateSnapshots
import com.cleevio.fatbot.application.module.market.service.AggregateMarketPositionsService
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.application.module.wallet.port.out.GetLatestWalletCurrencyPositions
import com.cleevio.fatbot.application.module.wallet.service.WalletBalanceService
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger
import java.math.MathContext
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

@Service
class PortfolioValueService(
	private val aggregateMarketPositionsService: AggregateMarketPositionsService,
	private val walletBalanceService: WalletBalanceService,
	private val walletFinderService: WalletFinderService,
	private val usdConverter: UsdConverter,
	private val chainProperties: ChainProperties,
	private val getLatestWalletCurrencyPositions: GetLatestWalletCurrencyPositions,
	private val botPortfolioValueService: BotPortfolioValueService,
	private val botFinderService: BotFinderService,
	private val botWalletFinderService: BotWalletFinderService,
	private val clock: Clock,
	private val findAllBotPortfolioValueSnapshot: FindAllBotPortfolioValueSnapshot,
	private val getLatestExchangeRateSnapshots: GetLatestExchangeRateSnapshots,
) {

	data class PerformanceResult(
		val totalValueUsd: BigDecimal,
		val totalAcquisitionValueUsd: BigDecimal,
		val totalValue24hUsd: BigDecimal,
		val totalPnlFraction: BigDecimal,
		val userValueUsd: BigDecimal,
		val userAcquisitionValueUsd: BigDecimal,
		val userValue24hUsd: BigDecimal,
		val userPnlFraction: BigDecimal,
		val botValueUsd: BigDecimal,
		val botAcquisitionValueUsd: BigDecimal,
		val botValue24hUsd: BigDecimal,
		val botPnlFraction: BigDecimal,
	)

	/**
	 * Returns the current total value of the portfolio in USD by chain.
	 *
	 * The total value of the portfolio on chain is calculated as a sum of all market positions on that chain and
	 * sum of all balances of user wallets on that chain
	 *
	 * Guaranteed to return all enabled chains, even if no market positions / wallets exists on that chain for user.
	 */
	fun byChainInUsdNow(userId: UUID): Map<Chain, BigDecimal> {
		val chainToMarketPositionsValue = getMarketPositionValueUsdByChain(userId)
		val chainToWalletBalance = getWalletBalancesUsdByChain(userId)

		val chainToPortfolioValue = chainProperties.enabledChains.associateWith {
			val marketPositionValue = chainToMarketPositionsValue[it] ?: BigDecimal.ZERO
			val walletBalance = chainToWalletBalance[it] ?: BigDecimal.ZERO

			marketPositionValue + walletBalance
		}

		val allBotIds = botFinderService.findAllByUserId(userId).mapToSet { it.id }

		val botIdToPortfolioValue = botPortfolioValueService.getBotPortfolioValues(
			userId = userId,
			botIds = allBotIds,
		)

		val totalBotPortfolioValue = botIdToPortfolioValue.values.sumOf { it }

		return chainToPortfolioValue.mapValues { (chain, portfolioValue) ->
			if (chain == Chain.SOLANA) portfolioValue + totalBotPortfolioValue else portfolioValue
		}
	}

	fun getChainToPerformance(userId: UUID): Map<Chain, PerformanceResult> {
		val chainToMarketPositionSum = aggregateMarketPositionsService.byChainNow(
			userId = userId,
			walletId = null,
			chains = chainProperties.enabledChains,
		)

		val now = Instant.now(clock)
		val chainToMarketPositionSumUsd24hAgo = aggregateMarketPositionsService.byChainTotalValueUsd24hAgo(
			userId = userId,
			walletId = null,
			chains = chainProperties.enabledChains,
			now = now,
		)

		val walletCurrencyPositions = getLatestWalletCurrencyPositions(
			userId = userId,
			walletId = null,
			chains = chainProperties.enabledChains,
			createdBefore = null,
		)

		val walletCurrencyPositions24Ago = getLatestWalletCurrencyPositions(
			userId = userId,
			walletId = null,
			chains = chainProperties.enabledChains,
			createdBefore = now.minus(1, ChronoUnit.DAYS),
		)

		val chainToCurrencyPositions = walletCurrencyPositions.groupBy { it.chain }
		val chainToCurrencyPositionsValueUsd = chainToCurrencyPositions.mapValues { (_, positions) ->
			positions.sumOf {
				val balance = it.totalBought - it.totalSold
				usdConverter.baseToUsd(balance.asBaseAmount(), it.chain)
			}
		}

		val currencyToRateUsd24h = getLatestExchangeRateSnapshots(
			currencies = CryptoCurrency.entries.toSet(),
			validBefore = now.minus(24, ChronoUnit.HOURS),
		)
		val chainToCurrencyPositions24Ago = walletCurrencyPositions24Ago.groupBy { it.chain }
		val chainToCurrencyPositionsValueUsd24hAgo = chainToCurrencyPositions24Ago
			.mapValues { (_, positions) ->
				positions.sumOf {
					val balance = it.totalBought - it.totalSold
					val exchangeRate = currencyToRateUsd24h[it.chain.currency]
					usdConverter.baseToUsd(balance.asBaseAmount(), it.chain, exchangeRate)
				}
			}

		val allBotIds = botFinderService.findAllByUserId(userId).mapToSet { it.id }

		val botIdToPortfolioValue = botPortfolioValueService.getBotPortfolioValues(
			userId = userId,
			botIds = allBotIds,
		)

		val totalBotPortfolioValue = botIdToPortfolioValue.values.sumOf { it }
		val botsAcquisitionValueUsd = botWalletFinderService.getAllByBotIds(allBotIds).sumOf { it.acquisitionValueUsd }

		// Using 10min window around the 24h mark to get the bot snapshots
		val ago24h = now.minus(1, ChronoUnit.DAYS)
		val botValue24hAgoUsd = findAllBotPortfolioValueSnapshot(
			botIds = allBotIds,
			from = ago24h.minus(5, ChronoUnit.MINUTES),
			to = ago24h.plus(5, ChronoUnit.MINUTES),
		)
			.groupBy { it.botId }
			.mapValues { (_, snapshots) ->
				val earliestSnapshot = snapshots.minBy { it.snapshotMadeAt }
				earliestSnapshot.portfolioValueUsd
			}
			.values
			.sumOf { it }

		return chainProperties.enabledChains.associateWith { chain ->
			// Current value
			val currencyPositionsValueUsd = chainToCurrencyPositionsValueUsd[chain] ?: BigDecimal.ZERO
			val marketPositionsSum = chainToMarketPositionSum[chain]
			val currencyPositionValue24hAgoUsd = chainToCurrencyPositionsValueUsd24hAgo[chain] ?: BigDecimal.ZERO
			val marketPositionsSum24AgoUsd = chainToMarketPositionSumUsd24hAgo[chain] ?: BigDecimal.ZERO

			val marketPositionsValueUsd = marketPositionsSum?.currentValueUsd ?: BigDecimal.ZERO
			val totalBotPortfolioValueInChain = if (chain == Chain.SOLANA) totalBotPortfolioValue else BigDecimal.ZERO

			val userValueUsd = marketPositionsValueUsd + currencyPositionsValueUsd
			val totalValueUsd = userValueUsd + totalBotPortfolioValueInChain

			// Current PnL Fraction
			val currencyAcquisitionCostUsdSum = chainToCurrencyPositions[chain]?.sumOf {
				if (it.totalBought == BigInteger.ZERO) return@sumOf BigDecimal.ZERO

				val averageCost =
					it.totalAcquisitionCostUsd.divide(it.totalBought.toBigDecimal(), MathContext.DECIMAL128)

				val balance = it.totalBought - it.totalSold

				balance.toBigDecimal() * averageCost
			} ?: BigDecimal.ZERO

			val marketPositionsAcquisitionValueUsd =
				marketPositionsSum?.acquisitionValueOwnedTokenUsd ?: BigDecimal.ZERO
			val botsAcquisitionValueUsdInChain = if (chain == Chain.SOLANA) botsAcquisitionValueUsd else BigDecimal.ZERO

			val userAcquisitionUsdNow = marketPositionsAcquisitionValueUsd + currencyAcquisitionCostUsdSum
			val totalAcquisitionUsdNow = userAcquisitionUsdNow + botsAcquisitionValueUsdInChain

			val pnlFractionNow = calculateFractionChange(totalValueUsd, totalAcquisitionUsdNow)

			val userValue24hAgoUsd = marketPositionsSum24AgoUsd + currencyPositionValue24hAgoUsd
			val botValue24AgoUsd = if (chain == Chain.SOLANA) botValue24hAgoUsd else BigDecimal.ZERO
			val totalValue24hUsd = userValue24hAgoUsd + botValue24AgoUsd

			PerformanceResult(
				totalValueUsd = totalValueUsd,
				totalAcquisitionValueUsd = totalAcquisitionUsdNow,
				totalPnlFraction = pnlFractionNow,
				totalValue24hUsd = totalValue24hUsd,
				userValueUsd = userValueUsd,
				userAcquisitionValueUsd = userAcquisitionUsdNow,
				userValue24hUsd = userValue24hAgoUsd,
				userPnlFraction = calculateFractionChange(userValueUsd, userAcquisitionUsdNow),
				botValueUsd = totalBotPortfolioValueInChain,
				botAcquisitionValueUsd = botsAcquisitionValueUsdInChain,
				botValue24hUsd = botValue24AgoUsd,
				botPnlFraction = calculateFractionChange(totalBotPortfolioValueInChain, botsAcquisitionValueUsdInChain),
			)
		}
	}

	private fun getWalletBalancesUsdByChain(userId: UUID): Map<Chain, BigDecimal> {
		val wallets = walletFinderService.findAllByUserIdAndChainIn(userId, chainProperties.enabledChains)

		val walletBalances = walletBalanceService.getBalances(wallets.map { it.address.toChainAddress(it.chain) })
		val chainToBalances = walletBalances.entries.groupBy({ it.key.chain }, { it.value })

		return chainToBalances.mapValues { (chain, balances) -> usdConverter.baseToUsd(balances.sum(), chain) }
	}

	private fun getMarketPositionValueUsdByChain(userId: UUID): Map<Chain, BigDecimal> {
		val marketPositionSummary = aggregateMarketPositionsService.byChainNow(
			userId = userId,
			walletId = null,
			chains = chainProperties.enabledChains,
		)

		return marketPositionSummary.mapValues { (chain, marketPositionResult) ->
			usdConverter.nativeToUsd(nativeAmount = marketPositionResult.currentValue, chain = chain)
		}
	}
}
