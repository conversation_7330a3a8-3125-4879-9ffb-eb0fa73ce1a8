package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.adapter.out.dexscreener.GetTokenDetailDexScreener
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.command.BuyTokenCommand
import com.cleevio.fatbot.application.module.market.command.QuickBuyTokenCommand
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.token.exception.TokenNotFoundException
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component

@Component
class QuickBuyTokenCommandHandler(
	private val usdConverter: UsdConverter,
	private val firebaseUserFinderService: FirebaseUserFinderService,
	private val getTokenDetailDexScreener: GetTokenDetailDexScreener,
	private val walletFinderService: WalletFinderService,
) : CommandHandler<QuickBuyTokenCommand.Result, QuickBuyTokenCommand> {

	@Lazy
	@Autowired
	private lateinit var commandBus: CommandBus

	override val command = QuickBuyTokenCommand::class

	override fun handle(command: QuickBuyTokenCommand): QuickBuyTokenCommand.Result {
		val wallet = walletFinderService.getUserDefaultWalletOnChain(command.userId, command.chain)

		val detail = getTokenDetailDexScreener(command.tokenAddress.toChainAddress(wallet.chain))
			?: throw TokenNotFoundException("Token not found on any supported DEX")

		val user = firebaseUserFinderService.getById(command.userId)
		val buyForNativeAmount = usdConverter.usdToBase(user.quickBuyAmountUsd, wallet.chain).toNative(wallet.chain)

		val result = commandBus(
			BuyTokenCommand(
				userId = command.userId,
				walletId = wallet.id,
				dexPairInfo = detail.dexInfo.dexPairInfo,
				tokenAddress = command.tokenAddress,
				buyForNativeAmount = buyForNativeAmount,
			),
		)

		return QuickBuyTokenCommand.Result(txHash = result.txHash)
	}
}
