package com.cleevio.fatbot.application.module.botwallet.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.util.UUID

interface GetBotWallets {

	data class Result(
		val botId: UUID,
		val id: UUID,
		val address: AddressWrapper,
		val balance: BaseAmount,
		val acquisitionValueUsd: BigDecimal,
		val chain: Chain,
		val latestSignature: String?,
	)

	operator fun invoke(userId: UUID, botId: UUID?): List<Result>
}
