package com.cleevio.fatbot.application.module.walletposition

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.application.module.wallet.service.WalletBalanceService
import com.cleevio.fatbot.application.module.walletposition.command.SyncWalletCurrencyPositionCommand
import org.springframework.stereotype.Component

@Component
class SyncWalletCurrencyPositionCommandHandler(
	private val walletFinderService: WalletFinderService,
	private val walletBalanceService: WalletBalanceService,
	private val syncWalletCurrencyPositionService: SyncWalletCurrencyPositionService,
) : CommandHandler<Unit, SyncWalletCurrencyPositionCommand> {

	override val command = SyncWalletCurrencyPositionCommand::class

	override fun handle(command: SyncWalletCurrencyPositionCommand) {
		val wallet = walletFinderService.getById(command.walletId)

		val walletBalanceResult = walletBalanceService.getBalance(wallet.address, wallet.chain)

		syncWalletCurrencyPositionService.syncWallet(
			walletId = wallet.id,
			chainBalance = walletBalanceResult.amount,
			chain = wallet.chain,
		)
	}
}
