package com.cleevio.fatbot.application.module.botportfolio

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotMarketPosition
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotWallet
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.UUID

@Service
class BotPortfolioValueService(
	private val findAllBotMarketPosition: FindAllBotMarketPosition,
	private val fileUrlMapper: FileUrlMapper,
	private val getTokenPrices: GetTokenPrices,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val findAllBotWallet: FindAllBotWallet,
) {
	fun getBotPortfolioValues(userId: UUID?, botIds: Set<UUID>?): Map<UUID, BigDecimal> {
		val allBotOpenPositions = findAllBotMarketPosition(
			userId = userId,
			botIds = botIds,
			searchString = null,
			isBotMarketPositionActive = true,
			fileToUrlMapper = fileUrlMapper::map,
		)

		// Fetch token prices for all open bot market positions
		val tokens = allBotOpenPositions.mapToSet { it.tokenAddress.toChainAddress(it.chain) }
		val tokenAddressToTokenPrice = getTokenPrices(tokens = tokens)

		// Fetch all current exchange rates by chains
		val chainToExchangeRate = Chain.entries.associateWith { getUsdExchangeRate(it.currency) }

		val openPositionsBotIds = botIds ?: allBotOpenPositions.mapToSet { it.botId }
		return calculate(openPositionsBotIds, allBotOpenPositions, tokenAddressToTokenPrice, chainToExchangeRate)
	}

	private fun calculate(
		botIds: Set<UUID>,
		allBotOpenPositions: List<BotMarketPositionCalculator.Input>,
		tokenAddressToTokenPrice: Map<ChainAddress, BaseAmount>,
		chainToExchangeRate: Map<Chain, BigDecimal>,
	): Map<UUID, BigDecimal> {
		val botIdToMarketPositionValues = BotMarketPositionCalculator.botToMarketPositionsResults(
			botMarketPositions = allBotOpenPositions,
			tokenAddressToTokenPrice = tokenAddressToTokenPrice,
			chainToExchangeRate = chainToExchangeRate,
		)

		val currentExchangeRate = chainToExchangeRate.getValue(Chain.SOLANA)

		// Fetch bot's balance - current balance of SOL on bot's wallet - each bot has max one wallet
		val botIdToBalanceUsd = findAllBotWallet(botIds = botIds)
			.associateBy({ it.botId }, { it.balance.toNative(Chain.SOLANA).amount * currentExchangeRate })

		return botIdToBalanceUsd.mapValues { (botId, walletBalanceUsd) ->
			val openPositionsValueUsd = botIdToMarketPositionValues[botId]
				?.let { it.currentValueUsd + it.rentUsd }
				?: BigDecimal.ZERO

			walletBalanceUsd + openPositionsValueUsd
		}
	}
}
