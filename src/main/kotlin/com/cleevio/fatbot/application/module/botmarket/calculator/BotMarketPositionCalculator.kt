package com.cleevio.fatbot.application.module.botmarket.calculator

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.BOT_BUY_RENT_BASE_AMOUNT
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.botmarket.query.SearchBotMarketPositionQuery
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator.calculate
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator.plus
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.util.UUID

object BotMarketPositionCalculator {

	interface Input : MarketPositionCalculator.Input {
		val botId: UUID
		val buyExchangeRate: BigDecimal
		val sellExchangeRate: BigDecimal
		override val chain: Chain
		override val tokenAddress: AddressWrapper
		override val tokenDecimals: Int
		override val tokenAmountBought: BaseAmount
		override val tokenAcquisitionCost: BaseAmount
		override val tokenAmountSold: BaseAmount
		override val tokenDispositionCost: BaseAmount
	}

	fun botToMarketPositionsResults(
		botMarketPositions: List<Input>,
		tokenAddressToTokenPrice: Map<ChainAddress, BaseAmount>,
		chainToExchangeRate: Map<Chain, BigDecimal>,
	): Map<UUID, MarketPositionCalculator.UsdResult> {
		val marketPositionsByBot = botMarketPositions.groupBy { it.botId }
		return marketPositionsByBot
			.mapValues { (_, marketPositions) -> sum(marketPositions, tokenAddressToTokenPrice, chainToExchangeRate) }
	}

	fun toBotMarketPositionResult(
		botMarketPosition: Input,
		tokenAddressToTokenPrice: Map<ChainAddress, BaseAmount>,
		currentExchangeRate: BigDecimal,
	): MarketPositionCalculator.UsdResult {
		if (botMarketPosition.tokenAmountBought == botMarketPosition.tokenAmountSold) {
			return calculateClosedBotMarketPosition(botMarketPosition)
		}

		val addressOnChain = botMarketPosition.tokenAddress.toChainAddress(botMarketPosition.chain)
		val currentPricePerToken =
			tokenAddressToTokenPrice[addressOnChain] ?: return MarketPositionCalculator.UsdResult.EMPTY

		return calculate(botMarketPosition, currentPricePerToken.toNative(botMarketPosition.chain), currentExchangeRate)
			.toUsd(
				pastExchangeRate = botMarketPosition.buyExchangeRate,
				currentExchangeRate = currentExchangeRate,
				rent = BOT_BUY_RENT_BASE_AMOUNT.toNative(Chain.SOLANA),
			)
	}

	fun numOfTransactions(botMarketPositions: List<SearchBotMarketPositionQuery.Result>): Int {
		return botMarketPositions.sumOf {
			val transactionsForPosition = when (it.closedTimeStampAt == null) {
				true -> 1 // open position so can only have buy
				false -> 2 // closed position so must have buy and sell
			}
			transactionsForPosition
		}
	}

	fun numOfProfitablePositions(botMarketPositions: List<SearchBotMarketPositionQuery.Result>): Int {
		return botMarketPositions.count {
			it.closeValueUsd != null && (it.closeValueUsd - it.openValueUsd) >= BigDecimal.ZERO
		}
	}

	fun numOfLossPositions(botMarketPositions: List<SearchBotMarketPositionQuery.Result>): Int {
		return botMarketPositions.count {
			it.closeValueUsd != null && (it.closeValueUsd - it.openValueUsd) < BigDecimal.ZERO
		}
	}

	fun calculatePositionsVolume(botMarketPositions: List<SearchBotMarketPositionQuery.Result>): BigDecimal {
		return botMarketPositions.sumOf { position ->
			position.openValueUsd + (position.closeValueUsd ?: BigDecimal.ZERO)
		}
	}

	fun List<MarketPositionCalculator.UsdResult>.profitablePositionsCount() = count {
		(it.currentValueUsd - it.acquisitionValueUsd) >= BigDecimal.ZERO
	}

	fun List<MarketPositionCalculator.UsdResult>.lossPositionsCount() = count {
		(it.currentValueUsd - it.acquisitionValueUsd) < BigDecimal.ZERO
	}

	fun List<MarketPositionCalculator.UsdResult>.totalClosedVolume() = sumOf {
		it.currentValueUsd + it.acquisitionValueUsd
	}

	fun List<MarketPositionCalculator.UsdResult>.totalOpenVolume() = sumOf { it.acquisitionValueUsd }

	fun sum(
		positions: List<Input>,
		tokenAddressToTokenPrice: Map<ChainAddress, BaseAmount>,
		chainToExchangeRate: Map<Chain, BigDecimal>,
	): MarketPositionCalculator.UsdResult {
		val (closedPositions, openPositions) = positions.partition { it.isPositionClosed() }

		val openPositionsResult = openPositions.fold(MarketPositionCalculator.UsdResult.EMPTY) { acc, position ->
			val chainAddress = position.tokenAddress.toChainAddress(position.chain)

			val tokenPrice = tokenAddressToTokenPrice[chainAddress] ?: return@fold MarketPositionCalculator.UsdResult.EMPTY
			val exchangeRate = chainToExchangeRate.getValue(position.chain)

			val result = calculate(position, tokenPrice.toNative(position.chain), exchangeRate)
			val usdResult = result.toUsd(
				pastExchangeRate = position.buyExchangeRate,
				currentExchangeRate = exchangeRate,
				rent = BOT_BUY_RENT_BASE_AMOUNT.toNative(Chain.SOLANA),
			)

			acc + usdResult
		}

		val closedPositionsResult = closedPositions.fold(MarketPositionCalculator.UsdResult.EMPTY) { acc, botMarketPosition ->
			acc + calculateClosedBotMarketPosition(botMarketPosition)
		}

		return openPositionsResult + closedPositionsResult
	}

	private fun calculateClosedBotMarketPosition(botMarketPosition: Input): MarketPositionCalculator.UsdResult {
		// Position is closed - do not calculate the current value from current token prices
		val tokenAcquisitionCostUsd = botMarketPosition.tokenAcquisitionCost.toNative(chain = botMarketPosition.chain).amount
			.multiply(botMarketPosition.buyExchangeRate)
		val tokenDispositionCostUsd = botMarketPosition.tokenDispositionCost.toNative(chain = botMarketPosition.chain).amount
			.multiply(botMarketPosition.sellExchangeRate)
		return MarketPositionCalculator.UsdResult(
			currentUserTokenAmount = BaseAmount.ZERO,
			acquisitionValueUsd = tokenAcquisitionCostUsd,
			currentValueUsd = tokenDispositionCostUsd,
			currentPnlUsd = tokenDispositionCostUsd - tokenAcquisitionCostUsd,
			currentPnlFraction = calculateFractionChange(tokenDispositionCostUsd, tokenAcquisitionCostUsd),
			rentUsd = BigDecimal.ZERO,
		)
	}

	private fun Input.isPositionClosed() = tokenAmountBought == tokenAmountSold
}
