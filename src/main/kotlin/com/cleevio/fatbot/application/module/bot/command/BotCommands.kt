package com.cleevio.fatbot.application.module.bot.command

import com.cleevio.fatbot.adapter.`in`.rest.dto.PatchBotSettingsRequest
import com.cleevio.fatbot.application.common.command.Command
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.Positive
import java.math.BigDecimal
import java.util.UUID

data class CreateBotCommand(
	val userId: UUID,
	val botDraftId: UUID,
) : Command<CreateBotCommand.Result> {

	data class Result(val botId: UUID)
}

data class PatchBotCommand(
	val userId: UUID,
	val botId: UUID,
	val patchRequest: PatchBotSettingsRequest,
) : Command<Unit>

data class DeleteBotCommand(
	val userId: UUID,
	val botId: UUID,
) : Command<Unit>

data class UpdateBotStatusCommand(
	val userId: UUID,
	val botId: UUID,
	val active: Boolean,
) : Command<Unit>

data class ResetBotBuyFrequencyCommand(
	val userId: UUID,
	val botId: UUID,
) : Command<Unit>

data class WithdrawFromBotPortfolioCommand(
	val userId: UUID,
	val botId: UUID,
	@field:Positive val nativeAmount: BigDecimal,
	@field:Positive @field:DecimalMax("1.0") val percentageOf: BigDecimal,
	@field:Valid val recipientAddress: AddressWrapper,
) : Command<WithdrawFromBotPortfolioCommand.Result> {

	@Schema(name = "WithdrawFromBotPortfolioResult")
	data class Result(val txHash: TxHash)
}
