package com.cleevio.fatbot.application.module.bot.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

interface GetBots {

	operator fun invoke(userId: UUID, botIds: Set<UUID>): List<Result>

	data class Result(
		val id: UUID,
		val name: String,
		val isActive: <PERSON><PERSON><PERSON>,
		val numberOfActiveDays: Long,
		val botAvatarFileId: UUID,
		val buyFrequency: Long,
		val remainingBuyFrequency: Long,
		val buyFrequencyLastResetAt: Instant,
		val walletBalance: BaseAmount,
		val walletAcquisitionValueUsd: BigDecimal,
		val walletChain: Chain,
	)
}
