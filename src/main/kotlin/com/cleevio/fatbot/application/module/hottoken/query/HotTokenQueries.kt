package com.cleevio.fatbot.application.module.hottoken.query

import com.cleevio.fatbot.adapter.`in`.InfiniteScroll
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import java.math.BigDecimal
import java.net.URI
import java.util.UUID

class GetHotTokensQuery private constructor(
	@field:Valid val infiniteScroll: InfiniteScroll<BigDecimal>,
	val signedUserParams: SignedUserParams?,
) : Query<InfiniteScrollSlice<GetHotTokensQuery.Result, BigDecimal>> {

	data class SignedUserParams(val userId: UUID, val useSelectedChains: Boolean)

	companion object {
		fun asAnonymousUser(infiniteScroll: InfiniteScroll<BigDecimal>) = GetHotTokensQuery(
			infiniteScroll = infiniteScroll,
			signedUserParams = null,
		)

		fun asSignedUser(infiniteScroll: InfiniteScroll<BigDecimal>, userId: UUID, useSelectedChains: Boolean) =
			GetHotTokensQuery(
				infiniteScroll = infiniteScroll,
				signedUserParams = SignedUserParams(userId = userId, useSelectedChains = useSelectedChains),
			)
	}

	@Schema(name = "GetHotTokensResult")
	data class Result(
		val id: UUID,
		val tokenAddress: AddressWrapper,
		val tokenDetailUrl: URI,
		val tokenName: String,
		val tokenSymbol: String,
		val tokenChain: Chain,
		val tokenImageUrl: URI?,
		val change24h: BigDecimal?,
		val volume24hUsd: BigDecimal,
		val priceUsd: BigDecimal,
	)
}
