package com.cleevio.fatbot.application.module.referral.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.util.UUID

interface GetReferralRewardsSummaryByChain {

	operator fun invoke(userId: UUID): Map<Chain, Result>

	data class Result(
		val totalBaseAmount: BaseAmount,
		val unclaimedBaseAmount: BaseAmount,
	)
}
