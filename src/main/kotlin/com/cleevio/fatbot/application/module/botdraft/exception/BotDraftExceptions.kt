package com.cleevio.fatbot.application.module.botdraft.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class BotDraftNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.BOT_DRAFT_NOT_FOUND,
	message = message,
)
