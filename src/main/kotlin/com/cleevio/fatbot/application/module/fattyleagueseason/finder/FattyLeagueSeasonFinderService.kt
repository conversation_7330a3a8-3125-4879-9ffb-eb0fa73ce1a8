package com.cleevio.fatbot.application.module.fattyleagueseason.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.module.fattyleagueseason.exception.FattyLeagueSeasonNotFoundException
import com.cleevio.fatbot.domain.fattyleagueseason.FattyLeagueSeason
import com.cleevio.fatbot.domain.fattyleagueseason.FattyLeagueSeasonRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class FattyLeagueSeasonFinderService(
	private val fattyLeagueSeasonRepository: FattyLeagueSeasonRepository,
) : BaseFinderService<FattyLeagueSeason>(fattyLeagueSeasonRepository) {

	override fun errorBlock(message: String) = throw FattyLeagueSeasonNotFoundException(message)

	override fun getEntityType() = FattyLeagueSeason::class

	@Transactional(readOnly = true)
	fun findLastSeason() = fattyLeagueSeasonRepository.findTopByOrderBySeasonNumberDesc()
}
