package com.cleevio.fatbot.application.module.token.constant

enum class CandleChartTimeRange(
	val intervalCount: Int,
	val timeInterval: TimeInterval,
	val limit: Int,
) {
	ONE_SECOND(intervalCount = 1, timeInterval = TimeInterval.SECOND, limit = 360),
	THREE_SECONDS(intervalCount = 3, timeInterval = TimeInterval.SECOND, limit = 360),
	FIVE_SECONDS(intervalCount = 5, timeInterval = TimeInterval.SECOND, limit = 360),
	FIFTEEN_SECONDS(intervalCount = 15, timeInterval = TimeInterval.SECOND, limit = 360),
	THIRTY_SECONDS(intervalCount = 30, timeInterval = TimeInterval.SECOND, limit = 360),

	ONE_MINUTE(intervalCount = 1, timeInterval = TimeInterval.MINUTE, limit = 360),
	THREE_MINUTES(intervalCount = 3, timeInterval = TimeInterval.MINUTE, limit = 360),
	FIVE_MINUTES(intervalCount = 5, timeInterval = TimeInterval.MINUTE, limit = 360),
	FIFTEEN_MINUTES(intervalCount = 15, timeInterval = TimeInterval.MINUTE, limit = 360),
	THIRTY_MINUTES(intervalCount = 30, timeInterval = TimeInterval.MINUTE, limit = 360),
	FORTY_FIVE_MINUTES(intervalCount = 45, timeInterval = TimeInterval.MINUTE, limit = 360),

	ONE_HOUR(intervalCount = 1, timeInterval = TimeInterval.HOUR, limit = 360),
	TWO_HOURS(intervalCount = 2, timeInterval = TimeInterval.HOUR, limit = 360),
	THREE_HOURS(intervalCount = 3, timeInterval = TimeInterval.HOUR, limit = 360),
	FOUR_HOURS(intervalCount = 4, timeInterval = TimeInterval.HOUR, limit = 360),

	ONE_DAY(intervalCount = 1, timeInterval = TimeInterval.DAY, limit = 360),
}
