package com.cleevio.fatbot.application.module.matchmaking.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.CONFLICT)
class BotMarketPositionNotOpenedException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.BOT_MARKET_POSITION_NOT_OPENED_EXCEPTION,
	message = message,
)
