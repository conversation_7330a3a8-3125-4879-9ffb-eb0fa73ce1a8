package com.cleevio.fatbot.application.module.hottoken.port.out

import com.cleevio.fatbot.adapter.`in`.InfiniteScroll
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.net.URI
import java.util.UUID

interface GetHotTokens {

	data class Result(
		val id: UUID,
		val tokenAddress: AddressWrapper,
		val tokenName: String,
		val tokenSymbol: String,
		val tokenChain: Chain,
		val tokenImageUrl: URI?,
		val volume24hUsd: BigDecimal,
	)

	operator fun invoke(
		chains: Set<Chain>,
		infiniteScroll: InfiniteScroll<BigDecimal>,
		fileToUrlMapper: FileToUrlMapper,
	): InfiniteScrollSlice<Result, BigDecimal>
}
