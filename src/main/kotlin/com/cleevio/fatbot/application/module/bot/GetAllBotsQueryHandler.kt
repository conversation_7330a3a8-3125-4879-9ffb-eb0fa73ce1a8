package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.bot.query.GetAllBotsQuery
import org.springframework.stereotype.Component

@Component
class GetAllBotsQueryHandler(
	private val botFinderService: BotFinderService,
) : QueryHandler<List<GetAllBotsQuery.Result>, GetAllBotsQuery> {

	override val query = GetAllBotsQuery::class

	override fun handle(query: GetAllBotsQuery): List<GetAllBotsQuery.Result> {
		val bots = botFinderService.findAllByUserId(query.userId)

		val result = bots.map {
			GetAllBotsQuery.Result(
				id = it.id,
				createdAt = it.createdAt,
				userReadableId = it.userReadableBotId(),
				name = it.name,
				avatarFileId = it.avatarFileId,
				tradeAmount = it.tradeAmount,
				buyFrequency = it.buyFrequency.toBigInteger(),
				remainingBuyFrequency = it.remainingBuyFrequency.toBigInteger(),
				buyFrequencyLastResetAt = it.buyFrequencyLastResetAt,
				profitTargetFraction = it.profitTargetFraction,
				stopLossFraction = it.stopLossFraction,
				buyVolume = it.buyVolume,
				sellVolume = it.sellVolume,
				dailyVolumeFromUsd = it.dailyVolumeFromUsd,
				dailyVolumeToUsd = it.dailyVolumeToUsd,
				liquidityFromUsd = it.liquidityFromUsd,
				liquidityToUsd = it.liquidityToUsd,
				marketCapFromUsd = it.marketCapFromUsd,
				marketCapToUsd = it.marketCapToUsd,
				numberOfHoldersFrom = it.numberOfHoldersFrom?.toBigInteger(),
				numberOfHoldersTo = it.numberOfHoldersTo?.toBigInteger(),
				tokenTickerCopyIsChecked = it.tokenTickerCopyIsChecked,
				creatorHighBuyIsChecked = it.creatorHighBuyIsChecked,
				bundledBuysDetectedIsChecked = it.bundledBuysDetectedIsChecked,
				suspiciousWalletsDetectedIsChecked = it.suspiciousWalletsDetectedIsChecked,
				singleHighBuyIsChecked = it.singleHighBuyIsChecked,
				shouldWaitBeforeBuying = it.buyTokensAliveAtLeastFor != null,
				shouldAutoSellAfterHoldTime = it.shouldAutoSellAfterHoldTime,
				sellTransactionFraction = it.sellTransactionFraction,
				buyTransactionFraction = it.buyTransactionFraction,
			)
		}

		return result
	}
}
