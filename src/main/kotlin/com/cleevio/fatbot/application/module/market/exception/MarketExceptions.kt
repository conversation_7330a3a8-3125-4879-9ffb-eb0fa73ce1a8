package com.cleevio.fatbot.application.module.market.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.BAD_REQUEST)
class IllegalChainException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.ILLEGAL_CHAIN,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class MissingChainIdException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.MISSING_CHAIN_ID,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class UnsupportedChainIdException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.UNSUPPORTED_CHAIN_ID,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class InvalidChainAddressCombinationException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.INVALID_CHAIN_ADDRESS_COMBINATION_EXCEPTION,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class InvalidDexPairProvidedException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.INVALID_DEX_PAIR_PROVIDED,
	message = message,
)

@ResponseStatus(HttpStatus.NOT_FOUND)
class MarketPositionNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.MARKET_POSITION_NOT_FOUND,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class DataNotProvided(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.DATA_NOT_PROVIDED,
	message = message,
)
