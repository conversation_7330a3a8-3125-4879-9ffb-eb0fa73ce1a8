package com.cleevio.fatbot.application.module.botdraft.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.module.botdraft.exception.BotDraftNotFoundException
import com.cleevio.fatbot.domain.botdraft.BotDraft
import com.cleevio.fatbot.domain.botdraft.BotDraftRepository
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class BotDraftFinderService(
	private val botDraftRepository: BotDraftRepository,
) : BaseFinderService<BotDraft>(botDraftRepository) {

	override fun errorBlock(message: String) = throw BotDraftNotFoundException(message)

	override fun getEntityType() = BotDraft::class

	fun findAllByUserId(userId: UUID): List<BotDraft> = botDraftRepository.findAllByUserId(userId)

	fun getByIdAndUserId(id: UUID, userId: UUID): BotDraft =
		botDraftRepository.findByIdAndUserId(id, userId) ?: errorBlock("BotDraft for user $userId with ID $id not found!")
}
