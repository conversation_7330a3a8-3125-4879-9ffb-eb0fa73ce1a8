package com.cleevio.fatbot.application.module.token.query

import com.cleevio.fatbot.adapter.`in`.rest.dto.DexPairInfoInputV2
import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.token.constant.CandleChartTimeRange
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant
import java.util.UUID

data class TokenPriceTimeIntervalItem(
	val timestamp: Instant,
	val close: BigDecimal,
	val closeNative: BigDecimal,
	val volume: BigDecimal,
)

data class TokenOHLCTimeIntervalItem(
	val timestamp: Instant,
	val openUsd: BigDecimal,
	val openNative: BigDecimal,
	val highUsd: BigDecimal,
	val lowUsd: BigDecimal,
	val closeUsd: BigDecimal,
	val closeNative: BigDecimal,
	val volumeUsd: BigDecimal,
)

data class GetContinuousTokenPriceChartQuery private constructor(
	val chain: Chain,
	val pairAddress: AddressWrapper,
	val timeRange: TimeRange,
	val includeNowItem: Boolean,
) : Query<GetContinuousTokenPriceChartQuery.Result> {

	companion object {
		fun withNowItem(chain: Chain, pairAddress: AddressWrapper, timeRange: TimeRange) = GetContinuousTokenPriceChartQuery(
			chain = chain,
			pairAddress = pairAddress,
			timeRange = timeRange,
			includeNowItem = true,
		)

		fun withoutNowItem(chain: Chain, pairAddress: AddressWrapper, timeRange: TimeRange) =
			GetContinuousTokenPriceChartQuery(
				chain = chain,
				pairAddress = pairAddress,
				timeRange = timeRange,
				includeNowItem = false,
			)
	}

	@Schema(name = "GetContinuousTokenPriceChartResult")
	data class Result(
		val data: List<TokenPriceTimeIntervalItem>,
	)
}

data class GetTokenPriceChartQuery private constructor(
	@field:Valid val chain: Chain,
	val pairAddress: AddressWrapper,
	val timeRange: TimeRange,
	val includeNowItem: Boolean,
) : Query<GetTokenPriceChartQuery.Result> {

	companion object {
		fun withNowItem(chain: Chain, pairAddress: AddressWrapper, timeRange: TimeRange) = GetTokenPriceChartQuery(
			chain = chain,
			pairAddress = pairAddress,
			timeRange = timeRange,
			includeNowItem = true,
		)

		fun withoutNowItem(chain: Chain, pairAddress: AddressWrapper, timeRange: TimeRange) = GetTokenPriceChartQuery(
			chain = chain,
			pairAddress = pairAddress,
			timeRange = timeRange,
			includeNowItem = false,
		)
	}

	@Schema(name = "GetTokenPriceChartResult")
	data class Result(
		val data: List<TokenPriceTimeIntervalItem>,
	)
}

data class GetTokenMaxBuyQuery(
	val userId: UUID,
	val tokenAddress: AddressWrapper,
	val walletId: UUID,
	val dexPairInfo: GetDex.Result,
) : Query<GetTokenMaxBuyQuery.Result> {

	@Schema(name = "GetTokenMaxBuyResult")
	data class Result(val maxBuy: BigInteger?)
}

data class GetTokenTaxQuery(
	val tokenAddress: AddressWrapper,
	val chain: Chain,
	// Use the Input structure which is flat to allow this to be used as a cache key
	val dexPairInfo: DexPairInfoInputV2,
) : Query<GetTokenTaxQuery.Result> {

	@Schema(name = "GetTokenTaxResult")
	data class Result(val buyTax: BigDecimal?, val sellTax: BigDecimal?, val isHoneypot: Boolean)
}

data class GetTokenHistoricalCandleChartQuery(
	val tokenAddress: AddressWrapper,
	val chain: Chain,
	val timeRange: CandleChartTimeRange,
	val before: Instant?,
	val after: Instant?,
) : Query<List<TokenOHLCTimeIntervalItem>>

data class GetIsTokenContractVerifiedQuery(
	val tokenAddress: AddressWrapper,
	val chain: Chain,
) : Query<GetIsTokenContractVerifiedQuery.Result> {
	@Schema(name = "GetIsTokenContractVerifiedResult")
	data class Result(val isVerified: Boolean)
}
