package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.net.URI

interface GetTokenImageUrl {
	operator fun invoke(token: AddressWrapper, chain: Chain, mapper: FileToUrlMapper): URI?
}
