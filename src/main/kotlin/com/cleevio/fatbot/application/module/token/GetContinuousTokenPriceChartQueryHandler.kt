package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.market.query.GetTokenDetailQuery
import com.cleevio.fatbot.application.module.token.finder.TokenPairInfoFinderService
import com.cleevio.fatbot.application.module.token.port.out.GetIntervalTokenPrices
import com.cleevio.fatbot.application.module.token.port.out.GetSingleIntervalTokenPrice
import com.cleevio.fatbot.application.module.token.query.GetContinuousTokenPriceChartQuery
import com.cleevio.fatbot.application.module.token.query.TokenPriceTimeIntervalItem
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Clock
import java.time.Duration
import java.time.Instant

@Component
class GetContinuousTokenPriceChartQueryHandler(
	private val getIntervalTokenPrices: GetIntervalTokenPrices,
	private val getSingleIntervalTokenPriceBitquery: GetSingleIntervalTokenPrice,
	private val tokenPairInfoFinderService: TokenPairInfoFinderService,
	private val clock: Clock,
) : QueryHandler<GetContinuousTokenPriceChartQuery.Result, GetContinuousTokenPriceChartQuery> {

	override val query = GetContinuousTokenPriceChartQuery::class

	@Lazy
	@Autowired
	private lateinit var queryBus: QueryBus

	override fun handle(query: GetContinuousTokenPriceChartQuery): GetContinuousTokenPriceChartQuery.Result {
		val pairInfo = tokenPairInfoFinderService.getByPairAddressAndChain(query.pairAddress, query.chain)

		val data = getIntervalTokenPrices(
			chain = query.chain,
			pairAddress = query.pairAddress,
			dex = pairInfo.dexType,
			tokenAddress = pairInfo.tokenAddress,
			timeRange = query.timeRange,
		)

		val intervalPeriod = query.timeRange.run {
			timeInterval.unit.duration.multipliedBy(intervalCount.toLong())
		}

		val result = completeTokenPriceIntervalData(
			intervalItems = data.intervalItems,
			extraIntervalItems = data.extraIntervalItems,
			intervalStart = data.intervalStart,
			intervalEnd = data.intervalEnd,
			intervalPeriod = intervalPeriod,
			fetchIntervalItemBeforeIntervalStart = {
				getSingleIntervalTokenPriceBitquery(
					chain = query.chain,
					pairAddress = query.pairAddress,
					tokenAddress = pairInfo.tokenAddress,
					dex = pairInfo.dexType,
					intervalCount = query.timeRange.intervalCount,
					timeInterval = query.timeRange.timeInterval,
					before = data.intervalStart,
				)
			},
		)

		if (!query.includeNowItem) return GetContinuousTokenPriceChartQuery.Result(result)

		// To make the graph end price and token detail end price the same, we have to use the cached data
		// from token detail
		val tokenDetail = queryBus(
			GetTokenDetailQuery.asAnonymousUser(
				chain = pairInfo.chain,
				tokenAddress = pairInfo.tokenAddress,
			),
		)

		val priceNowNative = (tokenDetail.tokenInfo as GetTokenDetailQuery.DexDetail).dexInfo.priceNative
		val priceNowUsd = tokenDetail.tokenInfo.dexInfo.priceUsd

		val tokenPriceTimeIntervalNow = TokenPriceTimeIntervalItem(
			timestamp = Instant.now(clock),
			close = priceNowUsd,
			closeNative = priceNowNative,
			volume = result.lastOrNull()?.volume ?: BigDecimal.ZERO,
		)

		return GetContinuousTokenPriceChartQuery.Result(result + tokenPriceTimeIntervalNow)
	}

	private fun completeTokenPriceIntervalData(
		intervalItems: List<TokenPriceTimeIntervalItem>,
		extraIntervalItems: List<TokenPriceTimeIntervalItem>,
		intervalStart: Instant,
		intervalEnd: Instant,
		intervalPeriod: Duration,
		fetchIntervalItemBeforeIntervalStart: () -> TokenPriceTimeIntervalItem?,
	): List<TokenPriceTimeIntervalItem> {
		val resultBeforeInterval = lazy {
			val beforeIntervalResult =
				extraIntervalItems.maxByOrNull { it.timestamp } ?: fetchIntervalItemBeforeIntervalStart()

			// Only the `close` value is applicable for the inferred (copied) result
			beforeIntervalResult?. copy(volume = BigDecimal.ZERO)
		}

		if (intervalItems.isNotEmpty()) {
			val lastResult = intervalItems.maxBy { it.timestamp }.copy(volume = BigDecimal.ZERO)
			val missingEnd = generateSequence(lastResult.timestamp) { it.plus(intervalPeriod) }
				.drop(1)
				.takeWhile { it < intervalEnd }
				.map { lastResult.copy(timestamp = it) }
				.toList()

			val resultWithEndFilled = intervalItems + missingEnd
			val resultWithAllFilled = resultWithEndFilled.fillMissing(intervalPeriod)

			return resultWithAllFilled
		}

		val beforeIntervalItem = resultBeforeInterval.value ?: return emptyList()

		// Millis to jump to / before the start of our interval
		val alignMillis = Duration.between(beforeIntervalItem.timestamp, intervalStart)
			.dividedBy(intervalPeriod)
			.times(intervalPeriod.toMillis())

		/*
		 This might either be one intervalPeriod before or after `after` (depending on the division)
		 The goal was to avoid floating points (and `ceil`),
		 so we rather ensure the interval with an additional `dropWhile { it < after }`
		 */
		val alignedStart = beforeIntervalItem.timestamp.plusMillis(alignMillis)

		val createdResults = generateSequence(alignedStart) { it.plus(intervalPeriod) }
			.dropWhile { it < intervalStart }
			.takeWhile { it < intervalEnd }
			.map { beforeIntervalItem.copy(timestamp = it) }
			.toList()

		return createdResults
	}

	private fun List<TokenPriceTimeIntervalItem>.fillMissing(intervalPeriod: Duration): List<TokenPriceTimeIntervalItem> {
		val sorted = sortedBy { it.timestamp }

		val missingEntries = sorted.zipWithNext { previous, current ->
			val intervalGap = Duration.between(previous.timestamp, current.timestamp).dividedBy(intervalPeriod)

			val missingEntries = intervalGap - 1

			(1..missingEntries).map {
				val intervalDuration = intervalPeriod.multipliedBy(it)

				previous.copy(
					timestamp = previous.timestamp.plus(intervalDuration),
					volume = BigDecimal.ZERO,
				)
			}
		}

		return this.plus(missingEntries.flatten()).sortedBy { it.timestamp }
	}
}
