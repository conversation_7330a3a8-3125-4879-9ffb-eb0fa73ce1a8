package com.cleevio.fatbot.application.module.matchmaking.event

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateType
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant
import java.util.UUID

sealed interface BotRequestedTransactionEvent {
	val tokenAddress: AddressWrapper
	val creator: AddressWrapper
}

sealed interface HighPriorityBotTransaction : BotRequestedTransactionEvent {
	val blockSlot: Int
	override val tokenAddress: AddressWrapper
}

sealed interface LowPriorityBotTransaction : BotRequestedTransactionEvent {
	val requestedAt: Instant
	override val tokenAddress: AddressWrapper
}

data class BotBuyTransactionRequest(
	override val blockSlot: Int,
	override val tokenAddress: AddressWrapper,
	override val creator: AddressWrapper,
	val botId: UUID,
	val botWalletId: UUID,
	val userId: UUID,
	val privateKey: String,
	val tradeAmountInBase: BaseAmount,
	val chain: Chain,
) : HighPriorityBotTransaction

data class BotSellTransactionRequest(
	override val blockSlot: Int,
	override val tokenAddress: AddressWrapper,
	override val creator: AddressWrapper,
	val botId: UUID,
	val botWalletId: UUID,
	val userId: UUID,
	val privateKey: String,
	val amountToSell: BigInteger,
	val chain: Chain,
) : HighPriorityBotTransaction

data class BotSellRedFlaggedTokenRequest(
	override val blockSlot: Int,
	override val tokenAddress: AddressWrapper,
	override val creator: AddressWrapper,
	val botId: UUID,
	val botWalletId: UUID,
	val userId: UUID,
	val privateKey: String,
	val amountToSell: BigInteger,
	val chain: Chain,
) : HighPriorityBotTransaction

data class BotSellPromotedTokenRequest(
	override val blockSlot: Int,
	override val tokenAddress: AddressWrapper,
	override val creator: AddressWrapper,
	val botWalletId: UUID,
	val privateKey: String,
	val pairAddress: AddressWrapper,
	val amountToSell: BaseAmount,
	val userId: UUID,
	val chain: Chain,
) : HighPriorityBotTransaction

data class BotSellStaleTokenRequest(
	override val requestedAt: Instant,
	override val tokenAddress: AddressWrapper,
	override val creator: AddressWrapper,
	val botId: UUID,
	val botWalletId: UUID,
	val userId: UUID,
	val privateKey: String,
	val amountToSell: BigInteger,
	val chain: Chain,
) : LowPriorityBotTransaction

data class BotForceSellTokenRequest(
	override val requestedAt: Instant,
	override val tokenAddress: AddressWrapper,
	override val creator: AddressWrapper,
	val botId: UUID,
	val botWalletId: UUID,
	val userId: UUID,
	val privateKey: String,
	val amountToSell: BigInteger,
	val chain: Chain,
) : LowPriorityBotTransaction

sealed interface BotTransactionSentEvent {
	val type: BotTransactionType
	val botWalletId: UUID
	val tradeAmount: BigInteger
	val signedTx: SignedTx
	val tokenAddress: AddressWrapper
	val chain: Chain
}

data class BotHighPriorityTransactionSentEvent(
	override val type: BotTransactionType,
	override val botWalletId: UUID,
	override val tradeAmount: BigInteger,
	override val signedTx: SignedTx,
	override val tokenAddress: AddressWrapper,
	override val chain: Chain,
	val blockSlot: Int,
) : BotTransactionSentEvent

data class BotLowPriorityTransactionSentEvent(
	override val type: BotTransactionType,
	override val botWalletId: UUID,
	override val tradeAmount: BigInteger,
	override val signedTx: SignedTx,
	override val tokenAddress: AddressWrapper,
	override val chain: Chain,
	val requestedAt: Instant,
) : BotTransactionSentEvent

data class BoughtTokensEvent(
	val boughtTokens: List<BoughtToken>,
) {
	data class BoughtToken(
		val chain: Chain,
		val tokenAddress: AddressWrapper,
		val name: String,
		val symbol: String,
		val infoUrl: String?,
	)
}

data class TradedBotMarketPositionsEvent(
	val positions: List<TradedPosition>,
) {
	data class TradedPosition(
		val botMarketPositionId: UUID,
		val type: BotMarketPositionTradeStateType,
		val marketCapUsd: BigDecimal,
		val liquidityUsd: BigDecimal,
		val volumeUsd: BigDecimal,
		val numOfAccountHolders: Long,
		val buyVolume: BigDecimal,
		val sellVolume: BigDecimal,
		val fractionOfSellTransactions: BigDecimal,
	)
}
