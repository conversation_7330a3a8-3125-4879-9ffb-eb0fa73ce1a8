package com.cleevio.fatbot.application.module.referral.query

import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.util.UUID

data class GetReferralRewardsSummaryQuery(
	val userId: UUID,
) : Query<GetReferralRewardsSummaryQuery.Result> {

	@Schema(name = "GetReferralRewardsSummaryResult")
	data class Result(
		val numberOfReferrals: Int,
		val referralsOnChains: List<ChainData>,
	)

	@Schema(name = "GetReferralRewardsSummaryResultChainData")
	data class ChainData(
		val chain: Chain,
		val totalAmountNative: NativeAmount,
		val totalAmountUsd: BigDecimal,
		val unclaimedAmountNative: NativeAmount,
		val unclaimedAmountUsd: BigDecimal,
	)
}

data class GetClaimingLimitsQuery(val userId: UUID) : Query<List<GetClaimingLimitsQuery.Result>> {

	@Schema(name = "GetClaimingLimitsResult")
	data class Result(
		val chain: Chain,
		val minimalClaimNativeAmount: NativeAmount,
	)
}
