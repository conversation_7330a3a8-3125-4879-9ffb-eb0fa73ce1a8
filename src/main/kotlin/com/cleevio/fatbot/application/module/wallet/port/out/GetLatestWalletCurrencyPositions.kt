package com.cleevio.fatbot.application.module.wallet.port.out

import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant
import java.util.UUID

interface GetLatestWalletCurrencyPositions {
	operator fun invoke(
		userId: UUID,
		walletId: UUID?,
		chains: Set<Chain>,
		createdBefore: Instant?,
	): List<WalletCurrencyPosition>

	data class WalletCurrencyPosition(
		val chain: Chain,
		val totalBought: BigInteger,
		val totalSold: BigInteger,
		val totalAcquisitionCostUsd: BigDecimal,
	)
}
