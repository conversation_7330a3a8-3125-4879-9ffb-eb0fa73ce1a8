package com.cleevio.fatbot.application.module.userfattycard.service

import com.cleevio.fatbot.application.module.fattycard.finder.FattyCardFinderService
import com.cleevio.fatbot.application.module.fattycard.properties.FattyCardProperties
import com.cleevio.fatbot.application.module.userfattycard.finder.UserFattyCardFinderService
import com.cleevio.fatbot.domain.fattycard.FattyCard
import com.cleevio.fatbot.domain.userfattycard.UserFattyCardCreateService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Clock
import java.time.LocalDate
import java.util.Random
import java.util.UUID
import kotlin.math.min

@Service
class CreateUserFattyCardsService(
	private val userFattyCardCreateService: UserFattyCardCreateService,
	private val userFattyCardFinderService: UserFattyCardFinderService,
	private val fattyCardFinderService: FattyCardFinderService,
	private val fattyCardProperties: FattyCardProperties,
	private val clock: Clock,
) {

	@Transactional
	fun create(newAmount: BigDecimal, originalAmount: BigDecimal, userId: UUID) {
		val amountOfFattyCardsEarnedToday = userFattyCardFinderService.countByUserIdAndCreatedAtDate(
			userId = userId,
			createdAt = LocalDate.now(clock),
		)

		if (amountOfFattyCardsEarnedToday >= fattyCardProperties.maxCardsEarnedInDay) {
			return
		}

		val tradedAmountForCard = fattyCardProperties.tradeAmountNeededForCard

		val unClaimedAmount = originalAmount % tradedAmountForCard
		val numberOfNewCardsEarned = (unClaimedAmount + newAmount)
			.divide(tradedAmountForCard, 2, RoundingMode.HALF_DOWN)
			.toInt()

		// There is a limit for number of cards that can be earned in one day
		val numOfCardsEarned = min(
			numberOfNewCardsEarned,
			fattyCardProperties.maxCardsEarnedInDay - amountOfFattyCardsEarnedToday,
		)

		selectCardsBasedOnProbability(numOfCardsEarned).forEach {
			userFattyCardCreateService.create(userId = userId, fattyCardId = it)
		}
	}

	/**
	 * Weighted Random Selection Algorithm
	 *
	 * Example: For cards with probabilities [15%, 50%, 5%, 10%, 20%]
	 *
	 * 1. Create cumulative probability array: [15, 65, 70, 80, 100]
	 * 2. Generate random number between 0-99 (inclusive)
	 * 3. Find where random number falls in cumulative array using binary search
	 */
	fun selectCardsBasedOnProbability(numOfCards: Int): List<UUID> {
		val allCards = fattyCardFinderService.findAll()
		require(allCards.isNotEmpty()) { "No fatty cards available in the database" }

		// Calculate total probability and normalize if needed
		val totalProbability = allCards.sumOf { it.probability }

		// creates array of cumulative probabilities
		val cumulativeProbabilities = allCards.map { it.probability }
			.runningReduce { acc, probability -> acc + probability }

		return List(numOfCards) { pickCard(allCards, cumulativeProbabilities, totalProbability) }
	}

	private fun pickCard(
		cards: List<FattyCard>,
		cumulativeProbabilities: List<BigDecimal>,
		totalProbability: BigDecimal,
	): UUID {
		// Generate random value in the range of total probability
		val rand = Random().nextDouble(0.0, totalProbability.toDouble()).toBigDecimal()

		// Find card index using binary search
		val index = cumulativeProbabilities.indexOfFirst { it > rand }
		return cards[index].id
	}
}
