package com.cleevio.fatbot.application.module.bot.model

import com.cleevio.fatbot.adapter.`in`.rest.dto.PatchBotSettingsRequest
import com.cleevio.fatbot.application.common.util.ifNull
import com.cleevio.fatbot.domain.bot.Bot
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.PositiveOrZero
import java.math.BigDecimal
import java.math.BigInteger
import java.math.RoundingMode
import java.time.Duration
import java.time.temporal.ChronoUnit
import java.util.UUID

data class BotUpdate(
	val name: String,
	val avatarFileId: UUID,
	@field:Positive val tradeAmount: BigDecimal,
	@field:Positive val buyFrequency: Long,
	@field:PositiveOrZero val remainingBuyFrequency: Long,
	@field:Positive val profitTargetFraction: BigDecimal,
	@field:Positive val stopLossFraction: BigDecimal,
	val tokenTickerCopyIsChecked: <PERSON><PERSON><PERSON>,
	val creatorHighBuyIsChecked: <PERSON><PERSON><PERSON>,
	val bundledBuysDetectedIsChecked: <PERSON>olean,
	val suspiciousWalletsDetectedIsChecked: Boolean,
	val singleHighBuyIsChecked: Boolean,
	val shouldAutoSellAfterHoldTime: Boolean,
	val buyTokensAliveAtLeastFor: Duration?,

	@field:PositiveOrZero val marketCapFromUsd: BigDecimal?,
	@field:Positive val marketCapToUsd: BigDecimal?,
	@field:PositiveOrZero val liquidityFromUsd: BigDecimal?,
	@field:Positive val liquidityToUsd: BigDecimal?,
	@field:PositiveOrZero val dailyVolumeFromUsd: BigDecimal?,
	@field:Positive val dailyVolumeToUsd: BigDecimal?,
	@field:PositiveOrZero val numberOfHoldersFrom: Long?,
	@field:Positive val numberOfHoldersTo: Long?,
	@field:DecimalMin("0.50") @field:DecimalMax("1.00") val buyVolume: BigDecimal?,
	@field:DecimalMin("0.00") @field:DecimalMax("0.50") val sellVolume: BigDecimal?,
	@field:DecimalMin("0.00") @field:DecimalMax("1.00") val sellTransactionFraction: BigDecimal?,
	@field:DecimalMin("0.00") @field:DecimalMax("1.00") val buyTransactionFraction: BigDecimal?,
)

private fun <T : Comparable<T>> getValidIntervalPair(from: T?, to: T?): Pair<T?, T?> {
	require(from == null || to == null || from < to) {
		"Invalid empty interval. $from >= $to"
	}

	return from to to
}

private fun getValidVolumeRange(buyVolume: BigDecimal?, sellVolume: BigDecimal?): Pair<BigDecimal?, BigDecimal?> {
	if ((buyVolume == null && sellVolume != null) || (buyVolume != null && sellVolume == null)) {
		throw IllegalArgumentException("Invalid volume, both buy volume and sell volume must be filled.")
	}

	if (buyVolume != null && sellVolume != null) {
		val volumeSum = buyVolume.plus(sellVolume).setScale(2, RoundingMode.CEILING)
		val requiredVolumeSum = BigDecimal.ONE.setScale(2)
		require(volumeSum == requiredVolumeSum) {
			"Invalid buy volume: $buyVolume and sell volume: $sellVolume, sum of two values must be always ${BigDecimal.ONE}."
		}
	}

	return buyVolume to sellVolume
}

private fun getValidTransactionFractionRange(
	buyTxFraction: BigDecimal?,
	sellTxFraction: BigDecimal?,
): Pair<BigDecimal?, BigDecimal?> {
	if ((buyTxFraction == null && sellTxFraction != null) || (buyTxFraction != null && sellTxFraction == null)) {
		throw IllegalArgumentException("Invalid transaction fraction, both buy and sell must be filled.")
	}

	if (buyTxFraction != null && sellTxFraction != null) {
		val sum = buyTxFraction.plus(sellTxFraction).setScale(2, RoundingMode.CEILING)
		val requiredSum = BigDecimal.ONE.setScale(2)
		require(sum == requiredSum) {
			"Invalid tx fraction: $buyTxFraction and tx fraction: $sellTxFraction, sum of two values must be always ${BigDecimal.ONE}."
		}
	}

	return buyTxFraction to sellTxFraction
}

fun PatchBotSettingsRequest.toValidBotUpdate(bot: Bot): BotUpdate {
	val (marketCapFrom, marketCapTo) = getValidIntervalPair(
		from = this.marketCapFromUsd.ifNull(bot.marketCapFromUsd),
		to = this.marketCapToUsd.ifNull(bot.marketCapToUsd),
	)

	val (liquidityFrom, liquidityTo) = getValidIntervalPair(
		from = this.liquidityFromUsd.ifNull(bot.liquidityFromUsd),
		to = this.liquidityToUsd.ifNull(bot.liquidityToUsd),
	)

	val (dailyVolumeFrom, dailyVolumeTo) = getValidIntervalPair(
		from = this.dailyVolumeFromUsd.ifNull(bot.dailyVolumeFromUsd),
		to = this.dailyVolumeToUsd.ifNull(bot.dailyVolumeToUsd),
	)

	val (numberOfHoldersFrom, numberOfHoldersTo) = getValidIntervalPair(
		from = this.numberOfHoldersFrom?.map { it.toLong() }.ifNull(bot.numberOfHoldersFrom),
		to = this.numberOfHoldersTo?.map { it.toLong() }.ifNull(bot.numberOfHoldersTo),
	)

	val (buyVolume, sellVolume) = getValidVolumeRange(
		buyVolume = this.buyVolume.ifNull(bot.buyVolume),
		sellVolume = this.sellVolume.ifNull(bot.sellVolume),
	)

	val (buyTransactionFraction, sellTransactionFraction) = getValidTransactionFractionRange(
		buyTxFraction = this.buyTransactionFraction.ifNull(bot.buyTransactionFraction),
		sellTxFraction = this.sellTransactionFraction.ifNull(bot.sellTransactionFraction),
	)

	val remainingBuyFrequency = this.buyFrequency?.let { newBuyFrequency ->
		val currentNumberOfBuys = bot.buyFrequency - bot.remainingBuyFrequency
		(newBuyFrequency - currentNumberOfBuys.toBigInteger()).coerceAtLeast(BigInteger.ZERO)
	}

	return BotUpdate(
		name = this.name ?: bot.name,
		avatarFileId = this.avatarFileId ?: bot.avatarFileId,
		tradeAmount = this.tradeAmount ?: bot.tradeAmount,
		buyFrequency = this.buyFrequency?.toLong() ?: bot.buyFrequency,
		remainingBuyFrequency = remainingBuyFrequency?.toLong() ?: bot.remainingBuyFrequency,
		profitTargetFraction = this.profitTargetFraction ?: bot.profitTargetFraction,
		stopLossFraction = this.stopLossFraction ?: bot.stopLossFraction,
		tokenTickerCopyIsChecked = this.tokenTickerCopyIsChecked ?: bot.tokenTickerCopyIsChecked,
		creatorHighBuyIsChecked = this.creatorHighBuyIsChecked ?: bot.creatorHighBuyIsChecked,
		bundledBuysDetectedIsChecked = this.bundledBuysDetectedIsChecked ?: bot.bundledBuysDetectedIsChecked,
		suspiciousWalletsDetectedIsChecked = this.suspiciousWalletsDetectedIsChecked
			?: bot.suspiciousWalletsDetectedIsChecked,
		singleHighBuyIsChecked = this.singleHighBuyIsChecked ?: bot.singleHighBuyIsChecked,
		buyTokensAliveAtLeastFor = when (this.shouldWaitBeforeBuying) {
			true -> Duration.of(5, ChronoUnit.MINUTES)
			false -> null
			null -> bot.buyTokensAliveAtLeastFor
		},
		shouldAutoSellAfterHoldTime = this.shouldAutoSellAfterHoldTime ?: bot.shouldAutoSellAfterHoldTime,

		marketCapFromUsd = marketCapFrom,
		marketCapToUsd = marketCapTo,
		liquidityFromUsd = liquidityFrom,
		liquidityToUsd = liquidityTo,
		dailyVolumeFromUsd = dailyVolumeFrom,
		dailyVolumeToUsd = dailyVolumeTo,
		numberOfHoldersFrom = numberOfHoldersFrom,
		numberOfHoldersTo = numberOfHoldersTo,
		buyVolume = buyVolume,
		sellVolume = sellVolume,
		sellTransactionFraction = sellTransactionFraction,
		buyTransactionFraction = buyTransactionFraction,
	)
}
