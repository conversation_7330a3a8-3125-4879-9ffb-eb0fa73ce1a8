package com.cleevio.fatbot.application.module.botportfolio

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.bot.BotFinderService
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.botportfolio.BotPortfolioValueSnapshotCreateService
import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit

private const val BOTS_WARNING_LIMIT = 500

@Service
class BotPortfolioValueSnapshotService(
	private val clock: Clock,
	private val botFinderService: BotFinderService,
	private val botPortfolioValueService: BotPortfolioValueService,
	private val botPortfolioValueSnapshotCreateService: BotPortfolioValueSnapshotCreateService,
	private val botWalletFinderService: BotWalletFinderService,
	private val getUsdExchangeRate: GetUsdExchangeRate,
) {

	private val logger = logger()

	fun createBotPortfolioValueSnapshots() {
		val snapshotDate = Instant.now(clock).minus(5, ChronoUnit.MINUTES)
		val botIds = botFinderService.findAll().mapToSet { it.id }

		logger.info("Calculating bot portfolio value for bots: ${botIds.size}")

		val botIdToPortfolioValue = botPortfolioValueService.getBotPortfolioValues(userId = null, botIds = botIds)
		val chainToExchangeRate = Chain.entries.associateWith { getUsdExchangeRate(it.currency) }

		val botWallets = botWalletFinderService.getAllByBotIds(botIds = botIds)

		val botIdWithoutPositionsToBalanceUsd = botWallets.filter { it.botId !in botIdToPortfolioValue.keys }.associateBy(
			{ it.botId },
			{ it.balance.asBaseAmount().toNative(it.chain).amount.multiply(chainToExchangeRate.getValue(it.chain)) },
		)

		val botIdToAcquisitionUsd = botWallets.associateBy({ it.botId }, { it.acquisitionValueUsd })

		if (botIdToPortfolioValue.size > BOTS_WARNING_LIMIT) {
			logger.warn(
				"High load warning: Processing ${botIdToPortfolioValue.size} bots." +
					"Consider implementing batching or optimizing user processing.",
			)
		}

		botIdWithoutPositionsToBalanceUsd.forEach { (botId, balanceValueUsd) ->
			botPortfolioValueSnapshotCreateService.create(
				botId = botId,
				snapshotMadeAt = snapshotDate,
				portfolioValueUsd = balanceValueUsd,
				acquisitionValueUsd = botIdToAcquisitionUsd[botId] ?: BigDecimal.ZERO,
			)
		}

		botIdToPortfolioValue.forEach { (botId, portfolioValueUsd) ->
			botPortfolioValueSnapshotCreateService.create(
				botId = botId,
				snapshotMadeAt = snapshotDate,
				portfolioValueUsd = portfolioValueUsd,
				acquisitionValueUsd = botIdToAcquisitionUsd[botId] ?: BigDecimal.ZERO,
			)
		}
	}
}
