package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.wallet.command.PatchUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.application.module.wallet.locks.CREATE_OR_UPDATE_USER_WALLETS
import com.cleevio.fatbot.application.module.wallet.locks.WALLET_MODULE
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class PatchUserWalletCommandHandler(
	private val walletFinderService: WalletFinderService,
) : CommandHandler<Unit, PatchUserWalletCommand> {

	override val command = PatchUserWalletCommand::class

	private val logger = logger()

	@Transactional
	@Lock(module = WALLET_MODULE, lockName = CREATE_OR_UPDATE_USER_WALLETS)
	override fun handle(@LockFieldParameter("userId") command: PatchUserWalletCommand) {
		val wallet = walletFinderService.getByIdAndUserId(id = command.walletId, userId = command.userId)

		wallet.patchSettings(
			customName = command.customName,
			buyAntiMevProtection = command.buyAntiMevProtection,
			sellAntiMevProtection = command.sellAntiMevProtection,
		)

		logger.info("User ${command.userId} has patched his wallet ${wallet.address.getAddressString()}.")
	}
}
