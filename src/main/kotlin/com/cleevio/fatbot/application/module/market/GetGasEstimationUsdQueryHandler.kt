package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.market.port.out.EstimateGas
import com.cleevio.fatbot.application.module.market.query.GetGasEstimationUsdQuery
import com.cleevio.fatbot.application.module.market.service.GasLimitEstimationService
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.transaction.constant.isSellOrTransfer
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class GetGasEstimationUsdQueryHandler(
	private val walletFinderService: WalletFinderService,
	private val tokenInfoFinderService: EvmTokenInfoFinderService,
	private val usdConverter: UsdConverter,
	private val gasLimitEstimationService: GasLimitEstimationService,
) : QueryHandler<GetGasEstimationUsdQuery.Result, GetGasEstimationUsdQuery> {

	override val query = GetGasEstimationUsdQuery::class

	override fun handle(query: GetGasEstimationUsdQuery): GetGasEstimationUsdQuery.Result = with(query) {
		val wallet = walletFinderService.getByIdAndUserId(id = walletId, userId = userId)
		val tokenInfo = tokenAddress?.let { tokenInfoFinderService.getByTokenAddressAndChain(it, wallet.chain) }

		val baseAmount = if (query.amount != null && transactionType.isSellOrTransfer() && tokenInfo != null) {
			query.amount.toBase(tokenInfo.decimals.toInt())
		} else {
			query.amount?.toBase(wallet.chain)
		}

		when (wallet.chain.type) {
			ChainType.EVM -> handleEvmGasEstimation(wallet.chain, baseAmount)
			ChainType.SOLANA -> handleSolanaGasEstimation(wallet.chain, baseAmount, tokenInfo?.isToken2022)
		}
	}

	private fun GetGasEstimationUsdQuery.handleEvmGasEstimation(
		chain: Chain,
		baseAmount: BaseAmount?,
	): GetGasEstimationUsdQuery.Result {
		val estimateGasResult = gasLimitEstimationService.estimateGas(
			transactionType = transactionType,
			chain = chain,
			userId = userId,
			tokenAddress = tokenAddress,
			isToken2022 = false,
			walletId = walletId,
			baseAmount = baseAmount,
			dexPairInfo = dexPairInfo,
			recipientAddress = recipientAddress,
		)

		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultEvm) error("Estimate gas must be of EVM type")

		val usdEstimatedGasLimit = estimateGasResult.gasLimitEstimate?.let {
			val gasLimitFee = it * estimateGasResult.gasInfo.maxFeePerGas
			val additionalFees = estimateGasResult.additionalFees?.amount ?: BigInteger.ZERO

			usdConverter.baseToUsd(gasLimitFee + additionalFees, chain.currency)
		}

		return GetGasEstimationUsdQuery.Result(usdEstimatedGasLimit)
	}

	private fun GetGasEstimationUsdQuery.handleSolanaGasEstimation(
		chain: Chain,
		baseAmount: BaseAmount?,
		isToken2022: Boolean?,
	): GetGasEstimationUsdQuery.Result {
		val estimateGasResult = gasLimitEstimationService.estimateGas(
			transactionType = transactionType,
			chain = chain,
			userId = userId,
			tokenAddress = tokenAddress,
			isToken2022 = isToken2022,
			walletId = walletId,
			baseAmount = baseAmount,
			dexPairInfo = dexPairInfo,
			recipientAddress = recipientAddress,
		)
		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultSolana) error("Estimate gas must be of Solana type")

		val usdEstimatedGasLimit = usdConverter.baseToUsd(estimateGasResult.feeBaseAmount, chain)
		return GetGasEstimationUsdQuery.Result(usdEstimatedGasLimit)
	}
}
