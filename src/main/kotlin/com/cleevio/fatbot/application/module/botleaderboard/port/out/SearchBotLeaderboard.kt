package com.cleevio.fatbot.application.module.botleaderboard.port.out

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.module.bot.constant.BotSortParameter
import com.cleevio.fatbot.application.module.botleaderboard.query.BotsLeaderboardSearchQuery
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

interface SearchBotLeaderboard {
	operator fun invoke(
		userId: UUID?,
		from: Instant,
		to: Instant,
		sortParameter: BotSortParameter,
		infiniteScroll: InfiniteScrollDesc<BigDecimal>,
		searchString: String?,
	): InfiniteScrollSlice<BotsLeaderboardSearchQuery.Result, BigDecimal>
}
