package com.cleevio.fatbot.application.module.fattyleagueseason.service

import com.cleevio.fatbot.application.module.fattyleagueseason.finder.FattyLeagueSeasonFinderService
import com.cleevio.fatbot.application.module.fattyleagueseason.properties.FattyLeagueSeasonProperties
import com.cleevio.fatbot.application.module.userfattyleagueseason.service.CreateUserFattyLeagueSeasonsService
import com.cleevio.fatbot.domain.fattyleagueseason.FattyLeagueSeasonCreateService
import com.cleevio.fatbot.domain.userstatistics.UserStatisticUpdateService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Clock
import java.time.Instant

@Service
class CloseFattyLeagueService(
	private val fattyLeagueSeasonFinderService: FattyLeagueSeasonFinderService,
	private val fattyLeagueSeasonCreateService: FattyLeagueSeasonCreateService,
	private val userStatisticUpdateService: UserStatisticUpdateService,
	private val createUserFattyLeagueSeasonsService: CreateUserFattyLeagueSeasonsService,
	private val fattyLeagueSeasonProperties: FattyLeagueSeasonProperties,
	private val clock: Clock,
) {

	@Transactional
	fun closeLastSeason() {
		val now = Instant.now(clock)
		val lastSeason = fattyLeagueSeasonFinderService.findLastSeason()
		lastSeason?.let {
			it.closeSeason(now)
			createUserFattyLeagueSeasonsService.createForAllUsers(fattyLeagueId = it.id)
		}

		val newSeasonNumber = (lastSeason?.seasonNumber ?: 0) + 1
		fattyLeagueSeasonCreateService.create(
			startAt = now,
			name = "Fatty League $newSeasonNumber",
			seasonNumber = newSeasonNumber,
			donutsToFattyTokensRatio = fattyLeagueSeasonProperties.donutsToFattyTokensRatio,
			claimCooldownPeriodInMinutes = fattyLeagueSeasonProperties.claimCooldownPeriodInMinutes,
			initialReleasePercentage = fattyLeagueSeasonProperties.initialReleasePercentage,
			remainingUnlockPeriodInMonths = fattyLeagueSeasonProperties.remainingUnlockPeriodInMonths,
			remainingUnlockPeriodParts = fattyLeagueSeasonProperties.remainingUnlockPeriodParts,
		)

		// reset donuts gained for users
		userStatisticUpdateService.resetLeagueDonutsForAll()
	}
}
