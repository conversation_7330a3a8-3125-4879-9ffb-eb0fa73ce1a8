package com.cleevio.fatbot.application.module.matchmaking.port.out

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.matchmaking.command.TokenStateChangesSellingCommand
import java.math.BigDecimal
import java.util.UUID

interface MatchSellingBots {
	operator fun invoke(states: List<TokenStateChangesSellingCommand.NewState>): List<Result>

	data class Result(
		val botId: UUID,
		val followerId: UUID?,
		val userId: UUID,
		val tradeAmount: BigDecimal,
		val tokenAddress: AddressWrapper,
	)
}
