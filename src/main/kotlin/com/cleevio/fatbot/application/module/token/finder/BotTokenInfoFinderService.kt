package com.cleevio.fatbot.application.module.token.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.exception.TokenNotFoundException
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.token.BotTokenInfo
import com.cleevio.fatbot.domain.token.BotTokenInfoRepository
import org.springframework.stereotype.Service

@Service
class BotTokenInfoFinderService(
	private val botTokenInfoRepository: BotTokenInfoRepository,
) : BaseFinderService<BotTokenInfo>(botTokenInfoRepository) {

	override fun errorBlock(message: String) = throw TokenNotFoundException(message)

	override fun getEntityType() = BotTokenInfo::class

	fun getByTokenAddressAndChain(tokenAddress: AddressWrapper, chain: Chain): BotTokenInfo {
		return botTokenInfoRepository.findByAddressAndChain(tokenAddress = tokenAddress, chain = chain)
			?: errorBlock("Bot token address with address $tokenAddress not found")
	}

	fun getAllByTokenAddressesAndChain(tokenAddresses: Set<AddressWrapper>, chain: Chain): List<BotTokenInfo> {
		return botTokenInfoRepository.findAllByAddressInAndChain(tokenAddresses = tokenAddresses, chain = chain)
	}

	fun findByTokenAddressAndChain(tokenAddress: AddressWrapper, chain: Chain): BotTokenInfo? {
		return botTokenInfoRepository.findByAddressAndChain(tokenAddress = tokenAddress, chain = chain)
	}
}
