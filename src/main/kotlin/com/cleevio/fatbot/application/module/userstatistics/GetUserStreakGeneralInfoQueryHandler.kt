package com.cleevio.fatbot.application.module.userstatistics

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.userstatistics.properties.UserStreakMultiplierProperties
import com.cleevio.fatbot.application.module.userstatistics.query.GetUserStreakGeneralInfoQuery
import org.springframework.stereotype.Component

@Component
class GetUserStreakGeneralInfoQueryHandler(
	private val userStreakMultiplierProperties: UserStreakMultiplierProperties,
) : QueryHandler<List<GetUserStreakGeneralInfoQuery.Result>, GetUserStreakGeneralInfoQuery> {

	override val query = GetUserStreakGeneralInfoQuery::class

	override fun handle(query: GetUserStreakGeneralInfoQuery): List<GetUserStreakGeneralInfoQuery.Result> {
		return userStreakMultiplierProperties.thresholds.map {
			GetUserStreakGeneralInfoQuery.Result(
				daysInStreak = it.key,
				multiplier = it.value,
			)
		}.sortedBy { it.daysInStreak }
	}
}
