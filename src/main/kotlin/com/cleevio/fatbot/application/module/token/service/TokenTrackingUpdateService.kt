package com.cleevio.fatbot.application.module.token.service

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.finder.MarketPositionFinderService
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.token.locks.TOKEN_MODULE
import com.cleevio.fatbot.application.module.token.locks.TRACK_TOKENS_FOR_USER
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.domain.market.MarketPositionDeleteService
import com.cleevio.fatbot.domain.transaction.TrackingTransactionCreateService
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import com.cleevio.library.lockinghandler.service.TryLock
import io.sentry.spring.jakarta.tracing.SentrySpan
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service
import java.math.BigInteger
import java.util.UUID

@Service
class TokenTrackingUpdateService(
	private val marketPositionFinderService: MarketPositionFinderService,
	private val trackingTransactionCreateService: TrackingTransactionCreateService,
	private val marketPositionDeleteService: MarketPositionDeleteService,
	private val evmTokenInfoFinderService: EvmTokenInfoFinderService,
) {

	private val logger = logger()

	@SentrySpan
	@Transactional
	@TryLock(TOKEN_MODULE, TRACK_TOKENS_FOR_USER)
	fun update(
		@LockArgumentParameter userId: UUID,
		positionIdToDetectedBalances: Map<UUID, Pair<BigInteger, BigInteger>>,
		tokenAddressToPrice: Map<ChainAddress, BaseAmount>,
	) {
		val positions = marketPositionFinderService.findAllByIds(positionIdToDetectedBalances.keys)

		// TODO: This is not pretty and should be refactored, we might get duplicates here if same token address is
		//  on multiple chains. It will get filtered down the line and chance that it happens is low, so im keeping it here
		//  for now
		val tokenAddresses = tokenAddressToPrice.keys.mapToSet { it.address }
		val tokenAddressToDecimals = evmTokenInfoFinderService.findAllByTokenAddresses(tokenAddresses)
			.associate { it.address to it.decimals.toInt() }

		positions.forEach { position ->
			val (storedBalance, chainBalance) = positionIdToDetectedBalances.getValue(position.id)

			if (position.getTokenBalance() != storedBalance) {
				logger.warn(
					"Detected balance is different than balance when updating position ${position.id}" +
						" (current: ${position.getTokenBalance()}, detected: $storedBalance)",
				)

				return@forEach
			}

			when {
				storedBalance < chainBalance -> {
					logger.info("Detected lower platform balance for token ${position.tokenAddress}, but skipping.")
				}

				storedBalance == chainBalance -> Unit
				storedBalance > chainBalance -> {
					logger.info("Detected higher platform balance for token ${position.tokenAddress}, creating SELL tx.")

					val baseAmountDifference = storedBalance - chainBalance

					val tokenPrice = tokenAddressToPrice[position.tokenAddress.toChainAddress(position.chain)]
						?: error("Failed to fetch token price for token tracking.")

					val tokenDecimals = tokenAddressToDecimals.getValue(position.tokenAddress)

					val baseTokenWeiAmount = baseAmountDifference * tokenPrice.amount
					val nativeWeiAmount = baseTokenWeiAmount / BigInteger.TEN.pow(tokenDecimals)

					trackingTransactionCreateService.create(
						walletId = position.walletId,
						chain = position.chain,
						transactionType = TransactionType.SELL,
						tokenAddress = position.tokenAddress,
						tokenAmount = baseAmountDifference,
						baseValue = nativeWeiAmount,
					)

					position.newSell(baseAmountDifference, nativeWeiAmount)
				}
			}

			if (position.isClosed()) {
				marketPositionDeleteService.delete(position.id)
			}
		}
	}
}
