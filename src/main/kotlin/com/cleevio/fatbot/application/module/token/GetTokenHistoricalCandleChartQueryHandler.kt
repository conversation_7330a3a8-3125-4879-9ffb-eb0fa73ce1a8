package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.token.constant.CandleChartTimeRange
import com.cleevio.fatbot.application.module.token.finder.TokenPairInfoFinderService
import com.cleevio.fatbot.application.module.token.port.out.GetTokenCandleChartHistoricalData
import com.cleevio.fatbot.application.module.token.query.GetTokenHistoricalCandleChartQuery
import com.cleevio.fatbot.application.module.token.query.TokenOHLCTimeIntervalItem
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.MathContext
import java.time.Duration
import java.time.Instant
import kotlin.math.ceil

private val CORRUPT_THRESHOLD_Z_SCORE = BigDecimal("3")

@Component
class GetTokenHistoricalCandleChartQueryHandler(
	private val tokenPairInfoFinderService: TokenPairInfoFinderService,
	private val getTokenCandleChartHistoricalData: GetTokenCandleChartHistoricalData,
) : QueryHandler<List<TokenOHLCTimeIntervalItem>, GetTokenHistoricalCandleChartQuery> {

	override val query = GetTokenHistoricalCandleChartQuery::class

	override fun handle(query: GetTokenHistoricalCandleChartQuery): List<TokenOHLCTimeIntervalItem> {
		val tokenPairs = tokenPairInfoFinderService.findAllByTokenAddressAndChain(
			tokenAddress = query.tokenAddress,
			chain = query.chain,
		)

		val isPumpfunExclusive = tokenPairs.all { it.dexType == GetDex.Dex.PUMP_FUN }

		val data = getTokenCandleChartHistoricalData(
			chain = query.chain,
			tokenAddress = query.tokenAddress,
			isPumpfunExclusive = isPumpfunExclusive,
			intervalCount = query.timeRange.intervalCount,
			timeInterval = query.timeRange.timeInterval,
			limit = query.timeRange.limit,
			before = query.before,
			after = query.after,
		)

		return data.fixCorrupted(intervalDuration = query.timeRange.intervalDuration()).sortedBy { it.timestamp }
	}

	fun List<TokenOHLCTimeIntervalItem>.fixCorrupted(intervalDuration: Duration): List<TokenOHLCTimeIntervalItem> {
		if (isEmpty()) return emptyList()

		val intervalDays = intervalDuration.toDays()

		// Targets ~1 month per chunk
		val chunkSize = when {
			intervalDays <= 31 -> size.toDouble()
			intervalDays <= 366 -> size / 12.0
			else -> size / 24.0
		}.let { ceil(it).toInt() }

		val result = this
			.sortedBy { it.timestamp }
			.chunked(chunkSize)
			.flatMap { chunk -> chunk.fixCorruptedChunk() }

		return result
	}

	private fun List<TokenOHLCTimeIntervalItem>.fixCorruptedChunk(): List<TokenOHLCTimeIntervalItem> {
		val timestampToExchangeRate = associate {
			val exchangeRate = it.closeUsd.divide(it.closeNative, MathContext.DECIMAL64)

			it.timestamp to exchangeRate
		}.detrended()

		val values = timestampToExchangeRate.values.map { it.residual }
		val count = values.size.toBigDecimal()
		val average = values.sumOf { it }.divide(count, MathContext.DECIMAL64)
		val stdDev = values
			.fold(BigDecimal.ZERO) { acc, value -> acc + (value - average).pow(2) }
			.sqrt(MathContext.DECIMAL64)
			.divide(count.sqrt(MathContext.DECIMAL64), MathContext.DECIMAL64)

		if (stdDev.compareTo(BigDecimal.ZERO) == 0) return this

		val (correctItems, corruptedItems) = this.partition { item ->
			val itemExchangeRate = timestampToExchangeRate.getValue(item.timestamp).residual

			val zScore = (itemExchangeRate - average).abs().divide(stdDev, MathContext.DECIMAL64)

			zScore < CORRUPT_THRESHOLD_Z_SCORE
		}

		if (corruptedItems.isEmpty()) return correctItems

		val correctItemPairs = correctItems.sortedBy { item -> item.timestamp }.zipWithNext()

		val fixedItems = corruptedItems.map { item ->
			val closestTimestamp = findClosestTimestamp(correctItemPairs, item)
			val rateToUse = timestampToExchangeRate.getValue(closestTimestamp).original

			val originalRate = timestampToExchangeRate
				.getValue(item.timestamp)
				.original
				.takeIf { it > BigDecimal.ZERO } ?: BigDecimal.ONE // If the rate is 0, the item values will be zero

			// Assume the wrong exchange rate is applied on the whole item
			item.copy(
				closeUsd = item.closeNative * rateToUse,
				openUsd = item.openNative * rateToUse,
				highUsd = item.highUsd.divide(originalRate, MathContext.DECIMAL64) * rateToUse,
				lowUsd = item.lowUsd.divide(originalRate, MathContext.DECIMAL64) * rateToUse,
				volumeUsd = item.volumeUsd.divide(originalRate, MathContext.DECIMAL64) * rateToUse,
			)
		}

		return correctItems + fixedItems
	}

	private fun findClosestTimestamp(
		correctItemPairs: List<Pair<TokenOHLCTimeIntervalItem, TokenOHLCTimeIntervalItem>>,
		item: TokenOHLCTimeIntervalItem,
	): Instant {
		val closestCorrectPair = correctItemPairs.firstOrNull { (_, b) -> b.timestamp > item.timestamp }

		val closestTimestamp = if (closestCorrectPair == null) {
			val firstTimestamp = correctItemPairs.first().first.timestamp
			val lastTimestamp = correctItemPairs.last().second.timestamp

			if (item.timestamp > lastTimestamp) lastTimestamp else firstTimestamp
		} else {
			val (closestBefore, closestAfter) = closestCorrectPair

			val afterTimeDiff = closestAfter.timestamp.epochSecond - item.timestamp.epochSecond
			val beforeTimeDiff = item.timestamp.epochSecond - closestBefore.timestamp.epochSecond

			val closerTimestamp = if (afterTimeDiff < beforeTimeDiff) {
				closestAfter.timestamp
			} else {
				closestBefore.timestamp
			}

			closerTimestamp
		}

		return closestTimestamp
	}

	private fun CandleChartTimeRange.intervalDuration(): Duration {
		val intervalPeriod = timeInterval.unit.duration.multipliedBy(intervalCount.toLong())
		val intervalDuration = intervalPeriod.multipliedBy(limit.toLong())

		return intervalDuration
	}

	data class DetrendedValue(
		val original: BigDecimal,
		val residual: BigDecimal,
	)

	/**
	 * Detrends { Timestamp -> ExchangeRate } using linear regression
	 *
	 * Assumes the input is sorted and backed by [LinkedHashMap].
	 */
	private fun Map<Instant, BigDecimal>.detrended(): Map<Instant, DetrendedValue> {
		if (size < 2) return mapValues { (_, rate) -> DetrendedValue(rate, rate) }

		val times = keys.map { it.toEpochMilli().toBigDecimal() }
		val rates = values

		val meanTime = times.sumOf { it }.divide(size.toBigDecimal(), MathContext.DECIMAL64)
		val meanRate = rates.sumOf { it }.divide(size.toBigDecimal(), MathContext.DECIMAL64)

		// Sums regression using centered values
		val sumT2Centered = times.sumOf { (it - meanTime).pow(2) }
		val sumTYCentered = times.zip(rates).sumOf { (time, rate) -> (time - meanTime) * (rate - meanRate) }

		val slope = sumTYCentered.divide(sumT2Centered, MathContext.DECIMAL64)

		val predictedRates = times.map { meanRate + slope * (it - meanTime) }

		val detrendedRates = rates.zip(predictedRates) { original, predicted ->
			DetrendedValue(
				original = original,
				residual = original - predicted,
			)
		}

		return keys.zip(detrendedRates).toMap()
	}
}
