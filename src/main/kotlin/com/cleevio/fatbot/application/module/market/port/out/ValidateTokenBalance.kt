package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper

interface ValidateTokenBalance {

	/**
	 * Returns address of Token Account on which the funds are located and also the balance of account
	 */
	operator fun invoke(
		walletAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		isToken2022: Boolean,
		amountToSend: BaseAmount,
		tokenDecimals: Int,
	): Result

	data class Result(
		val tokenAccountBalance: BaseAmount,
		val tokenAccountAddress: AddressWrapper,
	)
}
