package com.cleevio.fatbot.application.module.matchmaking.scheduled

import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.matchmaking.command.BotsSellStalePositionsCommand
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Clock
import java.time.Instant

@Component
class BotsSellStalePositionsTrigger(
	private val commandBus: CommandBus,
	private val clock: Clock,
) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.bots-sell-stale-tokens")
	@Scheduled(cron = "\${fatbot.bot-transaction.sell-stale-tokens.cron}")
	@SchedulerLock(
		name = "BOTS_SELL_STALE_POSITIONS",
		lockAtMostFor = "\${fatbot.bot-transaction.sell-stale-tokens.lock-for}",
		lockAtLeastFor = "\${fatbot.bot-transaction.sell-stale-tokens.lock-for}",
	)
	fun trigger() {
		logger.info("BOTS_SELL_STALE_POSITIONS cron started")
		val now = Instant.now(clock)
		commandBus(BotsSellStalePositionsCommand(now = now))
		logger.info("BOTS_SELL_STALE_POSITIONS cron ended")
	}
}
