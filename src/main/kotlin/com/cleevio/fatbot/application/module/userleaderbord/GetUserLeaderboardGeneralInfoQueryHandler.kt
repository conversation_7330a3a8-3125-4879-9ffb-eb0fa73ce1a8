package com.cleevio.fatbot.application.module.userleaderbord

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.userleaderbord.exception.IllegalUserLeaderboardAccessException
import com.cleevio.fatbot.application.module.userleaderbord.finder.UserLeaderboardFinderService
import com.cleevio.fatbot.application.module.userleaderbord.properties.LeaderboardMultiplierProperties
import com.cleevio.fatbot.application.module.userleaderbord.query.GetUserLeaderboardGeneralInfoQuery
import org.springframework.stereotype.Component

@Component
class GetUserLeaderboardGeneralInfoQueryHandler(
	private val leaderboardMultiplierProperties: LeaderboardMultiplierProperties,
	private val leaderboardFinderService: UserLeaderboardFinderService,
) : QueryHandler<List<GetUserLeaderboardGeneralInfoQuery.Result>, GetUserLeaderboardGeneralInfoQuery> {

	override val query = GetUserLeaderboardGeneralInfoQuery::class

	override fun handle(query: GetUserLeaderboardGeneralInfoQuery): List<GetUserLeaderboardGeneralInfoQuery.Result> {
		if (!leaderboardFinderService.existsByUserId(query.userId)) {
			throw IllegalUserLeaderboardAccessException("User not present in leaderboard doesnt have access.")
		}

		return leaderboardMultiplierProperties.thresholds.map {
			GetUserLeaderboardGeneralInfoQuery.Result(
				rank = it.key.toInt(),
				multiplier = it.value,
			)
		}.sortedBy { it.rank }
	}
}
