package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.service.PasswordValidationService
import com.cleevio.fatbot.application.common.service.command.VerifyPasswordCommand
import com.cleevio.fatbot.application.module.wallet.port.out.ExportUserWallet
import com.cleevio.fatbot.application.module.wallet.query.ExportUserWalletQuery
import org.springframework.stereotype.Component

@Component
class ExportUserWalletQueryHandler(
	private val exportUserWallet: ExportUserWallet,
	private val passwordValidationService: PasswordValidationService,
) : QueryHandler<ExportUserWalletQuery.Result, ExportUserWalletQuery> {

	override val query = ExportUserWalletQuery::class

	override fun handle(query: ExportUserWalletQuery): ExportUserWalletQuery.Result {
		passwordValidationService(VerifyPasswordCommand(userId = query.userId, password = query.password))

		return exportUserWallet(
			userId = query.userId,
			walletId = query.walletId,
		)
	}
}
