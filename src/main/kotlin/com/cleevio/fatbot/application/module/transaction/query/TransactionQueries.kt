package com.cleevio.fatbot.application.module.transaction.query

import com.cleevio.fatbot.adapter.`in`.InfiniteScroll
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.common.validation.NullOrNotBlank
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailureReason
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import jakarta.validation.constraints.NotEmpty
import java.math.BigDecimal
import java.net.URI
import java.time.Instant
import java.util.UUID

class SearchUserTransactionQuery private constructor(
	val userId: UUID,
	val useSelectedChains: Boolean,
	@field:Valid val filter: Filter,
	@field:Valid val infiniteScroll: InfiniteScroll<UUID>,
) : Query<InfiniteScrollSlice<SearchUserTransactionQuery.Result, UUID>> {

	data class Filter(
		val chain: Chain?,
		val tokenAddress: AddressWrapper?,
		val walletId: UUID?,
		@field:NullOrNotBlank val searchString: String?,
		@field:NotEmpty val allowedTypes: Set<TransactionType>,
		@field:NotEmpty val allowedStatuses: Set<TransactionStatus>,
	)

	companion object {

		fun ofAllTokens(
			userId: UUID,
			useSelectedChains: Boolean,
			walletId: UUID?,
			searchString: String?,
			infiniteScroll: InfiniteScroll<UUID>,
			allowedStatuses: Set<TransactionStatus>,
		) = SearchUserTransactionQuery(
			userId = userId,
			useSelectedChains = useSelectedChains,
			filter = Filter(
				tokenAddress = null,
				chain = null,
				walletId = walletId,
				searchString = searchString,
				allowedTypes = ALLOWED_TRANSACTION_TYPES,
				allowedStatuses = allowedStatuses,
			),
			infiniteScroll = infiniteScroll,
		)

		fun ofSpecificToken(
			chain: Chain,
			userId: UUID,
			tokenAddress: AddressWrapper,
			walletId: UUID?,
			infiniteScroll: InfiniteScroll<UUID>,
			allowedStatuses: Set<TransactionStatus>,
		) = SearchUserTransactionQuery(
			userId = userId,
			useSelectedChains = false,
			filter = Filter(
				tokenAddress = tokenAddress,
				chain = chain,
				walletId = walletId,
				searchString = null,
				allowedTypes = ALLOWED_TRANSACTION_TYPES,
				allowedStatuses = allowedStatuses,
			),
			infiniteScroll = infiniteScroll,
		)

		fun ofSpecificTokenLastFive(chain: Chain, userId: UUID, tokenAddress: AddressWrapper) = SearchUserTransactionQuery(
			userId = userId,
			useSelectedChains = false,
			filter = Filter(
				chain = chain,
				tokenAddress = tokenAddress,
				walletId = null,
				searchString = null,
				allowedTypes = ALLOWED_TRANSACTION_TYPES,
				allowedStatuses = TransactionStatus.entries.toSet(),
			),
			infiniteScroll = InfiniteScrollDesc.UUID(
				size = 5,
				lastId = null,
			),
		)
	}

	@Schema(name = "SearchUserTransactionResult")
	data class Result(
		val txId: UUID,
		val txHash: TxHash,
		val txDetailUrl: URI,
		val transactionType: TransactionType,
		val transactionStatus: TransactionStatus,
		val txFailReason: TransactionFailureReason?,
		val walletId: UUID,
		val walletCustomName: String?,
		val createdAt: Instant,
		val tokenAddress: AddressWrapper?,
		val tokenName: String?,
		val tokenSymbol: String?,
		val tokenImageUrl: URI?,
		val tokenNativeAmount: NativeAmount?,
		val currencyNativeAmount: NativeAmount?,
		val currencyAmountUsd: BigDecimal?,
		val transactionChain: Chain,
	)
}

private val ALLOWED_TRANSACTION_TYPES = setOf(
	TransactionType.SELL,
	TransactionType.BUY,
	TransactionType.TRANSFER_TOKEN,
	TransactionType.TRANSFER_CURRENCY,
)

class GetUserLifetimeTradeVolumeQuery(
	val userId: UUID,
) : Query<GetUserLifetimeTradeVolumeQuery.Result> {

	@Schema(name = "GetLifetimeTradeVolumeResult")
	data class Result(
		val lifetimeTradeVolumeUsd: BigDecimal,
	)
}
