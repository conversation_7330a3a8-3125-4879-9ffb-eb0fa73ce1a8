package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.adapter.out.evm.DecodeEvmSignedTransaction
import com.cleevio.fatbot.adapter.out.solana.DecodeSolanaSignedTransaction
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.domain.transaction.TransactionCreateService
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class TransactionService(
	private val transactionCreateService: TransactionCreateService,
	private val walletFinderService: WalletFinderService,
	private val decodeEvmTransaction: DecodeEvmSignedTransaction,
	private val decodeSolanaTransaction: DecodeSolanaSignedTransaction,
) {

	fun saveTransactions(
		walletId: UUID,
		signedTxList: List<SignedTx>,
		transactionType: TransactionType,
		tokenAddress: AddressWrapper?,
	) {
		val wallet = walletFinderService.getById(walletId)

		val transactions = signedTxList.map {
			when (wallet.chain.type) {
				ChainType.EVM -> decodeEvmTransaction(walletId, it, transactionType, tokenAddress)
				ChainType.SOLANA -> decodeSolanaTransaction(walletId, it, transactionType, tokenAddress)
			}
		}

		transactionCreateService.create(walletId, transactions)
	}
}
