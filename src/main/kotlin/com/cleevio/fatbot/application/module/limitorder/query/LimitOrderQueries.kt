package com.cleevio.fatbot.application.module.limitorder.query

import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class GetAllUserLimitOrdersQuery(
	val userId: UUID,
) : Query<List<GetAllUserLimitOrdersQuery.Result>> {
	@Schema(name = "GetAllUserLimitOrdersResult")
	data class Result(
		val walletId: UUID,
		val limitOrderId: UUID,
		val type: LimitOrderType,
		val tokenAddress: AddressWrapper,
		val chain: Chain,
		val limitPrice: NativeAmount,
		val remainingAmount: NativeAmount,
		val filledAmount: NativeAmount,
		val isLocked: Boolean,
	)
}

data class GetLimitOrderDetailQuery(
	val userId: UUID,
	val limitOrderId: UUID,
) : Query<GetLimitOrderDetailQuery.Result> {
	@Schema(name = "GetLimitOrderDetailResult")
	data class Result(
		val walletId: UUID,
		val limitOrderId: UUID,
		val type: LimitOrderType,
		val tokenAddress: AddressWrapper,
		val chain: Chain,
		val limitPrice: NativeAmount,
		val remainingAmount: NativeAmount,
		val filledAmount: NativeAmount,
		val isLocked: Boolean,
	)
}
