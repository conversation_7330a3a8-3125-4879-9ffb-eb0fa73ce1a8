package com.cleevio.fatbot.application.module.referral.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class ReferralRewardNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.REFERRAL_REWARD_NOT_FOUND,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class ReferralRewardAlreadyClaimedException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.REFERRAL_REWARD_ALREADY_CLAIMED,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class ReferralRewardBelowThresholdException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.REFERRAL_REWARD_BELOW_THRESHOLD,
	message = message,
)
