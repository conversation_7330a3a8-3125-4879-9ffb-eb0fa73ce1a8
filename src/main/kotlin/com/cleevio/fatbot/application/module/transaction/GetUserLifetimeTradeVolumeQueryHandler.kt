package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.transaction.port.out.GetUserLifetimeTradeVolume
import com.cleevio.fatbot.application.module.transaction.query.GetUserLifetimeTradeVolumeQuery
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.stereotype.Component

@Component
class GetUserLifetimeTradeVolumeQueryHandler(
	private val getUserLifetimeTradeVolume: GetUserLifetimeTradeVolume,
	private val chainProperties: ChainProperties,
) : Query<PERSON>andler<GetUserLifetimeTradeVolumeQuery.Result, GetUserLifetimeTradeVolumeQuery> {

	override val query = GetUserLifetimeTradeVolumeQuery::class

	override fun handle(query: GetUserLifetimeTradeVolumeQuery) =
		getUserLifetimeTradeVolume(userId = query.userId, chains = chainProperties.enabledChains)
}
