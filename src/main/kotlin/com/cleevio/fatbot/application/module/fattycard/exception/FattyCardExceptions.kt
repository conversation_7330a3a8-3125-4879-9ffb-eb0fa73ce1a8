package com.cleevio.fatbot.application.module.fattycard.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class FattyCardNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.FATTY_CARD_NOT_FOUND,
	message = message,
)
