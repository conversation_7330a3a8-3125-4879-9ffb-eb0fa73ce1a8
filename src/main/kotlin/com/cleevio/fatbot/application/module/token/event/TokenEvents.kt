package com.cleevio.fatbot.application.module.token.event

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant

data class TokenOnDexScreenerFoundEvent(
	val chain: Chain,
	val tokenAddress: AddressWrapper,
	val pairAddress: AddressWrapper,
	val decimals: BigInteger,
	val name: String,
	val symbol: String,
	val imageUrl: String?,
)

data class TokenPairFoundEvent(
	val chain: Chain,
	val dex: GetDex.Result,
	val tokenAddress: AddressWrapper,
	val tokenDecimals: BigInteger,
)

data class NewEvmTokenInfoCreatedEvent(
	val chain: Chain,
	val tokenAddress: AddressWrapper,
	val pairAddress: AddressWrapper,
)

sealed interface TokenSessionEvent {
	val sessionId: String
}

data class TokenAddedToSessionEvent(
	override val sessionId: String,
	val tokenAddress: AddressWrapper,
	val chain: Chain,
	val dex: GetDex.Dex,
) : TokenSessionEvent

data class TokenRemovedFromSessionEvent(
	override val sessionId: String,
) : TokenSessionEvent

sealed interface TokenTradedEvent {
	val tokenAddress: AddressWrapper
	val priceUsd: BigDecimal
	val amountUsd: BigDecimal
	val timestamp: Instant
}

data class SolanaTokenTradedEvent(
	override val tokenAddress: AddressWrapper,
	override val priceUsd: BigDecimal,
	override val amountUsd: BigDecimal,
	override val timestamp: Instant,
) : TokenTradedEvent

data class EVMTokenTradedEvent(
	override val tokenAddress: AddressWrapper,
	override val priceUsd: BigDecimal,
	override val amountUsd: BigDecimal,
	override val timestamp: Instant,
	val chainId: Long,
) : TokenTradedEvent
