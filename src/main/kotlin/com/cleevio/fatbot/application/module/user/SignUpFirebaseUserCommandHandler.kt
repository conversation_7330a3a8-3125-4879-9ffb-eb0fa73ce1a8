package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.user.command.SignUpFirebaseUserCommand
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.user.locks.SIGN_UP
import com.cleevio.fatbot.application.module.user.locks.USER_MODULE
import com.cleevio.fatbot.application.module.wallet.command.CreateNewUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.user.FirebaseUserCreateService
import com.cleevio.fatbot.domain.userstatistics.UserStatisticsCreateService
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
class SignUpFirebaseUserCommandHandler(
	private val firebaseUserCreateService: FirebaseUserCreateService,
	private val firebaseUserFinderService: FirebaseUserFinderService,
	private val userStatisticsCreateService: UserStatisticsCreateService,
	private val chainProperties: ChainProperties,
) : CommandHandler<Unit, SignUpFirebaseUserCommand> {

	override val command = SignUpFirebaseUserCommand::class

	@Lazy
	@Autowired
	private lateinit var commandBus: CommandBus

	@Transactional
	@Lock(module = USER_MODULE, lockName = SIGN_UP)
	override fun handle(@LockFieldParameter("email") command: SignUpFirebaseUserCommand) {
		if (firebaseUserFinderService.existByEmailIgnoringCase(email = command.email)) return

		val referredByUserId = command.referralCode?.let {
			firebaseUserFinderService.getByReferralCode(referralCode = (command.referralCode)).id
		}

		val user = firebaseUserCreateService.create(
			email = command.email,
			referredByUserId = referredByUserId,
			selectedChains = chainProperties.enabledChains,
		)

		generateEvmWallets(userId = user.id)
		generateSolanaWallet(userId = user.id)
		userStatisticsCreateService(userId = user.id)
	}

	private fun generateEvmWallets(userId: UUID) {
		chainProperties.supportedEvmChains.forEach {
			commandBus(CreateNewUserWalletCommand(userId = userId, chain = it))
		}
	}

	private fun generateSolanaWallet(userId: UUID) {
		commandBus(CreateNewUserWalletCommand(userId = userId, chain = Chain.SOLANA))
	}
}
