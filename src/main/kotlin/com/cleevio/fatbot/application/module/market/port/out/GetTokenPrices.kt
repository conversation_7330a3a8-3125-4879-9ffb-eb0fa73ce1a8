package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.ChainAddress

interface GetTokenPrices {
	operator fun invoke(tokens: Set<ChainAddress>): Map<ChainAddress, BaseAmount>
	fun getSingle(token: ChainAddress): BaseAmount

	/**
	 * Use this when there is a chance that getting the price for [token] might cause an error.
	 * As this call does not coalesce with others, it will not topple the entire batch.
	 */
	fun getSingleWithoutCoalescing(token: ChainAddress): BaseAmount
}
