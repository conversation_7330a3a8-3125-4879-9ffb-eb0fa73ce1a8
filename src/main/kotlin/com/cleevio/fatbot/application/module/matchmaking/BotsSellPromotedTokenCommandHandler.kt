package com.cleevio.fatbot.application.module.matchmaking

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.bot.BotFinderService
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.botmarket.finder.BotMarketPositionFinderService
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.matchmaking.command.BotsSellPromotedTokenCommand
import com.cleevio.fatbot.application.module.matchmaking.event.BotSellPromotedTokenRequest
import com.cleevio.fatbot.application.module.matchmaking.locks.MATCH_BOTS
import com.cleevio.fatbot.application.module.matchmaking.locks.MATCH_MAKING_MODULE
import com.cleevio.fatbot.application.module.matchmaking.port.out.SendBotTransaction
import com.cleevio.fatbot.application.module.token.finder.BotTokenInfoFinderService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class BotsSellPromotedTokenCommandHandler(
	private val botMarketPositionFinderService: BotMarketPositionFinderService,
	private val botWalletFinderService: BotWalletFinderService,
	private val botFinderService: BotFinderService,
	private val botTokenInfoFinderService: BotTokenInfoFinderService,
	private val sendBotTransaction: SendBotTransaction,
) : CommandHandler<Unit, BotsSellPromotedTokenCommand> {

	private val logger = logger()

	override val command = BotsSellPromotedTokenCommand::class

	@Transactional(transactionManager = "matchMakingTransactionManager")
	@Lock(module = MATCH_MAKING_MODULE, lockName = MATCH_BOTS)
	override fun handle(command: BotsSellPromotedTokenCommand) {
		logger.info("Processing sell of promoted token on block ${command.blockSlot} to pair ${command.pairAddress}")

		val botTokenInfo = botTokenInfoFinderService.findByTokenAddressAndChain(
			tokenAddress = command.tokenAddress,
			chain = Chain.SOLANA,
		)
		botTokenInfo?.graduate(pairAddress = command.pairAddress)

		val positionsToSell = botMarketPositionFinderService.findAllByTokenAddressAndState(
			tokenAddress = command.tokenAddress,
			states = BotMarketPositionState.openStates,
		)
			// sorted so leader bots will sell before followers (their positions are created first when buying)
			.sortedBy { it.id }

		if (positionsToSell.isEmpty()) return

		val walletIds = positionsToSell.mapToSet { it.botWalletId }
		val walletIdToWallet = botWalletFinderService.getAllByIds(ids = walletIds).associateBy { it.id }

		val botIds = walletIdToWallet.values.mapToSet { it.botId }
		val botIdToBot = botFinderService.getAllByIds(ids = botIds).associateBy { it.id }

		positionsToSell.forEach { position ->
			// TODO: Ordering?

			val wallet = walletIdToWallet.getValue(position.botWalletId)
			val bot = botIdToBot.getValue(wallet.botId)

			position.closePositionAsPromoted()

			sendBotTransaction(
				BotSellPromotedTokenRequest(
					blockSlot = command.blockSlot,
					tokenAddress = command.tokenAddress,
					creator = command.creator,
					botWalletId = wallet.id,
					privateKey = wallet.privateKey,
					pairAddress = command.pairAddress,
					amountToSell = position.totalTokenAmountBought.asBaseAmount(),
					userId = bot.userId,
					chain = position.chain,
				),
			)
		}
	}
}
