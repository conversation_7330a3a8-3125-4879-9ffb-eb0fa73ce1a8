package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.finder.MarketPositionFinderService
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.referral.ReferralRewardService
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.transaction.constant.BuySellTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailure
import com.cleevio.fatbot.application.module.transaction.constant.TransactionPending
import com.cleevio.fatbot.application.module.transaction.constant.TransactionResult
import com.cleevio.fatbot.application.module.transaction.constant.TransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.constant.TransferCurrencyTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransferTokenTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.event.BuyTransactionSuccessfulEvent
import com.cleevio.fatbot.application.module.transaction.event.TransactionTryCountIncreaseEvent
import com.cleevio.fatbot.application.module.transaction.finder.TransactionFinderService
import com.cleevio.fatbot.application.module.transaction.locks.TRANSACTION_MODULE
import com.cleevio.fatbot.application.module.transaction.locks.UPDATE_TRANSACTION
import com.cleevio.fatbot.application.module.transaction.port.out.PublishUserTransactionCreated
import com.cleevio.fatbot.application.module.userstatistics.service.UpdateTradedAmountService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.domain.market.MarketPositionCreateService
import com.cleevio.fatbot.domain.market.MarketPositionDeleteService
import com.cleevio.fatbot.infrastructure.config.properties.TransactionProperties
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.BigInteger
import java.util.UUID

@Service
class TransactionStatusUpdateService(
	private val transactionFinderService: TransactionFinderService,
	private val transactionProperties: TransactionProperties,
	private val getExchangeRateUsd: GetUsdExchangeRate,
	private val marketPositionFinderService: MarketPositionFinderService,
	private val marketPositionCreateService: MarketPositionCreateService,
	private val marketPositionDeleteService: MarketPositionDeleteService,
	private val referralRewardService: ReferralRewardService,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val publishUserTransactionCreated: PublishUserTransactionCreated,
	private val walletFinderService: WalletFinderService,
	private val evmTokenInfoFinderService: EvmTokenInfoFinderService,
	private val updateTradedAmountService: UpdateTradedAmountService,
) {

	@SentrySpan
	@Transactional
	@Lock(TRANSACTION_MODULE, UPDATE_TRANSACTION)
	fun updateStatus(@LockArgumentParameter transactionId: UUID, transactionResult: TransactionResult) {
		val tx = transactionFinderService.getById(transactionId)
		val userId = walletFinderService.getById(tx.walletId).userId
		when (transactionResult) {
			is TransactionPending -> {
				tx.increaseTryCount(transactionProperties.verificationLimit)

				tx.signedTx?.let { applicationEventPublisher.publishEvent(TransactionTryCountIncreaseEvent(transactionId)) }
			}

			is TransactionFailure -> {
				tx.markAsFailed(failReason = transactionResult.failReason)
			}

			is TransferTokenTransactionSuccess -> {
				val exchangeRate = getExchangeRateUsd(cryptoCurrency = tx.chain.currency)
				tx.markAsSuccess(transactionResult, exchangeRate)
				updateMarketPosition(
					walletId = tx.walletId,
					chain = tx.chain,
					tokenAddress = tx.tokenAddress ?: error("Transaction's tokenAddress is null."),
					type = tx.type,
					amountIn = transactionResult.amountIn,
					amountOut = transactionResult.amountOut,
					exchangeRate = exchangeRate,
				)
			}

			is TransferCurrencyTransactionSuccess -> {
				tx.markAsSuccess(transactionResult, getExchangeRateUsd(cryptoCurrency = tx.chain.currency))

				publishUserTransactionCreated(transactionType = tx.type, userId = userId, txHash = tx.txHash!!)
			}

			is BuySellTransactionSuccess -> {
				requireNotNull(tx.txHash) { "Transaction's txHash must not be null." }
				val exchangeRate = getExchangeRateUsd(cryptoCurrency = tx.chain.currency)
				tx.markAsSuccess(transactionResult, exchangeRate)
				updateMarketPosition(
					walletId = tx.walletId,
					chain = tx.chain,
					tokenAddress = tx.tokenAddress ?: error("Transaction's tokenAddress is null."),
					type = tx.type,
					amountIn = transactionResult.amountIn,
					amountOut = transactionResult.amountOut,
					exchangeRate = exchangeRate,
				)
				referralRewardService.saveReferralReward(
					userId = transactionResult.referralRewardRecipient,
					txHash = tx.txHash,
					amount = transactionResult.referralFee,
					chain = tx.chain,
				)

				val amount = when (tx.type) {
					TransactionType.BUY -> {
						val token = evmTokenInfoFinderService.getByTokenAddressAndChain(tx.tokenAddress, tx.chain)

						// update donuts
						val solIn = tx.amountIn!!.asBaseAmount().toNative(tx.chain).amount
						updateTradedAmountService.update(
							userId = userId,
							tradeUsdAmount = tx.exchangeRateUsd!!.multiply(solIn),
						)

						tx.amountOut!!.asBaseAmount().toNative(decimals = token.decimals.toInt())
					}
					TransactionType.SELL -> {
						// update donuts
						val solOut = tx.amountOut!!.asBaseAmount().toNative(tx.chain).amount
						updateTradedAmountService.update(
							userId = userId,
							tradeUsdAmount = tx.exchangeRateUsd!!.multiply(solOut),
						)

						tx.amountOut!!.asBaseAmount().toNative(tx.chain)
					}
					else -> error("Unsupported transaction type")
				}

				if (tx.type == TransactionType.BUY && tx.chain.id != null) {
					applicationEventPublisher.publishEvent(
						BuyTransactionSuccessfulEvent(
							walletId = tx.walletId,
							tokenAddress = tx.tokenAddress,
							chainId = tx.chain.evmId,
						),
					)
				}

				publishUserTransactionCreated(
					transactionType = tx.type,
					userId = userId,
					txHash = tx.txHash,
					amountOut = amount,
				)
			}

			is TransactionSuccess -> {
				tx.markAsSuccess()

				publishUserTransactionCreated(transactionType = tx.type, userId = userId, txHash = tx.txHash!!)
			}
		}
	}

	private fun updateMarketPosition(
		walletId: UUID,
		chain: Chain,
		tokenAddress: AddressWrapper,
		type: TransactionType,
		amountIn: BigInteger,
		amountOut: BigInteger,
		exchangeRate: BigDecimal,
	) {
		val marketPosition = marketPositionFinderService.findMarketPosition(
			walletId = walletId,
			chain = chain,
			tokenAddress = tokenAddress,
		) ?: marketPositionCreateService.create(walletId, chain, tokenAddress)

		when (type) {
			TransactionType.BUY -> marketPosition.newBuy(
				amountOfTokensReceived = amountOut,
				amountOfBaseCurrencyPaid = amountIn,
				exchangeRate = exchangeRate,
			)

			TransactionType.SELL -> marketPosition.newSell(
				amountOfTokensSold = amountIn,
				amountOfBaseCurrencyReceived = amountOut,
			)

			TransactionType.TRANSFER_CURRENCY -> TODO("When transfer ETH is done.")
			TransactionType.TRANSFER_TOKEN -> marketPosition.newSell(
				amountOfTokensSold = amountOut,
				amountOfBaseCurrencyReceived = amountIn,
			)

			TransactionType.APPROVE -> error("Not possible to update market position based on APPROVE.")
			TransactionType.CLAIM_REFERRAL_REWARD -> error(
				"Not possible to update market position based on CLAIM_REFERRAL_REWARD.",
			)
		}

		if (marketPosition.isClosed()) {
			marketPositionDeleteService.delete(marketPosition.id)
		}
	}
}
