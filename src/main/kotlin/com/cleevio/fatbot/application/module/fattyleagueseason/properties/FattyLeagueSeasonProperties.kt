package com.cleevio.fatbot.application.module.fattyleagueseason.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.math.BigDecimal

@ConfigurationProperties(prefix = "fatty-league.season")
class FattyLeagueSeasonProperties(
	val claimCooldownPeriodInMinutes: Long,
	val initialReleasePercentage: BigDecimal,
	val remainingUnlockPeriodInMonths: Long,
	val remainingUnlockPeriodParts: Long,
	val donutsToFattyTokensRatio: BigDecimal,
)
