package com.cleevio.fatbot.application.module.gassnapshot

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.stereotype.Service

@Service
class GasSnapshotProcessingService(
	private val chainProperties: ChainProperties,
	private val evmChainContextFactory: EvmChainContextFactory,
	private val gasInfoSnapshotService: GasInfoSnapshotService,
) {

	fun processGasSnapshots() {
		val chains = chainProperties.enabledEvmChains

		val chainToGasInfo = chains.associateWith {
			val gasInfo = evmChainContextFactory.ofChainId(it.evmId) {
				client.getGasInfo()
			}

			gasInfo
		}

		gasInfoSnapshotService.saveOrUpdateGasInfoSnapshots(chainToGasInfo)
	}
}
