package com.cleevio.fatbot.application.module.userfattycard.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.module.wallet.exception.WalletNotFoundException
import com.cleevio.fatbot.domain.userfattycard.UserFattyCard
import com.cleevio.fatbot.domain.userfattycard.UserFattyCardRepository
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.util.UUID

@Service
class UserFattyCardFinderService(
	private val userFattyCardRepository: UserFattyCardRepository,
) : BaseFinderService<UserFattyCard>(userFattyCardRepository) {

	override fun errorBlock(message: String) = throw WalletNotFoundException(message)

	override fun getEntityType() = UserFattyCard::class

	fun getByIdAndUserId(id: UUID, userId: UUID): UserFattyCard {
		return userFattyCardRepository.findByIdAndUserId(
			id = id,
			userId = userId,
		) ?: errorBlock("Fatty card for user $userId not found")
	}

	fun findAllByIdInAndUserId(ids: Set<UUID>, userId: UUID): List<UserFattyCard> {
		return userFattyCardRepository.findAllByIdInAndUserId(
			ids = ids,
			userId = userId,
		)
	}

	fun countByUserIdAndCreatedAtDate(userId: UUID, createdAt: LocalDate): Int {
		return userFattyCardRepository.countByUserIdAndCreatedAtDate(
			userId = userId,
			createdAt = createdAt,
		)
	}
}
