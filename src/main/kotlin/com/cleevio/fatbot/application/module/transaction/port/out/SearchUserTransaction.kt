package com.cleevio.fatbot.application.module.transaction.port.out

import com.cleevio.fatbot.adapter.`in`.InfiniteScroll
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.transaction.query.SearchUserTransactionQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.net.URI
import java.util.UUID

interface SearchUserTransaction {
	operator fun invoke(
		userId: UUID,
		chains: Set<Chain>,
		filter: SearchUserTransactionQuery.Filter,
		infiniteScroll: InfiniteScroll<UUID>,
		txHashToURIMapper: (TxHash, Chain) -> URI,
		fileToUrlMapper: FileToUrlMapper,
	): InfiniteScrollSlice<SearchUserTransactionQuery.Result, UUID>
}
