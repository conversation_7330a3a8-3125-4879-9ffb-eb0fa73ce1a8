package com.cleevio.fatbot.application.module.limitorder.scheduled

import com.cleevio.fatbot.application.module.limitorder.locks.PROCESS_TOKEN_TRADES
import com.cleevio.fatbot.application.module.limitorder.service.LimitOrderTradesProcessingService
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class LimitOrderTradesProcessingTrigger(
	private val limitOrderTradesProcessingService: LimitOrderTradesProcessingService,
) {

	@Scheduled(cron = "\${fatbot.limit-order.trades-processing.cron}")
	@SchedulerLock(
		name = PROCESS_TOKEN_TRADES,
		lockAtLeastFor = "\${fatbot.limit-order.trades-processing.lock-for}",
		lockAtMostFor = "\${fatbot.limit-order.trades-processing.lock-for}",
	)
	fun trigger() {
		limitOrderTradesProcessingService.tryStartTradesProcessing()
	}
}
