package com.cleevio.fatbot.application.module.market.query

import com.cleevio.fatbot.adapter.`in`.InfiniteScroll
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.common.validation.NullOrNotBlank
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.tokenaudit.service.GetTokenAudit
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.query.SearchUserTransactionQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import jakarta.validation.constraints.Positive
import java.math.BigDecimal
import java.math.BigInteger
import java.net.URI
import java.time.Instant
import java.util.UUID

class UserMarketPositionOverviewQuery private constructor(
	val userId: UUID,
	val useSelectedChains: Boolean,
	@field:Valid val filter: Filter,
) : Query<UserMarketPositionOverviewQuery.Result> {

	companion object {
		fun ofAll(userId: UUID, useSelectedChains: Boolean) = UserMarketPositionOverviewQuery(
			userId = userId,
			useSelectedChains = useSelectedChains,
			filter = Filter(walletId = null),
		)

		fun ofWallet(userId: UUID, walletId: UUID) = UserMarketPositionOverviewQuery(
			userId = userId,
			useSelectedChains = false, // Chains already scoped by walletId
			filter = Filter(walletId = walletId),
		)
	}

	data class Filter(
		val walletId: UUID?,
	)

	@Schema(name = "UserMarketPositionOverviewResult")
	data class Result(
		val totalValueAmountUsd: BigDecimal,
		val totalPnlAmountUsd: BigDecimal,
		val totalPnlAmountFraction: BigDecimal,
		val oneDayChangeAmountUsd: BigDecimal,
		val oneDayChangeFraction: BigDecimal,
	)
}

data class SearchUserMarketPositionQuery(
	val userId: UUID,
	val useSelectedChains: Boolean,
	@field:Valid val infiniteScroll: InfiniteScroll<UUID>,
	@field:Valid val filter: Filter,
) : Query<InfiniteScrollSlice<SearchUserMarketPositionQuery.Result, UUID>> {

	data class Filter(
		val walletId: UUID?,
		@field:NullOrNotBlank val searchString: String?,
	)

	@Schema(name = "SearchUserMarketPositionResult")
	data class Result(
		val id: UUID,
		val tokenAddress: AddressWrapper,
		val tokenDetailUrl: URI,
		val tokenName: String,
		val tokenSymbol: String,
		val tokenChain: Chain,
		val tokenImageUrl: URI?,
		val pricePerTokenInUsd: BigDecimal,
		val tokenNativeAmount: NativeAmount,
		val oneHourChangeFraction: BigDecimal?,
		val oneDayChangeFraction: BigDecimal?,
		val currentValueUsd: BigDecimal,
		val currentValueChangeUsd: BigDecimal,
		val currentValueChangeFraction: BigDecimal,
	)
}

data class SearchTokenQuery(
	val tokenAddress: AddressWrapper,
) : Query<List<SearchTokenQuery.Result>> {

	@Schema(name = "SearchTokenResult")
	data class Result(
		val chain: Chain,
		val imageUrl: String?,
		val token: GetTokenDetailQuery.DexInfo.Token,
		val priceUsd: BigDecimal,
		val priceChangeHours24: BigDecimal?,
		val oneHourVolumeUsd: BigDecimal?,
	)
}

class GetAllZeroFeeTokensQuery : Query<List<GetAllZeroFeeTokensQuery.Result>> {

	@Schema(name = "GetAllZeroFeeTokensResult")
	data class Result(
		val chain: Chain,
		val imageUrl: URI?,
		val tokenAddress: AddressWrapper,
		val tokenName: String,
		val tokenSymbol: String,
		val tokenDetailUrl: URI,
	)
}

data class GetTokenDetailQuery private constructor(
	val chain: Chain,
	val userId: UUID?,
	val tokenAddress: AddressWrapper,
) : Query<GetTokenDetailQuery.Result> {

	companion object {
		fun asAnonymousUser(chain: Chain, tokenAddress: AddressWrapper) = GetTokenDetailQuery(
			chain = chain,
			userId = null,
			tokenAddress = tokenAddress,
		)

		fun asSignedUser(chain: Chain, userId: UUID, tokenAddress: AddressWrapper) = GetTokenDetailQuery(
			chain = chain,
			userId = userId,
			tokenAddress = tokenAddress,
		)
	}

	@Schema(name = "GetTokenDetailResult")
	data class Result(
		val tokenInfo: Detail,
		val userWalletMarketPositionsOnThisToken: List<WalletMarketPosition>,
		val lastUserTransactionsOnThisToken: InfiniteScrollSlice<SearchUserTransactionQuery.Result, UUID>,
		val tokenAudit: GetTokenAudit.TokenAuditDto?,
	) {
		data class WalletMarketPosition(
			val walletId: UUID,
			val walletDetailUrl: URI,
			val walletCustomName: String?,
			val walletChain: Chain,
			val tokenName: String,
			val tokenSymbol: String,
			val tokenImageUrl: URI?,
			val tokenAmount: NativeAmount,
			val tokenChain: Chain,
			val currentValueUsd: BigDecimal,
			val currentValueChangeUsd: BigDecimal,
			val currentValueChangeFraction: BigDecimal,
		)
	}

	@Schema(name = "Detail")
	sealed interface Detail {
		val isOnDex: Boolean
	}

	@Schema(name = "NonDexDetail")
	data class NonDexDetail(
		val nonDexInfo: NonDexInfo,
	) : Detail {
		override val isOnDex = false
	}

	@Schema(name = "DexDetail")
	data class DexDetail(
		val tokenDecimals: BigInteger,
		val dexInfo: DexInfo,
	) : Detail {
		override val isOnDex = true
	}

	@Schema(name = "NonDexInfo")
	data class NonDexInfo(
		val name: String,
		val symbol: String,
	)

	@Schema(name = "DexInfo")
	data class DexInfo(
		val chainId: String,
		val url: String,
		val dexPairInfo: GetDex.Result,
		val baseToken: Token,
		val quoteToken: Token,
		val priceUsd: BigDecimal,
		val priceNative: BigDecimal,
		val volume: TimeIntervals,
		val priceChange: TimeIntervals,
		val fullyDilutedValue: BigDecimal?,
		val marketCap: BigDecimal?,
		val liquidity: Liquidity?,
		val buys: TimeIntervals,
		val sells: TimeIntervals,
		val pairCreatedAt: Instant,
		val info: Info?,
	) {
		data class Token(
			val address: AddressWrapper,
			val name: String,
			val symbol: String,
		)

		data class TimeIntervals(
			val hours24: BigDecimal?,
			val hours06: BigDecimal?,
			val hours01: BigDecimal?,
			val minutes05: BigDecimal?,
		)

		data class Liquidity(
			val usd: BigDecimal,
			val base: BigDecimal,
			val quote: BigDecimal,
		)

		data class Info(
			val imageUrl: String,
			val websites: List<Website>,
			val socials: List<Social>,
		) {
			data class Website(
				val label: String,
				val url: String,
			)

			data class Social(
				val type: String,
				val url: String,
			)
		}
	}
}

data class GetPredictedTokenAmountOnBuyQuery(
	val tokenAddress: AddressWrapper,
	@field:Positive val buyForCurrencyNativeAmount: NativeAmount,
	val chain: Chain,
	val dexPairInfo: GetDex.Result,
) : Query<GetPredictedTokenAmountOnBuyQuery.Result> {

	@Schema(name = "GetPredictedTokenAmountOnBuyResult")
	data class Result(
		val predictedTokenNativeAmount: NativeAmount,
	)
}

data class GetPredictedNativeAmountOnSellQuery(
	val tokenAddress: AddressWrapper,
	@field:Positive val toSellTokenNativeAmount: NativeAmount,
	val chain: Chain,
	val dexPairInfo: GetDex.Result,
) : Query<GetPredictedNativeAmountOnSellQuery.Result> {

	@Schema(name = "GetPredictedNativeAmountOnSellResult")
	data class Result(
		val predictedCurrencyNativeAmount: NativeAmount,
	)
}

data class GetTokenBalanceQuery(
	val userId: UUID,
	val walletId: UUID,
	val tokenAddress: AddressWrapper,
) : Query<GetTokenBalanceQuery.Result> {

	@Schema(name = "GetTokenBalanceResult")
	data class Result(
		val platformBalance: NativeAmount,
		val onChainBalance: NativeAmount,
	)
}

data class GetGasEstimationUsdQuery(
	val transactionType: TransactionType,
	val userId: UUID,
	val walletId: UUID,
	val tokenAddress: AddressWrapper?,
	val amount: NativeAmount?,
	val dexPairInfo: GetDex.Result?,
	val recipientAddress: AddressWrapper?,
) : Query<GetGasEstimationUsdQuery.Result> {

	@Schema(name = "GetGasEstimationUsdResult")
	data class Result(
		val gasFeeUsd: BigDecimal?,
	)
}
