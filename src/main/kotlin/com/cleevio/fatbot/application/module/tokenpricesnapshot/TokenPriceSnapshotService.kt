package com.cleevio.fatbot.application.module.tokenpricesnapshot

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.tokenprice.TokenPriceRepository
import com.cleevio.fatbot.domain.tokenprice.TokenPriceSnapshotCreateService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.Instant

@Service
class TokenPriceSnapshotService(
	private val tokenPriceSnapshotCreateService: TokenPriceSnapshotCreateService,
	private val tokenPriceSnapshotRepository: TokenPriceRepository,
) {

	@Transactional
	fun saveTokenPriceSnapshots(
		tokenToPrice: Map<ChainAddress, BaseAmount>,
		cryptoCurrencyToExchangeRate: Map<CryptoCurrency, BigDecimal>,
		validAt: Instant,
	) {
		tokenToPrice.forEach { (tokenOnChain, price) ->
			tokenPriceSnapshotCreateService.create(
				tokenAddress = tokenOnChain.address,
				chain = tokenOnChain.chain,
				price = price.amount.asBaseAmount(),
				exchangeRate = cryptoCurrencyToExchangeRate[tokenOnChain.chain.currency]
					?: error("No exchange rate for ${tokenOnChain.chain.currency} found"),
				validAt = validAt,
			)
		}
	}

	@Transactional
	fun saveTokenPrices(
		tokenAddress: AddressWrapper,
		chain: Chain,
		priceExchangeRateTimestampTriples: List<Triple<BaseAmount, BigDecimal, Instant>>,
	) {
		priceExchangeRateTimestampTriples.forEach { (price, exchangeRate, validAt) ->
			tokenPriceSnapshotCreateService.create(
				tokenAddress = tokenAddress,
				chain = chain,
				price = price,
				exchangeRate = exchangeRate,
				validAt = validAt,
			)
		}
	}

	@Transactional
	fun deleteSnapshotsBefore(before: Instant) = tokenPriceSnapshotRepository.deleteSnapshotsBefore(before = before)
}
