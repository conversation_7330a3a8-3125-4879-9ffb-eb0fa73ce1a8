package com.cleevio.fatbot.application.module.walletposition.event.listener

import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.walletposition.command.SyncWalletCurrencyPositionCommand
import com.cleevio.fatbot.application.module.walletposition.event.NewWalletImportedEvent
import com.cleevio.fatbot.application.module.walletposition.event.TransactionSuccessfulEvent
import io.sentry.spring.jakarta.tracing.SentrySpan
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component

@Component
class WalletCurrencyPositionEventListener(
	private val commandBus: CommandBus,
) {

	@EventListener
	@SentrySpan
	fun onNewWalletImportedEvent(event: NewWalletImportedEvent) {
		commandBus(SyncWalletCurrencyPositionCommand(walletId = event.walletId))
	}

	@Async
	@EventListener
	@SentryTransaction(operation = "async.sync-wallet-on-tx-success")
	fun onTransactionSuccessfulEvent(event: TransactionSuccessfulEvent) {
		commandBus(SyncWalletCurrencyPositionCommand(walletId = event.walletId))
	}
}
