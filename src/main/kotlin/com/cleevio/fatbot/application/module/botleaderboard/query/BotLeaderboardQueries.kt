package com.cleevio.fatbot.application.module.botleaderboard.query

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.common.validation.NullOrNotBlank
import com.cleevio.fatbot.application.module.bot.constant.BotSortParameter
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import java.math.BigDecimal
import java.util.UUID

data class BotsLeaderboardSearchQuery(
	val userId: UUID?,
	@field:Valid val filter: Filter,
	@field:Valid val infiniteScroll: InfiniteScrollDesc<BigDecimal>,
) : Query<InfiniteScrollSlice<BotsLeaderboardSearchQuery.Result, BigDecimal>> {

	data class Filter(
		val timeRange: TimeRange,
		@field:NullOrNotBlank
		val searchString: String?,
		val allBots: Boolean,
		val sortParameter: BotSortParameter,
	)

	@Schema(name = "BotsLeaderboardSearchResult")
	data class Result(
		val botId: UUID,
		val botName: String,
		val botAvatarFileId: UUID,
		val profitUsd: BigDecimal,
		val profitPercentage: BigDecimal,
		val walletBalance: BigDecimal,
		val daysActive: Int,
		val buyTransactionsCount: Int,
		val sellTransactionsCount: Int,
	)
}
