package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import java.math.BigDecimal

/**
 * Returns how much USD is one unit of given cryptoCurrency worth
 */
interface GetUsdExchangeRate {
	operator fun invoke(cryptoCurrency: CryptoCurrency): BigDecimal
	fun getAll(cryptoCurrencies: Set<CryptoCurrency>): Map<CryptoCurrency, BigDecimal>
}
