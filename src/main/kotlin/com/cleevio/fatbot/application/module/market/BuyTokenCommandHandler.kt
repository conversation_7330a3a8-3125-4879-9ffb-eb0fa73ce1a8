package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.adapter.out.evm.BuyTokenEVM
import com.cleevio.fatbot.adapter.out.solana.BuyTokenSolana
import com.cleevio.fatbot.adapter.out.solana.pumpfun.GetPumpfunCurveState
import com.cleevio.fatbot.adapter.out.solana.pumpswap.GetPumpswapPool
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.extractFirstSignatureAsTxHash
import com.cleevio.fatbot.application.common.crypto.hashToTxHash
import com.cleevio.fatbot.application.common.crypto.minusBasisPoints
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.module.market.command.BuyTokenCommand
import com.cleevio.fatbot.application.module.market.exception.InvalidDexPairProvidedException
import com.cleevio.fatbot.application.module.market.port.out.EstimateGas
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutUniswap
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.port.out.ValidateFundsForEvmTransaction
import com.cleevio.fatbot.application.module.market.port.out.ValidateFundsForSvmTransaction
import com.cleevio.fatbot.application.module.market.port.out.toDexPairInfoInput
import com.cleevio.fatbot.application.module.market.service.GasLimitEstimationService
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.token.finder.TokenPairInfoFinderService
import com.cleevio.fatbot.application.module.token.query.GetTokenTaxQuery
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.event.TokenBoughtEvent
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.domain.wallet.Wallet
import com.cleevio.fatbot.infrastructure.coroutines.createSupervisorJobScope
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class BuyTokenCommandHandler(
	private val walletFinderService: WalletFinderService,
	private val evmTokenInfoFinderService: EvmTokenInfoFinderService,
	private val buyTokenEvm: BuyTokenEVM,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val validateFundsForEvmTransaction: ValidateFundsForEvmTransaction,
	private val validateFundsForSvmTransaction: ValidateFundsForSvmTransaction,
	private val gasLimitEstimationService: GasLimitEstimationService,
	private val pairInfoFinderService: TokenPairInfoFinderService,
	private val buyTokenSolana: BuyTokenSolana,
	private val getAmountOutUniswap: GetAmountOutUniswap,
	private val getPumpswapPool: GetPumpswapPool,
	private val getPumpfunCurveState: GetPumpfunCurveState,
) : CommandHandler<BuyTokenCommand.Result, BuyTokenCommand> {

	override val command = BuyTokenCommand::class

	@Lazy
	@Autowired
	private lateinit var queryBus: QueryBus

	override fun handle(command: BuyTokenCommand): BuyTokenCommand.Result = with(command) {
		val wallet = walletFinderService.getByIdAndUserId(id = command.walletId, userId = command.userId)

		val tokenInfo = evmTokenInfoFinderService.getByTokenAddressAndChain(
			tokenAddress = command.tokenAddress,
			chain = wallet.chain,
		)

		val signedTx = when (wallet.chain.type) {
			ChainType.EVM -> handleEvmBuy(wallet)
			ChainType.SOLANA -> handleSolanaBuy(wallet, isToken2022 = tokenInfo.isToken2022)
		}

		applicationEventPublisher.publishEvent(
			TokenBoughtEvent(
				walletId = wallet.id,
				signedTxList = listOf(signedTx),
				tokenAddress = command.tokenAddress,
			),
		)

		return when (wallet.chain.type) {
			ChainType.EVM -> BuyTokenCommand.Result(txHash = signedTx.hashToTxHash())
			ChainType.SOLANA -> BuyTokenCommand.Result(txHash = signedTx.extractFirstSignatureAsTxHash())
		}
	}

	private fun BuyTokenCommand.handleEvmBuy(wallet: Wallet): SignedTx {
		if (dexPairInfo !is GetDex.Evm) {
			throw InvalidDexPairProvidedException("Non-EVM $dexPairInfo provided!")
		}

		val scope = createSupervisorJobScope()
		val tokenTaxDeferred = scope.async {
			queryBus(
				GetTokenTaxQuery(
					tokenAddress = tokenAddress,
					chain = wallet.chain,
					dexPairInfo = dexPairInfo.toDexPairInfoInput(),
				),
			)
		}

		val buyForBaseAmount = buyForNativeAmount.toBase(wallet.chain)

		val estimateGasResult = gasLimitEstimationService.estimateGas(
			transactionType = TransactionType.BUY,
			chain = wallet.chain,
			userId = userId,
			walletId = wallet.id,
			tokenAddress = tokenAddress,
			isToken2022 = false,
			baseAmount = buyForBaseAmount,
			dexPairInfo = dexPairInfo,
			recipientAddress = wallet.address,
		)

		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultEvm) error("Estimate gas must be of EVM type")

		val validateResult = validateFundsForEvmTransaction(
			walletAddress = wallet.address,
			tokenAddress = tokenAddress,
			chainId = wallet.chain.id!!,
			transactionType = TransactionType.BUY,
			transactionAmount = buyForBaseAmount,
			gasInfo = estimateGasResult.gasInfo,
			gasLimitEstimate = estimateGasResult.gasLimitEstimate,
		)

		val expectedAmountOut = when (dexPairInfo) {
			is GetDex.UniswapV2, is GetDex.PancakeswapV2 -> {
				getAmountOutUniswap.getAmountOutV2(
					chainId = wallet.chain.evmId,
					tokenAddress = tokenAddress,
					amountIn = buyForBaseAmount,
					isBuy = true,
				)
			}
			is GetDex.PancakeswapV3 -> getAmountOutUniswap.getAmountOutV3(
				chainId = wallet.chain.evmId,
				tokenAddress = tokenAddress,
				amountIn = buyForBaseAmount,
				fee = dexPairInfo.fee,
				isBuy = true,
			)

			is GetDex.UniswapV3 -> getAmountOutUniswap.getAmountOutV3(
				chainId = wallet.chain.evmId,
				tokenAddress = tokenAddress,
				amountIn = buyForBaseAmount,
				fee = dexPairInfo.fee,
				isBuy = true,
			)
		}

		val gasInfo = when (wallet.chain) {
			// In case of arbitrum it is safer to refetch the gasInfo right before the call
			Chain.EVM_ARBITRUM_ONE -> null
			else -> estimateGasResult.gasInfo
		}

		val buyTax = runBlocking { tokenTaxDeferred.await() }.buyTax ?: BigDecimal.ZERO
		val buyTaxBp = BasisPoint.ofFraction(buyTax)

		val expectedAmountOutAfterTax = expectedAmountOut.minusBasisPoints(buyTaxBp)

		return buyTokenEvm(
			BuyTokenEVM.EvmInput(
				userId = userId,
				tokenAddress = tokenAddress,
				privateKey = wallet.privateKey,
				tokenChainId = wallet.chain.id,
				buyForBaseAmount = buyForBaseAmount,
				expectedAmountOut = expectedAmountOutAfterTax,
				gasInfoHint = gasInfo,
				dexPairInfo = dexPairInfo,
				gasLimit = validateResult.gasLimitEstimate,
			),
		)
	}

	private fun BuyTokenCommand.handleSolanaBuy(wallet: Wallet, isToken2022: Boolean): SignedTx {
		if (dexPairInfo !is GetDex.Solana) {
			throw InvalidDexPairProvidedException("Non-Solana $dexPairInfo provided!")
		}
		val buyForBaseAmount = buyForNativeAmount.toBase(wallet.chain)

		val estimateGasResult = gasLimitEstimationService.estimateGas(
			transactionType = TransactionType.BUY,
			chain = wallet.chain,
			userId = userId,
			walletId = wallet.id,
			tokenAddress = tokenAddress,
			isToken2022 = isToken2022,
			baseAmount = buyForBaseAmount,
			dexPairInfo = dexPairInfo,
			recipientAddress = wallet.address,
		)

		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultSolana) error("Estimate gas must be of Solana type")

		validateFundsForSvmTransaction(
			walletId = wallet.id,
			walletAddress = wallet.address,
			transactionAmount = buyForBaseAmount,
			estimatedFees = estimateGasResult.feeBaseAmount,
		)

		val tokenPair by lazy { pairInfoFinderService.getByPairAddressAndChain(dexPairInfo.pairAddress, Chain.SOLANA) }

		return buyTokenSolana(
			BuyTokenSolana.SolanaInput(
				userId = wallet.userId,
				tokenAddress = tokenAddress,
				isToken2022 = isToken2022,
				privateKey = wallet.privateKey,
				buyForLamportAmount = buyForBaseAmount,
				dexPairInfo = dexPairInfo,
				getRaydiumAMMMarketData = { tokenPair.raydiumAmmMarketData!! },
				getRaydiumCPMMMarketData = { tokenPair.raydiumCpmmMarketData!! },
				getPumpfunCreator = {
					tokenPair.creatorAddress ?: getPumpfunCurveState(dexPairInfo.pairAddress).creator
						?: error("Bonding curve ${dexPairInfo.pairAddress} does not provide creator address")
				},
				getPumpswapCoinCreator = { tokenPair.creatorAddress ?: getPumpswapPool(dexPairInfo.pairAddress).coinCreator },
				useStakedEndpoint = false,
				useMevProtection = wallet.buyAntiMevProtection,
			),
		)
	}
}
