package com.cleevio.fatbot.application.module.userfattycard.command

import com.cleevio.fatbot.application.common.command.Command
import jakarta.validation.constraints.NotEmpty
import java.util.UUID

data class ClaimUserFattyCardCommand(
	val userId: UUID,
	val userFattyCardId: UUID,
) : Command<Unit>

data class DisplayUserFattyCardsCommand(
	val userId: UUID,
	@field:NotEmpty
	val userFattyCardIds: Set<UUID>,
) : Command<Unit>
