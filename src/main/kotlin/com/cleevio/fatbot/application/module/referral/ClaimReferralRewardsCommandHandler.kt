package com.cleevio.fatbot.application.module.referral

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.asLamport
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.compareTo
import com.cleevio.fatbot.application.common.crypto.extractFirstSignatureAsTxHash
import com.cleevio.fatbot.application.common.crypto.hashToTxHash
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.module.bot.port.out.TransferSol
import com.cleevio.fatbot.application.module.market.port.out.EstimateGas
import com.cleevio.fatbot.application.module.market.port.out.TransferEth
import com.cleevio.fatbot.application.module.market.service.GasLimitEstimationService
import com.cleevio.fatbot.application.module.referral.command.ClaimReferralRewardsCommand
import com.cleevio.fatbot.application.module.referral.exception.ReferralRewardBelowThresholdException
import com.cleevio.fatbot.application.module.referral.locks.CLAIM_REFERRAL_REWARDS
import com.cleevio.fatbot.application.module.referral.locks.REFERRAL_MODULE
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.event.ReferralRewardClaimedEvent
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.domain.referral.ReferralRewardFinderService
import com.cleevio.fatbot.domain.wallet.Wallet
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
class ClaimRewardsCommandHandler(
	private val referralRewardFinderService: ReferralRewardFinderService,
	private val walletFinderService: WalletFinderService,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val transferEth: TransferEth,
	private val transferSol: TransferSol,
	private val gasLimitEstimationService: GasLimitEstimationService,
	private val evmChainContextFactory: EvmChainContextFactory,
	private val chainProperties: ChainProperties,
) : CommandHandler<ClaimReferralRewardsCommand.Result, ClaimReferralRewardsCommand> {

	override val command = ClaimReferralRewardsCommand::class

	@Transactional
	@Lock(module = REFERRAL_MODULE, lockName = CLAIM_REFERRAL_REWARDS)
	override fun handle(
		@LockFieldParameter("userId") command: ClaimReferralRewardsCommand,
	): ClaimReferralRewardsCommand.Result = with(command) {
		val destinationWallet = walletFinderService.getUserDefaultWalletOnChain(userId = userId, chain = chain)
		val toClaimNativeAmount = claimRewards(command.userId, destinationWallet.chain)

		val txHash = when (destinationWallet.chain.type) {
			ChainType.EVM -> claimOnEvm(toClaimNativeAmount, destinationWallet)
			ChainType.SOLANA -> claimOnSolana(toClaimNativeAmount, destinationWallet)
		}

		return ClaimReferralRewardsCommand.Result(claimedAmountNative = toClaimNativeAmount, txHash = txHash)
	}

	private fun claimRewards(userId: UUID, chain: Chain): NativeAmount {
		val unclaimedRewards = referralRewardFinderService.findAllUserUnclaimedOnChain(userId = userId, chain = chain)
		val claimedAmount = unclaimedRewards.sumOf { it.baseAmount }.asBaseAmount().toNative(chain)

		val thresholdNativeAmount = when (chain.type) {
			ChainType.EVM -> evmChainContextFactory.ofChainId(chain.evmId) { properties.referralClaimThresholdNativeAmount }
			ChainType.SOLANA -> chainProperties.svm.solana.referralClaimThresholdNativeAmount
		}.asNativeAmount()

		if (claimedAmount < thresholdNativeAmount) {
			throw ReferralRewardBelowThresholdException(
				"Cannot claim less than ${thresholdNativeAmount.amount} on chain $chain.",
			)
		}

		unclaimedRewards.forEach { it.claim() }

		return claimedAmount
	}

	private fun ClaimReferralRewardsCommand.claimOnEvm(nativeAmount: NativeAmount, destinationWallet: Wallet): TxHash {
		destinationWallet.assertIsEvmChain()

		val sendingWalletPrivateKey = evmChainContextFactory.ofChainId(destinationWallet.chain.evmId) {
			properties.referralWalletPrivateKey
		}

		val estimateGasResult = gasLimitEstimationService.estimateGas(
			transactionType = TransactionType.TRANSFER_CURRENCY,
			chain = destinationWallet.chain,
			userId = userId,
			walletId = destinationWallet.id,
			baseAmount = nativeAmount.toBase(destinationWallet.chain),
			recipientAddress = destinationWallet.address,
		)

		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultEvm) error("Estimate gas must be of EVM type")

		val transferSignedTx = transferEth(
			destinationWalletAddress = destinationWallet.address,
			privateKey = sendingWalletPrivateKey,
			chainId = destinationWallet.chain.evmId,
			amountToTransfer = nativeAmount.toBase(destinationWallet.chain),
			gasInfo = estimateGasResult.gasInfo,
			gasLimit = estimateGasResult.gasLimitEstimate,
		)

		applicationEventPublisher.publishEvent(
			ReferralRewardClaimedEvent(
				destinationWalletId = destinationWallet.id,
				signedTxList = listOf(transferSignedTx),
			),
		)

		return transferSignedTx.hashToTxHash()
	}

	private fun claimOnSolana(nativeAmount: NativeAmount, destinationWallet: Wallet): TxHash {
		destinationWallet.assertIsSolanaChain()

		val transferSignedTx = transferSol(
			privateKey = chainProperties.svm.solana.referralWalletPrivateKey,
			amount = nativeAmount.toBase(destinationWallet.chain).amount.asLamport(),
			destinationWalletAddress = destinationWallet.address,
		)

		applicationEventPublisher.publishEvent(
			ReferralRewardClaimedEvent(
				destinationWalletId = destinationWallet.id,
				signedTxList = listOf(transferSignedTx),
			),
		)

		return transferSignedTx.extractFirstSignatureAsTxHash()
	}
}
