package com.cleevio.fatbot.application.module.userleaderbord.port.out

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollAsc
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.module.userleaderbord.query.SearchUserLeaderboardQuery

interface SearchUserLeaderboard {

	operator fun invoke(
		infiniteScroll: InfiniteScrollAsc.Integer,
	): InfiniteScrollSlice<SearchUserLeaderboardQuery.Result, Int>
}
