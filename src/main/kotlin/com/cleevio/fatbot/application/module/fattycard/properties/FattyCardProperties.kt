package com.cleevio.fatbot.application.module.fattycard.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.validation.annotation.Validated
import java.math.BigDecimal

@ConfigurationProperties(prefix = "fatbot.fatty-cards")
@Validated
data class FattyCardProperties(
	val tradeAmountNeededForCard: BigDecimal,
	val maxCardsEarnedInDay: Int,
)
