package com.cleevio.fatbot.application.module.portfolio

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.bot.BotFinderService
import com.cleevio.fatbot.application.module.botportfolio.finder.BotPortfolioValueSnapshotFinderService
import com.cleevio.fatbot.application.module.market.event.UserPortfolioViewedEvent
import com.cleevio.fatbot.application.module.portfolio.constant.PieChartValueType
import com.cleevio.fatbot.application.module.portfolio.query.GetPortfolioValuesQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.portfolio.PortfolioValueSnapshotFinderService
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Clock
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID

@Component
class GetPortfolioValuesQueryHandler(
	private val portfolioValueService: PortfolioValueService,
	private val portfolioValueSnapshotFinderService: PortfolioValueSnapshotFinderService,
	private val botFinderService: BotFinderService,
	private val botPortfolioValueSnapshotFinderService: BotPortfolioValueSnapshotFinderService,
	private val clock: Clock,
	private val applicationEventPublisher: ApplicationEventPublisher,
) : QueryHandler<GetPortfolioValuesQuery.Result, GetPortfolioValuesQuery> {

	override val query = GetPortfolioValuesQuery::class

	override fun handle(query: GetPortfolioValuesQuery): GetPortfolioValuesQuery.Result {
		val chainToPortfolioPerformanceNow = portfolioValueService.getChainToPerformance(query.userId)

		val today = LocalDate.now(clock)
		val valueUsdNow = chainToPortfolioPerformanceNow.values.sumOf { it.totalValueUsd }
		val acquisitionUsdNow = chainToPortfolioPerformanceNow.values.sumOf { it.totalAcquisitionValueUsd }
		val value24hUsd = chainToPortfolioPerformanceNow.values.sumOf { it.totalValue24hUsd }
		val todayValue = GetPortfolioValuesQuery.PortfolioPastValue(portfolioValueUsd = valueUsdNow, date = today)

		val pastValues = getPortfolioPastValues(query.userId)
		val botPastValues = getBotPortfolioPastValues(userId = query.userId)

		val values = pastValues.map { pastValue ->
			val botPastValueAtDate = botPastValues[pastValue.date]?.portfolioValueUsd ?: BigDecimal.ZERO
			GetPortfolioValuesQuery.PortfolioPastValue(
				portfolioValueUsd = pastValue.portfolioValueUsd + botPastValueAtDate,
				date = pastValue.date,
			)
		} + todayValue

		applicationEventPublisher.publishEvent(UserPortfolioViewedEvent(query.userId))

		return GetPortfolioValuesQuery.Result(
			// it's called pastValues even when it's containing today value as we don't want to break FE
			pastValues = values,
			pieChartValues = chainToPortfolioPerformanceNow.getPieChartValues(),
			totalValueAmountUsd = valueUsdNow,
			totalPnlAmountUsd = valueUsdNow - acquisitionUsdNow,
			totalPnlAmountFraction = calculateFractionChange(valueUsdNow, acquisitionUsdNow),
			oneDayChangeAmountUsd = valueUsdNow - value24hUsd,
			oneDayChangeFraction = calculateFractionChange(valueUsdNow, value24hUsd),
		)
	}

	private fun getPortfolioPastValues(userId: UUID) =
		portfolioValueSnapshotFinderService.findAllPortfolioValueSnapshots(userId)
			.groupBy { it.date }
			.map { (date, chainSnapshots) ->
				GetPortfolioValuesQuery.PortfolioPastValue(
					portfolioValueUsd = chainSnapshots.sumOf { it.valueUsd },
					date = date,
				)
			}

	private fun getBotPortfolioPastValues(userId: UUID): Map<LocalDate, GetPortfolioValuesQuery.PortfolioPastValue> {
		val botIds = botFinderService.findAllByUserId(userId = userId).mapToSet { it.id }

		// TODO: This does definitely need optimization, it will only grow the more older bots will get
		return botPortfolioValueSnapshotFinderService.findAllByBotIds(botIds = botIds)
			.groupBy { LocalDate.ofInstant(it.snapshotMadeAt, ZoneId.of(ZoneOffset.UTC.id)) }
			.mapValues { (date, botPortfolioValueSnapshots) ->
				val earliest = botPortfolioValueSnapshots.minBy { it.snapshotMadeAt }
				GetPortfolioValuesQuery.PortfolioPastValue(
					portfolioValueUsd = earliest.portfolioValueUsd,
					date = date,
				)
			}
	}

	private fun Map<Chain, PortfolioValueService.PerformanceResult>.getPieChartValues():
		List<GetPortfolioValuesQuery.PieChartValue> {
		val manualTradesPieValues = this.map { (chain, performance) ->
			GetPortfolioValuesQuery.PieChartValue(
				chain = chain,
				portfolioValueUsd = performance.userValueUsd,
				portfolioPnlFraction = performance.userPnlFraction,
				type = PieChartValueType.MANUAL,
			)
		}
		val botTradesPieValues = this.filter { it.key == Chain.SOLANA }.map { (chain, performance) ->
			GetPortfolioValuesQuery.PieChartValue(
				chain = chain,
				portfolioValueUsd = performance.botValueUsd,
				portfolioPnlFraction = performance.botPnlFraction,
				type = PieChartValueType.BOT,
			)
		}
		return manualTradesPieValues + botTradesPieValues
	}
}
