package com.cleevio.fatbot.application.module.market.service

import com.cleevio.fatbot.adapter.out.evm.constants.EVM_GAS_LIMIT_MULTIPLIER
import com.cleevio.fatbot.adapter.out.evm.model.GasInfo
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.gassnapshot.finder.GasInfoSnapshotFinderService
import com.cleevio.fatbot.application.module.market.exception.DataNotProvided
import com.cleevio.fatbot.application.module.market.port.out.EstimateGas
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.port.out.estimate
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import org.springframework.stereotype.Service
import java.math.BigInteger
import java.util.UUID

@Service
class GasLimitEstimationService(
	private val estimateGas: List<EstimateGas>,
	private val walletFinderService: WalletFinderService,
	private val gasInfoSnapshotFinderService: GasInfoSnapshotFinderService,
) {

	fun estimateGas(
		transactionType: TransactionType,
		chain: Chain,
		userId: UUID,
		walletId: UUID,
		tokenAddress: AddressWrapper? = null,
		isToken2022: Boolean? = null,
		baseAmount: BaseAmount?,
		dexPairInfo: GetDex.Result? = null,
		recipientAddress: AddressWrapper?,
	): EstimateGas.Result {
		assertParamsCorrectBasedOnChain(
			chain = chain,
			tokenAddress = tokenAddress,
			dexPairInfo = dexPairInfo,
			recipientAddress = recipientAddress,
		)

		val wallet = walletFinderService.getByIdAndUserId(id = walletId, userId = userId)

		val gasInfoHint = chain.id?.let {
			val snapshot = gasInfoSnapshotFinderService.findRecentByChainId(chainId = it)

			snapshot?.let { GasInfo.fromSnapshot(snapshot) }
		}

		val estimateGasLimitResult = when (transactionType) {
			TransactionType.BUY -> estimateGas.estimate(
				EstimateGas.BuyInput(
					privateKey = wallet.privateKey,
					chain = chain,
					gasInfo = gasInfoHint,
					userId = userId,
					useAntiMevProtection = wallet.buyAntiMevProtection,
					tokenAddress = tokenAddress ?: throw DataNotProvided("Token address not provided"),
					isToken2022 = isToken2022 ?: throw DataNotProvided("IsToken2022 not provided"),
					amount = baseAmount ?: throw DataNotProvided("Amount not provided"),
					dexPairInfo = dexPairInfo ?: throw DataNotProvided("Dex pair not provided"),
				),
			)

			TransactionType.SELL -> estimateGas.estimate(
				EstimateGas.SellInput(
					privateKey = wallet.privateKey,
					chain = chain,
					gasInfo = gasInfoHint,
					userId = userId,
					useAntiMevProtection = wallet.sellAntiMevProtection,
					tokenAddress = tokenAddress ?: throw DataNotProvided("Token address not provided"),
					amount = baseAmount ?: throw DataNotProvided("Amount not provided"),
					dexPairInfo = dexPairInfo ?: throw DataNotProvided("Dex pair not provided"),
				),
			)

			TransactionType.TRANSFER_CURRENCY -> estimateGas.estimate(
				EstimateGas.TransferCurrencyInput(
					privateKey = wallet.privateKey,
					chain = chain,
					gasInfo = gasInfoHint,
					amount = baseAmount ?: throw DataNotProvided("Amount not provided"),
					toAddress = recipientAddress ?: throw DataNotProvided("Recipient address not provided"),
				),
			)

			TransactionType.TRANSFER_TOKEN -> estimateGas.estimate(
				EstimateGas.TransferTokenInput(
					privateKey = wallet.privateKey,
					chain = chain,
					gasInfo = gasInfoHint,
					amount = baseAmount ?: throw DataNotProvided("Amount not provided"),
					tokenAddress = tokenAddress ?: throw DataNotProvided("Token address not provided"),
					isToken2022 = isToken2022 ?: throw DataNotProvided("IsToken2022 not provided"),
					toAddress = recipientAddress ?: throw DataNotProvided("Recipient address not provided"),
				),
			)

			TransactionType.APPROVE -> estimateGas.estimate(
				EstimateGas.ApproveInput(
					privateKey = wallet.privateKey,
					chain = chain,
					tokenAddress = tokenAddress ?: throw DataNotProvided("Token address not provided"),
					gasInfo = gasInfoHint,
				),
			)

			TransactionType.CLAIM_REFERRAL_REWARD -> throw UnsupportedOperationException()
		}

		return when (estimateGasLimitResult) {
			is EstimateGas.ResultEvm -> {
				val gasLimitEstimate = estimateGasLimitResult.gasLimitEstimate
				val bufferedGasLimitEstimate = gasLimitEstimate?.multiply(EVM_GAS_LIMIT_MULTIPLIER)?.divide(HUNDRED)

				return estimateGasLimitResult.copy(gasLimitEstimate = bufferedGasLimitEstimate)
			}
			// no need to do any buffering
			is EstimateGas.ResultSolana -> estimateGasLimitResult
		}
	}

	private fun assertParamsCorrectBasedOnChain(
		chain: Chain,
		tokenAddress: AddressWrapper?,
		dexPairInfo: GetDex.Result?,
		recipientAddress: AddressWrapper?,
	) {
		when (chain.type) {
			ChainType.EVM -> {
				tokenAddress?.let { require(it.isEvm()) }
				dexPairInfo?.let { require(it is GetDex.Evm) }
				recipientAddress?.let { require(it.isEvm()) }
			}
			ChainType.SOLANA -> {
				tokenAddress?.let { require(it.isSolana()) }
				dexPairInfo?.let { require(it is GetDex.Solana) }
				recipientAddress?.let { require(it.isSolana()) }
			}
		}
	}
}

private val HUNDRED = BigInteger.valueOf(100)
