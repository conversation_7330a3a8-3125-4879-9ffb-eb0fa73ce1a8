package com.cleevio.fatbot.application.module.userstatistics.query

import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.module.userstatistics.constant.StreakDayState
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.util.UUID

data class GetUserStreakQuery(
	val userId: UUID,
) : Query<GetUserStreakQuery.Result> {

	@Schema(name = "GetUserStreakResult")
	data class Result(
		val userId: UUID,
		val daysInStreak: Int,
		val currentMultiplier: BigDecimal,
		val daysToNextStreak: Int?,
		val nextMultiplier: BigDecimal?,
		val streakExpiresAt: Instant,
		val isThresholdDay: Boolean,
		val thresholds: List<MultipliersThresholdResult>,
		val streakDates: List<StreakDayResult>,
	)

	@Schema(name = "GetMultipliersThresholdResult")
	data class MultipliersThresholdResult(
		val daysInStreak: Int,
		val multiplier: BigDecimal,
		val completed: Boolean,
	)

	@Schema(name = "GetStreakDayResult")
	data class StreakDayResult(
		val date: LocalDate,
		val state: StreakDayState,
	)
}

data class GetUserStreakGeneralInfoQuery(
	val userId: UUID,
) : Query<List<GetUserStreakGeneralInfoQuery.Result>> {

	@Schema(name = "GetUserStreakGeneralInfoResult")
	data class Result(
		val daysInStreak: Int,
		val multiplier: BigDecimal,
	)
}
