package com.cleevio.fatbot.application.module.transaction.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class TransactionNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.TRANSACTION_NOT_FOUND,
	message = message,
)
