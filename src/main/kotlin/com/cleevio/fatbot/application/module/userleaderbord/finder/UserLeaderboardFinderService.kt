package com.cleevio.fatbot.application.module.userleaderbord.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.domain.userleaderboad.UserLeaderBoardRepository
import com.cleevio.fatbot.domain.userleaderboad.UserLeaderboard
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class UserLeaderboardFinderService(
	private val userLeaderBoardRepository: UserLeaderBoardRepository,
) : BaseFinderService<UserLeaderboard>(userLeaderBoardRepository) {

	override fun errorBlock(message: String) = error(message)

	override fun getEntityType() = UserLeaderboard::class

	fun findByUserId(userId: UUID): UserLeaderboard? = userLeaderBoardRepository.findByUserId(userId)
	fun existsByUserId(userId: UUID): Boolean = userLeaderBoardRepository.existsByUserId(userId)
}
