package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.command.TrackTokenCommand
import com.cleevio.fatbot.application.module.market.finder.MarketPositionFinderService
import com.cleevio.fatbot.application.module.market.port.out.GetTokenBalancesEVM
import com.cleevio.fatbot.application.module.market.port.out.GetTokenBalancesSolana
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.token.locks.TOKEN_MODULE
import com.cleevio.fatbot.application.module.token.locks.TRACK_TOKENS_FOR_USER
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.domain.market.MarketPositionCreateService
import com.cleevio.fatbot.domain.market.MarketPositionDeleteService
import com.cleevio.fatbot.domain.transaction.TrackingTransactionCreateService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.math.BigInteger

@Component
class TrackTokenCommandHandler(
	private val walletFinderService: WalletFinderService,
	private val marketPositionFinderService: MarketPositionFinderService,
	private val marketPositionCreateService: MarketPositionCreateService,
	private val marketPositionDeleteService: MarketPositionDeleteService,
	private val getTokenBalancesEVM: GetTokenBalancesEVM,
	private val getTokenBalancesSolana: GetTokenBalancesSolana,
	private val getTokenPrices: GetTokenPrices,
	private val trackingTransactionCreateService: TrackingTransactionCreateService,
	private val evmTokenInfoFinderService: EvmTokenInfoFinderService,
	private val getUsdExchangeRate: GetUsdExchangeRate,
) : CommandHandler<TrackTokenCommand.Result, TrackTokenCommand> {

	override val command = TrackTokenCommand::class

	@Transactional
	@Lock(TOKEN_MODULE, TRACK_TOKENS_FOR_USER)
	override fun handle(@LockFieldParameter("userId") command: TrackTokenCommand): TrackTokenCommand.Result {
		val wallet = walletFinderService.getByIdAndUserId(id = command.walletId, userId = command.userId)

		val marketPosition = marketPositionFinderService.findMarketPosition(
			walletId = wallet.id,
			chain = wallet.chain,
			tokenAddress = command.tokenAddress,
		) ?: marketPositionCreateService.create(wallet.id, wallet.chain, command.tokenAddress)
		val platformBalance = marketPosition.getTokenBalance()

		val tokenInfo = evmTokenInfoFinderService.getByTokenAddressAndChain(command.tokenAddress, wallet.chain)
		val tokenDecimals = tokenInfo.decimals

		val onChainBalance = when (wallet.chain.type) {
			ChainType.EVM -> getTokenBalancesEVM(
				chainId = wallet.chain.evmId,
				walletAddress = wallet.address,
				tokenAddresses = listOf(command.tokenAddress),
			).values.firstOrNull() ?: error("Didn't receive ERC20 token balance from router.")

			ChainType.SOLANA -> getTokenBalancesSolana.single(
				ownerAddress = wallet.address,
				tokenAddress = command.tokenAddress,
			).amount
		}

		val exchangeRate = getUsdExchangeRate(cryptoCurrency = wallet.chain.currency)

		val tokenTrackingResult = when {
			platformBalance < onChainBalance -> {
				val tokenPrice = getTokenPrices.getSingle(command.tokenAddress.toChainAddress(wallet.chain))

				val baseAmountDifference = onChainBalance - platformBalance
				val baseTokenAmount = baseAmountDifference * tokenPrice.amount

				val nativeAmount = baseTokenAmount / BigInteger.TEN.pow(tokenDecimals.toInt())

				trackingTransactionCreateService.create(
					walletId = marketPosition.walletId,
					chain = wallet.chain,
					transactionType = TransactionType.BUY,
					tokenAddress = marketPosition.tokenAddress,
					tokenAmount = baseAmountDifference,
					baseValue = nativeAmount,
				)

				marketPosition.newBuy(
					amountOfTokensReceived = baseAmountDifference,
					amountOfBaseCurrencyPaid = nativeAmount,
					exchangeRate = exchangeRate,
				)

				TrackTokenCommand.Result(
					TrackTokenCommand.TrackTokenResult.BALANCES_EVENED,
				)
			}

			platformBalance == onChainBalance -> TrackTokenCommand.Result(
				TrackTokenCommand.TrackTokenResult.BALANCES_ARE_EQUAL,
			)

			platformBalance > onChainBalance -> TrackTokenCommand.Result(
				TrackTokenCommand.TrackTokenResult.PLATFORM_BALANCE_HIGHER,
			)

			else -> error("Failed to determine TrackTokenCommand.Result.")
		}

		if (marketPosition.isClosed()) {
			marketPositionDeleteService.delete(marketPosition.id)
		}

		return tokenTrackingResult
	}
}
