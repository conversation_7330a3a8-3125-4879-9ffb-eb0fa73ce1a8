package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.user.command.UpdateQuickBuyAmountCommand
import com.cleevio.fatbot.application.module.user.exception.UserNotFoundException
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.user.locks.UPDATE_USER
import com.cleevio.fatbot.application.module.user.locks.USER_MODULE
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateQuickBuyAmountCommandHandler(
	private val userFinderService: FirebaseUserFinderService,
) : CommandHandler<Unit, UpdateQuickBuyAmountCommand> {
	override val command = UpdateQuickBuyAmountCommand::class

	@Transactional
	@Lock(module = USER_MODULE, lockName = UPDATE_USER)
	override fun handle(@LockFieldParameter("userId") command: UpdateQuickBuyAmountCommand) {
		val user = userFinderService.findById(command.userId) ?: throw UserNotFoundException("User not found")

		user.setQuickBuyAmount(command.quickBuyAmountUsd)
	}
}
