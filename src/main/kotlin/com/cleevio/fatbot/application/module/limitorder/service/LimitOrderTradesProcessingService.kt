package com.cleevio.fatbot.application.module.limitorder.service

import com.cleevio.fatbot.adapter.out.bitquery.TokenTradeProvider
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.collectCatching
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bottransaction.port.out.UpdateHeartBeat
import com.cleevio.fatbot.application.module.heartbeat.finder.HeartBeatFinderService
import com.cleevio.fatbot.application.module.limitorder.finder.LimitOrderFinderService
import com.cleevio.fatbot.application.module.limitorder.locks.LIMIT_ORDER_MODULE
import com.cleevio.fatbot.application.module.limitorder.locks.PROCESS_TOKEN_TRADES
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.token.event.SolanaTokenTradedEvent
import com.cleevio.fatbot.domain.heartbeat.HeartBeatCreateService
import com.cleevio.fatbot.domain.heartbeat.HeartBeatType
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.TryLock
import jakarta.annotation.PreDestroy
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.MathContext
import java.time.Clock
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentMap
import kotlin.time.Duration.Companion.seconds

private val ACTIVE_LIMIT_ORDERS_REFRESH_INTERVAL = 10.seconds

@Service
class LimitOrderTradesProcessingService(
	private val heartBeatFinderService: HeartBeatFinderService,
	private val heartBeatCreateService: HeartBeatCreateService,
	private val clock: Clock,
	private val updateHeartBeat: UpdateHeartBeat,
	private val limitOrderFinderService: LimitOrderFinderService,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	tokenTradeProvider: TokenTradeProvider,
) {

	private val logger = logger()

	private val scope = CoroutineScope(
		Dispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	private val tradesFlow = tokenTradeProvider.getTokenTradeFlow()

	private data class LimitOrderPriceTrigger(
		/**
		 * Represents the highest buy price of a Buy order.
		 * If a trade with lower price is detected, at least this limit order should match
		 */
		val maxBuyPrice: BigDecimal?,
		/**
		 * Represents the lowest buy price of a Sell order.
		 * If a trade with higher price is detected, at least this limit order should match
		 */
		val minSellPrice: BigDecimal?,
	)

	private val tokenAddressToPriceTrigger: ConcurrentMap<AddressWrapper, LimitOrderPriceTrigger> = ConcurrentHashMap()

	@Transactional
	@TryLock(module = LIMIT_ORDER_MODULE, lockName = PROCESS_TOKEN_TRADES)
	fun tryStartTradesProcessing() {
		val heartBeat = heartBeatFinderService.findByType(type = HeartBeatType.LIMIT_ORDER)
			?: heartBeatCreateService.create(type = HeartBeatType.LIMIT_ORDER)

		// If it's active, early return
		val now = Instant.now(clock)
		if (heartBeat.isActive(now = now)) return

		heartBeat.beat(now = now)

		scope.launch {
			launch { heartBeat() }
			launch { processTradeEvents() }
		}

		logger.info("Started limit order trades processing")
	}

	@PreDestroy
	fun onStop() {
		scope.cancel()
	}

	suspend fun heartBeat() {
		while (true) {
			val now = Instant.now(clock)
			updateHeartBeat(now = now, type = HeartBeatType.LIMIT_ORDER)

			// Reset existing limit orders
			val allLimitOrders = withContext(Dispatchers.IO) {
				limitOrderFinderService.findAll()
			}

			// TODO: Move the query to jooq implementation
			val tokenToMinLimitPrice = allLimitOrders
				.groupBy({ it.tokenAddress }, { it.type to it.limitPrice.asBaseAmount().toNative(it.chain) })
				.mapValues { (_, prices) ->
					val maxBuyPrice =
						prices.filter { (type) -> type == LimitOrderType.BUY }.maxOfOrNull { (_, price) -> price.amount }
					val minSellPrice =
						prices.filter { (type) -> type == LimitOrderType.SELL }.minOfOrNull { (_, price) -> price.amount }

					LimitOrderPriceTrigger(
						maxBuyPrice = maxBuyPrice,
						minSellPrice = minSellPrice,
					)
				}

			tokenAddressToPriceTrigger.clear()
			tokenAddressToPriceTrigger.putAll(tokenToMinLimitPrice)

			// How many seconds do we want for limit orders? Bot transaction processing has 1s interval
			delay(ACTIVE_LIMIT_ORDERS_REFRESH_INTERVAL)
		}
	}

	private suspend fun processTradeEvents() {
		tradesFlow.collectCatching(
			exceptionHandler = { logger.error("Error in process trades events for limit orders", it) },
			collector = { trades ->
				val relevantTrades = trades
					.filterIsInstance<SolanaTokenTradedEvent>()
					.filter { trade -> trade.tokenAddress in tokenAddressToPriceTrigger }

				// TODO: Receive native price from stream
				val exchangeRate = getUsdExchangeRate(CryptoCurrency.SOL)
				val latestRelevantTrades = relevantTrades
					.asReversed()
					.distinctBy { trade -> trade.tokenAddress }
					.associate { trade -> trade.tokenAddress to trade.priceUsd.divide(exchangeRate, MathContext.DECIMAL64) }
					.filter { (token, price) ->
						val limitPrice = tokenAddressToPriceTrigger[token]!!
						val minSellPrice = limitPrice.minSellPrice
						val maxBuyPrice = limitPrice.maxBuyPrice

						minSellPrice.notNullAnd { it < price } || maxBuyPrice.notNullAnd { it > price }
					}

				if (latestRelevantTrades.isEmpty()) return@collectCatching

				// TODO: Submit token price updates to matching service
				// limitOrderMatchingService.submitTokenPriceUpdate(latestRelevantTrades)
			},
		)
	}

	private fun <T> T?.notNullAnd(predicate: (T) -> Boolean) = this != null && predicate(this)
}
