package com.cleevio.fatbot.application.module.market.scheduled

import com.cleevio.fatbot.application.module.market.service.MarketPositionSnapshotService
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class CreateMarketPositionSnapshotsTrigger(
	private val marketPositionSnapshotService: MarketPositionSnapshotService,
) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.create-market-position-snapshots")
	@Scheduled(cron = "\${fatbot.market-position.create-snapshot.cron}")
	@SchedulerLock(
		name = "CREATE_MARKET_POSITION_SNAPSHOT",
		lockAtMostFor = "\${fatbot.market-position.create-snapshot.lock-for}",
		lockAtLeastFor = "\${fatbot.market-position.create-snapshot.lock-for}",
	)
	fun trigger() {
		logger.info("CREATE_MARKET_POSITION_SNAPSHOT cron started")
		marketPositionSnapshotService.createNewSnapshotsOfMarketPositionTableAndDeleteOldSnapshots()
		logger.info("CREATE_MARKET_POSITION_SNAPSHOT cron ended")
	}
}
