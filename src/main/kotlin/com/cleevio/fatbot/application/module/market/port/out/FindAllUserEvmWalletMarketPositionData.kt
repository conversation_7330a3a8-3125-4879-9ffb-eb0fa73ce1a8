package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.adapter.out.jooq.FindAllUserEvmWalletMarketPositionDataJooq
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.util.UUID

interface FindAllUserEvmWalletMarketPositionData {
	operator fun invoke(
		chain: Chain,
		userId: UUID,
		tokenAddress: AddressWrapper,
		fileToUrlMapper: FileToUrlMapper,
	): List<FindAllUserEvmWalletMarketPositionDataJooq.Result>
}
