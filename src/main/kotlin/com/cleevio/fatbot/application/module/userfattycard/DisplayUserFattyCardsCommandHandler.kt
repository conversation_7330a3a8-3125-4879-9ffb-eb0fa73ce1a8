package com.cleevio.fatbot.application.module.userfattycard

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.userfattycard.command.DisplayUserFattyCardsCommand
import com.cleevio.fatbot.application.module.userfattycard.finder.UserFattyCardFinderService
import com.cleevio.fatbot.application.module.userfattycard.locks.CREATE_OR_UPDATE_USER_FATTY_CARDS
import com.cleevio.fatbot.application.module.userfattycard.locks.USER_FATTY_CARD_MODULE
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class DisplayUserFattyCardsCommandHandler(
	private val userFattyCardFinderService: UserFattyCardFinderService,
) : CommandHandler<Unit, DisplayUserFattyCardsCommand> {

	override val command = DisplayUserFattyCardsCommand::class

	@Transactional
	@Lock(module = USER_FATTY_CARD_MODULE, lockName = CREATE_OR_UPDATE_USER_FATTY_CARDS)
	override fun handle(@LockFieldParameter("userId") command: DisplayUserFattyCardsCommand) {
		userFattyCardFinderService.findAllByIdInAndUserId(
			ids = command.userFattyCardIds,
			userId = command.userId,
		).forEach {
			it.display()
		}
	}
}
