package com.cleevio.fatbot.application.module.wallet.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class WalletNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.WALLET_NOT_FOUND,
	message = message,
)

@ResponseStatus(HttpStatus.CONFLICT)
class MaxWalletCountExceededException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.MAX_WALLET_COUNT_EXCEEDED,
	message = message,
)

@ResponseStatus(HttpStatus.CONFLICT)
class WalletAlreadyImportedException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.WALLET_ALREADY_IMPORTED,
	message = message,
)

@ResponseStatus(HttpStatus.CONFLICT)
class LastWalletDeletionException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.LAST_WALLET_DELETION_EXCEPTION,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class FailedToGetAddressFromPrivateKeyException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.FAILED_TO_GET_ADDRESS_FROM_PRIVATE_KEY,
	message = message,
)

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
class FailedToGetWalletBalanceException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.FAILED_TO_GET_WALLET_BALANCE,
	message = message,
)

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
class FailedToGetTokenBalanceException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.FAILED_TO_GET_TOKEN_BALANCE,
	message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class InsufficientFundsForTransactionFeesException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.INSUFFICIENT_FUNDS_FOR_TRANSACTION_FEES,
	message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class InsufficientFundsForTransactionException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.INSUFFICIENT_FUNDS_FOR_TRANSACTION,
	message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class TransactionLikelyFailOnBlockchainException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.TRANSACTION_LIKELY_FAIL,
	message = message,
)
