package com.cleevio.fatbot.application.module.bot.scheduled

import com.cleevio.fatbot.application.module.bot.service.ResetRemainingBotBuyFrequencyService
import com.cleevio.fatbot.infrastructure.config.logger
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class ResetRemainingBotBuyFrequencyTrigger(
	private val resetRemainingBotBuyFrequencyService: ResetRemainingBotBuyFrequencyService,
) {

	private val logger = logger()

	@Scheduled(cron = "\${fatbot.bot.reset-remaining-buy-frequency.cron}")
	@SchedulerLock(
		name = "RESET_REMAINING_BOT_BUY_FREQUENCY",
		lockAtMostFor = "\${fatbot.bot.reset-remaining-buy-frequency.lock-for}",
		lockAtLeastFor = "\${fatbot.bot.reset-remaining-buy-frequency.lock-for}",
	)
	fun trigger() {
		logger.info("RESET_REMAINING_BOT_BUY_FREQUENCY cron started")
		resetRemainingBotBuyFrequencyService()
		logger.info("RESET_REMAINING_BOT_BUY_FREQUENCY cron ended")
	}
}
