package com.cleevio.fatbot.application.module.bot.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.util.UUID

interface FindAllBotWallet {
	operator fun invoke(botIds: Set<UUID>): List<Result>

	data class Result(
		val botId: UUID,
		val botWalletId: UUID,
		val balance: BaseAmount,
		val chain: Chain,
	)
}
