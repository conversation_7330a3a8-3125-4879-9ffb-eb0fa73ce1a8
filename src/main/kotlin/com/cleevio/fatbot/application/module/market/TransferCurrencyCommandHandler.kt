package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.asLamport
import com.cleevio.fatbot.application.common.crypto.extractFirstSignatureAsTxHash
import com.cleevio.fatbot.application.common.crypto.hashToTxHash
import com.cleevio.fatbot.application.common.crypto.minus
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.service.PasswordValidationService
import com.cleevio.fatbot.application.common.service.command.VerifyPasswordCommand
import com.cleevio.fatbot.application.module.bot.port.out.TransferSol
import com.cleevio.fatbot.application.module.market.command.TransferCurrencyCommand
import com.cleevio.fatbot.application.module.market.port.out.EstimateGas
import com.cleevio.fatbot.application.module.market.port.out.TransferEth
import com.cleevio.fatbot.application.module.market.port.out.ValidateDestinationWalletFundsForRent
import com.cleevio.fatbot.application.module.market.port.out.ValidateFundsForEvmTransaction
import com.cleevio.fatbot.application.module.market.port.out.ValidateFundsForSvmTransaction
import com.cleevio.fatbot.application.module.market.service.GasLimitEstimationService
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.event.EthTransferredEvent
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.application.module.wallet.service.WalletBalanceService
import com.cleevio.fatbot.domain.wallet.Wallet
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class TransferCurrencyCommandHandler(
	private val walletFinderService: WalletFinderService,
	private val validateFundsForEvmTransaction: ValidateFundsForEvmTransaction,
	private val validateFundsForSvmTransaction: ValidateFundsForSvmTransaction,
	private val validateDestinationWalletFundsForRent: ValidateDestinationWalletFundsForRent,
	private val transferEth: TransferEth,
	private val transferSol: TransferSol,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val gasLimitEstimationService: GasLimitEstimationService,
	private val walletBalanceService: WalletBalanceService,
	private val passwordValidationService: PasswordValidationService,
) : CommandHandler<TransferCurrencyCommand.Result, TransferCurrencyCommand> {

	override val command = TransferCurrencyCommand::class

	override fun handle(command: TransferCurrencyCommand): TransferCurrencyCommand.Result = with(command) {
		passwordValidationService(VerifyPasswordCommand(userId, password))

		val wallet = walletFinderService.getByIdAndUserId(id = command.walletId, userId = command.userId)

		val result = when (wallet.chain.type) {
			ChainType.EVM -> handleEthTransfer(wallet)
			ChainType.SOLANA -> handleSolanaTransfer(wallet)
		}

		applicationEventPublisher.publishEvent(
			EthTransferredEvent(
				walletId = wallet.id,
				signedTxList = listOf(result),
			),
		)

		return when (wallet.chain.type) {
			ChainType.EVM -> TransferCurrencyCommand.Result(txHash = result.hashToTxHash())
			ChainType.SOLANA -> TransferCurrencyCommand.Result(txHash = result.extractFirstSignatureAsTxHash())
		}
	}

	private fun TransferCurrencyCommand.handleEthTransfer(wallet: Wallet): SignedTx {
		val estimateGasResult = gasLimitEstimationService.estimateGas(
			transactionType = TransactionType.TRANSFER_CURRENCY,
			chain = wallet.chain,
			userId = userId,
			walletId = wallet.id,
			baseAmount = toTransferNativeAmount.toBase(wallet.chain),
			recipientAddress = destinationWalletAddress,
		)

		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultEvm) error("Estimate gas must be of EVM type")

		val validateResult = validateFundsForEvmTransaction(
			walletAddress = wallet.address,
			tokenAddress = null,
			chainId = wallet.chain.id!!,
			transactionType = TransactionType.TRANSFER_CURRENCY,
			transactionAmount = toTransferNativeAmount.toBase(wallet.chain),
			gasInfo = estimateGasResult.gasInfo,
			gasLimitEstimate = estimateGasResult.gasLimitEstimate,
		)

		return transferEth(
			destinationWalletAddress = destinationWalletAddress,
			privateKey = wallet.privateKey,
			chainId = wallet.chain.id,
			amountToTransfer = toTransferNativeAmount.toBase(wallet.chain),
			gasInfo = estimateGasResult.gasInfo,
			gasLimit = validateResult.gasLimitEstimate,
		)
	}

	private fun TransferCurrencyCommand.handleSolanaTransfer(wallet: Wallet): SignedTx {
		val estimateGasResult = gasLimitEstimationService.estimateGas(
			transactionType = TransactionType.TRANSFER_CURRENCY,
			chain = wallet.chain,
			userId = userId,
			walletId = wallet.id,
			baseAmount = toTransferNativeAmount.toBase(wallet.chain),
			recipientAddress = destinationWalletAddress,
		)

		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultSolana) error("Estimate gas must be of EVM type")

		val toTransferBaseAmount = toTransferNativeAmount.toBase(wallet.chain)
		// TODO: This is not ideal because same call is done in validateFundsForSvmTransaction
		//  but for now it works
		val walletBalance = walletBalanceService.getBalance(wallet.address, wallet.chain)

		val balanceAfter = walletBalance - toTransferBaseAmount
		// Handle special case when we are trying to send everything from the wallet
		val toTransferSafeBaseAmount = when (balanceAfter.amount) {
			BigInteger.ZERO -> toTransferBaseAmount - estimateGasResult.feeBaseAmount
			else -> toTransferBaseAmount
		}

		validateFundsForSvmTransaction(
			walletId = wallet.id,
			walletAddress = wallet.address,
			transactionAmount = toTransferSafeBaseAmount,
			estimatedFees = estimateGasResult.feeBaseAmount,
		)

		validateDestinationWalletFundsForRent(
			destinationAddress = destinationWalletAddress,
			toTransferBaseAmount = toTransferSafeBaseAmount,
		)

		return transferSol(
			privateKey = wallet.privateKey,
			amount = toTransferSafeBaseAmount.amount.asLamport(),
			destinationWalletAddress = destinationWalletAddress,
		)
	}
}
