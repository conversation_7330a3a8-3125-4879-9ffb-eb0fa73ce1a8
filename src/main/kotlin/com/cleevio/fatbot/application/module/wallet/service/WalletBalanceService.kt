package com.cleevio.fatbot.application.module.wallet.service

import com.cleevio.fatbot.adapter.out.evm.GetWalletBalanceEvm
import com.cleevio.fatbot.adapter.out.solana.GetWalletBalanceSolana
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.infrastructure.coroutines.createSupervisorJobScope
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import org.springframework.stereotype.Service

@Service
class WalletBalanceService(
	private val getWalletBalanceEvm: GetWalletBalanceEvm,
	private val getWalletBalanceSolana: GetWalletBalanceSolana,
) {

	fun getBalance(address: AddressWrapper, chain: Chain) = when (chain.type) {
		ChainType.EVM -> getWalletBalanceEvm(address, chain.evmId)
		ChainType.SOLANA -> getWalletBalanceSolana(address)
	}

	fun getBalances(wallets: List<ChainAddress>): Map<ChainAddress, BaseAmount> {
		val scope = createSupervisorJobScope()

		val chainToAddress = wallets.groupBy({ it.chain }, { it.address })
		val resultsDeferred = chainToAddress.map { (chain, addresses) ->
			scope.async {
				val addressToBalance = getBalances(addresses = addresses, chain = chain)

				addressToBalance.mapKeys { (address) -> address.toChainAddress(chain) }
			}
		}

		val results = runBlocking { resultsDeferred.awaitAll() }

		return results.reduceOrNull { acc, result -> acc + result } ?: emptyMap()
	}

	private fun getBalances(addresses: List<AddressWrapper>, chain: Chain) = when (chain.type) {
		ChainType.EVM -> getWalletBalanceEvm.getMany(addresses, chain.evmId)
		ChainType.SOLANA -> getWalletBalanceSolana.getMany(addresses)
	}
}
