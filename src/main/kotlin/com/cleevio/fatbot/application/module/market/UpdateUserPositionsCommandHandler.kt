package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.service.TransactionalService
import com.cleevio.fatbot.application.module.botwallet.command.SyncUserBotWalletBalancesCommand
import com.cleevio.fatbot.application.module.market.command.UpdateUserPositionsCommand
import com.cleevio.fatbot.application.module.market.finder.MarketPositionRefreshFinderService
import com.cleevio.fatbot.application.module.token.service.TokenTrackingService
import com.cleevio.fatbot.application.module.walletposition.command.SyncUserWalletCurrencyPositionsCommand
import com.cleevio.fatbot.domain.market.MarketPositionRefreshCreateService
import com.cleevio.library.lockinghandler.service.AdvisoryLockRepository
import com.cleevio.library.lockinghandler.service.AdvisoryLockService
import com.cleevio.library.lockinghandler.service.LockService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import java.time.Clock
import java.time.Instant
import java.util.UUID
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible

@Component
class UpdateUserPositionsCommandHandler(
	private val tokenTrackingService: TokenTrackingService,
	private val transactionalService: TransactionalService,
	private val lockService: LockService,
	private val marketPositionRefreshFinderService: MarketPositionRefreshFinderService,
	private val marketPositionRefreshCreateService: MarketPositionRefreshCreateService,
	private val clock: Clock,
) : CommandHandler<Unit, UpdateUserPositionsCommand> {

	override val command = UpdateUserPositionsCommand::class

	@Lazy
	@Autowired
	private lateinit var commandBus: CommandBus

	// TODO: Remove 'Market' from name
	override fun handle(command: UpdateUserPositionsCommand) {
		val staleState = getUserPositionsStaleState(userId = command.userId)

		if (staleState.isWalletStale) {
			commandBus(SyncUserWalletCurrencyPositionsCommand(userId = command.userId))
			commandBus(SyncUserBotWalletBalancesCommand(userId = command.userId, botId = null))
		}

		if (staleState.isMarketStale) {
			tokenTrackingService.trackTokensForUser(command.userId)
		}
	}

	private fun getUserPositionsStaleState(userId: UUID): StaleState {
		return transactionalService.inTransaction {
			when (tryObtainLock(userId)) {
				true -> getStaleState(userId)

				// someone else holds the lock, so no need to do update
				false -> StaleState(isWalletStale = false, isMarketStale = false)
			}
		}
	}

	private fun tryObtainLock(userId: UUID): Boolean {
		val lockName = "$UPDATE_USER_POSITIONS_LOCK/$userId"
		return getAdvisoryLockRepositoryViaReflection().tryLockExclusively(lockName)
	}

	// Due to changes in locking library, advisory lock repository is no longer allowed to be Autowired
	// so we have to hack it this way
	private fun getAdvisoryLockRepositoryViaReflection(): AdvisoryLockRepository {
		return (lockService as AdvisoryLockService)
			.getPrivateProperty<AdvisoryLockService, AdvisoryLockRepository>("advisoryLockRepository")
	}

	data class StaleState(
		val isMarketStale: Boolean,
		val isWalletStale: Boolean,
	)

	private fun getStaleState(userId: UUID): StaleState {
		val marketPositionRefresh = getUserMarketPositionRefresh(userId = userId)
		val now = Instant.now(clock)

		val isWalletStale = marketPositionRefresh.isWalletStale(now = now)
		val isMarketStale = marketPositionRefresh.isMarketStale(now = now)

		if (isMarketStale) {
			// at this point we know data update is needed, we mark it on entity,
			// so next read will consider data fresh
			marketPositionRefresh.marketViewed(now)
		}

		if (isWalletStale) {
			// at this point we know data update is needed, we mark it on entity,
			// so next read will consider data fresh
			marketPositionRefresh.walletViewed(now)
		}

		return StaleState(
			isMarketStale = isMarketStale,
			isWalletStale = isWalletStale,
		)
	}

	private fun getUserMarketPositionRefresh(userId: UUID) =
		marketPositionRefreshFinderService.findByUserId(userId = userId)
			// we set epoch (1970-01-01) on new entity, so it will be definitely stale
			?: marketPositionRefreshCreateService.create(userId = userId, initialLastViewedAt = Instant.EPOCH)
}

private inline fun <reified T : Any, R> T.getPrivateProperty(name: String): R = T::class
	.memberProperties
	.firstOrNull { it.name == name }
	?.apply { isAccessible = true }
	?.get(this) as R

private val UPDATE_USER_POSITIONS_LOCK = "UPDATE_USER_POSITIONS"
