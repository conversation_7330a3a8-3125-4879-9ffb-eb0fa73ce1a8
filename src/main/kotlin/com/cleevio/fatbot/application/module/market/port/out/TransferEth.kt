package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.adapter.out.evm.model.GasInfo
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import java.math.BigInteger

interface TransferEth {
	operator fun invoke(
		destinationWalletAddress: AddressWrapper,
		privateKey: String,
		chainId: Long,
		amountToTransfer: BaseAmount,
		gasInfo: GasInfo,
		gasLimit: BigInteger?,
	): SignedTx
}
