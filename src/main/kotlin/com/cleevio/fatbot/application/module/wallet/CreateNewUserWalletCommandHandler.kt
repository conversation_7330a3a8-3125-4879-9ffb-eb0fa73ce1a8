package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.WalletGenerator
import com.cleevio.fatbot.application.module.wallet.command.CreateNewUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.application.module.wallet.locks.CREATE_OR_UPDATE_USER_WALLETS
import com.cleevio.fatbot.application.module.wallet.locks.WALLET_MODULE
import com.cleevio.fatbot.application.module.wallet.util.validateWalletCount
import com.cleevio.fatbot.domain.wallet.WalletCreateService
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class CreateNewUserWalletCommandHandler(
	private val walletFinderService: WalletFinderService,
	private val walletCreateService: WalletCreateService,
) : CommandHandler<CreateNewUserWalletCommand.Result, CreateNewUserWalletCommand> {

	override val command = CreateNewUserWalletCommand::class

	private val logger = logger()

	@Transactional
	@Lock(module = WALLET_MODULE, lockName = CREATE_OR_UPDATE_USER_WALLETS)
	override fun handle(
		@LockFieldParameter("userId") command: CreateNewUserWalletCommand,
	): CreateNewUserWalletCommand.Result {
		val walletCount = walletFinderService.countAllByUserIdAndChain(
			userId = command.userId,
			chain = command.chain,
		)
		validateWalletCount(walletCount = walletCount, chain = command.chain)

		val wallet = WalletGenerator.createWallet(chainType = command.chain.type)

		val createdWallet = walletCreateService.create(
			userId = command.userId,
			addressWrapper = wallet.address,
			chain = command.chain,
			privateKey = wallet.privateKey,
			walletCount = walletCount,
		)

		logger.info("User ${command.userId} created new wallet ${wallet.address.getAddressString()}.")

		return CreateNewUserWalletCommand.Result(
			walletId = createdWallet.id,
			walletAddress = createdWallet.address,
			privateKey = createdWallet.privateKey,
		)
	}
}
