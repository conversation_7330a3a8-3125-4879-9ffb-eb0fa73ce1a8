package com.cleevio.fatbot.application.module.user.query

import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import java.math.BigDecimal
import java.util.UUID

data class GetUserQuery(val userId: UUID) : Query<GetUserQuery.Result> {

	@Schema(name = "GetUserResult")
	data class Result(
		val userId: UUID,
		val email: String,
		val referralCode: String?,
		val quickBuyAmountUsd: BigDecimal,
		val isReferred: Boolean,
		val selectedChains: Set<Chain>,
	)
}

data class IsUniqueReferralCodeQuery(
	@field:NotBlank val referralCode: String,
) : Query<IsUniqueReferralCodeQuery.Result> {

	@Schema(name = "CheckReferralCodeResult")
	data class Result(
		val isUnique: Boolean,
	)
}

class GetEnabledChainsQuery : Query<GetEnabledChainsQuery.Result> {

	@Schema(name = "GetEnabledChainsResult")
	data class Result(
		val enabledChains: Set<Chain>,
	)
}
