package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.WalletGenerator
import com.cleevio.fatbot.application.module.bot.command.CreateBotCommand
import com.cleevio.fatbot.application.module.bot.exception.MaxActiveBotsExceeded
import com.cleevio.fatbot.application.module.bot.locks.BOT_MODULE
import com.cleevio.fatbot.application.module.bot.locks.CREATE_BOT
import com.cleevio.fatbot.application.module.botdraft.BotDraftService
import com.cleevio.fatbot.application.module.botdraft.finder.BotDraftFinderService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.domain.bot.BotCreateService
import com.cleevio.fatbot.domain.bot.MAX_ACTIVE_BOTS_PER_USER
import com.cleevio.fatbot.domain.botwallet.BotWalletCreateService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class CreateBotCommandHandler(
	private val botDraftFinderService: BotDraftFinderService,
	private val botDraftService: BotDraftService,
	private val botCreateService: BotCreateService,
	private val botFinderService: BotFinderService,
	private val botWalletCreateService: BotWalletCreateService,
) : CommandHandler<CreateBotCommand.Result, CreateBotCommand> {

	override val command = CreateBotCommand::class

	@SentrySpan
	@Transactional
	@Lock(module = BOT_MODULE, lockName = CREATE_BOT)
	override fun handle(@LockFieldParameter("userId") command: CreateBotCommand): CreateBotCommand.Result {
		val activeBots = botFinderService.findAllByUserIdAndIsActiveTrue(command.userId)
		if (activeBots.size >= MAX_ACTIVE_BOTS_PER_USER) {
			throw MaxActiveBotsExceeded(
				"User ${command.userId} can have maximum of $MAX_ACTIVE_BOTS_PER_USER active bots",
			)
		}

		val botDraft = botDraftFinderService.getByIdAndUserId(command.botDraftId, command.userId)

		val newBot = botCreateService.create(
			isActive = true,
			userId = botDraft.userId,
			name = botDraft.name.requiredNotNull(propertyName = "name"),
			avatarFileId = botDraft.avatarFileId.requiredNotNull(propertyName = "avatarFileId"),
			tradeAmount = botDraft.tradeAmount.requiredNotNull(propertyName = "tradeAmount"),
			buyFrequency = botDraft.buyFrequency.requiredNotNull(propertyName = "buyFrequency"),
			profitTargetFraction = botDraft.profitTargetFraction.requiredNotNull(propertyName = "profitTargetFraction"),
			stopLossFraction = botDraft.stopLossFraction.requiredNotNull(propertyName = "stopLossFraction"),
			marketCapFromUsd = botDraft.marketCapFromUsd,
			marketCapToUsd = botDraft.marketCapToUsd,
			liquidityFromUsd = botDraft.liquidityFromUsd,
			liquidityToUsd = botDraft.liquidityToUsd,
			dailyVolumeFromUsd = botDraft.dailyVolumeFromUsd,
			dailyVolumeToUsd = botDraft.dailyVolumeToUsd,
			numberOfHoldersFrom = botDraft.numberOfHoldersFrom,
			numberOfHoldersTo = botDraft.numberOfHoldersTo,
			buyVolume = botDraft.buyVolume,
			sellVolume = botDraft.sellVolume,
			sellTransactionFraction = botDraft.sellTransactionFraction,
			buyTransactionFraction = botDraft.buyTransactionFraction,
		)

		// Only SOLANA bots are supported for now
		val generatedWallet = WalletGenerator.createWallet(chainType = ChainType.SOLANA)

		botWalletCreateService.create(
			botId = newBot.id,
			address = generatedWallet.address,
			privateKey = generatedWallet.privateKey,
			chain = Chain.SOLANA,
		)

		botDraftService.deleteById(command.botDraftId)

		return CreateBotCommand.Result(botId = newBot.id)
	}

	private fun <T> T?.requiredNotNull(propertyName: String): T {
		requireNotNull(this) { "$propertyName is null on BotDraft and is required for Bot creation" }

		return this
	}
}
