package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.adapter.out.evm.SellTokenEVM
import com.cleevio.fatbot.adapter.out.evm.model.GasInfo
import com.cleevio.fatbot.adapter.out.evm.simulation.SetTokensApprovalEVM
import com.cleevio.fatbot.adapter.out.solana.SellTokenSolana
import com.cleevio.fatbot.adapter.out.solana.pumpfun.GetPumpfunCurveState
import com.cleevio.fatbot.adapter.out.solana.pumpswap.GetPumpswapPool
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.extractFirstSignatureAsTxHash
import com.cleevio.fatbot.application.common.crypto.hashToTxHash
import com.cleevio.fatbot.application.common.crypto.minusBasisPoints
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.util.runWithRetry
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.module.gassnapshot.finder.GasInfoSnapshotFinderService
import com.cleevio.fatbot.application.module.market.command.SellTokenCommand
import com.cleevio.fatbot.application.module.market.exception.InvalidDexPairProvidedException
import com.cleevio.fatbot.application.module.market.port.out.EstimateGas
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutUniswap
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.port.out.GetTransactionReceipt
import com.cleevio.fatbot.application.module.market.port.out.ValidateFundsForEvmTransaction
import com.cleevio.fatbot.application.module.market.port.out.ValidateFundsForSvmTransaction
import com.cleevio.fatbot.application.module.market.port.out.ValidateTokenBalance
import com.cleevio.fatbot.application.module.market.port.out.toDexPairInfoInput
import com.cleevio.fatbot.application.module.market.service.GasLimitEstimationService
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.token.finder.TokenPairInfoFinderService
import com.cleevio.fatbot.application.module.token.query.GetTokenTaxQuery
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.event.TokenSoldEvent
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.exception.TransactionLikelyFailOnBlockchainException
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.domain.wallet.Wallet
import com.cleevio.fatbot.infrastructure.coroutines.createSupervisorJobScope
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.BigInteger
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.toJavaDuration

private const val MAX_APPROVE_TX_RETRY_COUNT = 15

@Component
class SellTokenCommandHandler(
	private val walletFinderService: WalletFinderService,
	private val evmTokenInfoFinderService: EvmTokenInfoFinderService,
	private val sellTokenEvm: SellTokenEVM,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val validateFundsForEvmTransaction: ValidateFundsForEvmTransaction,
	private val validateFundsForSvmTransaction: ValidateFundsForSvmTransaction,
	private val gasLimitEstimationService: GasLimitEstimationService,
	private val pairInfoFinderService: TokenPairInfoFinderService,
	private val sellTokenSolana: SellTokenSolana,
	private val validateTokenBalance: ValidateTokenBalance,
	private val getAmountOutUniswap: GetAmountOutUniswap,
	private val setTokensApprovalEVM: SetTokensApprovalEVM,
	private val gasInfoSnapshotFinderService: GasInfoSnapshotFinderService,
	private val getTransactionReceipt: GetTransactionReceipt,
	private val getPumpswapPool: GetPumpswapPool,
	private val getPumpfunCurveState: GetPumpfunCurveState,
) : CommandHandler<SellTokenCommand.Result, SellTokenCommand> {

	override val command = SellTokenCommand::class

	@Lazy
	@Autowired
	private lateinit var queryBus: QueryBus

	override fun handle(command: SellTokenCommand): SellTokenCommand.Result = with(command) {
		val wallet = walletFinderService.getByIdAndUserId(id = command.walletId, userId = command.userId)

		val result = when (wallet.chain.type) {
			ChainType.EVM -> handleEvmSell(wallet)
			ChainType.SOLANA -> handleSolanaSell(wallet)
		}

		applicationEventPublisher.publishEvent(
			TokenSoldEvent(
				walletId = wallet.id,
				signedTxList = result,
				tokenAddress = command.tokenAddress,
			),
		)

		return SellTokenCommand.Result(
			txHashes = result.map {
				when (wallet.chain.type) {
					ChainType.EVM -> it.hashToTxHash()
					ChainType.SOLANA -> it.extractFirstSignatureAsTxHash()
				}
			},
		)
	}

	private fun SellTokenCommand.handleEvmSell(wallet: Wallet): List<SignedTx> {
		if (dexPairInfo !is GetDex.Evm) {
			throw InvalidDexPairProvidedException("Non-EVM $dexPairInfo provided!")
		}

		val scope = createSupervisorJobScope()
		val tokenTaxDeferred = scope.async {
			queryBus(
				GetTokenTaxQuery(
					tokenAddress = tokenAddress,
					chain = wallet.chain,
					dexPairInfo = dexPairInfo.toDexPairInfoInput(),
				),
			)
		}

		val tokenDecimals = evmTokenInfoFinderService.getByTokenAddressAndChain(tokenAddress, wallet.chain).decimals.toInt()
		val toSellBaseAmount = toSellNativeAmount.toBase(tokenDecimals)

		val gasInfoHint = gasInfoSnapshotFinderService.findRecentByChainId(wallet.chain.evmId)
			?.let { GasInfo.fromSnapshot(it) }

		val approvalSignedTx = setTokensApprovalEVM(
			tokenAddress = tokenAddress,
			minimumTokenAllowance = toSellBaseAmount,
			privateKey = wallet.privateKey,
			chainId = wallet.chain.evmId,
			gasInfoHint = gasInfoHint,
		)

		if (approvalSignedTx != null) {
			awaitApprovalTransaction(approvalSignedTx, wallet.chain)
		}

		val estimateGasResult = gasLimitEstimationService.estimateGas(
			transactionType = TransactionType.SELL,
			chain = wallet.chain,
			userId = userId,
			walletId = wallet.id,
			tokenAddress = tokenAddress,
			baseAmount = toSellBaseAmount,
			dexPairInfo = dexPairInfo,
			recipientAddress = wallet.address,
		)

		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultEvm) error("Estimate gas must be of EVM type")

		val validateResult = validateFundsForEvmTransaction(
			walletAddress = wallet.address,
			tokenAddress = tokenAddress,
			chainId = wallet.chain.evmId,
			transactionType = TransactionType.SELL,
			transactionAmount = toSellBaseAmount,
			gasInfo = estimateGasResult.gasInfo,
			gasLimitEstimate = estimateGasResult.gasLimitEstimate,
		)

		val expectedAmountOut = when (dexPairInfo) {
			is GetDex.UniswapV2, is GetDex.PancakeswapV2 -> {
				getAmountOutUniswap.getAmountOutV2(
					chainId = wallet.chain.evmId,
					tokenAddress = tokenAddress,
					amountIn = toSellBaseAmount,
					isBuy = false,
				)
			}
			is GetDex.PancakeswapV3 -> getAmountOutUniswap.getAmountOutV3(
				chainId = wallet.chain.evmId,
				tokenAddress = tokenAddress,
				amountIn = toSellBaseAmount,
				fee = dexPairInfo.fee,
				isBuy = false,
			)

			is GetDex.UniswapV3 -> getAmountOutUniswap.getAmountOutV3(
				chainId = wallet.chain.evmId,
				tokenAddress = tokenAddress,
				amountIn = toSellBaseAmount,
				fee = dexPairInfo.fee,
				isBuy = false,
			)
		}

		val gasInfo = when (wallet.chain) {
			// In case of arbitrum it is safer to refetch the gasInfo right before the call
			Chain.EVM_ARBITRUM_ONE -> null
			else -> estimateGasResult.gasInfo
		}

		val sellTax = runBlocking { tokenTaxDeferred.await() }.sellTax ?: BigDecimal.ZERO
		val sellTaxBp = BasisPoint.ofFraction(sellTax)

		val expectedAmountOutAfterTax = expectedAmountOut.minusBasisPoints(sellTaxBp)

		val sellSignedTx = sellTokenEvm(
			SellTokenEVM.EvmInput(
				userId = userId,
				tokenAddress = tokenAddress,
				privateKey = wallet.privateKey,
				tokenChainId = wallet.chain.evmId,
				amountToSell = toSellBaseAmount,
				expectedAmountOut = expectedAmountOutAfterTax,
				gasInfoHint = gasInfo,
				dexPairInfo = dexPairInfo,
				gasLimit = validateResult.gasLimitEstimate,
			),
		)

		return listOfNotNull(approvalSignedTx, sellSignedTx)
	}

	private fun awaitApprovalTransaction(signedTx: SignedTx, chain: Chain) {
		val txHash = signedTx.hashToTxHash()

		val approveTxReceiptResult = runWithRetry(
			retries = MAX_APPROVE_TX_RETRY_COUNT,
			retryDelay = 1000.milliseconds.toJavaDuration(),
			block = { runCatching { getTransactionReceipt(txHash, chain.evmId)!! } },
		)

		approveTxReceiptResult.fold(
			onSuccess = {
				if (!it.isStatusOK) {
					throw TransactionLikelyFailOnBlockchainException("Approve transaction failed. Please try again later.")
				}
			},
			onFailure = {
				throw TransactionLikelyFailOnBlockchainException("Failed to await approve transaction. Please try again later.")
			},
		)
	}

	private fun SellTokenCommand.handleSolanaSell(wallet: Wallet): List<SignedTx> {
		if (dexPairInfo !is GetDex.Solana) {
			throw InvalidDexPairProvidedException("Non-Solana $dexPairInfo provided!")
		}
		val tokenInfo = evmTokenInfoFinderService.getByTokenAddressAndChain(tokenAddress, wallet.chain)
		val tokenDecimals = tokenInfo.decimals.toInt()

		val toSellBaseAmount = toSellNativeAmount.toBase(tokenDecimals)

		val estimateGasResult = gasLimitEstimationService.estimateGas(
			transactionType = TransactionType.SELL,
			chain = wallet.chain,
			userId = userId,
			walletId = wallet.id,
			tokenAddress = tokenAddress,
			baseAmount = toSellBaseAmount,
			dexPairInfo = dexPairInfo,
			recipientAddress = wallet.address,
		)

		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultSolana) error("Estimate gas must be of Solana type")

		validateFundsForSvmTransaction(
			walletId = wallet.id,
			walletAddress = wallet.address,
			transactionAmount = BigInteger.ZERO.asBaseAmount(),
			estimatedFees = estimateGasResult.feeBaseAmount,
		)

		validateTokenBalance(
			walletAddress = wallet.address,
			tokenAddress = tokenAddress,
			isToken2022 = tokenInfo.isToken2022,
			tokenDecimals = tokenDecimals,
			amountToSend = toSellBaseAmount,
		)

		val amountToSell = toSellNativeAmount.toBase(tokenDecimals)
		val tokenPair by lazy { pairInfoFinderService.getByPairAddressAndChain(dexPairInfo.pairAddress, Chain.SOLANA) }

		val signedTx = sellTokenSolana(
			SellTokenSolana.SolanaInput(
				userId = wallet.userId,
				tokenAddress = tokenAddress,
				isToken2022 = tokenInfo.isToken2022,
				privateKey = wallet.privateKey,
				amountToSell = amountToSell,
				dexPairInfo = dexPairInfo,
				getRaydiumAMMMarketData = { tokenPair.raydiumAmmMarketData!! },
				getRaydiumCPMMMarketData = { tokenPair.raydiumCpmmMarketData!! },
				getPumpfunCreator = {
					tokenPair.creatorAddress ?: getPumpfunCurveState(dexPairInfo.pairAddress).creator
						?: error("Bonding curve ${dexPairInfo.pairAddress} does not provide creator address")
				},
				getPumpswapCoinCreator = { tokenPair.creatorAddress ?: getPumpswapPool(dexPairInfo.pairAddress).coinCreator },
				useStakedEndpoint = false,
				useMevProtection = wallet.sellAntiMevProtection,
			),
		)

		return listOf(signedTx)
	}
}
