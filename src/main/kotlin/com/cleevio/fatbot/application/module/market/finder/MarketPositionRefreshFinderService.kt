package com.cleevio.fatbot.application.module.market.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.domain.market.MarketPositionRefresh
import com.cleevio.fatbot.domain.market.MarketPositionRefreshRepository
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class MarketPositionRefreshFinderService(
	private val marketPositionRefreshRepository: MarketPositionRefreshRepository,
) : BaseFinderService<MarketPositionRefresh>(marketPositionRefreshRepository) {

	override fun errorBlock(message: String) = error(message)

	override fun getEntityType() = MarketPositionRefresh::class

	fun findByUserId(userId: UUID) = marketPositionRefreshRepository.findByUserId(userId)
}
