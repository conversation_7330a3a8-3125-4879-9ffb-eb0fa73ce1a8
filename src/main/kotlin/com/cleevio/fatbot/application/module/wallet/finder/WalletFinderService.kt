package com.cleevio.fatbot.application.module.wallet.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.exception.WalletNotFoundException
import com.cleevio.fatbot.domain.wallet.Wallet
import com.cleevio.fatbot.domain.wallet.WalletRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class WalletFinderService(
	private val walletRepository: WalletRepository,
) : BaseFinderService<Wallet>(walletRepository) {

	override fun errorBlock(message: String) = throw WalletNotFoundException(message)

	override fun getEntityType() = Wallet::class

	@Transactional(readOnly = true)
	fun getByIdAndUserId(id: UUID, userId: UUID): Wallet = walletRepository.findByIdAndUserId(id = id, userId = userId)
		?: errorBlock("Wallet for user $userId with id $id not found!")

	@Transactional(readOnly = true)
	fun countAllByUserIdAndChain(userId: UUID, chain: Chain) = walletRepository.countAllByUserIdAndChain(userId, chain)

	@Transactional(readOnly = true)
	fun findAllByUserIdAndChain(userId: UUID, chain: Chain) = walletRepository.findAllByUserIdAndChain(userId, chain)

	@Transactional(readOnly = true)
	fun findAllByUserId(userId: UUID) = walletRepository.findAllByUserId(userId)

	@Transactional(readOnly = true)
	fun findAllByUserIdAndChainIn(userId: UUID, chains: Set<Chain>) =
		walletRepository.findAllByUserIdAndChainIn(userId = userId, chains = chains)

	@Transactional(readOnly = true)
	fun getUserDefaultWalletOnChain(userId: UUID, chain: Chain) =
		walletRepository.findByUserIdAndChainAndIsDefault(userId, chain, isDefault = true)
			?: errorBlock("No default wallet for user $userId on chain $chain")

	fun findByWalletAddress(userId: UUID, address: AddressWrapper) = walletRepository.findByUserIdAndAddress(
		userId = userId,
		address = address,
	)
}
