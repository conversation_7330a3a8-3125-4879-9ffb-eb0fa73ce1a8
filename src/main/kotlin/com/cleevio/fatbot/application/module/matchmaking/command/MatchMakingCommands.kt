package com.cleevio.fatbot.application.module.matchmaking.command

import com.cleevio.fatbot.adapter.`in`.aggregator.dto.TokenStateChangesRequest
import com.cleevio.fatbot.adapter.out.solana.pumpfun.model.PumpFunCurveState
import com.cleevio.fatbot.application.common.command.Command
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.constant.SolanaTokenRedFlag
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID
import kotlin.time.Duration

data class TokenStateChangesCommand(
	val blockSlot: Int,
	val states: List<NewState>,
) : Command<Unit> {
	data class NewState(
		val tgeBlockSlot: Int,
		val tokenAddress: AddressWrapper,
		val name: String,
		val symbol: String,
		val infoUrl: String?,
		val marketCapUsd: BigDecimal,
		val liquidityUsd: BigDecimal,
		val numOfAccountHolders: Long,
		val volumeUsd: BigDecimal,
		val buyVolume: BigDecimal,
		val sellVolume: BigDecimal,
		val curveState: PumpFunCurveState,
		val fractionOfSellTransactions: BigDecimal,
		val creatorGraduationSuccessRateFraction: BigDecimal,
		val isRedFlagTokenTickerCopy: Boolean,
		val isRedFlagCreatorHighBuy: Boolean,
		val isRedFlagBundledBuysDetected: Boolean,
		val isRedFlagSuspiciousWalletsDetected: Boolean,
		val isRedFlagSingleHighBuy: Boolean,
	)
}

data class TokenStateChangesBuyingCommand(
	val blockSlot: Int,
	val states: List<NewState>,
) : Command<Unit> {
	data class NewState(
		val tokenAliveFor: Duration,
		val tokenAddress: AddressWrapper,
		val name: String,
		val symbol: String,
		val infoUrl: String?,
		val marketCapUsd: BigDecimal,
		val liquidityUsd: BigDecimal,
		val numOfAccountHolders: Long,
		val volumeUsd: BigDecimal,
		val buyVolume: BigDecimal,
		val sellVolume: BigDecimal,
		val curveState: PumpFunCurveState,
		val tokenPrice: BigDecimal,
		val fractionOfSellTransactions: BigDecimal,
		val creatorGraduationSuccessRateFraction: BigDecimal,
		val isRedFlagTokenTickerCopy: Boolean,
		val isRedFlagCreatorHighBuy: Boolean,
		val isRedFlagBundledBuysDetected: Boolean,
		val isRedFlagSuspiciousWalletsDetected: Boolean,
		val isRedFlagSingleHighBuy: Boolean,
	)
}

data class TokenRedFlaggedCommand(
	val blockSlot: Int,
	val tokenAddress: AddressWrapper,
	val creator: AddressWrapper,
	val type: SolanaTokenRedFlag,
	val marketCapUsd: BigDecimal,
	val liquidityUsd: BigDecimal,
	val volumeUsd: BigDecimal,
	val numOfAccountHolders: Long,
	val buyVolume: BigDecimal,
	val sellVolume: BigDecimal,
	val fractionOfSellTransactions: BigDecimal,
) : Command<Unit>

data class TokenStateChangesSellingCommand(
	val blockSlot: Int,
	val states: List<NewState>,
) : Command<Unit> {
	data class NewState(
		val tokenAddress: AddressWrapper,
		val marketCapUsd: BigDecimal,
		val liquidityUsd: BigDecimal,
		val numOfAccountHolders: Long,
		val volumeUsd: BigDecimal,
		val buyVolume: BigDecimal,
		val sellVolume: BigDecimal,
		val curveState: PumpFunCurveState,
		val tokenPrice: BigDecimal,
		val fractionOfSellTransactions: BigDecimal,
	)
}

fun TokenStateChangesRequest.toCommand() = TokenStateChangesCommand(
	blockSlot = blockSlot,
	states = states.map {
		TokenStateChangesCommand.NewState(
			tgeBlockSlot = it.tgeBlockSlot,
			tokenAddress = it.tokenAddress,
			name = it.name,
			symbol = it.symbol,
			infoUrl = it.infoUrl,
			marketCapUsd = it.marketCapUsd,
			liquidityUsd = it.liquidityUsd,
			numOfAccountHolders = it.numOfAccountHolders,
			volumeUsd = it.volumeUsd,
			buyVolume = it.buyVolume,
			sellVolume = it.sellVolume,
			fractionOfSellTransactions = it.fractionOfSellTransactions,
			creatorGraduationSuccessRateFraction = it.creatorGraduationSuccessRateFraction,
			curveState = PumpFunCurveState.fromVirtual(
				virtualSolReserves = it.solanaReserves.asNativeAmount().toBase(chain = Chain.SOLANA),
				virtualTokenReserves = it.tokenReserves.asNativeAmount().toBase(decimals = 6),
				creator = it.creator,
			),
			isRedFlagTokenTickerCopy = it.isRedFlagTokenTickerCopy,
			isRedFlagCreatorHighBuy = it.isRedFlagCreatorHighBuy,
			isRedFlagSuspiciousWalletsDetected = it.isRedFlagSuspiciousWalletsDetected,
			isRedFlagBundledBuysDetected = it.isRedFlagBundledBuysDetected,
			isRedFlagSingleHighBuy = it.isRedFlagSingleHighBuy,
		)
	},
)

data class BotsSellPromotedTokenCommand(
	val blockSlot: Int,
	val pairAddress: AddressWrapper,
	val tokenAddress: AddressWrapper,
	val creator: AddressWrapper,
) : Command<Unit>

data class BotsSellStalePositionsCommand(
	val now: Instant,
) : Command<Unit>

data class ForceSellBotMarketPositionCommand(
	val userId: UUID,
	val botId: UUID,
	val botMarketPositionId: UUID,
) : Command<Unit>
