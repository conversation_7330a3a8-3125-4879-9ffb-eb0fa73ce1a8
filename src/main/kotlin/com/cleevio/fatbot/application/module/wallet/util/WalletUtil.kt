package com.cleevio.fatbot.application.module.wallet.util

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.constant.MAX_WALLETS_PER_CHAIN_AND_USER
import com.cleevio.fatbot.application.module.wallet.exception.FailedToGetAddressFromPrivateKeyException
import com.cleevio.fatbot.application.module.wallet.exception.MaxWalletCountExceededException
import org.bitcoinj.core.Base58
import org.p2p.solanaj.core.Account
import org.web3j.crypto.Credentials

fun validateWalletCount(walletCount: Int, chain: Chain) {
	if (walletCount >= MAX_WALLETS_PER_CHAIN_AND_USER) {
		throw MaxWalletCountExceededException(
			"Maximum number of wallets per chain $chain exceeded ($MAX_WALLETS_PER_CHAIN_AND_USER).",
		)
	}
}

fun getAddressFromPrivateKey(privateKey: String, chainType: ChainType): AddressWrapper {
	val result = runCatching {
		val addressString = when (chainType) {
			ChainType.EVM -> Credentials.create(privateKey).address
			ChainType.SOLANA -> Account(Base58.decode(privateKey)).publicKeyBase58
		}

		AddressWrapper(addressString)
	}

	return result.getOrNull() ?: throw FailedToGetAddressFromPrivateKeyException(
		"Failed to get $chainType wallet address from private key staring with ${privateKey.take(6)}.",
	)
}
