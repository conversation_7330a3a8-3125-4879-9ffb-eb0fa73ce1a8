package com.cleevio.fatbot.application.module.bot.service

import com.cleevio.fatbot.application.module.bot.port.out.ResetRemainingBotBuyFrequency
import com.cleevio.fatbot.application.module.matchmaking.locks.MATCH_BOTS
import com.cleevio.fatbot.application.module.matchmaking.locks.MATCH_MAKING_MODULE
import com.cleevio.library.lockinghandler.service.Lock
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ResetRemainingBotBuyFrequencyService(
	private val resetRemainingBotBuyFrequency: ResetRemainingBotBuyFrequency,
) {
	@Transactional(transactionManager = "matchMakingTransactionManager")
	@Lock(module = MATCH_MAKING_MODULE, lockName = MATCH_BOTS)
	operator fun invoke() {
		// WARNING: This function acquires lock for the whole bot trading, thus blocking it, so it
		// must be quick!
		resetRemainingBotBuyFrequency()
	}
}
