package com.cleevio.fatbot.application.module.userfattyleagueseason

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.fattyleagueseason.finder.FattyLeagueSeasonFinderService
import com.cleevio.fatbot.application.module.userfattyleagueseason.command.ClaimUserFattyTokensCommand
import com.cleevio.fatbot.application.module.userfattyleagueseason.exception.UserFattyTokensNotClaimableException
import com.cleevio.fatbot.application.module.userfattyleagueseason.finder.UserFattyLeagueSeasonFinderService
import com.cleevio.fatbot.application.module.userfattyleagueseason.locks.CREATE_OR_UPDATE_USER_FATTY_LEAGUE_SEASON
import com.cleevio.fatbot.application.module.userfattyleagueseason.locks.USER_FATTY_LEAGUE_SEASON_MODULE
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Clock
import java.time.Instant

@Component
class ClaimFattyTokensCommandHandler(
	private val userFattyLeagueSeasonFinderService: UserFattyLeagueSeasonFinderService,
	private val fattyLeagueSeasonFinderService: FattyLeagueSeasonFinderService,
	private val clock: Clock,
) : CommandHandler<Unit, ClaimUserFattyTokensCommand> {

	override val command = ClaimUserFattyTokensCommand::class

	@Transactional
	@Lock(module = USER_FATTY_LEAGUE_SEASON_MODULE, lockName = CREATE_OR_UPDATE_USER_FATTY_LEAGUE_SEASON)
	override fun handle(@LockFieldParameter("userId") command: ClaimUserFattyTokensCommand) {
		val now = Instant.now(clock)
		val userFattyLeagueSeason = userFattyLeagueSeasonFinderService.getByIdAndUserId(
			id = command.userFattyLeagueId,
			userId = command.userId,
		)
		val fattyLeagueSeason = fattyLeagueSeasonFinderService.getById(userFattyLeagueSeason.fattyLeagueSeasonId)

		if (!fattyLeagueSeason.isClaimableNow(now = now, lastClaimedAt = userFattyLeagueSeason.lastClaimedAt)) {
			throw UserFattyTokensNotClaimableException("User fatty tokens are not claimable")
		}

		val claimableTokens = fattyLeagueSeason.computeClaimableTokens(
			lastClaimedAt = userFattyLeagueSeason.lastClaimedAt,
			totalTokens = userFattyLeagueSeason.totalTokens,
			remainingTokens = userFattyLeagueSeason.getRemainingTokens(),
			now = now,
		)

		userFattyLeagueSeason.claimTokens(now, claimableTokens)
	}
}
