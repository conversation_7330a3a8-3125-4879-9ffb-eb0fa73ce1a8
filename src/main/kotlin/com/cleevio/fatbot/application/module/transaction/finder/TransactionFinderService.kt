package com.cleevio.fatbot.application.module.transaction.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.exception.TransactionNotFoundException
import com.cleevio.fatbot.domain.transaction.Transaction
import com.cleevio.fatbot.domain.transaction.TransactionRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class TransactionFinderService(
	private val transactionRepository: TransactionRepository,
) : BaseFinderService<Transaction>(transactionRepository) {

	override fun errorBlock(message: String) = throw TransactionNotFoundException(message)

	override fun getEntityType() = Transaction::class

	@Transactional(readOnly = true)
	fun findAllRealPending(): List<Transaction> = transactionRepository.findAllByStatusAndSignedTxIsNotNull(
		status = TransactionStatus.PENDING,
	)
}
