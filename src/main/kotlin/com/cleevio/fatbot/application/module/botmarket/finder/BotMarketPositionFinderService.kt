package com.cleevio.fatbot.application.module.botmarket.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.botmarket.exception.BotMarketPositionNotFoundException
import com.cleevio.fatbot.domain.botmarket.BotMarketPosition
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionRepository
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class BotMarketPositionFinderService(
	private val botMarketPositionRepository: BotMarketPositionRepository,
) : BaseFinderService<BotMarketPosition>(botMarketPositionRepository) {

	override fun errorBlock(message: String) = throw BotMarketPositionNotFoundException(message)
	override fun getEntityType() = BotMarketPosition::class

	fun findAllByTokenAddressAndState(
		tokenAddress: AddressWrapper,
		states: Set<BotMarketPositionState>,
	): List<BotMarketPosition> {
		return botMarketPositionRepository.findAllByTokenAddressAndStateIn(tokenAddress, states)
	}

	fun findAllByTokenAddresses(tokenAddresses: Set<AddressWrapper>): Set<BotMarketPosition> {
		return botMarketPositionRepository.findAllByTokenAddressIn(tokenAddresses)
	}

	fun getByTokenAddressAndBotWalletId(tokenAddress: AddressWrapper, botWalletId: UUID) =
		botMarketPositionRepository.findByTokenAddressAndBotWalletId(
			tokenAddress = tokenAddress,
			botWalletId = botWalletId,
		) ?: errorBlock("Bot market position not found for token address $tokenAddress!")

	fun findByTokenAddressAndBotWalletId(tokenAddress: AddressWrapper, botWalletId: UUID) =
		botMarketPositionRepository.findByTokenAddressAndBotWalletId(
			tokenAddress = tokenAddress,
			botWalletId = botWalletId,
		)

	fun getByIdAndBotWalletId(id: UUID, botWalletId: UUID) =
		botMarketPositionRepository.findByIdAndBotWalletId(id = id, botWalletId = botWalletId)
			?: errorBlock("Bot market position not found!")

	fun getByIdAndBotId(id: UUID, botId: UUID) = botMarketPositionRepository.findByIdAndBotId(id = id, botId = botId)
		?: errorBlock("Bot market position with id: $id not found")
}
