package com.cleevio.fatbot.application.module.bot.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class BotNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.BOT_NOT_FOUND,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class MaxActiveBotsExceeded(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.MAX_ACTIVE_BOTS_EXCEEDED,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class InvalidBotStateTransitionException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.INVALID_BOT_STATE_TRANSITION,
	message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class WithdrawalBotInvalidStateException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.WITHDRAWAL_BOT_INVALID_STATE,
	message = message,
)
