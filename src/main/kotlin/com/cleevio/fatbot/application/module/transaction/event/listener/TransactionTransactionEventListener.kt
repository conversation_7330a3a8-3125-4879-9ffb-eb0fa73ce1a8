package com.cleevio.fatbot.application.module.transaction.event.listener

import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.market.command.PreApproveTokenCommand
import com.cleevio.fatbot.application.module.transaction.SendSignedTransactionService
import com.cleevio.fatbot.application.module.transaction.TransactionStatusPublishingService
import com.cleevio.fatbot.application.module.transaction.event.BuyTransactionSuccessfulEvent
import com.cleevio.fatbot.application.module.transaction.event.TransactionTryCountIncreaseEvent
import com.cleevio.fatbot.application.module.transaction.event.TransactionsStatusesResolvedEvent
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionalEventListener

@Component
class TransactionTransactionEventListener(
	private val transactionStatusPublishingService: TransactionStatusPublishingService,
	private val sendSignedTransactionService: SendSignedTransactionService,
	private val commandBus: CommandBus,
) {

	@Async
	@EventListener
	fun handleAsyncTransactionsStatusesResolvedEvent(event: TransactionsStatusesResolvedEvent) {
		transactionStatusPublishingService.publishTransactionsToUsers(
			transactionStatusModels = event.transactionStatusModels,
		)
	}

	@Async
	@TransactionalEventListener(fallbackExecution = true)
	fun handleAsyncTransactionTryCountIncreaseEvent(event: TransactionTryCountIncreaseEvent) {
		sendSignedTransactionService.sendSignedTransaction(event.transactionId)
	}

	@Async
	@TransactionalEventListener(fallbackExecution = true)
	@SentryTransaction(operation = "async.pre-approve-token")
	fun handleAsyncBuyTransactionSuccessful(event: BuyTransactionSuccessfulEvent) {
		commandBus(
			PreApproveTokenCommand(
				walletId = event.walletId,
				tokenAddress = event.tokenAddress,
				chainId = event.chainId,
			),
		)
	}
}
