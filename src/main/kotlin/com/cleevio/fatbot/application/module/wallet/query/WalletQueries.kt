package com.cleevio.fatbot.application.module.wallet.query

import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.net.URI
import java.util.UUID

data class GetAllUserWalletsQuery(
	val userId: UUID,
	val useSelectedChains: Boolean,
) : Query<List<GetAllUserWalletsQuery.Result>> {

	@Schema(name = "GetAllUserWalletsResult")
	data class Result(
		val walletId: UUID,
		val isDefault: Boolean,
		val walletAddress: AddressWrapper,
		val walletDetailUrl: URI,
		val walletBalance: NativeAmount,
		val walletBalanceUsd: BigDecimal,
		val customName: String?,
		val chain: Chain,
		val currentPortfolioValueUsd: BigDecimal,
		val currentPortfolioValueChangeUsd: BigDecimal,
		val currentPortfolioValueChangeFraction: BigDecimal,
	)
}

data class GetUserWalletQuery(
	val userId: UUID,
	val walletId: UUID,
) : Query<GetUserWalletQuery.Result> {

	@Schema(name = "GetUserWalletResult")
	data class Result(
		val id: UUID,
		val isDefault: Boolean,
		val customName: String?,
		val walletAddress: AddressWrapper,
		val walletDetailUri: URI,
		val walletBalance: NativeAmount,
		val walletBalanceUsd: BigDecimal,
		val chain: Chain,
		val buyAntiMevProtection: Boolean,
		val sellAntiMevProtection: Boolean,
		val currentPortfolioValueUsd: BigDecimal,
		val currentPortfolioValueChangeUsd: BigDecimal,
		val currentPortfolioValueChangeFraction: BigDecimal,
		val portfolioOneDayChangeAmountUsd: BigDecimal,
		val portfolioOneDayChangeFraction: BigDecimal,
	)
}

data class ExportUserWalletQuery(
	val userId: UUID,
	val walletId: UUID,
	val password: String,
) : Query<ExportUserWalletQuery.Result> {

	@Schema(name = "ExportUserWalletResult")
	data class Result(
		val privateKey: String,
	)
}
