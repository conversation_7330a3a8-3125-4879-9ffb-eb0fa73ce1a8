package com.cleevio.fatbot.application.module.user.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.module.user.exception.ReferralCodeNotFoundException
import com.cleevio.fatbot.application.module.user.exception.UserNotFoundException
import com.cleevio.fatbot.domain.user.FirebaseUser
import com.cleevio.fatbot.domain.user.FirebaseUserRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class FirebaseUserFinderService(
	private val firebaseUserRepository: FirebaseUserRepository,
) : BaseFinderService<FirebaseUser>(firebaseUserRepository) {

	override fun errorBlock(message: String) = throw UserNotFoundException(message)

	override fun getEntityType() = FirebaseUser::class

	@Transactional(readOnly = true)
	fun getByEmailIgnoringCase(email: String) = firebaseUserRepository.findByEmail(email.lowercase())
		?: errorBlock("User with email $email not found")

	@Transactional(readOnly = true)
	fun existByEmailIgnoringCase(email: String) = firebaseUserRepository.existsByEmail(email.lowercase())

	@Transactional(readOnly = true)
	fun getByReferralCode(referralCode: String) = firebaseUserRepository.findByReferralCode(referralCode.lowercase())
		?: throw ReferralCodeNotFoundException("No user with referral code $referralCode found.")

	@Transactional(readOnly = true)
	fun existByReferralCode(referralCode: String) = firebaseUserRepository.existsByReferralCode(referralCode.lowercase())
}
