package com.cleevio.fatbot.application.module.fattycard.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.module.fattycard.exception.FattyCardNotFoundException
import com.cleevio.fatbot.domain.fattycard.FattyCard
import com.cleevio.fatbot.domain.fattycard.FattyCardRepository
import org.springframework.stereotype.Service

@Service
class FattyCardFinderService(
	private val fattyCardRepository: FattyCardRepository,
) : BaseFinderService<FattyCard>(fattyCardRepository) {

	override fun errorBlock(message: String) = throw FattyCardNotFoundException(message)

	override fun getEntityType() = FattyCard::class
}
