package com.cleevio.fatbot.application.module.userfattyleagueseason

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.fattyleagueseason.finder.FattyLeagueSeasonFinderService
import com.cleevio.fatbot.application.module.userfattyleagueseason.finder.UserFattyLeagueSeasonFinderService
import com.cleevio.fatbot.application.module.userfattyleagueseason.query.GetUserFattyLeagueSeasonsQuery
import com.cleevio.fatbot.application.module.userfattyleagueseason.query.GetUserFattyLeagueSeasonsQuery.UserFattyLeagueSeasonResult
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Clock
import java.time.Instant

@Component
class GetUserFattyLeagueSeasonsQueryHandler(
	private val userFattyLeagueSeasonFinderService: UserFattyLeagueSeasonFinderService,
	private val fattyLeagueSeasonFinderService: FattyLeagueSeasonFinderService,
	private val clock: Clock,
) : QueryHandler<GetUserFattyLeagueSeasonsQuery.Result, GetUserFattyLeagueSeasonsQuery> {
	override val query = GetUserFattyLeagueSeasonsQuery::class

	override fun handle(query: GetUserFattyLeagueSeasonsQuery): GetUserFattyLeagueSeasonsQuery.Result {
		val now = Instant.now(clock)
		val userFattyLeagueSeasons = userFattyLeagueSeasonFinderService.findAllByUserId(query.userId)
		val idToFattyLeagueSeason = fattyLeagueSeasonFinderService
			.findAllByIds(userFattyLeagueSeasons.mapToSet { it.fattyLeagueSeasonId })
			.associateBy { it.id }

		val userFattyLeagueSeasonsResult = userFattyLeagueSeasons.map { userFattyLeague ->
			val fattyLeague = idToFattyLeagueSeason[userFattyLeague.fattyLeagueSeasonId]!!

			val remainingTokens = userFattyLeague.getRemainingTokens()
			val areTokensExpired = fattyLeague.areTokensExpired(now)
			UserFattyLeagueSeasonResult(
				id = fattyLeague.id,
				seasonName = fattyLeague.name,
				earnedTokens = userFattyLeague.totalTokens,
				claimedTokens = userFattyLeague.claimedTokens,
				areAllTokensClaimed = userFattyLeague.totalTokens == userFattyLeague.claimedTokens,
				claimableTokens = if (areTokensExpired) BigDecimal.ZERO else remainingTokens,
				expiredTokens = if (areTokensExpired) remainingTokens else BigDecimal.ZERO,
				tokensExpireAt = fattyLeague.getTokensExpireAt(),
				claimableTokensNow = fattyLeague.computeClaimableTokens(
					lastClaimedAt = userFattyLeague.lastClaimedAt,
					totalTokens = userFattyLeague.totalTokens,
					remainingTokens = remainingTokens,
					now = now,
				),
				nextClaimableAt = fattyLeague.nextPossibleClaim(
					lastClaimedAt = userFattyLeague.lastClaimedAt,
					now = now,
				),
				isExpired = areTokensExpired,
			)
		}

		val totalClaimedTokens = userFattyLeagueSeasonsResult.sumOf { it.claimedTokens }

		val totalClaimableTokens = userFattyLeagueSeasonsResult.sumOf { it.claimableTokens }

		return GetUserFattyLeagueSeasonsQuery.Result(
			userFattyLeagueSeasons = userFattyLeagueSeasonsResult,
			totalClaimedTokens = totalClaimedTokens,
			totalClaimableTokens = totalClaimableTokens,
		)
	}
}
