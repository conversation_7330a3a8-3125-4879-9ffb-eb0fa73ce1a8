package com.cleevio.fatbot.application.module.userstatistics.port.out

import com.cleevio.fatbot.application.module.userstatistics.constant.StreakDayState
import java.time.LocalDate
import java.util.UUID

interface GetUserStreak {
	operator fun invoke(userId: UUID): Result

	data class Result(
		val userId: UUID,
		val daysInStreak: Int,
		val streakDaysInWeek: List<StreakDayResult>,
	)

	data class StreakDayResult(
		val date: LocalDate,
		val state: StreakDayState,
	)
}
