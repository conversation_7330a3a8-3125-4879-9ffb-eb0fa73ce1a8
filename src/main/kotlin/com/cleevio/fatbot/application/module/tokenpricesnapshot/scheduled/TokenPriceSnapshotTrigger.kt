package com.cleevio.fatbot.application.module.tokenpricesnapshot.scheduled

import com.cleevio.fatbot.application.module.tokenpricesnapshot.TokenPriceSnapshotProcessingService
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class TokenPriceSnapshotTrigger(private val tokenPriceSnapshotProcessingService: TokenPriceSnapshotProcessingService) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.snapshot-token-prices")
	@Scheduled(cron = "\${fatbot.token.price-processing.cron}")
	@SchedulerLock(
		name = "PROCESSING_TOKEN_PRICES",
		lockAtLeastFor = "\${fatbot.token.price-processing.lock-for}",
		lockAtMostFor = "\${fatbot.token.price-processing.lock-for}",
	)
	fun trigger() {
		logger.info("PROCESSING_TOKEN_PRICES cron started")
		tokenPriceSnapshotProcessingService.processSnapshots()
		logger.info("PROCESSING_TOKEN_PRICES cron ended")
	}
}
