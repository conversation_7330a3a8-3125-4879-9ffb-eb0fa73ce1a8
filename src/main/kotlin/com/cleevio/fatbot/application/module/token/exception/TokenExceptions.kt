package com.cleevio.fatbot.application.module.token.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class TokenNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.TOKEN_NOT_FOUND,
	message = message,
)

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
class TokenImageCouldNotBeDownloadedException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.DOWNLOAD_FAILED_EXCEPTION,
	message = message,
)

@ResponseStatus(HttpStatus.TOO_EARLY)
class FailedToVerifyPumpswapPairException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.FAILED_TO_VERIFY_PUMPSWAP_PAIR,
	message = message,
)
