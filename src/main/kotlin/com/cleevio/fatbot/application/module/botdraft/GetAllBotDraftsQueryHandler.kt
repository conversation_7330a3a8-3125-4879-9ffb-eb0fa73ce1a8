package com.cleevio.fatbot.application.module.botdraft

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.botdraft.finder.BotDraftFinderService
import com.cleevio.fatbot.application.module.botdraft.query.GetAllBotDraftsQuery
import org.springframework.stereotype.Component

@Component
class GetAllBotDraftsQueryHandler(
	private val botDraftFinderService: BotDraftFinderService,
) : QueryHandler<List<GetAllBotDraftsQuery.Result>, GetAllBotDraftsQuery> {
	override val query = GetAllBotDraftsQuery::class

	override fun handle(query: GetAllBotDraftsQuery): List<GetAllBotDraftsQuery.Result> {
		val botDrafts = botDraftFinderService.findAllByUserId(query.userId)

		val result = botDrafts.map {
			GetAllBotDraftsQuery.Result(
				id = it.id,
				createdAt = it.createdAt,
				name = it.name,
				avatarFileId = it.avatarFileId,
				tradeAmount = it.tradeAmount,
				buyFrequency = it.buyFrequency?.toBigInteger(),
				profitTargetFraction = it.profitTargetFraction,
				stopLossFraction = it.stopLossFraction,
				buyVolume = it.buyVolume,
				sellVolume = it.sellVolume,
				dailyVolumeFromUsd = it.dailyVolumeFromUsd,
				dailyVolumeToUsd = it.dailyVolumeToUsd,
				liquidityFromUsd = it.liquidityFromUsd,
				liquidityToUsd = it.liquidityToUsd,
				marketCapFromUsd = it.marketCapFromUsd,
				marketCapToUsd = it.marketCapToUsd,
				numberOfHoldersFrom = it.numberOfHoldersFrom?.toBigInteger(),
				numberOfHoldersTo = it.numberOfHoldersTo?.toBigInteger(),
				sellTransactionFraction = it.sellTransactionFraction,
				buyTransactionFraction = it.buyTransactionFraction,
			)
		}

		return result
	}
}
