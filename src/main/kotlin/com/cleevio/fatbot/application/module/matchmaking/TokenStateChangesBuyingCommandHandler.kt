package com.cleevio.fatbot.application.module.matchmaking

import com.cleevio.fatbot.adapter.out.solana.pumpfun.GetAmountOutPumpfunSolana
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.compareTo
import com.cleevio.fatbot.application.common.crypto.plus
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bot.BotFinderService
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.matchmaking.command.TokenStateChangesBuyingCommand
import com.cleevio.fatbot.application.module.matchmaking.event.BotBuyTransactionRequest
import com.cleevio.fatbot.application.module.matchmaking.event.BoughtTokensEvent
import com.cleevio.fatbot.application.module.matchmaking.port.out.MatchBuyingBots
import com.cleevio.fatbot.application.module.matchmaking.port.out.SendBotTransaction
import com.cleevio.fatbot.application.module.matchmaking.port.out.UpsertBotTokenInfo
import com.cleevio.fatbot.application.module.matchmaking.port.out.UpsertPumpfunTokenPairInfo
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionCreateService
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateCreateService
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateType
import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

/**
 * Following command handler, handles logic for matching Solana token state change for buying tokens
 * automatically with trading bot. This logic needs to be fast. Acceptable results are max 10ms of response time.
 *
 * We're assuming positive results in the buying processing. Therefore, we're decreasing available daily cap buy limit
 * and balance of the bot wallet immediately without waiting for the result from Solana token buy.
 * Buy of the Solana tokens should always succeed, if it does not there is something seriously wrong on our side.
 *
 * TODO: If the buy on the Solana side fails, we need to account for this and fix the bot's state retrospectively.
 *
 * Buy match making logic has the following flow:
 *
 *		1. Bulk lock match making for bots in one common transaction
 *		2. Fetch matched buying bots
 *		3. Fetch JPA entities for bots and their wallets to be updated with the new buying state
 *		4. Fire an async event for starting the buy process against Solana
 *		5. Update bot buy frequency and bot wallet balance based on the amount of state changes and trade amount of the bot
 *		6. Add bot market position for bot's wallet for token buy
 *		7. Save all with batch insert/merge
 *
 * There's a possibility that this logic won't be fast enough and we'll need to parallelize the state changes processing.
 * Especially if we're going to be receiving large number of state changes per request.
 */
@Component
class TokenStateChangesBuyingCommandHandler(
	private val matchBuyingBots: MatchBuyingBots,
	private val botFinderService: BotFinderService,
	private val botWalletFinderService: BotWalletFinderService,
	private val getAmountOutPumpfunSolana: GetAmountOutPumpfunSolana,
	private val botMarketPositionCreateService: BotMarketPositionCreateService,
	private val sendBotTransaction: SendBotTransaction,
	private val upsertBotTokenInfo: UpsertBotTokenInfo,
	private val upsertPumpfunTokenPairInfo: UpsertPumpfunTokenPairInfo,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val botMarketPositionTradeStateCreateService: BotMarketPositionTradeStateCreateService,
) : CommandHandler<Unit, TokenStateChangesBuyingCommand> {

	private val logger = logger()

	override val command = TokenStateChangesBuyingCommand::class

	@Transactional(transactionManager = "matchMakingTransactionManager")
	override fun handle(command: TokenStateChangesBuyingCommand) {
		if (command.states.isEmpty()) {
			logger.warn("No token state changes provided. Skipping matching buying bots.")
			return
		}

		val matchedBuyingBots = matchBuyingBots(states = command.states)
		if (matchedBuyingBots.isEmpty()) {
			return
		}

		val matchedBuyingBotsWithFollowers = matchedBuyingBots.expandFollowers()
		val botIdToLeaderBotId = matchedBuyingBots
			.filter { it.followerId != null }
			.associate { it.followerId to it.botId }

		logger.info(
			"Starting to match token state changes to buying bots, " +
				"state changes size is: ${command.states.size}. Matched bots: ${matchedBuyingBotsWithFollowers.size}",
		)

		val botIds = matchedBuyingBotsWithFollowers.mapToSet { it.botId }
		val botIdToBot = botFinderService.getAllByIds(ids = botIds).associateBy { it.id }
		val botIdToWallet = botWalletFinderService.getAllByBotIds(botIds = botIds).associateBy { it.botId }

		val botMarketPositionIdToBoughtTokenAddress = matchedBuyingBotsWithFollowers.fold(
			// botId to (createdMarketPositionId to boughtTokenAddress)
			initial = mapOf<UUID, Pair<UUID, AddressWrapper>>(),
		) { acc, matchResult ->

			// bot already bought token in this block, so it's no longer eligible to buy another one
			// in the future at this point we could track how much the bot is being "rate-limited" by this rule
			if (acc.containsKey(matchResult.botId)) return@fold acc

			// if follower bot is buying, check if leader bought too, if not we can not buy as follower
			// this can happen when leader runs out balance on wallet for example
			val leaderBotId = botIdToLeaderBotId[matchResult.botId]
			val isFollower = leaderBotId != null
			val leaderDidNotBuy = !acc.containsKey(leaderBotId)
			if (isFollower && leaderDidNotBuy) return@fold acc

			val bot = botIdToBot.getValue(matchResult.botId)
			val botWallet = botIdToWallet.getValue(matchResult.botId)

			if (bot.hasZeroRemainingBuyFrequency) return@fold acc

			val botTradeAmount = bot.tradeAmount.asNativeAmount().toBase(chain = botWallet.chain)

			val tokenAccountRent = SolanaConstants.BOT_BUY_RENT_BASE_AMOUNT
			val buyFeesToFreeze = SolanaConstants.BOT_BUY_FEE_FREEZE_BASE_AMOUNT
			val sellFeesToFreeze = SolanaConstants.BOT_SELL_FEE_FREEZE_BASE_AMOUNT
			val minimumRentOnWalletAfterBuy = SolanaConstants.MINIMUM_RENT_THRESHOLD

			val minimumBalanceNeededForTrade = botTradeAmount +
				tokenAccountRent +
				buyFeesToFreeze +
				sellFeesToFreeze +
				minimumRentOnWalletAfterBuy

			val botWalletBalance = botWallet.balance.asBaseAmount()
			if (botWalletBalance < minimumBalanceNeededForTrade) return@fold acc

			bot.decreaseRemainingBuys()
			val botMarketPosition = botMarketPositionCreateService.create(
				botWalletId = botWallet.id,
				chain = botWallet.chain,
				tokenAddress = matchResult.tokenAddress,
				tokenPrice = matchResult.tokenPrice,
				profitTargetFraction = bot.profitTargetFraction,
				stopLossFraction = bot.stopLossFraction,
				blockSlot = command.blockSlot,
			)

			val tokenBoughtAmount = getAmountOutPumpfunSolana.fromCurveState(
				amount = botTradeAmount,
				curveState = matchResult.curveState,
				isBuy = true,
			)

			botMarketPosition.newAssumedBuy(
				amountOfTokensReceived = tokenBoughtAmount.amount,
				amountOfBaseCurrencyPaid = botTradeAmount.amount,
			)
			botMarketPosition.setFrozenAmount(
				buyFreezeAmount = botTradeAmount + buyFeesToFreeze + tokenAccountRent,
				sellFreezeAmount = sellFeesToFreeze,
			)

			botMarketPosition.setDexScreenerDebugUrl(
				url = constructDexScreenerUrl(
					walletAddress = botWallet.address,
					tokenAddress = matchResult.tokenAddress,
				),
			)

			botWallet.decreaseBalance(balance = botMarketPosition.balanceFrozen)

			sendBotTransaction(
				BotBuyTransactionRequest(
					blockSlot = command.blockSlot,
					botId = bot.id,
					botWalletId = botWallet.id,
					userId = bot.userId,
					tokenAddress = matchResult.tokenAddress,
					creator = matchResult.curveState.creator!!,
					privateKey = botWallet.privateKey,
					tradeAmountInBase = botTradeAmount,
					chain = botWallet.chain,
				),
			)

			acc + mapOf(bot.id to (botMarketPosition.id to botMarketPosition.tokenAddress))
		}.values.toMap()

		if (botMarketPositionIdToBoughtTokenAddress.isNotEmpty()) {
			val boughtTokenAddresses = botMarketPositionIdToBoughtTokenAddress.values.toSet()
			val boughtStates = command.states.filter { it.tokenAddress in boughtTokenAddresses }

			val inputs = boughtStates.map {
				UpsertBotTokenInfo.Input(
					chain = Chain.SOLANA,
					tokenAddress = it.tokenAddress,
					creator = it.curveState.creator!!,
					name = it.name,
					symbol = it.symbol,
					infoUrl = it.infoUrl,
				)
			}
			upsertBotTokenInfo(inputs = inputs)
			upsertPumpfunTokenPairInfo(tokenAddresses = boughtTokenAddresses)

			applicationEventPublisher.publishEvent(
				BoughtTokensEvent(
					boughtTokens = inputs.map {
						BoughtTokensEvent.BoughtToken(
							chain = Chain.SOLANA,
							tokenAddress = it.tokenAddress,
							name = it.name,
							symbol = it.symbol,
							infoUrl = it.infoUrl,
						)
					},
				),
			)

			val tokenAddressToState = command.states.associateBy { it.tokenAddress }
			botMarketPositionIdToBoughtTokenAddress.forEach { (botMarketPositionId, tokenAddress) ->
				val state = tokenAddressToState.getValue(tokenAddress)
				botMarketPositionTradeStateCreateService.create(
					botMarketPositionId = botMarketPositionId,
					type = BotMarketPositionTradeStateType.BUY,
					marketCapUsd = state.marketCapUsd,
					liquidityUsd = state.liquidityUsd,
					volumeUsd = state.volumeUsd,
					numOfAccountHolders = state.numOfAccountHolders,
					buyVolume = state.buyVolume,
					sellVolume = state.sellVolume,
					fractionOfSellTransactions = state.fractionOfSellTransactions,
				)
			}
		}

		logger.info("Finished matching token state changes to buying bots.")
	}

	private fun List<MatchBuyingBots.Result>.expandFollowers() = this.flatMap { originalBot ->
		val followerBot = when (originalBot.followerId) {
			null -> null
			else -> originalBot.copy(botId = originalBot.followerId, followerId = null)
		}

		listOfNotNull(originalBot, followerBot)
	}
}

private fun constructDexScreenerUrl(walletAddress: AddressWrapper, tokenAddress: AddressWrapper) =
	"https://dexscreener.com/solana/${tokenAddress.getAddressString()}?maker=${walletAddress.getAddressString()}"
