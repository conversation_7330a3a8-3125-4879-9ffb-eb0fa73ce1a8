package com.cleevio.fatbot.application.module.botleaderboard

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.adapter.`in`.toInfiniteScrollSlice
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.bot.constant.BotSortParameter
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotMarketPosition
import com.cleevio.fatbot.application.module.botleaderboard.port.out.SearchBotLeaderboard
import com.cleevio.fatbot.application.module.botleaderboard.query.BotsLeaderboardSearchQuery
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.token.constant.toInstant
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Clock
import java.time.Instant

@Component
class BotsLeaderboardSearchQueryHandler(
	private val clock: Clock,
	private val searchBotLeaderboard: SearchBotLeaderboard,
	private val findAllBotMarketPosition: FindAllBotMarketPosition,
	private val botWalletFinderService: BotWalletFinderService,
	private val getTokenPrices: GetTokenPrices,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val usdConverter: UsdConverter,
	private val fileUrlMapper: FileUrlMapper,
) : QueryHandler<InfiniteScrollSlice<BotsLeaderboardSearchQuery.Result, BigDecimal>, BotsLeaderboardSearchQuery> {

	override val query = BotsLeaderboardSearchQuery::class

	override fun handle(
		query: BotsLeaderboardSearchQuery,
	): InfiniteScrollSlice<BotsLeaderboardSearchQuery.Result, BigDecimal> {
		val userId = query.userId?.takeUnless { query.filter.allBots }
		val now = Instant.now(clock)
		val from = query.filter.timeRange.toInstant(now)

		val botLeaderboardSnapshots = searchBotLeaderboard(
			userId = userId,
			from = from,
			to = now,
			sortParameter = query.filter.sortParameter,
			infiniteScroll = query.infiniteScroll,
			searchString = query.filter.searchString,
		)

		val botIds = botLeaderboardSnapshots.content.mapToSet { it.botId }
		val botWallets = botWalletFinderService.getAllByBotIds(botIds = botIds)

		val openPositions = findAllBotMarketPosition(
			userId = userId,
			botIds = botIds,
			isBotMarketPositionActive = true,
			searchString = null,
			fileToUrlMapper = fileUrlMapper::map,
		)

		val openPositionTokens = openPositions.mapToSet { it.tokenAddress.toChainAddress(it.chain) }
		val tokenAddressToTokenPrice = getTokenPrices(tokens = openPositionTokens)

		val chainToExchangeRate = Chain.entries.associateWith { getUsdExchangeRate(it.currency) }

		val botIdToMarketPositionValue = BotMarketPositionCalculator.botToMarketPositionsResults(
			botMarketPositions = openPositions,
			tokenAddressToTokenPrice = tokenAddressToTokenPrice,
			chainToExchangeRate = chainToExchangeRate,
		)

		val botIdToPortfolioValues = botWallets.associate { botWallet ->
			val balanceBaseAmount = botWallet.balance.asBaseAmount()
			val walletBalanceUsd = usdConverter.baseToUsd(
				baseAmount = balanceBaseAmount,
				chain = botWallet.chain,
				customRate = chainToExchangeRate.getValue(botWallet.chain),
			)
			val marketPositionsValueUsd = botIdToMarketPositionValue[botWallet.botId]
				?.let { it.currentValueUsd + it.rentUsd }
				?: BigDecimal.ZERO

			val totalValueUsd = marketPositionsValueUsd + walletBalanceUsd
			val pnlUsd = totalValueUsd - botWallet.acquisitionValueUsd
			val pnlFraction = calculateFractionChange(totalValueUsd, botWallet.acquisitionValueUsd)

			botWallet.botId to Triple(totalValueUsd, pnlUsd, pnlFraction)
		}

		val leaderboardChangeResults = botLeaderboardSnapshots.content.map { result ->
			val currentPortfolioValues = botIdToPortfolioValues.getValue(result.botId)

			val walletBalanceChange = currentPortfolioValues.first - result.walletBalance
			val profitUsdChange = currentPortfolioValues.second - result.profitUsd
			val profitPercentageChange = currentPortfolioValues.third - result.profitPercentage

			result.copy(
				walletBalance = walletBalanceChange,
				profitUsd = profitUsdChange,
				profitPercentage = profitPercentageChange,
			)
		}

		return leaderboardChangeResults.toInfiniteScrollSlice(infiniteScroll = query.infiniteScroll) {
			when (query.filter.sortParameter) {
				BotSortParameter.PROFIT_USD -> profitUsd
				BotSortParameter.PROFIT_PERCENTAGE -> profitPercentage
				BotSortParameter.WALLET_BALANCE -> walletBalance
			}
		}
	}
}
