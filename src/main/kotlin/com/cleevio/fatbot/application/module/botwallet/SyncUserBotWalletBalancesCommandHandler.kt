package com.cleevio.fatbot.application.module.botwallet

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.asTxHash
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bottransaction.locks.BOT_TRANSACTION_MODULE
import com.cleevio.fatbot.application.module.bottransaction.locks.SYNC_BOT_WALLET_BALANCES
import com.cleevio.fatbot.application.module.botwallet.command.SyncUserBotWalletBalancesCommand
import com.cleevio.fatbot.application.module.botwallet.port.out.GetBotWallets
import com.cleevio.fatbot.application.module.botwallet.port.out.UpdateBotWallets
import com.cleevio.fatbot.application.module.botwallet.port.out.UpsertBotDepositTransactions
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import com.cleevio.library.lockinghandler.service.TryLock
import org.bitcoinj.core.Base58
import org.bitcoinj.core.Utils
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.ConfirmedTransaction
import org.p2p.solanaj.rpc.types.SignatureInformation
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.math.BigInteger
import java.util.UUID

const val GET_SIGNATURES_FOR_ADDRESS_MAX_LIMIT = 25

@Component
class SyncUserBotWalletBalancesCommandHandler(
	private val updateBotWallets: UpdateBotWallets,
	private val upsertBotDepositTransactions: UpsertBotDepositTransactions,
	private val getBotWallets: GetBotWallets,
	// TODO: Extract to port out
	private val rpcClient: RpcClient,
	private val getUsdExchangeRate: GetUsdExchangeRate,
) : CommandHandler<Unit, SyncUserBotWalletBalancesCommand> {

	override val command = SyncUserBotWalletBalancesCommand::class

	@Autowired
	@Lazy
	private lateinit var self: SyncUserBotWalletBalancesCommandHandler

	private data class SignaturesState(
		val botWallet: GetBotWallets.Result,
		val signatures: List<String>,
		val newLatestSignature: String?,
	)

	private data class DepositInformation(
		val botWalletId: UUID,
		val txHash: TxHash,
		val value: BaseAmount,
	)

	/*
	Sync logic:
	1. Fetch all bot wallets belonging to the user
	2. Fetch all signatures relevant to bot wallets since the last signature we synced (tracked in BotWallet)
	3. Fetch transactions of the signatures and filter to only deposits targeting the bot wallet
	4. Update balance of each bot wallet by the sum of deposits we detected
	5. Update the latestSignature to the latest one we detected
	6. Create deposit transactions of detected deposits
	 */
	override fun handle(command: SyncUserBotWalletBalancesCommand) {
		val botWallets = getBotWallets(userId = command.userId, botId = command.botId)

		val signaturesStates = botWallets.map { wallet ->
			val signaturesResponse = getSignatureInformation(wallet.address, wallet.latestSignature)

			val successfulSignatures = signaturesResponse.mapNotNull { response ->
				response.signature.takeIf { response.err == null }
			}
			val newLatestSignature = signaturesResponse.firstOrNull()?.signature ?: wallet.latestSignature

			SignaturesState(
				botWallet = wallet,
				signatures = successfulSignatures,
				newLatestSignature = newLatestSignature,
			)
		}
		val allSignatures = signaturesStates.flatMap { it.signatures }
		val transactionsResponse = getTransactions(allSignatures)

		check(transactionsResponse.size == allSignatures.size) {
			"Missing transaction response for some signatures! " +
				"Signatures: ${allSignatures.size}, Transactions: ${transactionsResponse.size}"
		}

		val initial = transactionsResponse to emptyList<DepositInformation>() // To avoid broken lines
		val (_, deposits) = signaturesStates.fold(initial) { (resultsLeft, collected), (botWallet, signatures) ->
			val newDeposits = resultsLeft
				.zip(signatures) // zip only takes the min size of the collections
				.map { (result, signature) -> (result to signature) to result.sumAmountFromTransfers(botWallet.address) }
				.filter { (_, amountGained) -> amountGained > BigInteger.ZERO }
				.map { (resultToSignature, amountGained) ->

					DepositInformation(
						botWalletId = botWallet.id,
						txHash = resultToSignature.second.asTxHash(),
						value = amountGained.asBaseAmount(),
					)
				}

			// Drop what the zip has taken and collect new deposits
			resultsLeft.drop(signatures.size) to collected + newDeposits
		}

		if (deposits.isEmpty() && signaturesStates.all { it.newLatestSignature == it.botWallet.latestSignature }) {
			// No need to open a transaction and update states
			return
		}

		val exchangeRate = getUsdExchangeRate(CryptoCurrency.SOL)

		self.inTransactionTryLock(command.userId) {
			val walletInputs = signaturesStates.map {
				val botWalletId = it.botWallet.id
				val balanceIncrease = deposits
					.filter { it.botWalletId == botWalletId }
					.sumOf { it.value.amount }

				val acquisitionValueUsd = balanceIncrease
					.asBaseAmount()
					.toNative(Chain.SOLANA)
					.amount
					.multiply(exchangeRate)

				UpdateBotWallets.Input(
					botWalletId = botWalletId,
					balanceIncrease = balanceIncrease,
					acquisitionValueUsdIncrease = acquisitionValueUsd,
					latestSignature = it.newLatestSignature?.asTxHash(),
					originalSignature = it.botWallet.latestSignature?.asTxHash(),
				)
			}

			val botDepositTransactionInputs = deposits.map {
				UpsertBotDepositTransactions.Input(
					botWalletId = it.botWalletId,
					txHash = it.txHash,
					baseValue = it.value.amount,
					exchangeRateUsd = exchangeRate,
				)
			}

			/*
				We can't use Hibernate at this point, as that could cause exception in the bot matchmaking,
				due to version column being changed. We also can't obtain global lock for matchmaking, as that could
				be very dangerous (this function is being run for each user)

				So to avoid that, we update bot wallets via JOOQ. Now it can happen, that updates to wallets could
				get lost as for example:

				T-0 - Bot matchmaking loads botWallet entity with balance of 100 in transaction
				T-1 - This function sets botWallet balance to 200
				T-2 - Bot matchmaking commits entity with balance 80, thus we miss the update of +100 completely

				However, as we also miss information about last processed signature for such wallet, it would be just
				processed in the next try.

				Also, bot transactions are upserted based on txHash, so it can happen that there will be already
				transaction of value +100 visible, while balance will not yet be updated. This however should be very
				rare.

				In the next run, the balance would be updated and as transactions would already be in the DB, nothing
				new would be created in bot_transaction table.

				Also, it can happen that this lambda is being run on multiple pods on the same time. Even though, the
				transaction itself is happening under lock, the network calls to RPC node could detect that balance
				needs to be changed by +100 and thus increase balance two times. This can be avoided by checking
				if the latestSignature in DB still matches the latest signature that was loaded at the start. If not,
				that is an indication that another transaction commited to DB so we can not do update then.
			 */
			updateBotWallets(inputs = walletInputs)
			upsertBotDepositTransactions(inputs = botDepositTransactionInputs)
		}
	}

	@Transactional
	@TryLock(module = BOT_TRANSACTION_MODULE, lockName = SYNC_BOT_WALLET_BALANCES)
	protected fun inTransactionTryLock(@LockArgumentParameter userId: UUID, block: () -> Unit) = block()

	private fun getSignatureInformation(address: AddressWrapper, latestSignature: String?): List<SignatureInformation> {
		// If the latestSignature is unknown, treat the initial request as a "ping" and timeout as "no txns"
		val initialLimit = if (latestSignature == null) 1 else GET_SIGNATURES_FOR_ADDRESS_MAX_LIMIT

		val initialSignatureResponse = runCatching {
			rpcClient.api.getSignaturesForAddress(
				address.getSolanaPublicKey(),
				initialLimit,
				Commitment.CONFIRMED,
				null,
				latestSignature,
			)
		}.getOrNull() ?: emptyList()

		val signaturesSeq = generateSequence(initialSignatureResponse to initialLimit) { (lastSignatures, lastLimit) ->
			// Last query returned fewer results than the limit -> we found all signatures
			if (lastSignatures.size < lastLimit) return@generateSequence null

			val before = lastSignatures.last().signature

			val signaturesResponse = rpcClient.api.getSignaturesForAddress(
				address.getSolanaPublicKey(),
				GET_SIGNATURES_FOR_ADDRESS_MAX_LIMIT,
				Commitment.CONFIRMED,
				before,
				latestSignature,
			)

			signaturesResponse to GET_SIGNATURES_FOR_ADDRESS_MAX_LIMIT
		}

		return signaturesSeq.flatMap { (signatures) -> signatures }.toList()
	}

	private fun getTransactions(signatures: List<String>): List<ConfirmedTransaction> {
		return signatures
			.chunked(25) { chunk -> rpcClient.api.getTransactions(chunk, Commitment.CONFIRMED) }
			.flatten()
	}

	private fun ConfirmedTransaction.sumAmountFromTransfers(botWalletAddress: AddressWrapper): BigInteger {
		val message = transaction.message

		val instruction = message.instructions.last()
		if (instruction.accounts.size != 2) return BigInteger.ZERO

		val accountKeys = message.accountKeys
		val programAddress = accountKeys[instruction.programIdIndex.toInt()]

		if (PublicKey(programAddress) != SolanaConstants.SYSTEM_PROGRAM_ID) {
			return BigInteger.ZERO
		}

		// to account for multi-transfer transactions (most of the time airdrops) we have to find which specific
		// instruction is involving our account
		return message
			.instructions
			.filter { it.accounts.size == 2 }
			.filter {
				val transferTargetAccountIndex = it.accounts.last()
				val transferTargetAccount = accountKeys[transferTargetAccountIndex.toInt()]
				runCatching { AddressWrapper(transferTargetAccount) == botWalletAddress }.getOrNull() ?: false
			}.sumOf {
				// read the amount received from instruction data
				Utils.readInt64(Base58.decode(instruction.data), 4)
			}.toBigInteger()
	}
}
