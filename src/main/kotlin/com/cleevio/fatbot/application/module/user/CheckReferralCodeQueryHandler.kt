package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.user.query.IsUniqueReferralCodeQuery
import org.springframework.stereotype.Component

@Component
class CheckReferralCodeQueryHandler(
	private val firebaseUserFinderService: FirebaseUserFinderService,
) : QueryHandler<IsUniqueReferralCodeQuery.Result, IsUniqueReferralCodeQuery> {
	override val query = IsUniqueReferralCodeQuery::class

	override fun handle(query: IsUniqueReferralCodeQuery): IsUniqueReferralCodeQuery.Result {
		val existsReferralCode = firebaseUserFinderService.existByReferralCode(query.referralCode)

		return IsUniqueReferralCodeQuery.Result(isUnique = !existsReferralCode)
	}
}
