package com.cleevio.fatbot.application.module.exchangerate.scheduled

import com.cleevio.fatbot.application.module.exchangerate.ExchangeRateSnapshotProcessingService
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class ExchangeRateSnapshotTrigger(
	private val exchangeRateSnapshotProcessingService: ExchangeRateSnapshotProcessingService,
) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.create-exchange-rate-snapshot")
	@Scheduled(cron = "\${fatbot.exchange-rate.create-snapshot.cron}")
	@SchedulerLock(
		name = "CREATE_EXCHANGE_RATE_SNAPSHOT",
		lockAtMostFor = "\${fatbot.exchange-rate.create-snapshot.lock-for}",
		lockAtLeastFor = "\${fatbot.exchange-rate.create-snapshot.lock-for}",
	)
	fun trigger() {
		logger.info("CREATE_EXCHANGE_RATE_SNAPSHOT cron started")
		exchangeRateSnapshotProcessingService.processSnapshots()
		logger.info("CREATE_EXCHANGE_RATE_SNAPSHOT cron ended")
	}
}
