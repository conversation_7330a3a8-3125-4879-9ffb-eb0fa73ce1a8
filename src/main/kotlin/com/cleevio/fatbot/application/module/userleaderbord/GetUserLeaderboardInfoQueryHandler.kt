package com.cleevio.fatbot.application.module.userleaderbord

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.userleaderbord.port.out.GetUserLeaderboardInfo
import com.cleevio.fatbot.application.module.userleaderbord.properties.LeaderboardMultiplierProperties
import com.cleevio.fatbot.application.module.userleaderbord.query.GetUserLeaderboardInfoQuery
import org.springframework.stereotype.Component

@Component
class GetUserLeaderboardInfoQueryHandler(
	private val getUserLeaderboardInfo: GetUserLeaderboardInfo,
	private val leaderboardMultiplierProperties: LeaderboardMultiplierProperties,
) : QueryHandler<GetUserLeaderboardInfoQuery.Result, GetUserLeaderboardInfoQuery> {

	override val query = GetUserLeaderboardInfoQuery::class

	override fun handle(query: GetUserLeaderboardInfoQuery): GetUserLeaderboardInfoQuery.Result {
		return getUserLeaderboardInfo(
			userId = query.userId,
			multiplierThresholds = leaderboardMultiplierProperties.thresholds.keys.map { it.toInt() }.toSet(),
		)
	}
}
