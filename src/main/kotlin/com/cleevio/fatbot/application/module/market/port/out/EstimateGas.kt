package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.adapter.out.evm.model.GasInfo
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.EstimateGas.Input
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import java.math.BigInteger
import java.util.UUID

interface EstimateGas {
	sealed interface Input {
		val privateKey: String
		val chain: Chain
		val gasInfo: GasInfo?
	}

	sealed interface Result

	data class ResultEvm(
		val gasLimitEstimate: BigInteger?,
		val additionalFees: BaseAmount?,
		val gasInfo: GasInfo,
	) : Result

	data class ResultSolana(
		val feeBaseAmount: BaseAmount,
	) : Result

	operator fun invoke(input: Input): Result
	fun supports(): ChainType

	data class BuyInput(
		override val privateKey: String,
		override val chain: Chain,
		override val gasInfo: GasInfo?,
		val userId: UUID,
		val useAntiMevProtection: Boolean,
		val tokenAddress: AddressWrapper,
		val isToken2022: Boolean,
		val amount: BaseAmount,
		val dexPairInfo: GetDex.Result,
	) : Input

	data class SellInput(
		override val privateKey: String,
		override val chain: Chain,
		override val gasInfo: GasInfo?,
		val useAntiMevProtection: Boolean,
		val userId: UUID,
		val tokenAddress: AddressWrapper,
		val amount: BaseAmount,
		val dexPairInfo: GetDex.Result,
	) : Input

	data class ApproveInput(
		override val privateKey: String,
		override val chain: Chain,
		override val gasInfo: GasInfo?,
		val tokenAddress: AddressWrapper,
	) : Input

	data class TransferTokenInput(
		override val privateKey: String,
		override val chain: Chain,
		override val gasInfo: GasInfo?,
		val amount: BaseAmount,
		val tokenAddress: AddressWrapper,
		val isToken2022: Boolean,
		val toAddress: AddressWrapper,
	) : Input

	data class TransferCurrencyInput(
		override val privateKey: String,
		override val chain: Chain,
		override val gasInfo: GasInfo?,
		val amount: BaseAmount,
		val toAddress: AddressWrapper,
	) : Input
}

// Helper function so we can call invoke on multiple EstimateGas instances, and find
// which one to use based on chain
fun List<EstimateGas>.estimate(input: Input): EstimateGas.Result = this
	.find { it.supports() == input.chain.type }
	?.invoke(input = input)
	?: error("EstimateGas is not supported for chainType ${input.chain.type}")
