package com.cleevio.fatbot.application.module.walletposition.query

import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import java.math.BigDecimal
import java.util.UUID

data class SearchUserWalletCurrencyPositionsQuery(
	val userId: UUID,
	val useSelectedChains: <PERSON><PERSON>an,
	@field:Valid val filter: Filter,
) : Query<List<SearchUserWalletCurrencyPositionsQuery.Result>> {

	data class Filter(val walletId: UUID?)

	@Schema(
		name = "SearchUserWalletPositionResult",
		description = "Note: all unspecified units default to the native unit, i.e. ETH for EVM, SOL for Solana",
	)
	data class Result(
		val chain: Chain,
		val currentValueNative: NativeAmount,
		val currentValueUsd: BigDecimal,
		val currentPriceUsd: BigDecimal,
		val oneHourChangeFraction: BigDecimal?,
		val oneDayChangeFraction: BigDecimal?,
		val pnlUsd: BigDecimal,
		val pnlFraction: BigDecimal,
	)
}
