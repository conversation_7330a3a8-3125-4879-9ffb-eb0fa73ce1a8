package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.token.command.SubscribeToTokenTradeStreamCommand
import com.cleevio.fatbot.application.module.token.finder.TokenPairInfoFinderService
import com.cleevio.fatbot.application.module.token.port.out.RealtimeTokenTradePublisher
import org.springframework.stereotype.Component

@Component
class SubscribeToTokenTradeStreamCommandHandler(
	private val tokenPairInfoFinderService: TokenPairInfoFinderService,
	private val realtimeTokenTradePublisher: RealtimeTokenTradePublisher,
) : CommandHandler<Unit, SubscribeToTokenTradeStreamCommand> {

	override val command = SubscribeToTokenTradeStreamCommand::class

	override fun handle(command: SubscribeToTokenTradeStreamCommand) {
		val pairs = tokenPairInfoFinderService.findAllByTokenAddressAndChain(
			tokenAddress = command.tokenAddress,
			chain = command.chain,
		)

		check(pairs.isNotEmpty()) {
			"Unable to find pair by token address ${command.tokenAddress} on chain ${command.chain}"
		}

		realtimeTokenTradePublisher.connectSession(
			chainAddress = command.tokenAddress.toChainAddress(command.chain),
			sessionId = command.sessionId,
		)
	}
}
