package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.wallet.command.DeleteUserWalletCommand
import com.cleevio.fatbot.application.module.wallet.exception.LastWalletDeletionException
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.application.module.wallet.locks.CREATE_OR_UPDATE_USER_WALLETS
import com.cleevio.fatbot.application.module.wallet.locks.WALLET_MODULE
import com.cleevio.fatbot.application.module.wallet.port.out.MarkOldestWalletOnChainAsDefault
import com.cleevio.fatbot.domain.market.MarketPositionDeleteService
import com.cleevio.fatbot.domain.market.MarketPositionSnapshotDeleteService
import com.cleevio.fatbot.domain.transaction.TransactionDeleteService
import com.cleevio.fatbot.domain.wallet.WalletDeleteService
import com.cleevio.fatbot.domain.walletposition.WalletCurrencyPositionDeleteService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class DeleteUserWalletCommandHandler(
	private val walletFinderService: WalletFinderService,
	private val walletDeleteService: WalletDeleteService,
	private val transactionDeleteService: TransactionDeleteService,
	private val marketPositionDeleteService: MarketPositionDeleteService,
	private val marketPositionSnapshotDeleteService: MarketPositionSnapshotDeleteService,
	private val walletCurrencyPositionDeleteService: WalletCurrencyPositionDeleteService,
	private val markOldestWalletOnChainAsDefault: MarkOldestWalletOnChainAsDefault,
) : CommandHandler<Unit, DeleteUserWalletCommand> {

	override val command = DeleteUserWalletCommand::class

	@Transactional
	@Lock(module = WALLET_MODULE, lockName = CREATE_OR_UPDATE_USER_WALLETS)
	override fun handle(@LockFieldParameter("userId") command: DeleteUserWalletCommand) {
		val wallet = walletFinderService.getByIdAndUserId(id = command.walletId, userId = command.userId)

		val walletCount = walletFinderService.countAllByUserIdAndChain(userId = command.userId, chain = wallet.chain)
		if (walletCount == 1) {
			throw LastWalletDeletionException("Unable to delete last wallet on chain ${wallet.chain}")
		}

		transactionDeleteService.deleteAllByWalletId(walletId = command.walletId)
		marketPositionDeleteService.deleteAllByWalletId(walletId = command.walletId)
		marketPositionSnapshotDeleteService.deleteAllByWalletId(walletId = command.walletId)
		walletCurrencyPositionDeleteService.deleteAllByWalletId(walletId = command.walletId)
		walletDeleteService.deleteById(walletId = command.walletId)
		if (wallet.isDefault) {
			// force flush here, so when jooq is triggered on next line, it will not consider the deleted wallet
			walletDeleteService.flush()
			markOldestWalletOnChainAsDefault(userId = command.userId, chain = wallet.chain)
		}
	}
}
