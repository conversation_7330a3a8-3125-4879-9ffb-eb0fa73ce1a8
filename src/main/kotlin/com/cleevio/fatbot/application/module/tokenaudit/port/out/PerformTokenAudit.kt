package com.cleevio.fatbot.application.module.tokenaudit.port.out

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain

interface PerformTokenAudit {

	data class Result(val issues: List<AuditIssue>, val riskFactor: RiskFactor, val riskFactorReason: String)

	data class AuditIssue(val summary: String, val detail: String, val severity: IssueSeverity)

	operator fun invoke(tokenAddress: AddressWrapper, chain: Chain)

	enum class IssueSeverity {
		HIGH,
		MEDIUM,
		LOW,
	}

	enum class RiskFactor {
		RED,
		ORANGE,
		GREEN,
	}
}
