package com.cleevio.fatbot.application.module.botleaderboard.service

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.times
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.module.botmarket.finder.BotMarketPositionFinderService
import com.cleevio.fatbot.application.module.botportfolio.BotPortfolioValueService
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.botleaderboard.BotLeaderboardSnapshotCreateService
import com.cleevio.fatbot.domain.botleaderboard.BotLeaderboardSnapshotDeleteService
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.Clock
import java.time.Instant

@Service
class BotLeaderboardSnapshotService(
	private val botPortfolioValueService: BotPortfolioValueService,
	private val botWalletFinderService: BotWalletFinderService,
	private val botMarketPositionFinderService: BotMarketPositionFinderService,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val botLeaderboardSnapshotCreateService: BotLeaderboardSnapshotCreateService,
	private val botLeaderboardSnapshotDeleteService: BotLeaderboardSnapshotDeleteService,
	private val clock: Clock,
) {

	fun createLeaderboardSnapshot() {
		val botIdToPortfolioValueUsd = botPortfolioValueService.getBotPortfolioValues(
			userId = null,
			botIds = null,
		)

		val botWallets = botWalletFinderService.findAll()
		val botWalletIdToMarketPositions = botMarketPositionFinderService.findAll()
			.groupBy { it.botWalletId }

		val currentExchangeRate = getUsdExchangeRate(CryptoCurrency.SOL)

		val botWalletToBotValueUsd = botWallets.associateWith { botWallet ->
			botIdToPortfolioValueUsd[botWallet.botId] ?: botWalletIdToMarketPositions[botWallet.id]?.let {
				botWallet.balance.asBaseAmount().toNative(Chain.SOLANA).amount * currentExchangeRate
			} ?: BigDecimal.ZERO
		}

		val profitableBotWalletsToBotValueUsd = botWalletToBotValueUsd.filter { (wallet, botValueUsd) ->
			botValueUsd > wallet.acquisitionValueUsd
		}

		profitableBotWalletsToBotValueUsd.forEach { (botWallet, botValueUsd) ->
			botLeaderboardSnapshotCreateService.create(
				botId = botWallet.botId,
				profitValueUsd = botValueUsd - botWallet.acquisitionValueUsd,
				profitValueFraction = calculateFractionChange(botValueUsd, botWallet.acquisitionValueUsd),
				balanceUsd = botValueUsd,
				snapshotMadeAt = Instant.now(clock),
			)
		}

		botLeaderboardSnapshotDeleteService.deleteOldSnapshots()
	}
}
