package com.cleevio.fatbot.application.module.userfattyleagueseason.query

import com.cleevio.fatbot.application.common.query.Query
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

data class GetUserFattyLeagueSeasonsQuery(
	val userId: UUID,
) : Query<GetUserFattyLeagueSeasonsQuery.Result> {

	@Schema(name = "GetUserFattyLeagueSeasonsResult")
	data class Result(
		val userFattyLeagueSeasons: List<UserFattyLeagueSeasonResult>,
		val totalClaimedTokens: BigDecimal,
		val totalClaimableTokens: BigDecimal,
	)

	@Schema(name = "UserFattyLeagueSeasonResult")
	data class UserFattyLeagueSeasonResult(
		val id: UUID,
		val seasonName: String,
		val earnedTokens: BigDecimal,
		val claimableTokens: BigDecimal,
		val claimedTokens: BigDecimal,
		val expiredTokens: BigDecimal,
		val claimableTokensNow: BigDecimal,
		val nextClaimableAt: Instant?,
		val areAllTokensClaimed: Boolean,
		val tokensExpireAt: Instant?,
		val isExpired: Boolean,
	)
}
