package com.cleevio.fatbot.application.module.user.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class UserNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.USER_NOT_FOUND,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class ReferralCodeNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.REFERRAL_CODE_NOT_FOUND,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class ReferralCodeNotValidException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.REFERRAL_CODE_NOT_VALID,
	message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ReferralCodeAlreadyTakenException(message: String) :
	FatbotApiException(reason = ExtendedErrorReasonType.REFERRAL_CODE_ALREADY_IN_USE, message = message)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class ReferralCodeAlreadySetException(message: String) :
	FatbotApiException(reason = ExtendedErrorReasonType.REFERRAL_CODE_ALREADY_SET, message = message)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class IllegalQuickBuyAmount(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.ILLEGAL_QUICK_BUY_VALUE,
	message = message,
)
