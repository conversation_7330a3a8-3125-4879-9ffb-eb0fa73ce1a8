package com.cleevio.fatbot.application.module.userfattycard

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.fattycard.properties.FattyCardProperties
import com.cleevio.fatbot.application.module.userfattycard.port.out.GetUserFattyCardsOverview
import com.cleevio.fatbot.application.module.userfattycard.query.GetUserFattyCardsOverviewQuery
import org.springframework.stereotype.Component

@Component
class GetUserFattyCardsOverviewQueryHandler(
	private val getUserFattyCardsOverview: GetUserFattyCardsOverview,
	private val fattyCardProperties: FattyCardProperties,
) : QueryHandler<GetUserFattyCardsOverviewQuery.Result, GetUserFattyCardsOverviewQuery> {
	override val query = GetUserFattyCardsOverviewQuery::class

	override fun handle(query: GetUserFattyCardsOverviewQuery): GetUserFattyCardsOverviewQuery.Result {
		return getUserFattyCardsOverview(
			userId = query.userId,
			tradeAmountForFattyCard = fattyCardProperties.tradeAmountNeededForCard,
			maxCardsEarnedInOneDay = fattyCardProperties.maxCardsEarnedInDay,
		)
	}
}
