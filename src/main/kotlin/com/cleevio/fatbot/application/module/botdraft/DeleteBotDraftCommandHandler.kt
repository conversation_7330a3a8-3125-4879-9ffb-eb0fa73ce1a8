package com.cleevio.fatbot.application.module.botdraft

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.botdraft.command.DeleteBotDraftCommand
import com.cleevio.fatbot.application.module.botdraft.finder.BotDraftFinderService
import com.cleevio.fatbot.application.module.botdraft.locks.BOT_DRAFT_MODULE
import com.cleevio.fatbot.application.module.botdraft.locks.BOT_DRAFT_UPDATE
import com.cleevio.fatbot.domain.botdraft.BotDraftDeleteService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class DeleteBotDraftCommandHandler(
	private val botDraftFinderService: BotDraftFinderService,
	private val botDraftDeleteService: BotDraftDeleteService,
) : Command<PERSON>andler<Unit, DeleteBotDraftCommand> {

	override val command = DeleteBotDraftCommand::class

	@Transactional
	@Lock(module = BOT_DRAFT_MODULE, lockName = BOT_DRAFT_UPDATE)
	override fun handle(@LockFieldParameter("userId") command: DeleteBotDraftCommand) {
		val botDraft = botDraftFinderService.getByIdAndUserId(command.botDraftId, command.userId)

		botDraftDeleteService.deleteById(botDraft.id)
	}
}
