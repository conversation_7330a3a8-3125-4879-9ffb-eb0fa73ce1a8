package com.cleevio.fatbot.application.module.walletposition

import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.exchangerate.port.out.GetLatestExchangeRateSnapshots
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.port.out.GetLatestWalletCurrencyPositions
import com.cleevio.fatbot.application.module.walletposition.query.SearchUserWalletCurrencyPositionsQuery
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.BigInteger
import java.math.MathContext
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit

@Component
class SearchUserWalletPositionsQueryHandler(
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val findWalletCurrencyPositions: GetLatestWalletCurrencyPositions,
	private val getLatestExchangeRateSnapshots: GetLatestExchangeRateSnapshots,
	private val userFinderService: FirebaseUserFinderService,
	private val properties: ChainProperties,
	private val usdConverter: UsdConverter,
	private val clock: Clock,
) : QueryHandler<List<SearchUserWalletCurrencyPositionsQuery.Result>, SearchUserWalletCurrencyPositionsQuery> {

	override val query = SearchUserWalletCurrencyPositionsQuery::class

	data class AggregatedWalletCurrencyPosition(
		val chain: Chain,
		val balance: NativeAmount,
		val balanceAcquisitionCostUsd: BigDecimal,
	)

	override fun handle(
		query: SearchUserWalletCurrencyPositionsQuery,
	): List<SearchUserWalletCurrencyPositionsQuery.Result> {
		val chains = when (query.useSelectedChains) {
			true -> userFinderService.getById(id = query.userId).selectedChains
			false -> properties.enabledChains
		}

		val walletCurrencyPositions = findWalletCurrencyPositions(
			userId = query.userId,
			walletId = query.filter.walletId,
			chains = chains,
			createdBefore = null,
		)

		val aggregatedPositions = walletCurrencyPositions.aggregateByChain()

		val currencies = chains.mapToSet { it.currency }
		val now = Instant.now(clock)

		val currencyToRate1h = getLatestExchangeRateSnapshots(
			currencies = currencies,
			validBefore = now.minus(1, ChronoUnit.HOURS),
		)
		val currencyToRate24h = getLatestExchangeRateSnapshots(
			currencies = currencies,
			validBefore = now.minus(24, ChronoUnit.HOURS),
		)

		// 	Note: This will be extracted into a calculator similar to MarketPositionCalculator
		return aggregatedPositions.map { position ->
			val currency = position.chain.currency

			val exchangeRate = getUsdExchangeRate(currency)
			val exchangeRate1h = currencyToRate1h[currency]
			val exchangeRate24h = currencyToRate24h[currency]

			val change1hFraction = exchangeRate1h?.let { _ -> calculateFractionChange(exchangeRate, exchangeRate1h) }
			val change24hFraction = exchangeRate24h?.let { _ -> calculateFractionChange(exchangeRate, exchangeRate24h) }

			val currentValueUsd = usdConverter.nativeToUsd(position.balance, position.chain)

			val (pnlUsd, pnlFraction) = if (position.balance.amount > BigDecimal.ZERO) {
				val averageNativePriceUsd = position.balanceAcquisitionCostUsd.divide(
					position.balance.amount,
					MathContext.DECIMAL128,
				)

				val pnlUsd = currentValueUsd - position.balanceAcquisitionCostUsd
				val pnlFraction = calculateFractionChange(exchangeRate, averageNativePriceUsd)

				pnlUsd to pnlFraction
			} else {
				BigDecimal.ZERO to BigDecimal.ZERO
			}

			SearchUserWalletCurrencyPositionsQuery.Result(
				chain = position.chain,
				currentPriceUsd = exchangeRate,
				currentValueNative = position.balance,
				currentValueUsd = currentValueUsd,
				oneDayChangeFraction = change24hFraction,
				oneHourChangeFraction = change1hFraction,
				pnlUsd = pnlUsd,
				pnlFraction = pnlFraction,
			)
		}
	}

	fun List<GetLatestWalletCurrencyPositions.WalletCurrencyPosition>.aggregateByChain() = this
		.groupBy { it.chain }
		.map { (chain, positions) ->
			val balanceSum = positions.sumOf { it.totalBought - it.totalSold }.asBaseAmount().toNative(chain)

			val balanceAcquisitionCostUsdSum = positions.sumOf {
				if (it.totalBought == BigInteger.ZERO) return@sumOf BigDecimal.ZERO

				val positionAverageCost = it.totalAcquisitionCostUsd.divide(
					it.totalBought.toBigDecimal(),
					MathContext.DECIMAL128,
				)

				val positionBalance = it.totalBought - it.totalSold

				positionBalance.toBigDecimal() * positionAverageCost
			}

			AggregatedWalletCurrencyPosition(
				chain = chain,
				balance = balanceSum,
				balanceAcquisitionCostUsd = balanceAcquisitionCostUsdSum,
			)
		}
}
