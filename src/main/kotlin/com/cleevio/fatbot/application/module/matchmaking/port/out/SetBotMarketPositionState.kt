package com.cleevio.fatbot.application.module.matchmaking.port.out

import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionSellReason
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import java.util.UUID

interface SetBotMarketPositionState {
	operator fun invoke(
		botMarketPositionId: UUID,
		state: BotMarketPositionState,
		sellReason: BotMarketPositionSellReason?,
	)
}
