package com.cleevio.fatbot.application.module.tokenaudit

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.tokenaudit.command.PerformTokenAuditCommand
import com.cleevio.fatbot.application.module.tokenaudit.port.out.PerformTokenAudit
import com.cleevio.fatbot.application.module.tokenaudit.service.TokenAuditFinderService
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.domain.tokenaudit.TokenAuditResult
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class PerformTokenAuditCommandHandler(
	private val performTokenAudit: PerformTokenAudit,
	private val tokenAuditFinderService: TokenAuditFinderService,
) : CommandHandler<Unit, PerformTokenAuditCommand> {

	override val command = PerformTokenAuditCommand::class

	override fun handle(command: PerformTokenAuditCommand) {
		require(command.tokenAddress.chain.type == ChainType.EVM) { "Only EVM token audit is supported." }

		performTokenAudit(
			tokenAddress = command.tokenAddress.address,
			chain = command.tokenAddress.chain,
		)
	}

	@Transactional
	fun setAuditResult(tokenAddress: ChainAddress, auditResult: PerformTokenAudit.Result) {
		val tokenAuditEntity = tokenAuditFinderService.getByTokenAddress(tokenAddress)

		tokenAuditEntity.setAuditResult(
			TokenAuditResult(
				issues = auditResult.issues.map {
					TokenAuditResult.TokenAuditIssue(
						summary = it.summary,
						detail = it.detail,
						severity = it.severity,
					)
				},
				riskFactor = auditResult.riskFactor,
				riskFactorReason = auditResult.riskFactorReason,
			),
		)
	}
}
