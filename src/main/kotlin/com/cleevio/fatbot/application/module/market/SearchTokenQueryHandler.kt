package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.port.out.GetTokenDetailFromDexScreener
import com.cleevio.fatbot.application.module.market.query.GetTokenDetailQuery
import com.cleevio.fatbot.application.module.market.query.SearchTokenQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.stereotype.Component

@Component
class SearchTokenQueryHandler(
	private val getTokenDetailFromDexScreenerOrNull: GetTokenDetailFromDexScreener,
	private val chainProperties: ChainProperties,
) : QueryHandler<List<SearchTokenQuery.Result>, SearchTokenQuery> {

	override val query = SearchTokenQuery::class

	override fun handle(query: SearchTokenQuery): List<SearchTokenQuery.Result> {
		return when (query.tokenAddress.addressType) {
			ChainType.EVM -> handleEvmTokenSearch(query.tokenAddress)
			ChainType.SOLANA -> handleSolanaTokenSearch(query.tokenAddress)
		}
	}

	private fun handleEvmTokenSearch(tokenAddress: AddressWrapper): List<SearchTokenQuery.Result> {
		val evmChains = chainProperties.enabledEvmChains

		val chainAddresses = evmChains.map { tokenAddress.toChainAddress(it) }

		return chainAddresses.mapNotNull { tokenChainAddress ->
			val dexScreenerResult = getTokenDetailFromDexScreenerOrNull(token = tokenChainAddress)

			dexScreenerResult?.toResult(tokenChainAddress.chain)
		}
	}

	private fun handleSolanaTokenSearch(tokenAddress: AddressWrapper): List<SearchTokenQuery.Result> {
		val token = ChainAddress(chain = Chain.SOLANA, address = tokenAddress)
		val dexScreenerResult = getTokenDetailFromDexScreenerOrNull(token = token)
		val result = dexScreenerResult?.toResult(Chain.SOLANA)

		return listOfNotNull(result)
	}

	private fun GetTokenDetailQuery.Detail.toResult(chain: Chain): SearchTokenQuery.Result {
		val casted = this as GetTokenDetailQuery.DexDetail

		return SearchTokenQuery.Result(
			chain = chain,
			imageUrl = casted.dexInfo.info?.imageUrl,
			token = casted.dexInfo.baseToken,
			priceUsd = casted.dexInfo.priceUsd,
			priceChangeHours24 = casted.dexInfo.priceChange.hours24,
			oneHourVolumeUsd = casted.dexInfo.volume.hours01,
		)
	}
}
