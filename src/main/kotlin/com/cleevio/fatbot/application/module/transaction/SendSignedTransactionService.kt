package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.application.module.transaction.port.out.GetSignedTransactionInfo
import com.cleevio.fatbot.application.module.transaction.port.out.SendSignedTransaction
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class SendSignedTransactionService(
	private val sendSignedTransaction: SendSignedTransaction,
	private val getSignedTransactionInfo: GetSignedTransactionInfo,
) {

	fun sendSignedTransaction(transactionId: UUID) =
		getSignedTransactionInfo(transactionId).let { signedTransactionInfoModel ->
			signedTransactionInfoModel.signedTx?.let { signedTx ->
				sendSignedTransaction(
					SendSignedTransaction.Input(
						signedTx = signedTx,
						chain = signedTransactionInfoModel.chain,
					),
				)
			}
		}
}
