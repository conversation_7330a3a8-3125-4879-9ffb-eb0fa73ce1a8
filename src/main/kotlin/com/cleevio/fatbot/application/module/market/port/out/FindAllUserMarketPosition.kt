package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.adapter.out.jooq.FindAllUserMarketPositionJooq
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.util.UUID

interface FindAllUserMarketPosition {
	operator fun invoke(
		userId: UUID,
		walletId: UUID? = null,
		chains: Set<Chain>,
		searchString: String? = null,
		fileToUrlMapper: FileToUrlMapper,
	): List<FindAllUserMarketPositionJooq.Result>
}
