package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.market.event.UserPortfolioViewedEvent
import com.cleevio.fatbot.application.module.market.service.PortfolioOverviewService
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.application.module.wallet.query.GetAllUserWalletsQuery
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import java.net.URI

@Component
class GetAllUserWalletsQueryHandler(
	private val walletFinderService: WalletFinderService,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val userFinderService: FirebaseUserFinderService,
	private val properties: ChainProperties,
	private val portfolioOverviewService: PortfolioOverviewService,
) : QueryHandler<List<GetAllUserWalletsQuery.Result>, GetAllUserWalletsQuery> {

	override val query = GetAllUserWalletsQuery::class

	override fun handle(query: GetAllUserWalletsQuery): List<GetAllUserWalletsQuery.Result> {
		val chains = when (query.useSelectedChains) {
			true -> userFinderService.getById(query.userId).selectedChains
			false -> properties.enabledChains
		}

		val wallets = walletFinderService.findAllByUserIdAndChainIn(
			userId = query.userId,
			chains = chains,
		)

		applicationEventPublisher.publishEvent(UserPortfolioViewedEvent(query.userId))

		// TODO: This is not too optimal as we are doing wallets.size amount of DB selects
		return wallets.map {
			val walletPortfolioOverview = portfolioOverviewService.getForWallet(
				userId = it.userId,
				walletId = it.id,
			)

			GetAllUserWalletsQuery.Result(
				walletId = it.id,
				isDefault = it.isDefault,
				walletAddress = it.address,
				walletDetailUrl = URI(it.address.getLinkToExplorer(it.chain)),
				walletBalance = walletPortfolioOverview.walletBalance,
				walletBalanceUsd = walletPortfolioOverview.walletBalanceUsd,
				customName = it.customName,
				chain = it.chain,
				currentPortfolioValueUsd = walletPortfolioOverview.totalValueAmountUsd,
				currentPortfolioValueChangeUsd = walletPortfolioOverview.totalPnlAmountUsd,
				currentPortfolioValueChangeFraction = walletPortfolioOverview.totalPnlAmountFraction,
			)
		}
	}
}
