package com.cleevio.fatbot.application.module.wallet.constant

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency.BNB
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency.ETH
import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency.SOL
import com.cleevio.fatbot.application.module.market.exception.UnsupportedChainIdException

enum class Chain(
	val type: ChainType,
	val id: Long?,
) {
	EVM_MAINNET(ChainType.EVM, 1),
	EVM_BASE(ChainType.EVM, 8453),
	EVM_BSC(ChainType.EVM, 56),
	EVM_ARBITRUM_ONE(ChainType.EVM, 42161),
	SOLANA(ChainType.SOLANA, null),
	;

	companion object {
		fun ofEVM(chainId: Long) = entries.find { it.type == ChainType.EVM && it.id == chainId }
			?: throw UnsupportedChainIdException("Chain ID $chainId not supported.")

		fun allEvm() = entries.filter { it.type == ChainType.EVM }
	}

	val evmId: Long
		get() {
			assert(type == ChainType.EVM)
			return id!!
		}

	val currency: CryptoCurrency
		get() = when (this) {
			EVM_MAINNET -> ETH
			EVM_BASE -> ETH
			EVM_BSC -> BNB
			EVM_ARBITRUM_ONE -> ETH
			SOLANA -> SOL
		}
}

enum class ChainType(val title: String) {
	EVM("EVM"),
	SOLANA("Solana"),
}
