package com.cleevio.fatbot.application.module.market.service

import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.module.exchangerate.port.out.GetLatestExchangeRateSnapshots
import com.cleevio.fatbot.application.module.wallet.port.out.GetLatestWalletCurrencyPositions
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger
import java.math.MathContext
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

@Service
class PortfolioOverviewService(
	private val aggregateMarketPositionsService: AggregateMarketPositionsService,
	private val clock: Clock,
	private val getLatestWalletCurrencyPositions: GetLatestWalletCurrencyPositions,
	private val usdConverter: UsdConverter,
	private val getLatestExchangeRateSnapshots: GetLatestExchangeRateSnapshots,
	private val properties: ChainProperties,
) {

	data class WalletResult(
		val totalValueAmountUsd: BigDecimal,
		val totalPnlAmountUsd: BigDecimal,
		val totalPnlAmountFraction: BigDecimal,
		val oneDayChangeAmountUsd: BigDecimal,
		val oneDayChangeFraction: BigDecimal,
		val walletBalance: NativeAmount,
		val walletBalanceUsd: BigDecimal,
	)

	fun getForWallet(userId: UUID, walletId: UUID): WalletResult {
		val now = Instant.now(clock)

		val marketPositionSumNow = aggregateMarketPositionsService.byChainNow(
			userId = userId,
			chains = properties.enabledChains,
			walletId = walletId,
		)

		val marketPositionsValueUsd24hAgo = aggregateMarketPositionsService.totalValueUsd24hAgo(
			userId = userId,
			walletId = walletId,
			chains = properties.enabledChains,
			now = now,
		)

		val walletCurrencyPositionNow = getLatestWalletCurrencyPositions(
			userId = userId,
			walletId = walletId,
			chains = properties.enabledChains,
			createdBefore = null,
		).singleOrNull()

		val walletCurrencyPosition24hAgo = getLatestWalletCurrencyPositions(
			userId = userId,
			walletId = walletId,
			chains = properties.enabledChains,
			createdBefore = now.minus(24, ChronoUnit.HOURS),
		).singleOrNull()

		val (walletBalance, walletBalanceUsd) = walletCurrencyPositionNow?.let {
			val balance = (it.totalBought - it.totalSold).asBaseAmount().toNative(it.chain)
			val balanceUsd = usdConverter.nativeToUsd(balance, it.chain)

			balance to balanceUsd
		} ?: (NativeAmount.ZERO to BigDecimal.ZERO)

		// Current value
		val marketValueUsdNow = marketPositionSumNow.entries.sumOf { (chain, result) ->
			usdConverter.nativeToUsd(result.currentValue, chain)
		}

		val totalValueUsdNow = marketValueUsdNow + walletBalanceUsd

		// Current PnL Fraction
		val walletBalanceAcquisitionCostUsdNow = walletCurrencyPositionNow?.let {
			if (it.totalBought == BigInteger.ZERO) return@let BigDecimal.ZERO
			val averageBuy = it.totalAcquisitionCostUsd.divide(it.totalBought.toBigDecimal(), MathContext.DECIMAL128)

			val balance = it.totalBought - it.totalSold

			balance.toBigDecimal() * averageBuy
		} ?: BigDecimal.ZERO

		val marketValueAcquisitionCostUsdNow = marketPositionSumNow.entries.sumOf { (chain, result) ->
			usdConverter.nativeToUsd(result.acquisitionValue, chain)
		}
		val totalAcquisitionUsdNow = marketValueAcquisitionCostUsdNow + walletBalanceAcquisitionCostUsdNow

		val pnlFractionNow = calculateFractionChange(totalValueUsdNow, totalAcquisitionUsdNow)

		// Current PnL USD
		val walletPnlUsdNow = walletBalanceUsd - walletBalanceAcquisitionCostUsdNow
		val marketPnlUsdNow = marketPositionSumNow.entries.sumOf { (chain, result) ->
			usdConverter.nativeToUsd(result.currentPnl, chain)
		}
		val totalPnlUsdNow = marketPnlUsdNow + walletPnlUsdNow

		// oneDay change amount Usd
		val walletBalance24hUsd = walletCurrencyPosition24hAgo?.let {
			val currencyToRateUsd24h = getLatestExchangeRateSnapshots(
				currencies = setOf(it.chain.currency),
				validBefore = now.minus(24, ChronoUnit.HOURS),
			)

			val balance = it.totalBought - it.totalSold
			val exchangeRate = currencyToRateUsd24h[it.chain.currency]

			usdConverter.baseToUsd(balance.asBaseAmount(), it.chain, exchangeRate)
		} ?: BigDecimal.ZERO

		val totalValue24hUsd = walletBalance24hUsd + marketPositionsValueUsd24hAgo

		val oneDayChangeUsd = totalValueUsdNow - totalValue24hUsd
		val oneDayChangeFraction = calculateFractionChange(totalValueUsdNow, totalValue24hUsd)

		return WalletResult(
			totalValueAmountUsd = totalValueUsdNow,
			totalPnlAmountUsd = totalPnlUsdNow,
			totalPnlAmountFraction = pnlFractionNow,
			oneDayChangeAmountUsd = oneDayChangeUsd,
			oneDayChangeFraction = oneDayChangeFraction,
			walletBalance = walletBalance,
			walletBalanceUsd = walletBalanceUsd,
		)
	}
}
