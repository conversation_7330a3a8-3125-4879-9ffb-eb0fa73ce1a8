package com.cleevio.fatbot.application.module.token.command

import com.cleevio.fatbot.application.common.command.Command
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigInteger

data class SaveTokenInfoCommand(
	val chain: Chain,
	val tokenAddress: AddressWrapper,
	val pairAddress: AddressWrapper,
	val decimals: BigInteger,
	val name: String,
	val symbol: String,
	val imageUrl: String?,
) : Command<Unit>

data class SaveTokenPairAddressInfoCommand(
	val chain: Chain,
	val dex: GetDex.Result,
	val tokenAddress: AddressWrapper,
	val tokenDecimals: BigInteger,
) : Command<Unit>

data class BackfillTokenPricesCommand(
	val chain: Chain,
	val tokenAddress: AddressWrapper,
	val pairAddress: AddressWrapper,
) : Command<Unit>

data class TokenPromotedToPumpswapCommand(
	val blockSlot: Int,
	val tokenAddress: AddressWrapper,
	val creator: AddressWrapper,
	val pairAddress: AddressWrapper,
) : Command<Unit>

data class SubscribeToTokenTradeStreamCommand(
	val sessionId: String,
	val tokenAddress: AddressWrapper,
	val chain: Chain,
) : Command<Unit>

data class UpdateTokenImageCommand(
	val tokenAddress: AddressWrapper,
	val chain: Chain,
	val imageUrl: String,
) : Command<Unit>
