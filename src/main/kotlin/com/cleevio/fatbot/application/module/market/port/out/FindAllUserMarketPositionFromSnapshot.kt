package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.adapter.out.jooq.FindAllUserMarketPositionFromSnapshotJooq
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.time.Instant
import java.util.UUID

interface FindAllUserMarketPositionFromSnapshot {
	operator fun invoke(
		userId: UUID,
		chains: Set<Chain>?,
		walletId: UUID?,
		snapshotMadeAt: Instant,
	): List<FindAllUserMarketPositionFromSnapshotJooq.Result>
}
