package com.cleevio.fatbot.application.module.transaction.port.out

import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.domain.transaction.Transaction
import java.util.UUID

interface DecodeSignedTransaction {

	operator fun invoke(
		walletId: UUID,
		signedTx: SignedTx,
		transactionType: TransactionType,
		tokenAddress: AddressWrapper?,
	): Transaction
}
