package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.bot.command.ResetBotBuyFrequencyCommand
import com.cleevio.fatbot.application.module.bot.locks.BOT_MODULE
import com.cleevio.fatbot.application.module.bot.locks.UPDATE_BOT
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class ResetBotBuyFrequencyCommandHandler(
	private val botFinderService: BotFinderService,
) : CommandHandler<Unit, ResetBotBuyFrequencyCommand> {

	override val command = ResetBotBuyFrequencyCommand::class

	@Transactional
	@Lock(module = BOT_MODULE, lockName = UPDATE_BOT)
	override fun handle(@LockFieldParameter("userId") command: ResetBotBuyFrequencyCommand) {
		// TODO: As we do not have lock acquired for the bot trading, it's possible
		//  that while this is updated, we already hold another version of bot entity in memory
		//  and Optimistic lock exception might happen. Maybe safer way here would be use Jooq directly to
		//  and not touch Hibernate at all (and just do remainingTrades = N in plain SQL)
		botFinderService.getByIdAndUserId(id = command.botId, userId = command.userId).resetRemainingBuyFrequency()
	}
}
