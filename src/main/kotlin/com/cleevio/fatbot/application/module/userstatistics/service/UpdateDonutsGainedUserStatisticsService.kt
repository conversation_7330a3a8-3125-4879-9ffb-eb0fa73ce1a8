package com.cleevio.fatbot.application.module.userstatistics.service

import com.cleevio.fatbot.application.module.userleaderbord.finder.UserLeaderboardFinderService
import com.cleevio.fatbot.application.module.userstatistics.finder.UserStatisticsFinderService
import com.cleevio.fatbot.application.module.userstatistics.locks.UPDATE_USER_STATISTICS
import com.cleevio.fatbot.application.module.userstatistics.locks.USER_STATISTICS_MODULE
import com.cleevio.fatbot.application.module.userstatistics.properties.UserStreakMultiplierProperties
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.util.UUID

@Service
class UpdateDonutsGainedUserStatisticsService(
	private val userLeaderboardFinderService: UserLeaderboardFinderService,
	private val userStatisticsFinderService: UserStatisticsFinderService,
	private val userStreakMultiplierProperties: UserStreakMultiplierProperties,
) {

	@Transactional
	@Lock(module = USER_STATISTICS_MODULE, lockName = UPDATE_USER_STATISTICS)
	operator fun invoke(@LockArgumentParameter userId: UUID, tradeUsdAmount: BigDecimal) {
		val donutMultiplierByRank = userLeaderboardFinderService.findByUserId(userId)?.donutMultiplier ?: BigDecimal.ONE
		val userStatistics = userStatisticsFinderService.getByUserId(userId)
		val streakMultiplier = userStreakMultiplierProperties.getCurrentMultiplier(userStatistics.daysInStreak)

		userStatistics.updateDonuts(donuts = tradeUsdAmount * donutMultiplierByRank * streakMultiplier)
	}
}
