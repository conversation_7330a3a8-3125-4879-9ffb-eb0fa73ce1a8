package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.domain.token.RaydiumAmmMarketData
import com.cleevio.fatbot.domain.token.RaydiumCPMMMarketData
import java.math.BigInteger

interface GetAmountOutRaydium {

	fun getAmountOutAMM(
		amount: BaseAmount,
		pairAddress: AddressWrapper,
		marketData: RaydiumAmmMarketData,
		isBuy: Boolean,
	): BaseAmount

	fun getAmountOutCLMM(
		amount: BaseAmount,
		pairAddress: AddressWrapper,
		tokenDecimals: BigInteger,
		isBuy: Boolean,
	): BaseAmount

	fun getAmountOutCPMM(
		amount: BaseAmount,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		marketData: RaydiumCPMMMarketData,
		isBuy: Boolean,
	): BaseAmount
}
