package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector.Companion.ETHERSCAN_RATE_LIMIT_COOLDOWN
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.util.runWithRetry
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.file.client.FileDownloadClient
import com.cleevio.fatbot.application.module.token.command.SaveTokenInfoCommand
import com.cleevio.fatbot.application.module.token.event.NewEvmTokenInfoCreatedEvent
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.token.port.out.GetIsTokenContractVerified
import com.cleevio.fatbot.application.module.token.port.out.GetSplTokenAccountInfo
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.domain.token.EvmTokenInfoCreateService
import com.cleevio.fatbot.infrastructure.config.logger
import org.p2p.solanaj.core.PublicKey
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component

private const val IS_VERIFIED_RETRY_LIMIT = 5

@Component
class SaveEvmTokenInfoCommandHandler(
	private val evmTokenInfoFinderService: EvmTokenInfoFinderService,
	private val fileDownloadClient: FileDownloadClient,
	private val evmTokenInfoCreateService: EvmTokenInfoCreateService,
	private val getTokenAccountInfo: GetSplTokenAccountInfo,
	private val isTokenContractVerified: GetIsTokenContractVerified,
	private val applicationEventPublisher: ApplicationEventPublisher,
) : CommandHandler<Unit, SaveTokenInfoCommand> {

	override val command = SaveTokenInfoCommand::class

	private val logger = logger()

	override fun handle(command: SaveTokenInfoCommand) {
		val token = evmTokenInfoFinderService.findByTokenAddressAndChain(command.tokenAddress, command.chain)
		if (token != null) return

		val imageFile = command.imageUrl?.let {
			runCatching { fileDownloadClient.downloadAndSaveFile(it) }.onFailure {
				logger.warn("Failed to downloadAndSave imageUrl ${command.imageUrl} for token ${command.tokenAddress}")
			}
		}?.getOrNull()

		evmTokenInfoCreateService.create(
			chain = command.chain,
			tokenAddress = command.tokenAddress,
			decimals = command.decimals,
			name = command.name,
			symbol = command.symbol,
			isToken2022 = isToken2022(command.tokenAddress),
			isVerified = isTokenVerified(command.tokenAddress, command.chain),
			imageFileId = imageFile?.id,
		)

		applicationEventPublisher.publishEvent(
			NewEvmTokenInfoCreatedEvent(
				chain = command.chain,
				tokenAddress = command.tokenAddress,
				pairAddress = command.pairAddress,
			),
		)
	}

	private fun isToken2022(address: AddressWrapper): Boolean {
		if (!address.isSolana()) return false

		val tokenInfo = getTokenAccountInfo(address)

		return PublicKey(tokenInfo.value.owner) == SolanaConstants.TOKEN_2022_PROGRAM_ID
	}

	private fun isTokenVerified(address: AddressWrapper, chain: Chain): Boolean {
		if (chain.type != ChainType.EVM) return true

		val isVerified = runWithRetry(
			retries = IS_VERIFIED_RETRY_LIMIT,
			retryDelay = ETHERSCAN_RATE_LIMIT_COOLDOWN,
			block = { runCatching { isTokenContractVerified(address, chain.evmId) } },
		).getOrNull()

		checkNotNull(isVerified) {
			"Failed to fetch isVerified information about token $address on chain $chain with $IS_VERIFIED_RETRY_LIMIT attempts."
		}

		return isVerified
	}
}
