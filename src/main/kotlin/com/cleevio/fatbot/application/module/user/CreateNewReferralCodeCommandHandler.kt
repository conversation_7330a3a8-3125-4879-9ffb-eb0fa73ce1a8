package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.user.command.CreateNewReferralCodeCommand
import com.cleevio.fatbot.application.module.user.exception.ReferralCodeAlreadyTakenException
import com.cleevio.fatbot.application.module.user.exception.ReferralCodeNotValidException
import com.cleevio.fatbot.application.module.user.exception.UserNotFoundException
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.user.locks.UPDATE_USER
import com.cleevio.fatbot.application.module.user.locks.USER_MODULE
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

// Referral codes must be 4-20 long with only alphanumeric characters and underscores
const val REFERRAL_CODE_REGEX = "^[a-z0-9_]{4,20}$"

@Component
class CreateNewReferralCodeCommandHandler(
	private val firebaseUserFinderService: FirebaseUserFinderService,
) : CommandHandler<Unit, CreateNewReferralCodeCommand> {

	override val command = CreateNewReferralCodeCommand::class

	@Transactional
	@Lock(module = USER_MODULE, lockName = UPDATE_USER)
	override fun handle(@LockFieldParameter("userId") command: CreateNewReferralCodeCommand) {
		validateReferralCode(command.referralCode)

		val user = firebaseUserFinderService.findById(command.userId) ?: throw UserNotFoundException("User not found.")

		user.setNewReferralCode(command.referralCode)
	}

	private fun validateReferralCode(referralCode: String) {
		if (!referralCode.lowercase().matches(Regex(REFERRAL_CODE_REGEX))) {
			throw ReferralCodeNotValidException(
				"Referral code must be between 4 and 20 characters long and can only contain " +
					"alphanumeric characters and underscores.",
			)
		}

		if (firebaseUserFinderService.existByReferralCode(referralCode)) {
			throw ReferralCodeAlreadyTakenException("This referral code is already taken.")
		}
	}
}
