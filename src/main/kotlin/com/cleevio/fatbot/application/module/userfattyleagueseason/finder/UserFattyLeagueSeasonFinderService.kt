package com.cleevio.fatbot.application.module.userfattyleagueseason.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.module.fattyleagueseason.exception.FattyLeagueSeasonNotFoundException
import com.cleevio.fatbot.domain.userfattyleagueseason.UserFattyLeagueSeason
import com.cleevio.fatbot.domain.userfattyleagueseason.UserFattyLeagueSeasonRepository
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class UserFattyLeagueSeasonFinderService(
	private val userFattyLeagueSeasonRepository: UserFattyLeagueSeasonRepository,
) : BaseFinderService<UserFattyLeagueSeason>(userFattyLeagueSeasonRepository) {

	override fun errorBlock(message: String) = throw FattyLeagueSeasonNotFoundException(message)

	override fun getEntityType() = UserFattyLeagueSeason::class

	fun getByIdAndUserId(id: UUID, userId: UUID): UserFattyLeagueSeason {
		return userFattyLeagueSeasonRepository.findByIdAndUserId(id = id, userId = userId)
			?: errorBlock("Fatty league season for user $userId not found")
	}

	fun findAllByUserId(userId: UUID): List<UserFattyLeagueSeason> {
		return userFattyLeagueSeasonRepository.findAllByUserId(userId = userId)
	}
}
