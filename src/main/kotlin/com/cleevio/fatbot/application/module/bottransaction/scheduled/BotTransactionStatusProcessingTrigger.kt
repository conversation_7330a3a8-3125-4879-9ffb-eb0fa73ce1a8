package com.cleevio.fatbot.application.module.bottransaction.scheduled

import com.cleevio.fatbot.application.module.bottransaction.locks.PROCESS_BOT_TRANSACTION_STATUSES
import com.cleevio.fatbot.application.module.bottransaction.service.BotTransactionProcessingService
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class BotTransactionStatusProcessingTrigger(
	private val botTransactionProcessingService: BotTransactionProcessingService,
) {

	@Scheduled(cron = "\${fatbot.bot-transaction.status-processing.cron}")
	@SchedulerLock(
		name = PROCESS_BOT_TRANSACTION_STATUSES,
		lockAtLeastFor = "\${fatbot.bot-transaction.status-processing.lock-for}",
		lockAtMostFor = "\${fatbot.bot-transaction.status-processing.lock-for}",
	)
	fun trigger() {
		botTransactionProcessingService.tryStartProcessor()
	}
}
