package com.cleevio.fatbot.application.module.userleaderbord.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.FORBIDDEN)
class IllegalUserLeaderboardAccessException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.ILLEGAL_USER_LEADERBOARD_ACCESS,
	message = message,
)
