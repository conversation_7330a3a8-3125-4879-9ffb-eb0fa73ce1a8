package com.cleevio.fatbot.application.module.matchmaking.port.out

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain

interface UpsertBotTokenInfo {

	data class Input(
		val chain: Chain,
		val tokenAddress: AddressWrapper,
		val creator: AddressWrapper,
		val name: String,
		val symbol: String,
		val infoUrl: String?,
	)

	operator fun invoke(inputs: List<Input>)
}
