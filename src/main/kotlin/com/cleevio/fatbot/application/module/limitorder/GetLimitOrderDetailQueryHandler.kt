package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.limitorder.port.out.GetLimitOrderDetail
import com.cleevio.fatbot.application.module.limitorder.query.GetLimitOrderDetailQuery
import org.springframework.stereotype.Component

@Component
class GetLimitOrderDetailQueryHandler(
	private val getLimitOrderDetail: GetLimitOrderDetail,
) : QueryHandler<GetLimitOrderDetailQuery.Result, GetLimitOrderDetailQuery> {

	override val query = GetLimitOrderDetailQuery::class

	override fun handle(query: GetLimitOrderDetailQuery): GetLimitOrderDetailQuery.Result {
		return getLimitOrderDetail(userId = query.userId, limitOrderId = query.limitOrderId)
	}
}
