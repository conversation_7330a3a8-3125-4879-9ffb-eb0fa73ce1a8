package com.cleevio.fatbot.application.module.transaction.constant

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import java.math.BigInteger
import java.util.UUID

enum class TransactionStatus {
	PENDING,
	SUCCESS,
	FAILED,
	NOT_LANDED,
}

enum class TransactionFailureReason {
	SLIPPAGE,
	RETRY_LIMIT_EXCEEDED,
	UNDEFINED,
}

sealed interface TransactionResult {
	val status: TransactionStatus
}

open class TransactionSuccess : TransactionResult {
	override val status: TransactionStatus = TransactionStatus.SUCCESS
}

data class TransferTokenTransactionSuccess(
	val amountIn: BigInteger,
	val amountOut: BigInteger,
) : TransactionSuccess()

data class TransferCurrencyTransactionSuccess(
	val value: BigInteger,
	val fee: BigInteger,
) : TransactionSuccess()

data class BuySellTransactionSuccess(
	val amountIn: BigInteger,
	val amountOut: BigInteger,
	val fee: BigInteger,
	val referralFee: BigInteger,
	val referralRewardRecipient: UUID,
	val balanceChange: BaseAmount,
) : TransactionSuccess()

class TransactionFailure(
	val balanceChange: BaseAmount,
	val isNoTokensOwnedErrorHint: Boolean?,
	val failReason: TransactionFailureReason,
) : TransactionResult {
	override val status = TransactionStatus.FAILED
}

class TransactionPending : TransactionResult {
	override val status = TransactionStatus.PENDING
}
