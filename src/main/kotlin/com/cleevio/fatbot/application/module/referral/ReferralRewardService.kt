package com.cleevio.fatbot.application.module.referral

import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.util.toUUID
import com.cleevio.fatbot.application.module.user.exception.UserNotFoundException
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.referral.ReferralRewardCreateService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigInteger
import java.util.UUID

@Service
class ReferralRewardService(
	private val referralRewardCreateService: ReferralRewardCreateService,
	private val userFinderService: FirebaseUserFinderService,
) {

	@Transactional
	fun saveReferralReward(userId: UUID, txHash: TxHash, amount: BigInteger, chain: Chain) {
		if (userId != ZERO_UUID) {
			assertUserExists(userId)
			referralRewardCreateService.createReferralReward(
				userId = userId,
				txHash = txHash,
				weiAmount = amount,
				chain = chain,
			)
		}
	}

	/**
	 * SC may return ZERO_UUID instead of null to indicate that the user who
	 * executed the transaction was not referred by anyone
	 */
	private fun assertUserExists(userId: UUID) {
		if (!userFinderService.existsById(userId)) throw UserNotFoundException("User $userId not found")
	}

	companion object {
		val ZERO_UUID = "00000000-0000-0000-0000-000000000000".toUUID()
	}
}
