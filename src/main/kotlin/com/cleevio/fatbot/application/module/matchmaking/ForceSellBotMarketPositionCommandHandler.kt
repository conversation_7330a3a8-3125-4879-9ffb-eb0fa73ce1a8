package com.cleevio.fatbot.application.module.matchmaking

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.bot.BotFinderService
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionSellReason
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.botmarket.finder.BotMarketPositionFinderService
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.matchmaking.command.ForceSellBotMarketPositionCommand
import com.cleevio.fatbot.application.module.matchmaking.event.BotForceSellTokenRequest
import com.cleevio.fatbot.application.module.matchmaking.event.TradedBotMarketPositionsEvent
import com.cleevio.fatbot.application.module.matchmaking.exception.BotMarketPositionNotOpenedException
import com.cleevio.fatbot.application.module.matchmaking.locks.FORCE_SELL
import com.cleevio.fatbot.application.module.matchmaking.locks.MATCH_MAKING_MODULE
import com.cleevio.fatbot.application.module.matchmaking.port.out.GetTokenState
import com.cleevio.fatbot.application.module.matchmaking.port.out.SendBotTransaction
import com.cleevio.fatbot.application.module.matchmaking.port.out.SetBotMarketPositionState
import com.cleevio.fatbot.application.module.matchmaking.port.out.UpsertBotMarketPositionTradeState
import com.cleevio.fatbot.application.module.token.finder.BotTokenInfoFinderService
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateType
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Clock
import java.time.Instant

@Component
class ForceSellBotMarketPositionCommandHandler(
	private val botFinderService: BotFinderService,
	private val botWalletFinderService: BotWalletFinderService,
	private val botMarketPositionFinderService: BotMarketPositionFinderService,
	private val sendBotTransaction: SendBotTransaction,
	private val clock: Clock,
	private val setBotMarketPositionState: SetBotMarketPositionState,
	private val getTokenState: GetTokenState,
	private val upsertBotMarketPositionTradeState: UpsertBotMarketPositionTradeState,
	private val botTokenInfoFinderService: BotTokenInfoFinderService,
) : CommandHandler<Unit, ForceSellBotMarketPositionCommand> {

	override val command = ForceSellBotMarketPositionCommand::class

	@Transactional
	@Lock(module = MATCH_MAKING_MODULE, lockName = FORCE_SELL)
	override fun handle(
		@LockFieldParameter("botMarketPositionId")
		command: ForceSellBotMarketPositionCommand,
	) {
		// check if user owns the bot
		val bot = botFinderService.getByIdAndUserId(id = command.botId, userId = command.userId)
		val botWallet = botWalletFinderService.getByBotId(botId = bot.id)
		val botMarketPosition = botMarketPositionFinderService.getByIdAndBotWalletId(
			id = command.botMarketPositionId,
			botWalletId = botWallet.id,
		)

		if (botMarketPosition.state != BotMarketPositionState.OPENED) {
			throw BotMarketPositionNotOpenedException("Position is not in state that enables force sell")
		}

		val botTokenInfo = botTokenInfoFinderService.getByTokenAddressAndChain(
			tokenAddress = botMarketPosition.tokenAddress,
			chain = botMarketPosition.chain,
		)

		/* We can't set the state here by Hibernate, as that would increase the version, which could then
			cause optimistic lock exception in matchmaking. It also might be dangerous to acquire lock for the whole
			matchmaking every time user is force selling.

		 	We overcome this by setting the state with JOOQ. What can now happen is that this update will be lost e.g:

		 	T+0 - In bot matchmaking market position is loaded into L1 cache
		 	T+1 - User forces sell, transaction commits, state is changed in DB
		 	T+2 - Matchmaking transaction commits, overriding the state to a different one and increasing version

		 	Main advantage of this approach is that optimistic lock exception can't happen in the matchmaking now.

		 	It's possible that this will cause two sell transactions to execute, however the change for that happening
		 	should be quite low so we are keeping this for now.
		 */
		setBotMarketPositionState(
			botMarketPositionId = botMarketPosition.id,
			state = BotMarketPositionState.PENDING_CLOSED_FROM_FORCE_SELL,
			sellReason = BotMarketPositionSellReason.MANUAL_SELL,
		)

		sendBotTransaction(
			BotForceSellTokenRequest(
				requestedAt = Instant.now(clock),
				botId = botWallet.botId,
				botWalletId = botWallet.id,
				userId = command.userId,
				tokenAddress = botMarketPosition.tokenAddress,
				creator = botTokenInfo.creatorAddress,
				privateKey = botWallet.privateKey,
				chain = botWallet.chain,
				amountToSell = botMarketPosition.totalTokenAmountBought,
			),
		)

		val tokenState = getTokenState.ofSingleToken(botMarketPosition.tokenAddress)

		upsertBotMarketPositionTradeState(
			tradedPositions = TradedBotMarketPositionsEvent(
				positions = listOf(
					TradedBotMarketPositionsEvent.TradedPosition(
						botMarketPositionId = botMarketPosition.id,
						type = BotMarketPositionTradeStateType.SELL,
						marketCapUsd = tokenState.marketCapUsd,
						liquidityUsd = tokenState.liquidityUsd,
						volumeUsd = tokenState.volumeUsd,
						numOfAccountHolders = tokenState.numOfAccountHolders,
						buyVolume = tokenState.buyVolume,
						sellVolume = tokenState.sellVolume,
						fractionOfSellTransactions = tokenState.fractionOfSellTransactions,
					),
				),
			),
		)

		bot.followerBotId?.let {
			val followerBot = botFinderService.getById(id = it)
			val followerBotWallet = botWalletFinderService.getByBotId(botId = it)
			val followerBotMarketPosition = botMarketPositionFinderService.findByTokenAddressAndBotWalletId(
				tokenAddress = botMarketPosition.tokenAddress,
				botWalletId = followerBotWallet.id,
			)

			// we can't throw here at any cost, as we already sent leader tx to chain and rollback
			// would mess the state of leader market position
			if (followerBotMarketPosition == null) return
			if (followerBotMarketPosition.state != BotMarketPositionState.OPENED) return

			setBotMarketPositionState(
				botMarketPositionId = followerBotMarketPosition.id,
				state = BotMarketPositionState.PENDING_CLOSED_FROM_FORCE_SELL,
				sellReason = BotMarketPositionSellReason.MANUAL_SELL,
			)

			sendBotTransaction(
				BotForceSellTokenRequest(
					requestedAt = Instant.now(clock),
					botId = followerBotWallet.botId,
					botWalletId = followerBotWallet.id,
					userId = followerBot.userId,
					tokenAddress = followerBotMarketPosition.tokenAddress,
					creator = botTokenInfo.creatorAddress,
					privateKey = followerBotWallet.privateKey,
					chain = followerBotWallet.chain,
					amountToSell = followerBotMarketPosition.totalTokenAmountBought,
				),
			)

			upsertBotMarketPositionTradeState(
				tradedPositions = TradedBotMarketPositionsEvent(
					positions = listOf(
						TradedBotMarketPositionsEvent.TradedPosition(
							botMarketPositionId = followerBotMarketPosition.id,
							type = BotMarketPositionTradeStateType.SELL,
							marketCapUsd = tokenState.marketCapUsd,
							liquidityUsd = tokenState.liquidityUsd,
							volumeUsd = tokenState.volumeUsd,
							numOfAccountHolders = tokenState.numOfAccountHolders,
							buyVolume = tokenState.buyVolume,
							sellVolume = tokenState.sellVolume,
							fractionOfSellTransactions = tokenState.fractionOfSellTransactions,
						),
					),
				),
			)
		}
	}
}
