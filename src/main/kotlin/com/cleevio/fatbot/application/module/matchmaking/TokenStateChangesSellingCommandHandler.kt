package com.cleevio.fatbot.application.module.matchmaking

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.botmarket.finder.BotMarketPositionFinderService
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.matchmaking.command.TokenStateChangesSellingCommand
import com.cleevio.fatbot.application.module.matchmaking.event.BotSellTransactionRequest
import com.cleevio.fatbot.application.module.matchmaking.event.TradedBotMarketPositionsEvent
import com.cleevio.fatbot.application.module.matchmaking.port.out.MatchSellingBots
import com.cleevio.fatbot.application.module.matchmaking.port.out.SendBotTransaction
import com.cleevio.fatbot.application.module.matchmaking.port.out.UpsertBotMarketPositionTradeState
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateType
import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class TokenStateChangesSellingCommandHandler(
	private val matchSellingBots: MatchSellingBots,
	private val botWalletFinderService: BotWalletFinderService,
	private val botMarketPositionFinderService: BotMarketPositionFinderService,
	private val sendBotTransaction: SendBotTransaction,
	private val upsertBotMarketPositionTradeState: UpsertBotMarketPositionTradeState,
) : CommandHandler<Unit, TokenStateChangesSellingCommand> {

	private val logger = logger()

	override val command = TokenStateChangesSellingCommand::class

	@Transactional(transactionManager = "matchMakingTransactionManager")
	override fun handle(command: TokenStateChangesSellingCommand) {
		if (command.states.isEmpty()) {
			logger.warn("No token state changes provided. Skipping matching selling bots.")
			return
		}

		val matchedSellingBots = matchSellingBots(states = command.states)

		if (matchedSellingBots.isEmpty()) {
			return
		}

		val matchedBuyingBotsWithFollowers = matchedSellingBots.expandFollowers()

		logger.info(
			"Starting to match token state changes to selling bots, " +
				"state changes size is: ${command.states.size}. Matched bots: ${matchedBuyingBotsWithFollowers.size}",
		)

		val botIds = matchedBuyingBotsWithFollowers.mapToSet { it.botId }
		val botIdToWallet = botWalletFinderService.getAllByBotIds(botIds = botIds).associateBy { it.botId }

		val tokenAddresses = command.states.mapToSet { it.tokenAddress }
		val botWalletIdToBotMarketPositions = botMarketPositionFinderService
			.findAllByTokenAddresses(tokenAddresses = tokenAddresses)
			.groupBy { it.botWalletId }

		val tokenAddressToTokenPrice = command.states.associate { it.tokenAddress to it.tokenPrice }
		val tokenAddressToCreator = command.states.associate { it.tokenAddress to it.curveState.creator!! }

		val soldBotMarketPositionIdToTokenAddress = matchedBuyingBotsWithFollowers.mapNotNull {
			val botWallet = botIdToWallet.getValue(it.botId)

			// With followers added, we now can not guarantee, that the follower will have the botMarketPosition,
			// so we have to treat this in a way that it's okay to not find botMarketPosition
			val botMarketPosition = botWalletIdToBotMarketPositions[botWallet.id]
				?.find { marketPosition -> marketPosition.tokenAddress == it.tokenAddress }

			if (botMarketPosition == null) {
				logger.warn(
					"Bot market position not found for bot wallet with id: " +
						"${botWallet.id} and token address: ${it.tokenAddress.getAddressString()}",
				)
				return@mapNotNull null
			}

			val currentTokenPrice = tokenAddressToTokenPrice.getValue(it.tokenAddress)
			botMarketPosition.closePositionWithSellMatch(assumedSellPrice = currentTokenPrice)

			val amountToSell = botMarketPosition.totalTokenAmountBought

			sendBotTransaction(
				BotSellTransactionRequest(
					blockSlot = command.blockSlot,
					botId = botWallet.botId,
					botWalletId = botWallet.id,
					userId = it.userId,
					tokenAddress = it.tokenAddress,
					creator = tokenAddressToCreator.getValue(it.tokenAddress),
					privateKey = botWallet.privateKey,
					chain = botWallet.chain,
					amountToSell = amountToSell,
				),
			)

			botMarketPosition.id to it.tokenAddress
		}.toMap()

		if (soldBotMarketPositionIdToTokenAddress.isNotEmpty()) {
			val tokenAddressToState = command.states.associateBy { it.tokenAddress }
			upsertBotMarketPositionTradeState(
				tradedPositions = TradedBotMarketPositionsEvent(
					positions = soldBotMarketPositionIdToTokenAddress.map { (botMarketPositionId, tokenAddress) ->
						val state = tokenAddressToState.getValue(tokenAddress)
						TradedBotMarketPositionsEvent.TradedPosition(
							botMarketPositionId = botMarketPositionId,
							type = BotMarketPositionTradeStateType.SELL,
							marketCapUsd = state.marketCapUsd,
							liquidityUsd = state.liquidityUsd,
							volumeUsd = state.volumeUsd,
							numOfAccountHolders = state.numOfAccountHolders,
							buyVolume = state.buyVolume,
							sellVolume = state.sellVolume,
							fractionOfSellTransactions = state.fractionOfSellTransactions,
						)
					},
				),
			)
		}

		logger.info("Finished matching token state changes to selling bots.")
	}

	private fun List<MatchSellingBots.Result>.expandFollowers() = this.flatMap { originalBot ->
		val followerBot = when (originalBot.followerId) {
			null -> null
			else -> originalBot.copy(botId = originalBot.followerId, followerId = null)
		}

		listOfNotNull(originalBot, followerBot)
	}
}
