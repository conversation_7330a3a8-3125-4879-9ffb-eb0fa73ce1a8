package com.cleevio.fatbot.application.module.token.event.listener

import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.market.command.UpdateUserPositionsCommand
import com.cleevio.fatbot.application.module.market.event.UserPortfolioViewedEvent
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.context.annotation.Profile
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component

@Component
@Profile("!test")
class MarketTokenEventListener(
	private val commandBus: CommandBus,
) {

	@Async
	@EventListener
	@SentryTransaction(operation = "async.user-portfolio-viewed")
	fun onUserPortfolioViewedEvent(event: UserPortfolioViewedEvent) {
		commandBus(UpdateUserPositionsCommand(userId = event.userId))
	}
}
