package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.compareTo
import com.cleevio.fatbot.application.common.crypto.plus
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.bot.event.BotDetailViewedEvent
import com.cleevio.fatbot.application.module.bot.query.GetBotQuery
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component

@Component
class GetBotByIdQueryHandler(
	private val botFinderService: BotFinderService,
	private val botWalletFinderService: BotWalletFinderService,
	private val usdConverter: UsdConverter,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val applicationEventPublisher: ApplicationEventPublisher,
) : QueryHandler<GetBotQuery.Result, GetBotQuery> {

	override val query = GetBotQuery::class

	override fun handle(query: GetBotQuery): GetBotQuery.Result {
		val bot = botFinderService.getByIdAndUserId(id = query.botId, userId = query.userId)
		val botWallet = botWalletFinderService.getByBotId(botId = bot.id)

		val botWalletBalance = botWallet.balance.asBaseAmount()
		val botTradeAmount = bot.tradeAmount.asNativeAmount().toBase(chain = botWallet.chain)

		val tokenAccountRent = SolanaConstants.BOT_BUY_RENT_BASE_AMOUNT
		val buyFeesToFreeze = SolanaConstants.BOT_BUY_FEE_FREEZE_BASE_AMOUNT
		val sellFeesToFreeze = SolanaConstants.BOT_SELL_FEE_FREEZE_BASE_AMOUNT
		val minimumRentOnWalletAfterBuy = SolanaConstants.MINIMUM_RENT_THRESHOLD

		val minimumBalanceNeededForTrade = botTradeAmount +
			tokenAccountRent +
			buyFeesToFreeze +
			sellFeesToFreeze +
			minimumRentOnWalletAfterBuy

		val hasSufficientBalanceForTrade = botWalletBalance >= minimumBalanceNeededForTrade

		val balanceUsd = usdConverter.baseToUsd(
			baseAmount = botWalletBalance,
			chain = botWallet.chain,
			customRate = getUsdExchangeRate(botWallet.chain.currency),
		)

		applicationEventPublisher.publishEvent(
			BotDetailViewedEvent(
				userId = query.userId,
				botId = query.botId,
			),
		)

		return GetBotQuery.Result(
			id = bot.id,
			botWalletAddress = botWallet.address,
			botWalletChain = botWallet.chain,
			botWalletBalanceNativeAmount = botWalletBalance.toNative(botWallet.chain),
			botWalletBalanceUsd = balanceUsd,
			createdAt = bot.createdAt,
			userReadableId = bot.userReadableBotId(),
			name = bot.name,
			avatarFileId = bot.avatarFileId,
			tradeAmount = bot.tradeAmount,
			buyFrequency = bot.buyFrequency.toBigInteger(),
			remainingBuyFrequency = bot.remainingBuyFrequency.toBigInteger(),
			buyFrequencyLastResetAt = bot.buyFrequencyLastResetAt,
			profitTargetFraction = bot.profitTargetFraction,
			stopLossFraction = bot.stopLossFraction,
			hasSufficientBalanceForTrade = hasSufficientBalanceForTrade,
			buyVolume = bot.buyVolume,
			sellVolume = bot.sellVolume,
			dailyVolumeFromUsd = bot.dailyVolumeFromUsd,
			dailyVolumeToUsd = bot.dailyVolumeToUsd,
			liquidityFromUsd = bot.liquidityFromUsd,
			liquidityToUsd = bot.liquidityToUsd,
			marketCapFromUsd = bot.marketCapFromUsd,
			marketCapToUsd = bot.marketCapToUsd,
			numberOfHoldersFrom = bot.numberOfHoldersFrom?.toBigInteger(),
			numberOfHoldersTo = bot.numberOfHoldersTo?.toBigInteger(),
			tokenTickerCopyIsChecked = bot.tokenTickerCopyIsChecked,
			creatorHighBuyIsChecked = bot.creatorHighBuyIsChecked,
			bundledBuysDetectedIsChecked = bot.bundledBuysDetectedIsChecked,
			suspiciousWalletsDetectedIsChecked = bot.suspiciousWalletsDetectedIsChecked,
			singleHighBuyIsChecked = bot.singleHighBuyIsChecked,
			shouldWaitBeforeBuying = bot.buyTokensAliveAtLeastFor != null,
			shouldAutoSellAfterHoldTime = bot.shouldAutoSellAfterHoldTime,
			sellTransactionFraction = bot.sellTransactionFraction,
			buyTransactionFraction = bot.buyTransactionFraction,
		)
	}
}
