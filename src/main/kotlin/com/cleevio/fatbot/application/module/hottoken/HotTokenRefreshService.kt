package com.cleevio.fatbot.application.module.hottoken

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.hottoken.port.out.ComputeHotTokens
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

const val MAX_HOT_TOKENS_PER_CHAIN_COUNT = 500

@Service
class HotTokenRefreshService(
	private val hotTokenService: HotTokenService,
	private val computeHotTokens: ComputeHotTokens,
	private val clock: Clock,
) {

	private val logger = logger()

	data class RefreshData(
		val chain: Chain,
		val tokenAddress: AddressWrapper,
		val tokenName: String,
		val tokenSymbol: String,
		val tokenDecimals: BigInteger,
		val tokenImageFileId: UUID?,
		val tokenImageFileExtension: String?,
		val volume24hUsd: BigDecimal,
	)

	@SentrySpan
	fun refreshHotTokens(chain: Chain) {
		val now = Instant.now(clock)
		logger.info("Going to compute hot tokens on chain $chain")

		val hotTokens = computeHotTokens(
			chain = chain,
			size = MAX_HOT_TOKENS_PER_CHAIN_COUNT,
			from = now.minus(24, ChronoUnit.HOURS),
			to = now,
		)

		val tokenRefreshData = hotTokens.map { baseHotToken ->
			RefreshData(
				chain = chain,
				tokenAddress = baseHotToken.tokenAddress,
				tokenName = baseHotToken.tokenName,
				tokenSymbol = baseHotToken.tokenSymbol,
				tokenDecimals = baseHotToken.tokenDecimals,
				tokenImageFileId = baseHotToken.tokenImageFileId,
				tokenImageFileExtension = baseHotToken.tokenImageFileExtension,
				volume24hUsd = baseHotToken.volumeUsd,
			)
		}

		hotTokenService.deleteAndSaveAll(tokenRefreshData, chain)
	}
}
