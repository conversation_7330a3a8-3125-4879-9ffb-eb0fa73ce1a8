package com.cleevio.fatbot.application.module.userfattycard.exceptions

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class UserFattyCardNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.USER_FATTY_CARD_NOT_FOUND,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class UserFattyCarAlreadyClaimedException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.USER_FATTY_CARD_ALREADY_CLAIMED,
	message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class UserFattyCarAlreadyDisplayedException(message: String) : <PERSON>botApiException(
	reason = ExtendedErrorReasonType.USER_FATTY_CARD_ALREADY_DISPLAYED,
	message = message,
)
