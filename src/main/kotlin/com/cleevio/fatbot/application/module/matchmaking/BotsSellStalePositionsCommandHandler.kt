package com.cleevio.fatbot.application.module.matchmaking

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bot.BotFinderService
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.botmarket.finder.BotMarketPositionFinderService
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.matchmaking.command.BotsSellStalePositionsCommand
import com.cleevio.fatbot.application.module.matchmaking.event.BotSellStaleTokenRequest
import com.cleevio.fatbot.application.module.matchmaking.event.TradedBotMarketPositionsEvent
import com.cleevio.fatbot.application.module.matchmaking.locks.MATCH_BOTS
import com.cleevio.fatbot.application.module.matchmaking.locks.MATCH_MAKING_MODULE
import com.cleevio.fatbot.application.module.matchmaking.port.out.FindAllStaleBotMarketPositionIdToTokenAddress
import com.cleevio.fatbot.application.module.matchmaking.port.out.GetTokenState
import com.cleevio.fatbot.application.module.matchmaking.port.out.SendBotTransaction
import com.cleevio.fatbot.application.module.matchmaking.port.out.UpsertBotMarketPositionTradeState
import com.cleevio.fatbot.application.module.token.finder.BotTokenInfoFinderService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateType
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Clock
import java.time.Instant
import java.util.UUID

@Component
class BotsSellStalePositionsCommandHandler(
	private val clock: Clock,
	private val findAllStaleBotMarketPositionIdToTokenAddress: FindAllStaleBotMarketPositionIdToTokenAddress,
	private val botMarketPositionFinderService: BotMarketPositionFinderService,
	private val botWalletFinderService: BotWalletFinderService,
	private val botFinderService: BotFinderService,
	private val sendBotTransaction: SendBotTransaction,
	private val upsertBotMarketPositionTradeState: UpsertBotMarketPositionTradeState,
	private val getTokenState: GetTokenState,
	private val botTokenInfoFinderService: BotTokenInfoFinderService,
) : CommandHandler<Unit, BotsSellStalePositionsCommand> {

	private val logger = logger()
	override val command = BotsSellStalePositionsCommand::class

	@Autowired
	@Lazy
	private lateinit var self: BotsSellStalePositionsCommandHandler

	@SentrySpan
	override fun handle(command: BotsSellStalePositionsCommand) {
		logger.info("Checking stale bot market positions")
		val now = Instant.now(clock)
		val staleBotMarketPositionIdToTokenAddress = findAllStaleBotMarketPositionIdToTokenAddress(now = now).toMap()
		if (staleBotMarketPositionIdToTokenAddress.isEmpty()) return

		val staleBotMarketPositionIds = staleBotMarketPositionIdToTokenAddress.keys
		val tokenAddresses = staleBotMarketPositionIdToTokenAddress.values.toSet()

		val tokenStates = getTokenState.ofManyTokens(tokenAddresses)
		val tokenAddressToCreator = botTokenInfoFinderService.getAllByTokenAddressesAndChain(
			tokenAddresses = tokenAddresses,
			chain = Chain.SOLANA,
		).associate { it.address to it.creatorAddress }

		val positions = staleBotMarketPositionIdToTokenAddress.map { (botMarketPositionId, tokenAddress) ->
			val state = tokenStates.getValue(tokenAddress)
			TradedBotMarketPositionsEvent.TradedPosition(
				botMarketPositionId = botMarketPositionId,
				type = BotMarketPositionTradeStateType.SELL,
				marketCapUsd = state.marketCapUsd,
				liquidityUsd = state.liquidityUsd,
				volumeUsd = state.volumeUsd,
				numOfAccountHolders = state.numOfAccountHolders,
				buyVolume = state.buyVolume,
				sellVolume = state.sellVolume,
				fractionOfSellTransactions = state.fractionOfSellTransactions,
			)
		}

		self.sellStaleBotMarketPositions(
			staleBotMarketPositionIds = staleBotMarketPositionIds,
			tokenAddressToCreator = tokenAddressToCreator,
			positions = positions,
			now = now,
		)
	}

	// Warning: This function acquires lock that blocks whole bot trading to not create conflict, so make sure it's fast
	@SentrySpan
	@Transactional(transactionManager = "matchMakingTransactionManager")
	@Lock(module = MATCH_MAKING_MODULE, lockName = MATCH_BOTS)
	fun sellStaleBotMarketPositions(
		staleBotMarketPositionIds: Set<UUID>,
		tokenAddressToCreator: Map<AddressWrapper, AddressWrapper>,
		positions: List<TradedBotMarketPositionsEvent.TradedPosition>,
		now: Instant,
	) {
		val staleBotMarketPositions = botMarketPositionFinderService.getAllByIds(ids = staleBotMarketPositionIds)

		// we have to recheck under lock if they are still open
		val openPositions = staleBotMarketPositions
			.filter { it.state == BotMarketPositionState.OPENED }
			// sorted so leader bots will sell before followers (their positions are created first when buying)
			.sortedBy { it.id }

		val botWalletIds = openPositions.mapToSet { it.botWalletId }
		val botWalletIdToWallet = botWalletFinderService.getAllByIds(ids = botWalletIds).associateBy { it.id }

		val botIds = botWalletIdToWallet.values.mapToSet { it.botId }
		val botIdToBot = botFinderService.getAllByIds(ids = botIds).associateBy { it.id }

		openPositions.forEach { position ->
			position.closePositionAsStale()

			val botWallet = botWalletIdToWallet.getValue(position.botWalletId)
			val bot = botIdToBot.getValue(botWallet.botId)

			sendBotTransaction(
				BotSellStaleTokenRequest(
					requestedAt = now,
					botWalletId = botWallet.id,
					botId = botWallet.botId,
					userId = bot.userId,
					tokenAddress = position.tokenAddress,
					creator = tokenAddressToCreator.getValue(position.tokenAddress),
					privateKey = botWallet.privateKey,
					chain = botWallet.chain,
					amountToSell = position.totalTokenAmountBought,
				),
			)
		}

		val openPositionIds = openPositions.mapToSet { it.id }
		val filteredPositions = positions.filter { it.botMarketPositionId in openPositionIds }
		upsertBotMarketPositionTradeState(tradedPositions = TradedBotMarketPositionsEvent(positions = filteredPositions))
	}
}
