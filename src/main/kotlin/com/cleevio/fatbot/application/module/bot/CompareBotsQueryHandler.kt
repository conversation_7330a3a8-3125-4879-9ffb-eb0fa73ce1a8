package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotMarketPosition
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotPortfolioValueSnapshot
import com.cleevio.fatbot.application.module.bot.port.out.GetBots
import com.cleevio.fatbot.application.module.bot.query.CompareBotsQuery
import com.cleevio.fatbot.application.module.bot.query.CompareBotsQuery.BotPortfolioPastValue
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator
import com.cleevio.fatbot.application.module.botmarket.query.SearchBotMarketPositionQuery
import com.cleevio.fatbot.application.module.bottransaction.port.out.SearchLastBotsTransaction
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.token.constant.toInstant
import com.cleevio.fatbot.application.module.transaction.port.out.GetTxDetailUri
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.net.URI
import java.time.Clock
import java.time.Instant

@Component
class CompareBotsQueryHandler(
	private val getBots: GetBots,
	private val findAllBotMarketPosition: FindAllBotMarketPosition,
	private val fileUrlMapper: FileUrlMapper,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val getTokenPrices: GetTokenPrices,
	private val clock: Clock,
	private val searchLastBotsTransaction: SearchLastBotsTransaction,
	private val getTxDetailUri: GetTxDetailUri,
	private val findAllBotPortfolioValueSnapshot: FindAllBotPortfolioValueSnapshot,
	private val usdConverter: UsdConverter,
) : QueryHandler<List<CompareBotsQuery.Result>, CompareBotsQuery> {

	override val query = CompareBotsQuery::class

	override fun handle(query: CompareBotsQuery): List<CompareBotsQuery.Result> {
		val allBots = getBots(userId = query.userId, botIds = query.filter.botIds)

		val allBotMarketPositions = findAllBotMarketPosition(
			userId = query.userId,
			botIds = query.filter.botIds,
			searchString = null,
			isBotMarketPositionActive = query.filter.isBotMarketPositionActive,
			fileToUrlMapper = fileUrlMapper::map,
		)

		// Fetch token prices for all open bot market positions
		val openPositions = allBotMarketPositions.filter { it.positionClosedAt == null }
		val openPositionTokens = openPositions.mapToSet { it.tokenAddress.toChainAddress(it.chain) }
		val tokenAddressToTokenPrice = getTokenPrices(tokens = openPositionTokens)

		val chainToExchangeRate = Chain.entries.associateWith { getUsdExchangeRate(it.currency) }

		val now = Instant.now(clock)
		val from = query.filter.timeRange.toInstant(now)

		val botIdToMarketPositionResult = BotMarketPositionCalculator.botToMarketPositionsResults(
			botMarketPositions = openPositions,
			tokenAddressToTokenPrice = tokenAddressToTokenPrice,
			chainToExchangeRate = chainToExchangeRate,
		)

		val botIdToMarketPositions = allBotMarketPositions
			.groupBy { it.botId }
			.mapValues { (_, positions) ->
				positions.map { position ->
					val positionUsdValue = BotMarketPositionCalculator.toBotMarketPositionResult(
						botMarketPosition = position,
						tokenAddressToTokenPrice = tokenAddressToTokenPrice,
						currentExchangeRate = chainToExchangeRate.getValue(position.chain),
					)

					SearchBotMarketPositionQuery.Result(
						id = position.id,
						state = position.state,
						tokenAddress = position.tokenAddress,
						tokenDetailUrl = position.tokenAddress.getLinkToExplorer(position.chain).let { URI.create(it) },
						tokenName = position.tokenName,
						tokenSymbol = position.tokenSymbol,
						tokenChain = position.chain,
						tokenImageUrl = position.tokenImageUrl,
						openValueUsd = positionUsdValue.acquisitionValueUsd,
						closeValueUsd = position.positionClosedAt?.let { positionUsdValue.currentValueUsd },
						openTimeStampAt = position.positionOpenedAt,
						closedTimeStampAt = position.positionClosedAt,
						pnlAmountUsd = positionUsdValue.currentPnlUsd,
						pnlAmountFraction = positionUsdValue.currentPnlFraction,
						currentValueUsd = positionUsdValue.currentValueUsd,
					)
				}
			}

		val botIdByPastPortfolioValues = findAllBotPortfolioValueSnapshot(
			botIds = query.filter.botIds,
			from = query.filter.timeRange.toInstant(now),
			to = now,
		).groupBy { it.botId }

		val botIdToTransactions = searchLastBotsTransaction(
			userId = query.userId,
			botIds = query.filter.botIds,
			from = from,
			to = now,
			fileToUrlMapper = fileUrlMapper::map,
			txHashToURIMapper = { txHash, chain -> getTxDetailUri(txHash, chain) },
		)

		return allBots.map { bot ->
			val currentMarketPositionsValueUsd = botIdToMarketPositionResult[bot.id]
				?.let { it.currentValueUsd + it.rentUsd }
				?: BigDecimal.ZERO
			val currentWalletBalanceUsd = usdConverter.baseToUsd(
				bot.walletBalance,
				bot.walletChain,
				chainToExchangeRate.getValue(bot.walletChain),
			)

			val currentPortfolioValueUsd = currentMarketPositionsValueUsd + currentWalletBalanceUsd

			val portfolioLastValues = botIdByPastPortfolioValues[bot.id] ?: listOf()

			val botMarketPositions = botIdToMarketPositions[bot.id]
				.orEmpty()
				.sortedByDescending { position -> position.openTimeStampAt }

			val botMarketPositionsInTimeRange = botMarketPositions.filter { it.openTimeStampAt in from..now }

			val transactionCount = BotMarketPositionCalculator.numOfTransactions(botMarketPositionsInTimeRange)
			val profitableMarketPositions = BotMarketPositionCalculator.numOfProfitablePositions(botMarketPositionsInTimeRange)
			val lossMarketPositions = BotMarketPositionCalculator.numOfLossPositions(botMarketPositionsInTimeRange)
			val positionsVolume = BotMarketPositionCalculator.calculatePositionsVolume(botMarketPositionsInTimeRange)

			val botPortfolioLastValues = portfolioLastValues.map { portfolioValue ->
				BotPortfolioPastValue(
					createdAt = portfolioValue.snapshotMadeAt,
					portfolioValueUsd = portfolioValue.portfolioValueUsd,
				)
			} + BotPortfolioPastValue(
				createdAt = now,
				portfolioValueUsd = currentPortfolioValueUsd,
			)

			val selectedBotMarketPositions = when (query.filter.isBotMarketPositionActive) {
				true -> botMarketPositions.filter { it.closedTimeStampAt == null }
				false -> botMarketPositionsInTimeRange.filter { it.closedTimeStampAt != null }
			}.take(5)

			CompareBotsQuery.Result(
				botId = bot.id,
				botName = bot.name,
				isActive = bot.isActive,
				numberOfActiveDays = bot.numberOfActiveDays,
				botAvatarFileId = bot.botAvatarFileId,
				buyFrequency = bot.buyFrequency.toBigInteger(),
				remainingBuyFrequency = bot.remainingBuyFrequency.toBigInteger(),
				buyFrequencyLastResetAt = bot.buyFrequencyLastResetAt,
				botTotalValueAmountUsd = currentPortfolioValueUsd,
				botTotalPnlAmountUsd = currentPortfolioValueUsd - bot.walletAcquisitionValueUsd,
				botTotalPnlAmountFraction = calculateFractionChange(currentPortfolioValueUsd, bot.walletAcquisitionValueUsd),
				botPortfolioLastValues = botPortfolioLastValues,
				transactionsCount = transactionCount,
				// TODO: What about withdrawals and so on?
				buyTransactionsCount = botMarketPositionsInTimeRange.size,
				profitableTransactionsCount = profitableMarketPositions,
				lossTransactionsCount = lossMarketPositions,
				transactionsVolumeSum = positionsVolume,
				botMarketPositions = selectedBotMarketPositions,
				botTransactions = botIdToTransactions[bot.id]!!,
			)
		}
	}
}
