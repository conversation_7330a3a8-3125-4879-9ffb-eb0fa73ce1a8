package com.cleevio.fatbot.application.module.gassnapshot

import com.cleevio.fatbot.adapter.out.evm.model.GasInfo
import com.cleevio.fatbot.application.module.gassnapshot.finder.GasInfoSnapshotFinderService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.gassnapshot.GasInfoSnapshotCreateService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class GasInfoSnapshotService(
	private val gasInfoSnapshotCreateService: GasInfoSnapshotCreateService,
	private val gasInfoSnapshotFinderService: GasInfoSnapshotFinderService,
) {

	@Transactional
	fun saveOrUpdateGasInfoSnapshots(chainToGasInfo: Map<Chain, GasInfo>) {
		val existingSnapshots = gasInfoSnapshotFinderService.findAll()

		chainToGasInfo.forEach { (chain, newGasInfo) ->
			val existingGasInfo = existingSnapshots.find { it.chainId == chain.evmId }

			if (existingGasInfo != null) {
				existingGasInfo.updateGasInfo(newGasInfo.maxPriorityFeePerGas, newGasInfo.maxFeePerGas)
			} else {
				gasInfoSnapshotCreateService.create(chain.evmId, newGasInfo.maxPriorityFeePerGas, newGasInfo.maxFeePerGas)
			}
		}
	}
}
