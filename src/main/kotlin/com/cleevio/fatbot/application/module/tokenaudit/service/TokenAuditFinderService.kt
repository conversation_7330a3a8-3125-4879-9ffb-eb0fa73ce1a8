package com.cleevio.fatbot.application.module.tokenaudit.service

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.domain.tokenaudit.TokenAudit
import com.cleevio.fatbot.domain.tokenaudit.TokenAuditRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class TokenAuditFinderService(
	private val repository: TokenAuditRepository,
) : BaseFinderService<TokenAudit>(repository) {
	override fun errorBlock(message: String) = error(message)

	override fun getEntityType() = TokenAudit::class

	@Transactional(readOnly = true)
	fun findByTokenAddress(tokenAddress: ChainAddress): TokenAudit? =
		repository.findByTokenAddressAndChain(tokenAddress = tokenAddress.address, chain = tokenAddress.chain)

	@Transactional(readOnly = true)
	fun getByTokenAddress(tokenAddress: ChainAddress): TokenAudit = findByTokenAddress(tokenAddress)
		?: errorBlock("TokenAudit for token address $tokenAddress not found")
}
