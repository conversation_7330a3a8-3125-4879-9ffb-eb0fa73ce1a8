package com.cleevio.fatbot.application.module.referral

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.referral.query.GetClaimingLimitsQuery
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.stereotype.Component

@Component
class GetClaimingLimitsQueryHandler(
	private val chainProperties: ChainProperties,
	private val evmChainContextFactory: EvmChainContextFactory,
) : QueryHandler<List<GetClaimingLimitsQuery.Result>, GetClaimingLimitsQuery> {

	override val query = GetClaimingLimitsQuery::class

	override fun handle(query: GetClaimingLimitsQuery): List<GetClaimingLimitsQuery.Result> {
		return chainProperties
			.enabledChains
			.map {
				when (it.type) {
					ChainType.EVM -> evmChainContextFactory.ofChainId(it.evmId) {
						GetClaimingLimitsQuery.Result(
							chain = it,
							minimalClaimNativeAmount = properties
								.referralClaimThresholdNativeAmount
								.asNativeAmount(),
						)
					}

					ChainType.SOLANA -> GetClaimingLimitsQuery.Result(
						chain = it,
						minimalClaimNativeAmount = chainProperties
							.svm
							.solana
							.referralClaimThresholdNativeAmount
							.asNativeAmount(),
					)
				}
			}
	}
}
