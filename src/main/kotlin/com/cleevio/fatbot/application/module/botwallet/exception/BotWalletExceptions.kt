package com.cleevio.fatbot.application.module.botwallet.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class BotWalletNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.BOT_WALLET_NOT_FOUND,
	message = message,
)
