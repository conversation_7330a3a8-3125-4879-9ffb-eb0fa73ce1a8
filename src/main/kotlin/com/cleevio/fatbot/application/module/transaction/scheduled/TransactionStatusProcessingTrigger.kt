package com.cleevio.fatbot.application.module.transaction.scheduled

import com.cleevio.fatbot.application.module.transaction.TransactionStatusProcessingService
import com.cleevio.fatbot.application.module.transaction.locks.PROCESS_TRANSACTION_STATUSES
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class TransactionStatusProcessingTrigger(
	private val transactionStatusProcessingService: TransactionStatusProcessingService,
) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.process-transaction-statuses")
	@Scheduled(cron = "\${fatbot.transaction.status-processing.cron}")
	@SchedulerLock(
		name = PROCESS_TRANSACTION_STATUSES,
		lockAtLeastFor = "\${fatbot.transaction.status-processing.lock-at-least-for}",
		lockAtMostFor = "\${fatbot.transaction.status-processing.lock-at-most-for}",
	)
	fun trigger() {
		logger.info("$PROCESS_TRANSACTION_STATUSES cron started")
		transactionStatusProcessingService.processTransactionStatuses()
		logger.info("$PROCESS_TRANSACTION_STATUSES cron ended")
	}
}
