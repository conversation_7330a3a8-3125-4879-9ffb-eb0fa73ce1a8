package com.cleevio.fatbot.application.module.token.event.listener

import com.cleevio.fatbot.application.module.matchmaking.event.BoughtTokensEvent
import com.cleevio.fatbot.application.module.token.service.DownloadBotTokenImageService
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class MatchMakingTokenEventListener(
	private val downloadBotTokenImageService: DownloadBotTokenImageService,
) {

	@TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
	fun onBoughtTokenEvent(event: BoughtTokensEvent) {
		downloadBotTokenImageService.processBoughtTokens(event.boughtTokens)
	}
}
