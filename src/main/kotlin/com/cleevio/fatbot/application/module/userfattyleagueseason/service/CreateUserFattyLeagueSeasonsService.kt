package com.cleevio.fatbot.application.module.userfattyleagueseason.service

import com.cleevio.fatbot.application.module.fattyleagueseason.finder.FattyLeagueSeasonFinderService
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.userstatistics.finder.UserStatisticsFinderService
import com.cleevio.fatbot.domain.userfattyleagueseason.UserFattyLeagueSeasonCreateService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class CreateUserFattyLeagueSeasonsService(
	private val userFinderService: FirebaseUserFinderService,
	private val userFattyLeagueSeasonCreateService: UserFattyLeagueSeasonCreateService,
	private val fattyLeagueSeasonFinderService: FattyLeagueSeasonFinderService,
	private val userStatisticsFinderService: UserStatisticsFinderService,
) {

	@Transactional
	fun createForAllUsers(fattyLeagueId: UUID) {
		val userStatistics = userStatisticsFinderService.findAll()
			.associateBy { it.userId }

		// TODO: add logic for converting donuts to tokens
		val fattyLeague = fattyLeagueSeasonFinderService.getById(fattyLeagueId)
		userFinderService.findAll().forEach {
			userFattyLeagueSeasonCreateService.create(
				userId = it.id,
				fattyLeagueSeasonId = fattyLeagueId,
				totalTokens = fattyLeague.donutsToFattyTokensRatio * userStatistics[it.id]!!.leaderboardDonuts,
			)
		}
	}
}
