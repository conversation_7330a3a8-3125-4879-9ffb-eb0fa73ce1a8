package com.cleevio.fatbot.application.module.botmarket.port.out

import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.net.URI
import java.time.Instant
import java.util.UUID

interface FindBotMarketPositionLatestTradeTransactions {
	operator fun invoke(
		tokenAddress: AddressWrapper,
		botWalletId: UUID,
		txHashToURIMapper: (TxHash, Chain) -> URI,
	): Pair<BuyTransaction, SellTransaction?>

	data class Result(
		val txId: UUID,
		val txHash: TxHash,
		val txDetailUrl: URI,
		val txType: BotTransactionType,
		val txStatus: TransactionStatus,
		val txCreatedAt: Instant,
	)
}

typealias BuyTransaction = FindBotMarketPositionLatestTradeTransactions.Result
typealias SellTransaction = FindBotMarketPositionLatestTradeTransactions.Result
