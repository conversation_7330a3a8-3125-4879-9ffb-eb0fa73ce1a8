package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.finder.MarketPositionFinderService
import com.cleevio.fatbot.application.module.market.port.out.GetTokenBalancesEVM
import com.cleevio.fatbot.application.module.market.port.out.GetTokenBalancesSolana
import com.cleevio.fatbot.application.module.market.port.out.GetTokenDecimals
import com.cleevio.fatbot.application.module.market.port.out.invoke
import com.cleevio.fatbot.application.module.market.query.GetTokenBalanceQuery
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class GetTokenBalanceQueryHandler(
	private val walletFinderService: WalletFinderService,
	private val marketPositionFinderService: MarketPositionFinderService,
	private val getTokenBalancesEVM: GetTokenBalancesEVM,
	private val getTokenBalancesSolana: GetTokenBalancesSolana,
	private val getTokenDecimals: List<GetTokenDecimals>,
) : QueryHandler<GetTokenBalanceQuery.Result, GetTokenBalanceQuery> {

	override val query = GetTokenBalanceQuery::class

	@SentrySpan
	override fun handle(query: GetTokenBalanceQuery): GetTokenBalanceQuery.Result {
		val wallet = walletFinderService.getByIdAndUserId(
			id = query.walletId,
			userId = query.userId,
		)

		val marketPosition = marketPositionFinderService.findMarketPosition(
			walletId = wallet.id,
			chain = wallet.chain,
			tokenAddress = query.tokenAddress,
		)

		val onChainBalance = when (query.tokenAddress.addressType) {
			ChainType.EVM -> getEvmOnChainBalance(wallet.address, wallet.chain.evmId, query.tokenAddress)
			ChainType.SOLANA -> getTokenBalancesSolana.single(ownerAddress = wallet.address, tokenAddress = query.tokenAddress)
		}

		val platformBalance = (marketPosition?.getTokenBalance() ?: BigInteger.ZERO).asBaseAmount()
		val tokenDecimals = getTokenDecimals(query.tokenAddress.toChainAddress(wallet.chain)).toInt()

		return GetTokenBalanceQuery.Result(
			platformBalance = platformBalance.toNative(tokenDecimals),
			onChainBalance = onChainBalance.toNative(tokenDecimals),
		)
	}

	private fun getEvmOnChainBalance(
		walletAddress: AddressWrapper,
		chainId: Long,
		tokenAddress: AddressWrapper,
	): BaseAmount {
		val tokenAddressToBalance = getTokenBalancesEVM(
			chainId = chainId,
			walletAddress = walletAddress,
			tokenAddresses = listOf(tokenAddress),
		)

		val balance = tokenAddressToBalance[tokenAddress] ?: error(
			"Failed to fetch token balance for $tokenAddress on EVM chainId $chainId.",
		)

		return balance.asBaseAmount()
	}
}
