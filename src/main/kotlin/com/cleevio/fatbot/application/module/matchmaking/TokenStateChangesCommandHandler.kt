package com.cleevio.fatbot.application.module.matchmaking

import com.cleevio.fatbot.adapter.out.solana.pumpfun.GetPumpfunPrice
import com.cleevio.fatbot.adapter.out.solana.pumpfun.model.PumpFunCurveState
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.matchmaking.command.TokenStateChangesBuyingCommand
import com.cleevio.fatbot.application.module.matchmaking.command.TokenStateChangesCommand
import com.cleevio.fatbot.application.module.matchmaking.command.TokenStateChangesSellingCommand
import com.cleevio.fatbot.application.module.matchmaking.locks.MATCH_BOTS
import com.cleevio.fatbot.application.module.matchmaking.locks.MATCH_MAKING_MODULE
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import io.sentry.Sentry
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import kotlin.time.Duration.Companion.milliseconds

@Component
class TokenStateChangesCommandHandler(
	private val getPumpfunPrice: GetPumpfunPrice,
) : CommandHandler<Unit, TokenStateChangesCommand> {

	override val command = TokenStateChangesCommand::class

	private val logger = logger()

	@Lazy
	@Autowired
	private lateinit var commandBus: CommandBus

	@Transactional(transactionManager = "matchMakingTransactionManager")
	@Lock(module = MATCH_MAKING_MODULE, lockName = MATCH_BOTS)
	override fun handle(command: TokenStateChangesCommand) {
		Sentry.setTag("block", command.blockSlot.toString())
		logger.info("Processing trades from slot: ${command.blockSlot}")
		commandBus(command.toBuyingCommand { curveState -> getPumpfunPrice.fromCurveState(curveState).amount })
		commandBus(command.toSellingCommand { curveState -> getPumpfunPrice.fromCurveState(curveState).amount })
	}

	private fun TokenStateChangesCommand.toBuyingCommand(getTokenPrice: (PumpFunCurveState) -> BigDecimal) =
		TokenStateChangesBuyingCommand(
			blockSlot = blockSlot,
			states = states.map {
				val tokenAliveForBlocks = blockSlot - it.tgeBlockSlot
				val tokenAliveFor = AVERAGE_BLOCK_SLOT_DURATION.times(tokenAliveForBlocks)

				TokenStateChangesBuyingCommand.NewState(
					tokenAliveFor = tokenAliveFor,
					tokenAddress = it.tokenAddress,
					name = it.name,
					symbol = it.symbol,
					infoUrl = it.infoUrl,
					marketCapUsd = it.marketCapUsd,
					liquidityUsd = it.liquidityUsd,
					numOfAccountHolders = it.numOfAccountHolders,
					volumeUsd = it.volumeUsd,
					buyVolume = it.buyVolume,
					sellVolume = it.sellVolume,
					curveState = it.curveState,
					tokenPrice = getTokenPrice(it.curveState),
					fractionOfSellTransactions = it.fractionOfSellTransactions,
					creatorGraduationSuccessRateFraction = it.creatorGraduationSuccessRateFraction,
					isRedFlagTokenTickerCopy = it.isRedFlagTokenTickerCopy,
					isRedFlagCreatorHighBuy = it.isRedFlagCreatorHighBuy,
					isRedFlagBundledBuysDetected = it.isRedFlagBundledBuysDetected,
					isRedFlagSuspiciousWalletsDetected = it.isRedFlagSuspiciousWalletsDetected,
					isRedFlagSingleHighBuy = it.isRedFlagSingleHighBuy,
				)
			},
		)

	fun TokenStateChangesCommand.toSellingCommand(getTokenPrice: (PumpFunCurveState) -> BigDecimal) =
		TokenStateChangesSellingCommand(
			blockSlot = blockSlot,
			states = states.map {
				TokenStateChangesSellingCommand.NewState(
					tokenAddress = it.tokenAddress,
					marketCapUsd = it.marketCapUsd,
					liquidityUsd = it.liquidityUsd,
					numOfAccountHolders = it.numOfAccountHolders,
					volumeUsd = it.volumeUsd,
					buyVolume = it.buyVolume,
					sellVolume = it.sellVolume,
					curveState = it.curveState,
					tokenPrice = getTokenPrice(it.curveState),
					fractionOfSellTransactions = it.fractionOfSellTransactions,
				)
			},
		)
}

private val AVERAGE_BLOCK_SLOT_DURATION = 400.milliseconds
