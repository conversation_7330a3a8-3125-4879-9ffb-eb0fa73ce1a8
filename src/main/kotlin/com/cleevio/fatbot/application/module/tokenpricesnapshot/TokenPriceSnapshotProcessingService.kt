package com.cleevio.fatbot.application.module.tokenpricesnapshot

import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.tokenpricesnapshot.port.out.GetKnownTokenAddresses
import org.springframework.stereotype.Service
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

private val SNAPSHOT_AGE_THRESHOLD = Duration.of(36, ChronoUnit.HOURS)

@Service
class TokenPriceSnapshotProcessingService(
	private val getTokenPrices: GetTokenPrices,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val tokenPriceSnapshotService: TokenPriceSnapshotService,
	private val getKnownTokenAddresses: GetKnownTokenAddresses,
	private val clock: Clock,
) {

	fun processSnapshots() {
		val trackedTokens = getKnownTokenAddresses()
		val tokenToPrice = getTokenPrices(trackedTokens)
		val pricesValidAt = Instant.now(clock)

		val tokenCurrencies = trackedTokens.mapToSet { it.chain.currency }
		val cryptoCurrencyToExchangeRate = getUsdExchangeRate.getAll(cryptoCurrencies = tokenCurrencies)

		tokenPriceSnapshotService.saveTokenPriceSnapshots(
			tokenToPrice = tokenToPrice,
			cryptoCurrencyToExchangeRate = cryptoCurrencyToExchangeRate,
			validAt = pricesValidAt,
		)

		tokenPriceSnapshotService.deleteSnapshotsBefore(before = pricesValidAt.minus(SNAPSHOT_AGE_THRESHOLD))
	}
}
