package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.user.query.GetEnabledChainsQuery
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.stereotype.Component

@Component
class GetEnabledChainsQueryHandler(
	private val chainProperties: ChainProperties,
) : QueryHandler<GetEnabledChainsQuery.Result, GetEnabledChainsQuery> {

	override val query = GetEnabledChainsQuery::class

	override fun handle(query: GetEnabledChainsQuery): GetEnabledChainsQuery.Result {
		return GetEnabledChainsQuery.Result(
			enabledChains = chainProperties.enabledChains,
		)
	}
}
