package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.extractFirstSignatureAsTxHash
import com.cleevio.fatbot.application.common.crypto.hashToTxHash
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.service.PasswordValidationService
import com.cleevio.fatbot.application.common.service.command.VerifyPasswordCommand
import com.cleevio.fatbot.application.module.market.command.TransferTokenCommand
import com.cleevio.fatbot.application.module.market.port.out.EstimateGas
import com.cleevio.fatbot.application.module.market.port.out.TransferEvmToken
import com.cleevio.fatbot.application.module.market.port.out.TransferSolanaToken
import com.cleevio.fatbot.application.module.market.port.out.ValidateFundsForEvmTransaction
import com.cleevio.fatbot.application.module.market.port.out.ValidateFundsForSvmTransaction
import com.cleevio.fatbot.application.module.market.port.out.ValidateTokenBalance
import com.cleevio.fatbot.application.module.market.service.GasLimitEstimationService
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.event.TokenTransferredEvent
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.domain.wallet.Wallet
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class TransferTokenCommandHandler(
	private val walletFinderService: WalletFinderService,
	private val validateFundsForEvmTransaction: ValidateFundsForEvmTransaction,
	private val validateFundsForSvmTransaction: ValidateFundsForSvmTransaction,
	private val validateTokenBalance: ValidateTokenBalance,
	private val tokenInfoFinderService: EvmTokenInfoFinderService,
	private val transferEvmToken: TransferEvmToken,
	private val transferSolanaToken: TransferSolanaToken,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val gasLimitEstimationService: GasLimitEstimationService,
	private val passwordValidationService: PasswordValidationService,
) : CommandHandler<TransferTokenCommand.Result, TransferTokenCommand> {

	override val command = TransferTokenCommand::class

	override fun handle(command: TransferTokenCommand): TransferTokenCommand.Result = with(command) {
		passwordValidationService(VerifyPasswordCommand(userId, password))

		val wallet = walletFinderService.getByIdAndUserId(id = walletId, userId = userId)
		val tokenInfo = tokenInfoFinderService.getByTokenAddressAndChain(tokenAddress = tokenAddress, chain = wallet.chain)
		val tokenDecimals = tokenInfo.decimals.toInt()

		val result = when (wallet.chain.type) {
			ChainType.EVM -> handleEvmTransferToken(wallet, tokenDecimals)
			ChainType.SOLANA -> handleSolanaTransferToken(wallet, tokenDecimals, tokenInfo.isToken2022)
		}

		applicationEventPublisher.publishEvent(
			TokenTransferredEvent(
				walletId = wallet.id,
				signedTxList = listOf(result),
				tokenAddress = command.tokenAddress,
			),
		)

		return when (wallet.chain.type) {
			ChainType.EVM -> TransferTokenCommand.Result(txHash = result.hashToTxHash())
			ChainType.SOLANA -> TransferTokenCommand.Result(txHash = result.extractFirstSignatureAsTxHash())
		}
	}

	private fun TransferTokenCommand.handleEvmTransferToken(wallet: Wallet, tokenDecimals: Int): SignedTx {
		val baseAmount = nativeAmount.toBase(tokenDecimals)

		val estimateGasResult = gasLimitEstimationService.estimateGas(
			transactionType = TransactionType.TRANSFER_TOKEN,
			chain = wallet.chain,
			userId = userId,
			walletId = wallet.id,
			tokenAddress = tokenAddress,
			isToken2022 = false,
			baseAmount = baseAmount,
			recipientAddress = destinationWalletAddress,
		)

		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultEvm) error("Estimate gas must be of EVM type")

		val validateResult = validateFundsForEvmTransaction(
			walletAddress = wallet.address,
			tokenAddress = tokenAddress,
			chainId = wallet.chain.evmId,
			transactionType = TransactionType.TRANSFER_TOKEN,
			transactionAmount = baseAmount,
			gasInfo = estimateGasResult.gasInfo,
			gasLimitEstimate = estimateGasResult.gasLimitEstimate,
		)

		val gasInfo = when (wallet.chain) {
			// In case of arbitrum it is safer to refetch the gasInfo right before the call
			Chain.EVM_ARBITRUM_ONE -> null
			else -> estimateGasResult.gasInfo
		}

		return transferEvmToken(
			tokenAddress = tokenAddress,
			destinationWalletAddress = destinationWalletAddress,
			privateKey = wallet.privateKey,
			chainId = wallet.chain.evmId,
			amountToTransfer = baseAmount,
			gasInfoHint = gasInfo,
			gasLimitEstimate = validateResult.gasLimitEstimate,
		)
	}

	private fun TransferTokenCommand.handleSolanaTransferToken(
		wallet: Wallet,
		tokenDecimals: Int,
		isToken2022: Boolean,
	): SignedTx {
		val baseAmount = nativeAmount.toBase(tokenDecimals)

		val estimateGasResult = gasLimitEstimationService.estimateGas(
			transactionType = TransactionType.TRANSFER_TOKEN,
			chain = wallet.chain,
			userId = userId,
			walletId = wallet.id,
			tokenAddress = tokenAddress,
			isToken2022 = isToken2022,
			baseAmount = baseAmount,
			recipientAddress = wallet.address,
		)

		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultSolana) error("Estimate gas must be of EVM type")

		validateFundsForSvmTransaction(
			walletId = wallet.id,
			walletAddress = wallet.address,
			transactionAmount = BigInteger.ZERO.asBaseAmount(),
			estimatedFees = estimateGasResult.feeBaseAmount,
		)

		val result = validateTokenBalance(
			walletAddress = wallet.address,
			tokenAddress = tokenAddress,
			isToken2022 = isToken2022,
			amountToSend = baseAmount,
			tokenDecimals = tokenDecimals,
		)

		return transferSolanaToken(
			senderTokenAccountAddress = result.tokenAccountAddress,
			senderTokenAccountBalance = result.tokenAccountBalance,
			tokenAddress = tokenAddress,
			isToken2022 = isToken2022,
			tokenDecimals = tokenDecimals,
			destinationWalletAddress = destinationWalletAddress,
			privateKey = wallet.privateKey,
			amountToTransfer = baseAmount,
		)
	}
}
