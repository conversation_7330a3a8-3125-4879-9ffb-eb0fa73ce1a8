package com.cleevio.fatbot.application.module.userfattyleagueseason.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.BAD_REQUEST)
class UserFattyTokensNotClaimableException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.FATTY_TOKENS_NOT_CLAIMABLE,
	message = message,
)
