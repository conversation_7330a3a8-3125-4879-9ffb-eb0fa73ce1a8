package com.cleevio.fatbot.application.module.userfattycard.query

import com.cleevio.fatbot.application.common.query.Query
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.util.UUID

data class SearchUserFattyCardsQuery(
	val userId: UUID,
	val claimed: Boolean?,
	val displayed: Boolean?,
) : Query<List<SearchUserFattyCardsQuery.Result>> {

	@Schema(name = "SearchUserFattyCardsResult")
	data class Result(
		val userFattyCardId: UUID,
		val avatarFileId: UUID,
		val rarity: String,
		val probability: BigDecimal,
		val donutReward: BigDecimal,
	)
}

data class GetUserFattyCardsOverviewQuery(
	val userId: UUID,
) : Query<GetUserFattyCardsOverviewQuery.Result> {

	@Schema(name = "GetUserFattyCardsOverviewResult")
	data class Result(
		val userId: UUID,
		val amountOfFattyCardsEarnedToday: Int,
		val amountNeededToNextFattyCard: BigDecimal,
	)
}

data class GetUserFattyCardQuery(
	val userId: UUID,
	val userFattyCardId: UUID,
) : Query<GetUserFattyCardQuery.Result> {

	@Schema(name = "GetUserFattyCardResult")
	data class Result(
		val userFattyCardId: UUID,
		val avatarFileId: UUID,
		val rarity: String,
		val probability: BigDecimal,
		val donutReward: BigDecimal,
	)
}
