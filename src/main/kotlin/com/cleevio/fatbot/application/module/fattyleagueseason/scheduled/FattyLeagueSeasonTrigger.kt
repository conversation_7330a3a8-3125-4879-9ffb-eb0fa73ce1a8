package com.cleevio.fatbot.application.module.fattyleagueseason.scheduled

import com.cleevio.fatbot.application.module.fattyleagueseason.locks.CLOSE_FATTY_LEAGUE_SEASON_STATUSES
import com.cleevio.fatbot.application.module.fattyleagueseason.service.CloseFattyLeagueService
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class FattyLeagueSeasonTrigger(
	private val closeFattyLeagueService: CloseFattyLeagueService,
) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.close-fatty-league-season")
	@Scheduled(cron = "\${fatty-league.season.close.cron}")
	@SchedulerLock(
		name = CLOSE_FATTY_LEAGUE_SEASON_STATUSES,
		lockAtLeastFor = "\${fatty-league.season.close.lock-for}",
		lockAtMostFor = "\${fatty-league.season.close.lock-for}",
	)
	fun trigger() {
		logger.info("$CLOSE_FATTY_LEAGUE_SEASON_STATUSES cron started")
		closeFattyLeagueService.closeLastSeason()
		logger.info("$CLOSE_FATTY_LEAGUE_SEASON_STATUSES cron ended")
	}
}
