package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.adapter.`in`.toInfiniteScrollSlice
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator
import com.cleevio.fatbot.application.module.market.event.UserPortfolioViewedEvent
import com.cleevio.fatbot.application.module.market.port.out.FindAllUserMarketPosition
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.market.query.SearchUserMarketPositionQuery
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.tokenpricesnapshot.port.out.GetTokenPriceSnapshots
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import java.net.URI
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

@Component
class SearchUserMarketPositionQueryHandler(
	private val findAllUserMarketPosition: FindAllUserMarketPosition,
	private val getTokenPrices: GetTokenPrices,
	private val fileUrlMapper: FileUrlMapper,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val getTokenPriceSnapshots: GetTokenPriceSnapshots,
	private val usdConverter: UsdConverter,
	private val clock: Clock,
	private val chainProperties: ChainProperties,
	private val userFinderService: FirebaseUserFinderService,
	private val getUsdExchangeRate: GetUsdExchangeRate,
) : QueryHandler<InfiniteScrollSlice<SearchUserMarketPositionQuery.Result, UUID>, SearchUserMarketPositionQuery> {

	override val query = SearchUserMarketPositionQuery::class

	override fun handle(
		query: SearchUserMarketPositionQuery,
	): InfiniteScrollSlice<SearchUserMarketPositionQuery.Result, UUID> {
		val chains = when (query.useSelectedChains) {
			true -> userFinderService.getById(id = query.userId).selectedChains
			false -> chainProperties.enabledChains
		}

		val userMarketPositions = findAllUserMarketPosition(
			userId = query.userId,
			chains = chains,
			searchString = query.filter.searchString,
			walletId = query.filter.walletId,
			fileToUrlMapper = fileUrlMapper::map,
		)

		val tokens = userMarketPositions.mapToSet { it.tokenAddress.toChainAddress(it.chain) }

		val now = Instant.now(clock)

		val tokenToPriceUsd24hAgo = getTokenPriceSnapshots(
			tokens = tokens,
			before = now.minus(24, ChronoUnit.HOURS),
		).mapValues { (token, snapshot) -> usdConverter.baseToUsd(snapshot.price, token.chain, snapshot.exchangeRateUsd) }

		val tokenToPriceUsd1hAgo = getTokenPriceSnapshots(
			tokens = tokens,
			before = now.minus(1, ChronoUnit.HOURS),
		).mapValues { (token, snapshot) -> usdConverter.baseToUsd(snapshot.price, token.chain, snapshot.exchangeRateUsd) }

		val tokenToPrice = getTokenPrices(tokens = tokens)
		val chainToExchangeRate = Chain.entries.associateWith { getUsdExchangeRate(it.currency) }

		val result = userMarketPositions.map {
			val tokenChainAddress = it.tokenAddress.toChainAddress(it.chain)

			val tokenPriceNow = tokenToPrice.getValue(tokenChainAddress).toNative(it.chain)

			val tokenPriceUsdNow = usdConverter.nativeToUsd(tokenPriceNow, it.chain)
			val tokenPriceUsd1hAgo = tokenToPriceUsd1hAgo[tokenChainAddress]
			val tokenPriceUsd24hAgo = tokenToPriceUsd24hAgo[tokenChainAddress]

			val change1hFraction = tokenPriceUsd1hAgo?.let { _ ->
				calculateFractionChange(tokenPriceUsdNow, tokenPriceUsd1hAgo)
			}
			val change24hFraction = tokenPriceUsd24hAgo?.let { _ ->
				calculateFractionChange(tokenPriceUsdNow, tokenPriceUsd24hAgo)
			}

			val currentExchangeRate = chainToExchangeRate.getValue(it.chain)

			val result = MarketPositionCalculator.calculate(
				input = it,
				currentPricePerToken = tokenPriceNow,
				currentExchangeRate = currentExchangeRate,
			)

			val currentValueUsd = usdConverter.nativeToUsd(result.currentValue, it.chain)

			SearchUserMarketPositionQuery.Result(
				id = it.id,
				tokenAddress = it.tokenAddress,
				tokenDetailUrl = URI.create(it.tokenAddress.getLinkToExplorer(it.chain)),
				tokenName = it.tokenName,
				tokenSymbol = it.tokenSymbol,
				tokenChain = it.chain,
				tokenImageUrl = it.tokenImageUrl,
				pricePerTokenInUsd = tokenPriceUsdNow,
				tokenNativeAmount = result.currentUserTokenAmount.toNative(it.tokenDecimals),
				oneHourChangeFraction = change1hFraction,
				oneDayChangeFraction = change24hFraction,
				currentValueUsd = currentValueUsd,
				currentValueChangeUsd = result.currentPnlUsd,
				currentValueChangeFraction = result.currentPnlFractionUsd,
			)
		}

		// we do the fake slice here, because we can't calculate all the performance in DB and then do pagination there
		val lastSeenId = query.infiniteScroll.lastId

		applicationEventPublisher.publishEvent(UserPortfolioViewedEvent(query.userId))

		return result
			.sortedByDescending { it.currentValueChangeUsd }
			.takeLastWhile { it.id != lastSeenId }
			.take(query.infiniteScroll.size)
			.toInfiniteScrollSlice(infiniteScroll = query.infiniteScroll, idSelector = { id })
	}
}
