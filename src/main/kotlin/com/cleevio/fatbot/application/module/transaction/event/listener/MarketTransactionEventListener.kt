package com.cleevio.fatbot.application.module.transaction.event.listener

import com.cleevio.fatbot.application.module.transaction.TransactionService
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.event.EthTransferredEvent
import com.cleevio.fatbot.application.module.transaction.event.ReferralRewardClaimedEvent
import com.cleevio.fatbot.application.module.transaction.event.TokenBoughtEvent
import com.cleevio.fatbot.application.module.transaction.event.TokenSoldEvent
import com.cleevio.fatbot.application.module.transaction.event.TokenTransferredEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionalEventListener

@Component
class MarketTransactionEventListener(
	private val transactionService: TransactionService,
) {

	@EventListener
	fun handleAsyncTokenBoughtEvent(event: TokenBoughtEvent) {
		transactionService.saveTransactions(
			walletId = event.walletId,
			signedTxList = event.signedTxList,
			transactionType = TransactionType.BUY,
			tokenAddress = event.tokenAddress,
		)
	}

	@Async
	@TransactionalEventListener(fallbackExecution = true)
	fun handleAsyncTokenSoldEvent(event: TokenSoldEvent) {
		transactionService.saveTransactions(
			walletId = event.walletId,
			signedTxList = event.signedTxList,
			transactionType = TransactionType.SELL,
			tokenAddress = event.tokenAddress,
		)
	}

	@Async
	@TransactionalEventListener(fallbackExecution = true)
	fun handleAsyncTokenTransferredEvent(event: TokenTransferredEvent) {
		transactionService.saveTransactions(
			walletId = event.walletId,
			signedTxList = event.signedTxList,
			transactionType = TransactionType.TRANSFER_TOKEN,
			tokenAddress = event.tokenAddress,
		)
	}

	@Async
	@TransactionalEventListener(fallbackExecution = true)
	fun handleAsyncEthTransferredEvent(event: EthTransferredEvent) {
		transactionService.saveTransactions(
			walletId = event.walletId,
			signedTxList = event.signedTxList,
			transactionType = TransactionType.TRANSFER_CURRENCY,
			tokenAddress = null,
		)
	}

	@Async
	@TransactionalEventListener(fallbackExecution = true)
	fun handleAsyncRewardClaimedEvent(event: ReferralRewardClaimedEvent) {
		transactionService.saveTransactions(
			walletId = event.destinationWalletId,
			signedTxList = event.signedTxList,
			transactionType = TransactionType.CLAIM_REFERRAL_REWARD,
			tokenAddress = null,
		)
	}
}
