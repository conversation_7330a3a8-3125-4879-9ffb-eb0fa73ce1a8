package com.cleevio.fatbot.application.module.matchmaking.port.out

import com.cleevio.fatbot.adapter.out.aggregator.response.GetTokenStateResponse
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import io.sentry.spring.jakarta.tracing.SentrySpan

interface GetTokenState {

	@SentrySpan
	fun ofSingleToken(tokenAddress: AddressWrapper): GetTokenStateResponse

	@SentrySpan
	fun ofManyTokens(tokenAddresses: Set<AddressWrapper>): Map<AddressWrapper, GetTokenStateResponse>
}
