package com.cleevio.fatbot.application.module.analytics.port.out

import com.cleevio.fatbot.application.common.util.UUIDv7
import java.math.BigInteger
import java.time.LocalDate
import java.util.UUID

interface UpsertDailyFees {

	data class Input(
		val id: UUID = UUIDv7(),
		val date: LocalDate,
		val type: DailyFeeType,
		val amount: BigInteger,
	)

	operator fun invoke(dailyFees: List<Input>)
}

enum class DailyFeeType {
	REVENUE,
	REFERRAL,
}
