package com.cleevio.fatbot.application.module.matchmaking.port.out

import com.cleevio.fatbot.adapter.out.solana.pumpfun.model.PumpFunCurveState
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.matchmaking.command.TokenStateChangesBuyingCommand
import java.math.BigDecimal
import java.util.UUID

interface MatchBuyingBots {
	operator fun invoke(states: List<TokenStateChangesBuyingCommand.NewState>): List<Result>

	data class Result(
		val botId: UUID,
		val followerId: UUID?,
		val tokenAddress: AddressWrapper,
		val curveState: PumpFunCurveState,
		val tokenPrice: BigDecimal,
	)
}
