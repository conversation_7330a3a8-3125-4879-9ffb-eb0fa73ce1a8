package com.cleevio.fatbot.application.module.simulatedtoken.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.domain.simulation.SimulatedToken
import com.cleevio.fatbot.domain.simulation.SimulatedTokenRepository
import org.springframework.stereotype.Service

@Service
class SimulatedTokenFinderService(
	private val simulatedTokenRepository: SimulatedTokenRepository,
) : BaseFinderService<SimulatedToken>(simulatedTokenRepository) {

	override fun errorBlock(message: String) = error(message)

	override fun getEntityType() = SimulatedToken::class
}
