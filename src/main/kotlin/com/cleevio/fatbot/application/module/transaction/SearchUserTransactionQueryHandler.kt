package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.transaction.port.out.GetTxDetailUri
import com.cleevio.fatbot.application.module.transaction.port.out.SearchUserTransaction
import com.cleevio.fatbot.application.module.transaction.query.SearchUserTransactionQuery
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class SearchUserTransactionQueryHandler(
	private val searchUserTransaction: SearchUserTransaction,
	private val getTxDetailUri: GetTxDetailUri,
	private val fileUrlMapper: FileUrlMapper,
	private val userFinderService: FirebaseUserFinderService,
	private val properties: ChainProperties,
) : QueryHandler<InfiniteScrollSlice<SearchUserTransactionQuery.Result, UUID>, SearchUserTransactionQuery> {

	override val query = SearchUserTransactionQuery::class

	@SentrySpan
	override fun handle(query: SearchUserTransactionQuery): InfiniteScrollSlice<SearchUserTransactionQuery.Result, UUID> {
		val chains = when (query.useSelectedChains) {
			true -> userFinderService.getById(query.userId).selectedChains
			false -> properties.enabledChains
		}

		return searchUserTransaction(
			userId = query.userId,
			chains = chains,
			filter = query.filter,
			infiniteScroll = query.infiniteScroll,
			txHashToURIMapper = { txHash, chain -> getTxDetailUri(txHash, chain) },
			fileToUrlMapper = fileUrlMapper::map,
		)
	}
}
