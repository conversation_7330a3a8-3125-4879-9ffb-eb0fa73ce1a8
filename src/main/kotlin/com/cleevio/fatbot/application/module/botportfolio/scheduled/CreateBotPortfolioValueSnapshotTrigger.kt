package com.cleevio.fatbot.application.module.botportfolio.scheduled

import com.cleevio.fatbot.application.module.botportfolio.BotPortfolioValueSnapshotService
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

private const val LOCK_NAME = "CREATE_BOT_PORTFOLIO_VALUE_SNAPSHOT"

@Component
class CreateBotPortfolioValueSnapshotTrigger(
	private val botPortfolioValueSnapshotService: BotPortfolioValueSnapshotService,
) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.create-bot-portfolio-value-snapshot")
	@Scheduled(cron = "\${fatbot.bot.portfolio-value.create-snapshots.cron}")
	@SchedulerLock(
		name = LOCK_NAME,
		lockAtMostFor = "\${fatbot.bot.portfolio-value.create-snapshots.lock-for}",
		lockAtLeastFor = "\${fatbot.bot.portfolio-value.create-snapshots.lock-for}",
	)
	fun trigger() {
		logger.info("$LOCK_NAME cron started")
		botPortfolioValueSnapshotService.createBotPortfolioValueSnapshots()
		logger.info("$LOCK_NAME cron ended")
	}
}
