package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.token.query.GetIsTokenContractVerifiedQuery
import org.springframework.stereotype.Component

@Component
class GetIsTokenContractVerifiedQueryHandler(
	private val tokenInfoFinderService: EvmTokenInfoFinderService,
) : QueryHandler<GetIsTokenContractVerifiedQuery.Result, GetIsTokenContractVerifiedQuery> {

	override val query = GetIsTokenContractVerifiedQuery::class

	override fun handle(query: GetIsTokenContractVerifiedQuery): GetIsTokenContractVerifiedQuery.Result {
		val tokenInfo = tokenInfoFinderService.getByTokenAddressAndChain(query.tokenAddress, query.chain)

		return GetIsTokenContractVerifiedQuery.Result(isVerified = tokenInfo.isVerified)
	}
}
