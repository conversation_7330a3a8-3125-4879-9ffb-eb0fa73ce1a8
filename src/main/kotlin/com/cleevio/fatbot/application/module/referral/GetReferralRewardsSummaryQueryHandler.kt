package com.cleevio.fatbot.application.module.referral

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.referral.port.out.CountNumberOfReferrals
import com.cleevio.fatbot.application.module.referral.port.out.GetReferralRewardsSummaryByChain
import com.cleevio.fatbot.application.module.referral.query.GetReferralRewardsSummaryQuery
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.stereotype.Component

@Component
class GetReferralRewardsSummaryQueryHandler(
	private val countNumberOfReferrals: CountNumberOfReferrals,
	private val getReferralRewardsSummaryByChain: GetReferralRewardsSummaryByChain,
	private val chainProperties: ChainProperties,
	private val usdConverter: UsdConverter,
) : QueryHandler<GetReferralRewardsSummaryQuery.Result, GetReferralRewardsSummaryQuery> {
	override val query = GetReferralRewardsSummaryQuery::class

	override fun handle(query: GetReferralRewardsSummaryQuery): GetReferralRewardsSummaryQuery.Result {
		val numberOfReferrals = countNumberOfReferrals(query.userId)
		val chainToReferralRewards = getReferralRewardsSummaryByChain(query.userId)

		val referralsOnChains = chainProperties.enabledChains.map { chain ->

			val totalAmount = chainToReferralRewards[chain]?.totalBaseAmount ?: BaseAmount.ZERO
			val unclaimedAmount = chainToReferralRewards[chain]?.unclaimedBaseAmount ?: BaseAmount.ZERO

			GetReferralRewardsSummaryQuery.ChainData(
				chain = chain,
				totalAmountNative = totalAmount.toNative(chain),
				totalAmountUsd = usdConverter.baseToUsd(totalAmount, chain),
				unclaimedAmountNative = unclaimedAmount.toNative(chain),
				unclaimedAmountUsd = usdConverter.baseToUsd(unclaimedAmount, chain),
			)
		}

		return GetReferralRewardsSummaryQuery.Result(
			numberOfReferrals = numberOfReferrals,
			referralsOnChains = referralsOnChains,
		)
	}
}
