package com.cleevio.fatbot.application.module.userleaderbord.service

import com.cleevio.fatbot.application.module.userleaderbord.locks.REFRESH_USER_LEADERBOARD
import com.cleevio.fatbot.application.module.userleaderbord.locks.USER_LEADERBOARD_MODULE
import com.cleevio.fatbot.application.module.userleaderbord.port.out.GetTopUsersByDonutGained
import com.cleevio.fatbot.application.module.userleaderbord.properties.LeaderboardMultiplierProperties
import com.cleevio.fatbot.domain.userleaderboad.UserLeaderboardCreateService
import com.cleevio.fatbot.domain.userleaderboad.UserLeaderboardDeleteAllService
import com.cleevio.library.lockinghandler.service.Lock
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class RefreshUserLeaderboardService(
	private val userLeaderboardCreateService: UserLeaderboardCreateService,
	private val userLeaderboardDeleteAllService: UserLeaderboardDeleteAllService,
	private val getTopUsersByDonutGained: GetTopUsersByDonutGained,
	private val leaderboardMultiplierProperties: LeaderboardMultiplierProperties,
) {

	@Transactional
	@Lock(module = USER_LEADERBOARD_MODULE, lockName = REFRESH_USER_LEADERBOARD)
	operator fun invoke() {
		userLeaderboardDeleteAllService.deleteAll()

		getTopUsersByDonutGained(1000).mapIndexed { index, stats ->
			userLeaderboardCreateService.create(
				userId = stats.userId,
				rank = index + 1,
				donutMultiplier = leaderboardMultiplierProperties.getMultiplierByRank(index + 1),
				donutGainedSnapshot = stats.donutGainedAmount,
			)
		}
	}
}
