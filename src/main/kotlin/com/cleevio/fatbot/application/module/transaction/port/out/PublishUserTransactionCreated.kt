package com.cleevio.fatbot.application.module.transaction.port.out

import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import java.util.UUID

interface PublishUserTransactionCreated {
	operator fun invoke(transactionType: TransactionType, userId: UUID, txHash: TxHash, amountOut: NativeAmount? = null)
}
