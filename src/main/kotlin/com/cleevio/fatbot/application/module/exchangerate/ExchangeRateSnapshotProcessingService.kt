package com.cleevio.fatbot.application.module.exchangerate

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import org.springframework.stereotype.Service

@Service
class ExchangeRateSnapshotProcessingService(
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val exchangeRateSnapshotService: ExchangeRateSnapshotService,
) {

	fun processSnapshots() {
		val currencyToRate = CryptoCurrency.entries.associateWith { getUsdExchangeRate(cryptoCurrency = it) }

		exchangeRateSnapshotService.createNewAndDeleteOld(currencyToRate)
	}
}
