package com.cleevio.fatbot.application.module.bot.event.listener

import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.bot.event.BotDetailViewedEvent
import com.cleevio.fatbot.application.module.botwallet.command.SyncUserBotWalletBalancesCommand
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component

@Component
class BotEventListener(
	private val commandBus: CommandBus,
) {

	@Async
	@EventListener
	@SentryTransaction(operation = "async.bot-detail-viewed")
	fun onBotDetailViewedEvent(event: BotDetailViewedEvent) {
		commandBus(SyncUserBotWalletBalancesCommand(userId = event.userId, botId = event.botId))
	}
}
