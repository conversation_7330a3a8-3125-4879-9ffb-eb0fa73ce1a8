package com.cleevio.fatbot.application.module.market.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.market.MarketPosition
import com.cleevio.fatbot.domain.market.MarketPositionRepository
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class MarketPositionFinderService(
	private val marketPositionRepository: MarketPositionRepository,
) : BaseFinderService<MarketPosition>(marketPositionRepository) {

	override fun errorBlock(message: String) = error(message)

	override fun getEntityType() = MarketPosition::class

	fun findMarketPosition(walletId: UUID, chain: Chain, tokenAddress: AddressWrapper) =
		marketPositionRepository.findByWalletIdAndChainAndTokenAddress(
			walletId = walletId,
			chain = chain,
			tokenAddress = tokenAddress,
		)

	fun findAllByWalletIds(walletIds: Set<UUID>): List<MarketPosition> =
		marketPositionRepository.findAllByWalletIdIn(walletIds)
}
