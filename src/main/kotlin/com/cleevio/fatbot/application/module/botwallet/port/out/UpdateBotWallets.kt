package com.cleevio.fatbot.application.module.botwallet.port.out

import com.cleevio.fatbot.application.common.crypto.TxHash
import java.math.BigDecimal
import java.math.BigInteger
import java.util.UUID

interface UpdateBotWallets {

	data class Input(
		val botWalletId: UUID,
		val balanceIncrease: BigInteger,
		val acquisitionValueUsdIncrease: BigDecimal,
		val latestSignature: TxHash?,
		val originalSignature: TxHash?,
	)

	operator fun invoke(inputs: List<Input>)
}
