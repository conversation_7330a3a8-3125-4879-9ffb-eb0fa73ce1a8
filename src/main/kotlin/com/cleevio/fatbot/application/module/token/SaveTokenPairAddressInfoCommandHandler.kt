package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.adapter.out.solana.pumpfun.GetPumpfunCurveState
import com.cleevio.fatbot.adapter.out.solana.pumpswap.GetPumpswapPool
import com.cleevio.fatbot.adapter.out.solana.raydium.GetRaydiumAMMMarketData
import com.cleevio.fatbot.adapter.out.solana.raydium.GetRaydiumCPMMMarketData
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.token.command.SaveTokenPairAddressInfoCommand
import com.cleevio.fatbot.application.module.token.finder.TokenPairInfoFinderService
import com.cleevio.fatbot.application.module.token.locks.CREATE_OR_UPDATE_TOKEN_PAIR
import com.cleevio.fatbot.application.module.token.locks.TOKEN_MODULE
import com.cleevio.fatbot.domain.token.TokenPairInfoCreateService
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class SaveTokenPairAddressInfoCommandHandler(
	private val tokenPairInfoCreateService: TokenPairInfoCreateService,
	private val tokenPairInfoFinderService: TokenPairInfoFinderService,
	private val getRaydiumAMMMarketData: GetRaydiumAMMMarketData,
	private val getRaydiumCPMMMarketData: GetRaydiumCPMMMarketData,
	private val getPumpswapPool: GetPumpswapPool,
	private val getPumpfunCurveState: GetPumpfunCurveState,
) : CommandHandler<Unit, SaveTokenPairAddressInfoCommand> {

	override val command = SaveTokenPairAddressInfoCommand::class

	@Autowired
	@Lazy
	private lateinit var self: SaveTokenPairAddressInfoCommandHandler

	private val logger = logger()

	override fun handle(command: SaveTokenPairAddressInfoCommand) = with(command) {
		val tokenPair = tokenPairInfoFinderService.findByPairAddressAndChain(dex.pairAddress, chain)

		if (tokenPair != null) return@with

		val raydiumPoolType = (dex as? GetDex.Raydium)?.poolType
		val ammMarketData = when (raydiumPoolType) {
			GetDex.PoolType.AMM -> getRaydiumAMMMarketData(pairAddress = dex.pairAddress)
			else -> null
		}
		val cpmmMarketData = when (raydiumPoolType) {
			GetDex.PoolType.CPMM -> getRaydiumCPMMMarketData(pairAddress = dex.pairAddress)
			else -> null
		}

		val creatorAddress = when (dex.dex) {
			GetDex.Dex.PUMP_FUN -> {
				val creator = getPumpfunCurveState(dex.pairAddress).creator

				if (creator == null) {
					logger.error(
						"""
							Failed to fetch creator for Pumpfun BondingCurve ${dex.pairAddress}.
							Saving this token pair with creator = null.
						""".trimIndent(),
					)
				}

				creator
			}
			GetDex.Dex.PUMP_SWAP -> getPumpswapPool(dex.pairAddress).coinCreator
			else -> null
		}

		self.inTransactionTryLock(pairAddress = command.dex.pairAddress) {
			tokenPairInfoCreateService.create(
				chain = chain,
				tokenAddress = tokenAddress,
				dex = dex,
				tokenDecimals = command.tokenDecimals,
				raydiumAmmMarketData = ammMarketData,
				raydiumCpmmMarketData = cpmmMarketData,
				creatorAddress = creatorAddress,
			)
		}
	}

	@Transactional
	@TryLock(module = TOKEN_MODULE, lockName = CREATE_OR_UPDATE_TOKEN_PAIR)
	protected fun inTransactionTryLock(@LockArgumentParameter pairAddress: AddressWrapper, block: () -> Unit) = block()
}
