package com.cleevio.fatbot.application.module.market.command

import com.cleevio.fatbot.application.common.command.Command
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.Positive
import java.util.UUID

data class BuyTokenCommand(
	val userId: UUID,
	val walletId: UUID,
	val dexPairInfo: GetDex.Result,
	val tokenAddress: AddressWrapper,
	@field:Positive val buyForNativeAmount: NativeAmount,
) : Command<BuyTokenCommand.Result> {

	@Schema(name = "BuyTokenResult")
	data class Result(val txHash: TxHash)
}

data class QuickBuyTokenCommand(
	val userId: UUID,
	val tokenAddress: AddressWrapper,
	val chain: Chain,
) : Command<QuickBuyTokenCommand.Result> {

	@Schema(name = "QuickBuyTokenResult")
	data class Result(val txHash: TxHash)
}

data class SellTokenCommand(
	val userId: UUID,
	val walletId: UUID,
	val dexPairInfo: GetDex.Result,
	val tokenAddress: AddressWrapper,
	@field:Positive val toSellNativeAmount: NativeAmount,
) : Command<SellTokenCommand.Result> {

	@Schema(name = "SellTokenResult")
	data class Result(val txHashes: List<TxHash>)
}

data class TransferTokenCommand(
	val userId: UUID,
	val walletId: UUID,
	val password: String,
	val tokenAddress: AddressWrapper,
	val destinationWalletAddress: AddressWrapper,
	// must be as @field: because of value class
	@field:Positive val nativeAmount: NativeAmount,
) : Command<TransferTokenCommand.Result> {

	@Schema(name = "TransferTokenResult")
	data class Result(val txHash: TxHash)
}

data class TransferCurrencyCommand(
	val userId: UUID,
	val walletId: UUID,
	val destinationWalletAddress: AddressWrapper,
	val password: String,
	// must be as @field: because of value class
	@field:Positive val toTransferNativeAmount: NativeAmount,
) : Command<TransferCurrencyCommand.Result> {

	@Schema(name = "TransferCurrencyResult")
	data class Result(val txHash: TxHash)
}

data class TrackTokenCommand(
	val userId: UUID,
	val walletId: UUID,
	val tokenAddress: AddressWrapper,
) : Command<TrackTokenCommand.Result> {

	@Schema(name = "TrackTokenResult")
	data class Result(val result: TrackTokenResult)

	enum class TrackTokenResult {
		BALANCES_EVENED,
		BALANCES_ARE_EQUAL,
		PLATFORM_BALANCE_HIGHER,
	}
}

data class UpdateUserPositionsCommand(val userId: UUID) : Command<Unit>

data class PreApproveTokenCommand(
	val walletId: UUID,
	val tokenAddress: AddressWrapper,
	val chainId: Long,
) : Command<Unit>
