package com.cleevio.fatbot.application.module.token.port.out

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.token.constant.TimeInterval
import com.cleevio.fatbot.application.module.token.query.TokenPriceTimeIntervalItem
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.time.Instant

interface GetSingleIntervalTokenPrice {

	operator fun invoke(
		chain: Chain,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		dex: GetDex.Dex,
		intervalCount: Int,
		timeInterval: TimeInterval,
		before: Instant,
	): TokenPriceTimeIntervalItem?
}
