package com.cleevio.fatbot.application.module.token.port.out

import com.cleevio.fatbot.adapter.out.bitquery.cache.GetIntervalTokenPricesCacheValue
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.token.constant.TimeInterval
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.wallet.constant.Chain

interface GetIntervalTokenPrices {

	operator fun invoke(
		chain: Chain,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		dex: GetDex.Dex,
		timeRange: TimeRange,
	): GetIntervalTokenPricesCacheValue

	operator fun invoke(
		chain: Chain,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		dex: GetDex.Dex,
		intervalCount: Int,
		timeInterval: TimeInterval,
		limit: Int,
	): GetIntervalTokenPricesCacheValue
}
