package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.module.bot.exception.BotNotFoundException
import com.cleevio.fatbot.domain.bot.Bot
import com.cleevio.fatbot.domain.bot.BotRepository
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class BotFinderService(private val botRepository: BotRepository) : BaseFinderService<Bot>(botRepository) {
	override fun errorBlock(message: String) = throw BotNotFoundException(message)

	override fun getEntityType() = Bot::class

	fun findAllByUserId(userId: UUID) = botRepository.findAllByUserId(userId)

	fun findAllByUserIdAndIsActiveTrue(userId: UUID) = botRepository.findAllByUserIdAndIsActiveTrue(userId)

	fun getByIdAndUserId(id: UUID, userId: UUID): Bot =
		botRepository.findByIdAndUserId(id, userId) ?: errorBlock("Bot for user $userId with ID $id not found!")
}
