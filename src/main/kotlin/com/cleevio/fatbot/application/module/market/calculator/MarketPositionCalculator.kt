package com.cleevio.fatbot.application.module.market.calculator

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.compareTo
import com.cleevio.fatbot.application.common.crypto.div
import com.cleevio.fatbot.application.common.crypto.minus
import com.cleevio.fatbot.application.common.crypto.plus
import com.cleevio.fatbot.application.common.crypto.times
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.math.BigInteger

object MarketPositionCalculator {

	interface Input {
		val chain: Chain
		val tokenAddress: AddressWrapper
		val tokenDecimals: Int
		val tokenAmountBought: BaseAmount
		val tokenAcquisitionCost: BaseAmount
		val tokenAmountSold: BaseAmount
		val tokenDispositionCost: BaseAmount
		val tokenAcquisitionCostUsd: BigDecimal
	}

	data class Result(
		val currentUserTokenAmount: BaseAmount,
		val currentUserTokenAmountNative: NativeAmount,
		val userBoughtTokenAmountNative: NativeAmount,
		val acquisitionValue: NativeAmount,
		val acquisitionValueUsd: BigDecimal,
		val acquisitionValueOwnedTokenUsd: BigDecimal,
		val currentValue: NativeAmount,
		val currentValueUsd: BigDecimal,
		val currentPnl: NativeAmount,
		val currentPnlFraction: BigDecimal,
		val currentPnlUsd: BigDecimal,
		val currentPnlFractionUsd: BigDecimal,
	) {
		companion object {
			val EMPTY = Result(
				currentUserTokenAmount = BigInteger.ZERO.asBaseAmount(),
				currentUserTokenAmountNative = BigDecimal.ZERO.asNativeAmount(),
				userBoughtTokenAmountNative = BigDecimal.ZERO.asNativeAmount(),
				acquisitionValue = BigDecimal.ZERO.asNativeAmount(),
				acquisitionValueUsd = BigDecimal.ZERO,
				acquisitionValueOwnedTokenUsd = BigDecimal.ZERO,
				currentValue = BigDecimal.ZERO.asNativeAmount(),
				currentValueUsd = BigDecimal.ZERO,
				currentPnl = BigDecimal.ZERO.asNativeAmount(),
				currentPnlFraction = BigDecimal.ZERO,
				currentPnlUsd = BigDecimal.ZERO,
				currentPnlFractionUsd = BigDecimal.ZERO,
			)
		}

		fun toUsd(pastExchangeRate: BigDecimal, currentExchangeRate: BigDecimal, rent: NativeAmount): UsdResult {
			val acquisitionExchangeRate = if (pastExchangeRate.compareTo(BigDecimal.ZERO) == 0) {
				currentExchangeRate
			} else {
				pastExchangeRate
			}

			val acquisitionValueUsd = acquisitionValue.amount.multiply(acquisitionExchangeRate)
			val currentValueUsd = currentValue.amount.multiply(currentExchangeRate)
			val rentUsd = rent.amount.multiply(currentExchangeRate)

			return UsdResult(
				currentUserTokenAmount = currentUserTokenAmount,
				acquisitionValueUsd = acquisitionValueUsd,
				currentValueUsd = currentValueUsd,
				currentPnlUsd = currentValueUsd - acquisitionValueUsd,
				currentPnlFraction = calculateFractionChange(currentValueUsd, acquisitionValueUsd),
				rentUsd = rentUsd,
			)
		}
	}

	data class UsdResult(
		val currentUserTokenAmount: BaseAmount,
		val acquisitionValueUsd: BigDecimal,
		val currentValueUsd: BigDecimal,
		val currentPnlUsd: BigDecimal,
		val currentPnlFraction: BigDecimal,
		val rentUsd: BigDecimal,
	) {
		companion object {
			val EMPTY = UsdResult(
				currentUserTokenAmount = BigInteger.ZERO.asBaseAmount(),
				acquisitionValueUsd = BigDecimal.ZERO,
				currentValueUsd = BigDecimal.ZERO,
				currentPnlUsd = BigDecimal.ZERO,
				currentPnlFraction = BigDecimal.ZERO,
				rentUsd = BigDecimal.ZERO,
			)
		}
	}

	fun sumByChain(
		marketPositions: List<Input>,
		tokenAddressToPriceData: Map<ChainAddress, BaseAmount>,
		chainToExchangeRate: Map<Chain, BigDecimal>,
	): Map<Chain, Result> = marketPositions
		.groupBy { it.chain }
		.mapValues { (chain, marketPositions) ->
			sum(
				marketPositions,
				tokenAddressToPriceData,
				chainToExchangeRate.getValue(chain),
			)
		}

	private fun sum(
		marketPositions: List<Input>,
		tokenAddressToTokenPrice: Map<ChainAddress, BaseAmount>,
		currentExchangeRate: BigDecimal,
	) = marketPositions
		.fold(Result.EMPTY) { acc, input ->
			val addressOnChain = input.tokenAddress.toChainAddress(input.chain)
			val currentPricePerToken = tokenAddressToTokenPrice[addressOnChain] ?: return@fold Result.EMPTY

			val result = calculate(
				input = input,
				currentPricePerToken = currentPricePerToken.toNative(input.chain),
				currentExchangeRate = currentExchangeRate,
			)
			acc + result
		}

	fun calculate(input: Input, currentPricePerToken: NativeAmount, currentExchangeRate: BigDecimal) = calculate(
		chain = input.chain,
		tokenDecimals = input.tokenDecimals,
		tokenAmountBought = input.tokenAmountBought,
		totalTokenAcquisitionCost = input.tokenAcquisitionCost,
		totalTokenAcquisitionCostUsd = input.tokenAcquisitionCostUsd,
		tokenAmountSold = input.tokenAmountSold,
		tokenDispositionCost = input.tokenDispositionCost,
		currentPricePerNativeToken = currentPricePerToken,
		currentExchangeRate = currentExchangeRate,
	)

	fun calculate(
		chain: Chain,
		tokenDecimals: Int,
		tokenAmountBought: BaseAmount,
		totalTokenAcquisitionCost: BaseAmount,
		totalTokenAcquisitionCostUsd: BigDecimal,
		tokenAmountSold: BaseAmount,
		tokenDispositionCost: BaseAmount,
		currentPricePerNativeToken: NativeAmount,
		currentExchangeRate: BigDecimal,
	): Result {
		val tokenAmountOwned = tokenAmountBought - tokenAmountSold
		check(tokenAmountOwned > BigInteger.ZERO) { "Market position with no holding should be closed!" }

		val tokenAmountOwnedNative = tokenAmountOwned.toNative(tokenDecimals)
		val tokenAmountBoughtNative = tokenAmountBought.toNative(tokenDecimals)
		val tokenAmountSoldNative = tokenAmountSold.toNative(tokenDecimals)

		val tokenAmountSoldValueNative = tokenDispositionCost.toNative(chain)
		val spentForAcquisitionOfAllTokens = totalTokenAcquisitionCost.toNative(chain)
		val averageTokenAcquisitionPrice = spentForAcquisitionOfAllTokens / tokenAmountBoughtNative

		/*
			TODO: 1. Add acquisitionUsd column for market position
				  2. Add new usd pln field to calculator result
				  3. Migration
		 */

		val tokenAmountOwnedMarketValue = tokenAmountOwnedNative * currentPricePerNativeToken
		val tokenAmountOwnedMarketValueUsd = tokenAmountOwnedMarketValue.amount * currentExchangeRate
		val tokenAmountOwnedAcquisitionValue = tokenAmountOwnedNative * averageTokenAcquisitionPrice
		val unrealizedGains = tokenAmountOwnedMarketValue - tokenAmountOwnedAcquisitionValue

		val tokenAmountSoldAcquisitionValue = tokenAmountSoldNative * averageTokenAcquisitionPrice
		val realizedGains = tokenAmountSoldValueNative - tokenAmountSoldAcquisitionValue

		val profitAndLoss = unrealizedGains + realizedGains

		val currentTotalPositionValue = tokenAmountSoldValueNative + tokenAmountOwnedMarketValue
		val pnlFractionChange =
			calculateFractionChange(currentTotalPositionValue.amount, spentForAcquisitionOfAllTokens.amount)

		val ownedTokenFraction = tokenAmountOwnedNative.amount / tokenAmountBoughtNative.amount
		val acquisitionValueOwnedTokenUsd = totalTokenAcquisitionCostUsd * ownedTokenFraction

		val pnlUsd = tokenAmountOwnedMarketValueUsd - acquisitionValueOwnedTokenUsd
		val pnlUsdFraction = calculateFractionChange(tokenAmountOwnedMarketValueUsd, acquisitionValueOwnedTokenUsd)

		return Result(
			currentUserTokenAmount = tokenAmountOwned,
			currentUserTokenAmountNative = tokenAmountOwnedNative,
			userBoughtTokenAmountNative = tokenAmountBoughtNative,
			acquisitionValue = tokenAmountOwnedAcquisitionValue,
			acquisitionValueUsd = totalTokenAcquisitionCostUsd,
			acquisitionValueOwnedTokenUsd = acquisitionValueOwnedTokenUsd,
			currentValue = tokenAmountOwnedMarketValue,
			currentValueUsd = tokenAmountOwnedMarketValueUsd,
			currentPnl = profitAndLoss,
			currentPnlFraction = pnlFractionChange,
			currentPnlUsd = pnlUsd,
			currentPnlFractionUsd = pnlUsdFraction,
		)
	}

	operator fun Result.plus(other: Result): Result {
		val totalUserTokenAmount = this.currentUserTokenAmount + other.currentUserTokenAmount
		val totalUserTokenAmountNative = this.currentUserTokenAmountNative + other.currentUserTokenAmountNative
		val totalUserBoughtTokenAmount = this.userBoughtTokenAmountNative + other.userBoughtTokenAmountNative
		val totalValue = this.currentValue + other.currentValue
		val totalValueUsd = this.currentValueUsd + other.currentValueUsd
		val totalAcquisitionValue = this.acquisitionValue + other.acquisitionValue
		val totalAcquisitionValueUsd = this.acquisitionValueUsd + other.acquisitionValueUsd
		val totalAcquisitionValueOwnedTokenUsd =
			this.acquisitionValueOwnedTokenUsd + other.acquisitionValueOwnedTokenUsd

		val totalPnl = totalValue - totalAcquisitionValue
		val totalPnlChangeFraction = calculateFractionChange(totalValue.amount, totalAcquisitionValue.amount)

		val totalPnlUsd = totalValueUsd - totalAcquisitionValueOwnedTokenUsd
		val totalPnlChangeFractionUsd = calculateFractionChange(totalValueUsd, totalAcquisitionValueOwnedTokenUsd)

		return Result(
			currentUserTokenAmount = totalUserTokenAmount,
			currentUserTokenAmountNative = totalUserTokenAmountNative,
			userBoughtTokenAmountNative = totalUserBoughtTokenAmount,
			acquisitionValue = totalAcquisitionValue,
			acquisitionValueUsd = totalAcquisitionValueUsd,
			acquisitionValueOwnedTokenUsd = totalAcquisitionValueOwnedTokenUsd,
			currentValue = totalValue,
			currentValueUsd = totalValueUsd,
			currentPnl = totalPnl,
			currentPnlFraction = totalPnlChangeFraction,
			currentPnlUsd = totalPnlUsd,
			currentPnlFractionUsd = totalPnlChangeFractionUsd,
		)
	}

	operator fun UsdResult.plus(other: UsdResult): UsdResult {
		val totalUserTokenAmount = this.currentUserTokenAmount + other.currentUserTokenAmount
		val totalValueUsd = this.currentValueUsd + other.currentValueUsd
		val totalAcquisitionValueUsd = this.acquisitionValueUsd + other.acquisitionValueUsd
		val totalRentValueUsd = this.rentUsd + other.rentUsd

		return UsdResult(
			currentUserTokenAmount = totalUserTokenAmount,
			acquisitionValueUsd = totalAcquisitionValueUsd,
			currentValueUsd = totalValueUsd,
			currentPnlUsd = totalValueUsd - totalAcquisitionValueUsd,
			currentPnlFraction = calculateFractionChange(totalValueUsd, totalAcquisitionValueUsd),
			rentUsd = totalRentValueUsd,
		)
	}
}
