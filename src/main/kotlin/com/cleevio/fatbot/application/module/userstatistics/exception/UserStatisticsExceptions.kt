package com.cleevio.fatbot.application.module.userstatistics.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class UserStatisticsNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.USER_STATISTICS_NOT_FOUND,
	message = message,
)
