package com.cleevio.fatbot.application.module.limitorder.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class LimitOrderExceptionNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.LIMIT_ORDER_NOT_FOUND,
	message = message,
)
