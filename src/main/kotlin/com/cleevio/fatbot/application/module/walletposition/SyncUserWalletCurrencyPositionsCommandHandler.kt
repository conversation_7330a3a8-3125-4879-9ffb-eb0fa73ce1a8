package com.cleevio.fatbot.application.module.walletposition

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.application.module.wallet.service.WalletBalanceService
import com.cleevio.fatbot.application.module.walletposition.command.SyncUserWalletCurrencyPositionsCommand
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component

@Component
class SyncUserWalletCurrencyPositionsCommandHandler(
	private val walletFinderService: WalletFinderService,
	private val walletBalanceService: WalletBalanceService,
	private val syncWalletCurrencyPositionService: SyncWalletCurrencyPositionService,
	private val chainProperties: ChainProperties,
) : CommandHandler<Unit, SyncUserWalletCurrencyPositionsCommand> {

	override val command = SyncUserWalletCurrencyPositionsCommand::class

	@SentrySpan
	override fun handle(command: SyncUserWalletCurrencyPositionsCommand) {
		val wallets = walletFinderService.findAllByUserIdAndChainIn(
			userId = command.userId,
			chains = chainProperties.enabledChains,
		)

		val walletIdToChainAddress = wallets.associate { it.id to it.address.toChainAddress(it.chain) }
		val addressToBalance = walletBalanceService.getBalances(walletIdToChainAddress.values.toList())

		val walletIdToChainBalance = walletIdToChainAddress.mapValues { (_, address) ->
			val balance = addressToBalance.getValue(address)

			balance.amount to address.chain
		}

		syncWalletCurrencyPositionService.syncWallets(walletIdToChainBalance)
	}
}
