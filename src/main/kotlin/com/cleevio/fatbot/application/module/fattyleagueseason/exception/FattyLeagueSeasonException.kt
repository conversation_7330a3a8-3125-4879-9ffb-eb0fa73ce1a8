package com.cleevio.fatbot.application.module.fattyleagueseason.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class FattyLeagueSeasonNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.FATTY_LEAGUE_SEASON_NOT_FOUND,
	message = message,
)
