package com.cleevio.fatbot.application.module.botmarket.constant

enum class Bot<PERSON>arketPositionState {
	OPENED,
	PENDING_CLOSED,
	PENDING_CLOSED_FROM_PROMOTED,
	PENDING_CLOSED_FROM_STALE,
	PENDING_CLOSED_FROM_RED_FLAG,
	PENDING_CLOSED_FROM_FORCE_SELL,
	CONFIRMED_CLOSED,

	OPEN_NOT_LANDED,
	OPEN_FAILED,

	UNKNOWN_ERROR,
	;

	companion object {
		val openStates = setOf(
			OPENED,
			PENDING_CLOSED,
			PENDING_CLOSED_FROM_PROMOTED,
			PENDING_CLOSED_FROM_STALE,
			PENDING_CLOSED_FROM_RED_FLAG,
			PENDING_CLOSED_FROM_FORCE_SELL,
		)

		val closedStates = setOf(CONFIRMED_CLOSED)
		val openingErrorStates = setOf(OPEN_NOT_LANDED, O<PERSON><PERSON>_FAILED)
		val closingErrorStates = setOf(UNKNOWN_ERROR)
	}
}
