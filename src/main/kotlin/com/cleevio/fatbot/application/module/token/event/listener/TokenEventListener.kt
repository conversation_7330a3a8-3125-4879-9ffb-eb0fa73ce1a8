package com.cleevio.fatbot.application.module.token.event.listener

import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.token.command.BackfillTokenPricesCommand
import com.cleevio.fatbot.application.module.token.command.SaveTokenInfoCommand
import com.cleevio.fatbot.application.module.token.command.SaveTokenPairAddressInfoCommand
import com.cleevio.fatbot.application.module.token.event.NewEvmTokenInfoCreatedEvent
import com.cleevio.fatbot.application.module.token.event.TokenOnDexScreenerFoundEvent
import com.cleevio.fatbot.application.module.token.event.TokenPairFoundEvent
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Component
class TokenEventListener(
	private val commandBus: CommandBus,
) {

	@Async
	@EventListener
	@SentryTransaction(operation = "async.save-token-info")
	fun onTokenOnDexScreenerFoundEvent(event: TokenOnDexScreenerFoundEvent) {
		commandBus(
			SaveTokenInfoCommand(
				chain = event.chain,
				tokenAddress = event.tokenAddress,
				pairAddress = event.pairAddress,
				decimals = event.decimals,
				name = event.name,
				symbol = event.symbol,
				imageUrl = event.imageUrl,
			),
		)
	}

	@EventListener
	@Transactional(propagation = Propagation.NEVER)
	fun onTokenPairFoundEvent(event: TokenPairFoundEvent) {
		commandBus(
			SaveTokenPairAddressInfoCommand(
				chain = event.chain,
				dex = event.dex,
				tokenDecimals = event.tokenDecimals,
				tokenAddress = event.tokenAddress,
			),
		)
	}

	@EventListener
	fun onNewEvmTokenInfoCreated(event: NewEvmTokenInfoCreatedEvent) {
		commandBus(
			BackfillTokenPricesCommand(
				chain = event.chain,
				tokenAddress = event.tokenAddress,
				pairAddress = event.pairAddress,
			),
		)
	}
}
