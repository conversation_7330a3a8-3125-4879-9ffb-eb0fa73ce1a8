package com.cleevio.fatbot.application.module.bottransaction.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.module.bottransaction.exception.BotTransactionNotFoundException
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.domain.bottransaction.BotTransaction
import com.cleevio.fatbot.domain.bottransaction.BotTransactionRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class BotTransactionFinderService(
	private val botTransactionRepository: BotTransactionRepository,
) : BaseFinderService<BotTransaction>(botTransactionRepository) {

	override fun errorBlock(message: String) = throw BotTransactionNotFoundException(message)

	override fun getEntityType() = BotTransaction::class

	@Transactional(readOnly = true)
	fun findAllByStatusOrderByCreatedAt(status: TransactionStatus) =
		botTransactionRepository.findAllByStatusOrderByCreatedAt(status)
}
