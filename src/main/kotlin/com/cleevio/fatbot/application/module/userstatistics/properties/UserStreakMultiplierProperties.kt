package com.cleevio.fatbot.application.module.userstatistics.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.math.BigDecimal

@ConfigurationProperties(prefix = "streak-system.multiplier")
class UserStreakMultiplierProperties(
	val thresholds: Map<Int, BigDecimal> = emptyMap(),
) {

	/**
	 * Returns the current multiplier based on days in streak
	 */
	fun getCurrentMultiplier(daysInStreak: Int): BigDecimal {
		// Find the greatest key less than or equal to daysInStreak
		val multiplier = thresholds.filterKeys { it <= daysInStreak }.maxByOrNull { (day) -> day }?.value

		return multiplier ?: BigDecimal.ONE
	}

	/**
	 * Returns information about the next multiplier threshold
	 */
	data class MultiplierInfo(val daysNeeded: Int, val multiplier: BigDecimal)

	fun getNextMultiplierInfo(currentDaysInStreak: Int): MultiplierInfo? {
		// Find the least key greater than currentDaysInStreak
		val nextMultiplier = thresholds
			.filterKeys { it > currentDaysInStreak }
			.minByOrNull { (day) -> day }

		return nextMultiplier?.let {
			MultiplierInfo(daysNeeded = nextMultiplier.key, multiplier = nextMultiplier.value)
		}
	}
}
