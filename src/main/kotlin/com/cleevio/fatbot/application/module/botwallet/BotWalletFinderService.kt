package com.cleevio.fatbot.application.module.botwallet

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.botwallet.exception.BotWalletNotFoundException
import com.cleevio.fatbot.domain.botwallet.BotWallet
import com.cleevio.fatbot.domain.botwallet.BotWalletRepository
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class BotWalletFinderService(
	private val botWalletRepository: BotWalletRepository,
) : BaseFinderService<BotWallet>(botWalletRepository) {

	override fun errorBlock(message: String) = throw BotWalletNotFoundException(message)

	override fun getEntityType() = BotWallet::class

	fun getByBotId(botId: UUID): BotWallet = botWalletRepository.findByBotId(botId)
		?: errorBlock("BotWallet with botId $botId not found!")

	fun getAllByBotIds(botIds: Set<UUID>) = botWalletRepository.findAllByBotIdIn(botIds = botIds).also { result ->
		if (botIds.size != result.size) {
			val resultIds = result.mapToSet { it.botId }
			val missing = botIds - resultIds
			errorBlock("Bot wallets with bot ids: [${missing.joinToString()}] not found!")
		}
	}

	fun findAllByBotIds(botIds: Set<UUID>) = botWalletRepository.findAllByBotIdIn(botIds = botIds)
}
