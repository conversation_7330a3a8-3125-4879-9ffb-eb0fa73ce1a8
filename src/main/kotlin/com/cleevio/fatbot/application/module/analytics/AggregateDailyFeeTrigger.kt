package com.cleevio.fatbot.application.module.analytics

import com.cleevio.fatbot.application.module.analytics.locks.AGGREGATE_DAILY_FEES
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class AggregateDailyFeeTrigger(
	private val dailyFeeProcessingService: DailyFeeProcessingService,
) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.aggregate-daily-fees")
	@Scheduled(cron = "\${fatbot.analytics.daily-fees.cron}")
	@SchedulerLock(
		name = AGGREGATE_DAILY_FEES,
		lockAtLeastFor = "\${fatbot.analytics.daily-fees.lock-for}",
		lockAtMostFor = "\${fatbot.analytics.daily-fees.lock-for}",
	)
	fun trigger() {
		logger.info("$AGGREGATE_DAILY_FEES cron started")
		dailyFeeProcessingService.processDailyFees()
		logger.info("$AGGREGATE_DAILY_FEES cron ended")
	}
}
