package com.cleevio.fatbot.application.module.userfattycard

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.userfattycard.port.out.GetUserFattyCard
import com.cleevio.fatbot.application.module.userfattycard.query.GetUserFattyCardQuery
import org.springframework.stereotype.Component

@Component
class GetUserFattyCardQueryHandler(
	private val getUserFattyCard: GetUserFattyCard,
) : QueryHandler<GetUserFattyCardQuery.Result, GetUserFattyCardQuery> {

	override val query = GetUserFattyCardQuery::class

	override fun handle(query: GetUserFattyCardQuery): GetUserFattyCardQuery.Result {
		return getUserFattyCard(
			userId = query.userId,
			userFattyCardId = query.userFattyCardId,
		)
	}
}
