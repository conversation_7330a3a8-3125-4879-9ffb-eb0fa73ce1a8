package com.cleevio.fatbot.application.module.bottransaction.port.out

import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator
import com.cleevio.fatbot.domain.botmarket.BotMarketPosition
import java.util.UUID

interface BroadcastBotTransactionCreated {
	operator fun invoke(
		transactionType: BotTransactionType,
		botId: UUID,
		botMarketPosition: BotMarketPosition,
		botTokenInfo: FindAllBotTokenInfo.Result,
		positionUsdValue: MarketPositionCalculator.UsdResult,
	)
}
