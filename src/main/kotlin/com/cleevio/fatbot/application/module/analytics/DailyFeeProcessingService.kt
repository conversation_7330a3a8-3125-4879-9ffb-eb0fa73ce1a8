package com.cleevio.fatbot.application.module.analytics

import com.cleevio.fatbot.application.module.analytics.port.out.AggregateDailyFees
import org.springframework.stereotype.Service
import java.time.Clock
import java.time.LocalDate

@Service
class DailyFeeProcessingService(
	private val aggregateDailyFees: AggregateDailyFees,
	private val platformDailyFeeService: PlatformDailyFeeService,
	private val clock: Clock,
) {

	fun processDailyFees() {
		val to = LocalDate.now(clock)
		val from = to.minusDays(1)

		val aggregatedDailyFees = aggregateDailyFees(from, to)

		platformDailyFeeService.saveAll(aggregatedDailyFees)
	}
}
