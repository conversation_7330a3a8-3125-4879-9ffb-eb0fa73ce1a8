package com.cleevio.fatbot.application.module.userleaderbord

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.userleaderbord.port.out.SearchUserLeaderboard
import com.cleevio.fatbot.application.module.userleaderbord.query.SearchUserLeaderboardQuery
import org.springframework.stereotype.Component

@Component
class SearchUserLeaderboardQueryHandler(
	private val searchUserLeaderboard: SearchUserLeaderboard,
) : QueryHandler<InfiniteScrollSlice<SearchUserLeaderboardQuery.Result, Int>, SearchUserLeaderboardQuery> {

	override val query = SearchUserLeaderboardQuery::class

	override fun handle(query: SearchUserLeaderboardQuery): InfiniteScrollSlice<SearchUserLeaderboardQuery.Result, Int> {
		return searchUserLeaderboard(query.infiniteScroll)
	}
}
