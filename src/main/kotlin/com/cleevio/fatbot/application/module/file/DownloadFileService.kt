package com.cleevio.fatbot.application.module.file

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.file.client.FileDownloadClient
import com.cleevio.fatbot.application.module.file.command.DownloadFileCommand
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class DownloadFileService(
	private val fileDownloadClient: FileDownloadClient,
) : CommandHandler<UUID, DownloadFileCommand> {

	override val command = DownloadFileCommand::class

	@Transactional(propagation = Propagation.NEVER)
	override fun handle(command: DownloadFileCommand): UUID {
		return fileDownloadClient.downloadAndSaveFile(downloadUrl = command.fileUrl).id
	}
}
