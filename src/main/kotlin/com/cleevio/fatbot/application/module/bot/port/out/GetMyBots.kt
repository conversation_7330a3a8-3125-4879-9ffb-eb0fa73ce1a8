package com.cleevio.fatbot.application.module.bot.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.math.BigInteger
import java.net.URI
import java.time.Instant
import java.util.UUID

interface GetMyBots {

	sealed interface Result {
		val id: UUID
		val isActive: Boolean
		val numberOfActiveDays: Long
		val name: String?
		val avatarFileUrl: URI?

		// TODO: Temporary fileId until fileUrl is fixed
		val avatarFileId: UUID?

		data class Bot(
			override val id: UUID,
			override val isActive: Boolean,
			override val numberOfActiveDays: Long,
			override val name: String,
			override val avatarFileUrl: URI?,
			override val avatarFileId: UUID?,
			val buyCount: BigInteger,
			val sellCount: BigInteger,
			val chain: Chain,
			val walletAddress: AddressWrapper,
			val walletBalance: BaseAmount,
			val acquisitionValueUsd: BigDecimal,
		) : Result

		data class BotDraft(
			override val id: UUID,
			override val isActive: Boolean,
			override val numberOfActiveDays: Long,
			override val name: String?,
			override val avatarFileUrl: URI?,
			override val avatarFileId: UUID?,
			val draftCompleteness: BigDecimal,
		) : Result
	}

	operator fun invoke(userId: UUID, timeRange: TimeRange, fileToUrlMapper: FileToUrlMapper, now: Instant): List<Result>
}
