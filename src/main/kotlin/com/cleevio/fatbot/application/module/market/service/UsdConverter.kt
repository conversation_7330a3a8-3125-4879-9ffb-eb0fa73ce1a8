package com.cleevio.fatbot.application.module.market.service

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.BigInteger
import java.math.MathContext

@Service
class UsdConverter(
	private val getUsdExchangeRate: GetUsdExchangeRate,
) {

	@Deprecated("Use baseToUsd with BaseAmount instead")
	fun baseToUsd(baseAmount: BigInteger, cryptoCurrency: CryptoCurrency): BigDecimal {
		val nativeAmount = baseAmount.asBaseAmount().toNative(cryptoCurrency.decimals)

		return nativeToUsd(nativeAmount, cryptoCurrency)
	}

	/**
	 * Converts a [baseAmount] (i.e., Wei for Ether) of [chain]'s [CryptoCurrency] to the equivalent value in USD.
	 * [customRate] can be supplied and will default to the latest rate if null
	 *
	 * The conversion is based on the USD exchange rate for the given cryptocurrency.
	 */
	fun baseToUsd(baseAmount: BaseAmount, chain: Chain, customRate: BigDecimal? = null): BigDecimal {
		return baseToUsd(baseAmount, chain.currency, customRate)
	}

	/**
	 * Converts a [nativeAmount] (i.e., Eth for Ether) of [chain]'s [CryptoCurrency] to the equivalent value in USD.
	 * [customRate] can be supplied and will default to the latest rate if null
	 *
	 * The conversion is based on the USD exchange rate for the given cryptocurrency.
	 */
	fun nativeToUsd(nativeAmount: NativeAmount, chain: Chain, customRate: BigDecimal? = null): BigDecimal {
		return nativeToUsd(nativeAmount, chain.currency, customRate)
	}

	private fun baseToUsd(
		baseAmount: BaseAmount,
		cryptoCurrency: CryptoCurrency,
		customRate: BigDecimal? = null,
	): BigDecimal {
		val nativeAmount = baseAmount.toNative(cryptoCurrency.decimals)

		return nativeToUsd(nativeAmount, cryptoCurrency, customRate)
	}

	private fun nativeToUsd(
		nativeAmount: NativeAmount,
		cryptoCurrency: CryptoCurrency,
		customRate: BigDecimal? = null,
	): BigDecimal {
		val usdExchangeRate = customRate ?: getUsdExchangeRate(cryptoCurrency)

		return nativeAmount.amount.multiply(usdExchangeRate)
	}

	/**
	 * Converts an [amountUsd] in USD to the equivalent value in base units of the specified [chain].
	 *
	 * The conversion is based on the USD exchange rate for the given cryptocurrency.
	 * The result is scaled to the smallest unit of the cryptocurrency (e.g., WEI for ETH)
	 * based on the number of decimals defined for the cryptocurrency.
	 */
	fun usdToBase(amountUsd: BigDecimal, chain: Chain): BaseAmount {
		val tokenToUsdRate = getUsdExchangeRate(chain.currency)
		val usdToTokenRate =
			BigDecimal.ONE.scaleByPowerOfTen(chain.currency.decimals).divide(tokenToUsdRate, MathContext.DECIMAL128)

		return amountUsd.multiply(usdToTokenRate).toBigInteger().asBaseAmount()
	}
}
