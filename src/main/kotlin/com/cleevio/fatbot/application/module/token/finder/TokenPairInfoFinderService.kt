package com.cleevio.fatbot.application.module.token.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.token.TokenPairInfo
import com.cleevio.fatbot.domain.token.TokenPairInfoRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class TokenPairInfoFinderService(private val tokenPairInfoRepository: TokenPairInfoRepository) :
	BaseFinderService<TokenPairInfo>(tokenPairInfoRepository) {

	override fun errorBlock(message: String) = error(message)

	override fun getEntityType() = TokenPairInfo::class

	@Transactional(readOnly = true)
	fun findByPairAddressAndChain(pairAddress: AddressWrapper, chain: Chain) =
		tokenPairInfoRepository.findByPairAddressAndChain(pairAddress, chain)

	@Transactional(readOnly = true)
	fun getByPairAddressAndChain(pairAddress: AddressWrapper, chain: Chain) =
		tokenPairInfoRepository.findByPairAddressAndChain(pairAddress, chain)
			?: errorBlock("Unable to find pair $pairAddress on chain $chain")

	@Transactional(readOnly = true)
	fun getByTokenAddressAndChain(tokenAddress: AddressWrapper, chain: Chain) =
		tokenPairInfoRepository.findByTokenAddressAndChain(tokenAddress = tokenAddress, chain = chain)
			?: errorBlock("Unable to find pair by token address $tokenAddress on chain $chain")

	@Transactional(readOnly = true)
	fun findAllByTokenAddressAndChain(tokenAddress: AddressWrapper, chain: Chain) =
		tokenPairInfoRepository.findAllByTokenAddressAndChain(tokenAddress, chain)
}
