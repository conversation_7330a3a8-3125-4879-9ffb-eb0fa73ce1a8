package com.cleevio.fatbot.application.module.hottoken

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.hottoken.port.out.GetHotTokens
import com.cleevio.fatbot.application.module.hottoken.query.GetHotTokensQuery
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.tokenpricesnapshot.port.out.GetTokenPriceSnapshots
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.net.URI
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit

@Component
class GetHotTokensQueryHandler(
	private val getHotTokens: GetHotTokens,
	private val fileUrlMapper: FileUrlMapper,
	private val getTokenPrices: GetTokenPrices,
	private val getTokenPriceSnapshots: GetTokenPriceSnapshots,
	private val userFinderService: FirebaseUserFinderService,
	private val properties: ChainProperties,
	private val usdConverter: UsdConverter,
	private val clock: Clock,
) : QueryHandler<InfiniteScrollSlice<GetHotTokensQuery.Result, BigDecimal>, GetHotTokensQuery> {

	override val query = GetHotTokensQuery::class

	@SentrySpan
	override fun handle(query: GetHotTokensQuery): InfiniteScrollSlice<GetHotTokensQuery.Result, BigDecimal> {
		val chains = when (query.signedUserParams?.useSelectedChains) {
			true -> userFinderService.getById(query.signedUserParams.userId).selectedChains
			false, null -> properties.enabledChains
		}

		val hotTokens = getHotTokens(
			chains = chains,
			infiniteScroll = query.infiniteScroll,
			fileToUrlMapper = fileUrlMapper::map,
		)

		val hotTokenAddresses = hotTokens.content.mapToSet {
			ChainAddress(
				address = it.tokenAddress,
				chain = it.tokenChain,
			)
		}
		val now = Instant.now(clock)

		val prices = getTokenPrices(tokens = hotTokenAddresses)
		val priceSnapshots24h = getTokenPriceSnapshots(
			tokens = hotTokenAddresses,
			before = now.minus(24, ChronoUnit.HOURS),
		)

		val resultContent = hotTokens.content.map {
			val chainAddress = it.tokenAddress.toChainAddress(it.tokenChain)

			val price = prices.getValue(chainAddress)
			val priceUsd = usdConverter.baseToUsd(price, it.tokenChain)

			val change24h = priceSnapshots24h[chainAddress]?.let { (price24h, exchangeRateUsd24h) ->
				val priceUsd24h = usdConverter.baseToUsd(price24h, it.tokenChain, exchangeRateUsd24h)

				calculateFractionChange(priceUsd, priceUsd24h)
			}

			GetHotTokensQuery.Result(
				id = it.id,
				tokenAddress = it.tokenAddress,
				tokenDetailUrl = URI(it.tokenAddress.getLinkToExplorer(it.tokenChain)),
				tokenName = it.tokenName,
				tokenSymbol = it.tokenSymbol,
				tokenChain = it.tokenChain,
				tokenImageUrl = it.tokenImageUrl,
				volume24hUsd = it.volume24hUsd,
				priceUsd = priceUsd,
				change24h = change24h,
			)
		}

		return InfiniteScrollSlice(
			content = resultContent,
			lastId = hotTokens.lastId,
			hasMore = hotTokens.hasMore,
		)
	}
}
