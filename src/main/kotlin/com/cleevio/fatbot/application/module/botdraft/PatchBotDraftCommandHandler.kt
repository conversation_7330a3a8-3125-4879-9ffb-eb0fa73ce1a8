package com.cleevio.fatbot.application.module.botdraft

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.service.ValidationService
import com.cleevio.fatbot.application.module.botdraft.command.PatchBotDraftCommand
import com.cleevio.fatbot.application.module.botdraft.finder.BotDraftFinderService
import com.cleevio.fatbot.application.module.botdraft.locks.BOT_DRAFT_MODULE
import com.cleevio.fatbot.application.module.botdraft.locks.BOT_DRAFT_UPDATE
import com.cleevio.fatbot.application.module.botdraft.model.toValidBotDraftUpdate
import com.cleevio.library.lockinghandler.service.Lock
import io.sentry.spring.jakarta.tracing.SentrySpan
import jakarta.transaction.Transactional
import org.springframework.stereotype.Component

@Component
class PatchBotDraftCommandHandler(
	private val botDraftFinderService: BotDraftFinderService,
	private val validationService: ValidationService,
) : CommandHandler<Unit, PatchBotDraftCommand> {

	override val command = PatchBotDraftCommand::class

	@SentrySpan
	@Transactional
	@Lock(module = BOT_DRAFT_MODULE, lockName = BOT_DRAFT_UPDATE)
	override fun handle(command: PatchBotDraftCommand) {
		val botDraft = botDraftFinderService.getByIdAndUserId(command.botDraftId, command.userId)

		val botUpdate = command.patchRequest.toValidBotDraftUpdate(botDraft)

		validationService.validate(mapOf("botDraftUpdateRequest" to botUpdate))

		botDraft.update(botUpdate)
	}
}
