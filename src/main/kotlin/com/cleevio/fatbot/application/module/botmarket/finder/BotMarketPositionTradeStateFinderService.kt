package com.cleevio.fatbot.application.module.botmarket.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.module.botmarket.exception.BotMarketPositionTradeStateNotFoundException
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeState
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateRepository
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateType
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class BotMarketPositionTradeStateFinderService(
	private val botMarketPositionTradeStateRepository: BotMarketPositionTradeStateRepository,
) : BaseFinderService<BotMarketPositionTradeState>(botMarketPositionTradeStateRepository) {

	override fun errorBlock(message: String) = throw BotMarketPositionTradeStateNotFoundException(message)

	override fun getEntityType() = BotMarketPositionTradeState::class

	fun findByPositionIdAndType(botMarketPositionId: UUID, type: BotMarketPositionTradeStateType) =
		botMarketPositionTradeStateRepository.findByBotMarketPositionIdAndType(
			botMarketPositionId = botMarketPositionId,
			type = type,
		)

	fun getByPositionIdAndType(botMarketPositionId: UUID, type: BotMarketPositionTradeStateType) =
		botMarketPositionTradeStateRepository.findByBotMarketPositionIdAndType(
			botMarketPositionId = botMarketPositionId,
			type = type,
		) ?: errorBlock("Bot market position trade with position id: $botMarketPositionId and type: $type not found.")
}
