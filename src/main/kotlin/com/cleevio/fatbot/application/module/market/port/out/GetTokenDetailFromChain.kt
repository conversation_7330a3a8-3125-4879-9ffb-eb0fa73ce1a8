package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.market.query.GetTokenDetailQuery
import com.cleevio.fatbot.application.module.wallet.constant.ChainType

interface GetTokenDetailFromChain {
	fun supports(): ChainType
	operator fun invoke(token: ChainAddress): GetTokenDetailQuery.NonDexDetail?
}

operator fun List<GetTokenDetailFromChain>.invoke(token: ChainAddress): GetTokenDetailQuery.NonDexDetail? = this
	.find { it.supports() == token.chain.type }
	?.invoke(token = token)
