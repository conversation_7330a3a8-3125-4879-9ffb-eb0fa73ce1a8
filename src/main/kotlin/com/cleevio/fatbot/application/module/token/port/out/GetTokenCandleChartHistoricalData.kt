package com.cleevio.fatbot.application.module.token.port.out

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.constant.TimeInterval
import com.cleevio.fatbot.application.module.token.query.TokenOHLCTimeIntervalItem
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.time.Instant

interface GetTokenCandleChartHistoricalData {
	operator fun invoke(
		chain: Chain,
		tokenAddress: AddressWrapper,
		// Whether the token is exclusively on Pumpfun
		// We need this information down the line to decide on the correct quote token
		isPumpfunExclusive: Boolean,
		intervalCount: Int,
		timeInterval: TimeInterval,
		limit: Int,
		before: Instant?,
		after: Instant?,
	): List<TokenOHLCTimeIntervalItem>
}
