package com.cleevio.fatbot.application.module.tokenpricesnapshot.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import java.math.BigDecimal
import java.time.Instant

interface GetTokenPriceSnapshots {

	data class Result(val price: BaseAmount, val exchangeRateUsd: BigDecimal)

	operator fun invoke(tokens: Set<ChainAddress>, before: Instant): Map<ChainAddress, Result>

	fun getSingleOrNull(token: ChainAddress, before: Instant): Result?
}
