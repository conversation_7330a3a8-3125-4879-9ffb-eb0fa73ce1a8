package com.cleevio.fatbot.application.module.bottransaction.port.out

import com.cleevio.fatbot.adapter.`in`.InfiniteScroll
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.module.bottransaction.query.SearchUserBotsTransactionsQuery
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.net.URI
import java.time.Instant
import java.util.UUID

interface SearchBotTransaction {
	operator fun invoke(
		userId: UUID,
		botIds: Set<UUID>?,
		searchString: String?,
		from: Instant?,
		to: Instant?,
		infiniteScroll: InfiniteScroll<UUID>,
		txHashToURIMapper: (TxHash, Chain) -> URI,
		fileToUrlMapper: FileToUrlMapper,
	): InfiniteScrollSlice<SearchUserBotsTransactionsQuery.Result, UUID>
}
