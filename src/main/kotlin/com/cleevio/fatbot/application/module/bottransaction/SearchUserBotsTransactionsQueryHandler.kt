package com.cleevio.fatbot.application.module.bottransaction

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.bottransaction.port.out.SearchBotTransaction
import com.cleevio.fatbot.application.module.bottransaction.query.SearchUserBotsTransactionsQuery
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.transaction.port.out.GetTxDetailUri
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class SearchUserBotsTransactionsQueryHandler(
	private val searchBotTransaction: SearchBotTransaction,
	private val getTxDetailUri: GetTxDetailUri,
	private val fileUrlMapper: FileUrlMapper,
) : QueryHandler<InfiniteScrollSlice<SearchUserBotsTransactionsQuery.Result, UUID>, SearchUserBotsTransactionsQuery> {

	override val query = SearchUserBotsTransactionsQuery::class

	override fun handle(
		query: SearchUserBotsTransactionsQuery,
	): InfiniteScrollSlice<SearchUserBotsTransactionsQuery.Result, UUID> {
		return searchBotTransaction(
			userId = query.userId,
			botIds = setOf(query.filter.botId),
			searchString = query.filter.searchString,
			from = null,
			to = null,
			infiniteScroll = query.infiniteScroll,
			txHashToURIMapper = { txHash, chain -> getTxDetailUri(txHash, chain) },
			fileToUrlMapper = fileUrlMapper::map,
		)
	}
}
