package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutPumpfun
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutPumpswap
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutRaydium
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutUniswap
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.query.GetPredictedNativeAmountOnSellQuery
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.token.finder.TokenPairInfoFinderService
import org.springframework.stereotype.Component

@Component
class GetPredictedNativeAmountOnSellQueryHandler(
	private val getAmountOutUniswap: GetAmountOutUniswap,
	private val getAmountOutPumpfun: GetAmountOutPumpfun,
	private val getAmountOutRaydium: GetAmountOutRaydium,
	private val getAmountOutPumpswap: GetAmountOutPumpswap,
	private val tokenInfoFinderService: EvmTokenInfoFinderService,
	private val tokenPairInfoFinderService: TokenPairInfoFinderService,
) : QueryHandler<GetPredictedNativeAmountOnSellQuery.Result, GetPredictedNativeAmountOnSellQuery> {

	override val query = GetPredictedNativeAmountOnSellQuery::class

	override fun handle(query: GetPredictedNativeAmountOnSellQuery): GetPredictedNativeAmountOnSellQuery.Result {
		val token = tokenInfoFinderService.getByTokenAddressAndChain(tokenAddress = query.tokenAddress, chain = query.chain)
		val tokenDecimals = token.decimals

		val baseAmountIn = query.toSellTokenNativeAmount.toBase(tokenDecimals.toInt())

		val amountOut = when (query.dexPairInfo) {
			is GetDex.UniswapV2, is GetDex.PancakeswapV2 -> getAmountOutUniswap.getAmountOutV2(
				chainId = query.chain.evmId,
				tokenAddress = query.tokenAddress,
				amountIn = baseAmountIn,
				isBuy = false,
			)

			is GetDex.UniswapV3 -> getAmountOutUniswap.getAmountOutV3(
				chainId = query.chain.evmId,
				tokenAddress = query.tokenAddress,
				amountIn = baseAmountIn,
				fee = query.dexPairInfo.fee,
				isBuy = false,
			)

			is GetDex.PancakeswapV3 -> getAmountOutUniswap.getAmountOutV3(
				chainId = query.chain.evmId,
				tokenAddress = query.tokenAddress,
				amountIn = baseAmountIn,
				fee = query.dexPairInfo.fee,
				isBuy = false,
			)

			is GetDex.PumpFun -> getAmountOutPumpfun(
				amount = baseAmountIn,
				pairAddress = query.dexPairInfo.pairAddress,
				isBuy = false,
			)
			is GetDex.Raydium -> when (query.dexPairInfo.poolType) {
				GetDex.PoolType.AMM -> {
					val tokenPair = tokenPairInfoFinderService.getByPairAddressAndChain(query.dexPairInfo.pairAddress, query.chain)
					val marketData = tokenPair.raydiumAmmMarketData ?: error(
						"AMM market data missing for pair ${tokenPair.pairAddress}",
					)

					getAmountOutRaydium.getAmountOutAMM(
						amount = baseAmountIn,
						pairAddress = query.dexPairInfo.pairAddress,
						marketData = marketData,
						isBuy = false,
					)
				}
				GetDex.PoolType.CLMM -> getAmountOutRaydium.getAmountOutCLMM(
					amount = baseAmountIn,
					pairAddress = query.dexPairInfo.pairAddress,
					tokenDecimals = tokenDecimals,
					isBuy = false,
				)
				GetDex.PoolType.CPMM -> {
					val tokenPair = tokenPairInfoFinderService.getByPairAddressAndChain(query.dexPairInfo.pairAddress, query.chain)
					val marketData = tokenPair.raydiumCpmmMarketData ?: error(
						"CPMM market data missing for pair ${tokenPair.pairAddress}",
					)

					getAmountOutRaydium.getAmountOutCPMM(
						amount = baseAmountIn,
						pairAddress = query.dexPairInfo.pairAddress,
						tokenAddress = query.tokenAddress,
						marketData = marketData,
						isBuy = false,
					)
				}
			}

			is GetDex.PumpSwap -> getAmountOutPumpswap(
				amount = baseAmountIn,
				pairAddress = query.dexPairInfo.pairAddress,
				tokenAddress = query.tokenAddress,
				isToken2022 = token.isToken2022,
				isBuy = false,
			)

			is GetDex.Meteora -> TODO("Implement for solana")
		}

		val amountOutNative = amountOut.toNative(query.chain)

		return GetPredictedNativeAmountOnSellQuery.Result(predictedCurrencyNativeAmount = amountOutNative)
	}
}
