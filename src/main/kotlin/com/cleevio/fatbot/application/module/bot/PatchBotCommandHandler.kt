package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.service.ValidationService
import com.cleevio.fatbot.application.module.bot.command.PatchBotCommand
import com.cleevio.fatbot.application.module.bot.locks.BOT_MODULE
import com.cleevio.fatbot.application.module.bot.locks.UPDATE_BOT
import com.cleevio.fatbot.application.module.bot.model.toValidBotUpdate
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import io.sentry.spring.jakarta.tracing.SentrySpan
import jakarta.transaction.Transactional
import org.springframework.stereotype.Component

@Component
class PatchBotCommandHandler(
	private val botFinderService: BotFinderService,
	private val validationService: ValidationService,
) : CommandHandler<Unit, PatchBotCommand> {

	override val command = PatchBotCommand::class

	@SentrySpan
	@Transactional
	@Lock(module = BOT_MODULE, lockName = UPDATE_BOT)
	override fun handle(@LockFieldParameter("userId") command: PatchBotCommand) {
		// TODO: As we do not have lock acquired for the bot trading, it's possible
		//  that while this is updated, we already hold another version of bot entity in memory
		//  and Optimistic lock exception might happen. Maybe safer way here would be use Jooq directly to
		//  and not touch Hibernate at all (and just set all the settings without touching version
		//  or setting version as version = version + 1
		val bot = botFinderService.getByIdAndUserId(command.botId, command.userId)

		val botUpdate = command.patchRequest.toValidBotUpdate(bot)

		validationService.validate(mapOf("botUpdateRequest" to botUpdate))

		bot.update(botUpdate)

		// Follower bots gets updated red flags only, as their buying parameter must stay the same
		// as they are intentionally left high so the follower bot will never buy token on its own
		bot.followerBotId?.let {
			val follower = botFinderService.getById(id = it)
			follower.updateRedFlagSettings(
				tokenTickerCopyIsChecked = bot.tokenTickerCopyIsChecked,
				creatorHighBuyIsChecked = bot.creatorHighBuyIsChecked,
				bundledBuysDetectedIsChecked = bot.bundledBuysDetectedIsChecked,
				suspiciousWalletsDetectedIsChecked = bot.suspiciousWalletsDetectedIsChecked,
				singleHighBuyIsChecked = bot.singleHighBuyIsChecked,
				buyTokensAliveAtLeastFor = bot.buyTokensAliveAtLeastFor,
				shouldAutoSellAfterHoldTime = bot.shouldAutoSellAfterHoldTime,
			)
		}
	}
}
