package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.adapter.out.evm.model.GasInfo
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import java.math.BigInteger

interface TransferEvmToken {
	operator fun invoke(
		tokenAddress: AddressWrapper,
		destinationWalletAddress: AddressWrapper,
		privateKey: String,
		chainId: Long,
		amountToTransfer: BaseAmount,
		gasInfoHint: GasInfo?,
		gasLimitEstimate: BigInteger,
	): SignedTx
}
