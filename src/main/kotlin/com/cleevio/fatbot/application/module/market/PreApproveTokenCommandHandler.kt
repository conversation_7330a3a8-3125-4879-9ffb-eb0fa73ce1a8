package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.adapter.out.evm.constants.EVM_MAX_UINT
import com.cleevio.fatbot.adapter.out.evm.simulation.SetTokensApprovalEVM
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.module.market.command.PreApproveTokenCommand
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class PreApproveTokenCommandHandler(
	private val setTokensApprovalEVM: SetTokensApprovalEVM,
	private val walletFinderService: WalletFinderService,
) : CommandHandler<Unit, PreApproveTokenCommand> {

	override val command = PreApproveTokenCommand::class

	override fun handle(command: PreApproveTokenCommand) {
		val wallet = walletFinderService.getById(command.walletId)

		assert(wallet.chain.id == command.chainId)

		setTokensApprovalEVM(
			tokenAddress = command.tokenAddress,
			privateKey = wallet.privateKey,
			chainId = command.chainId,
			gasInfoHint = null,
			minimumTokenAllowance = (EVM_MAX_UINT / BigInteger.TWO).asBaseAmount(),
		)
	}
}
