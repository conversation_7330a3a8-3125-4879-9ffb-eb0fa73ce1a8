package com.cleevio.fatbot.application.module.portfolio

import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

private const val LOCK_NAME = "CREATE_PORTFOLIO_VALUE_SNAPSHOT"

@Component
class CreatePortfolioValueSnapshotTrigger(
	private val portfolioValueSnapshotService: PortfolioValueSnapshotService,
) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.create-portfolio-value-snapshot")
	@Scheduled(cron = "\${fatbot.portfolio-value.create-snapshots.cron}")
	@SchedulerLock(
		name = LOCK_NAME,
		lockAtMostFor = "\${fatbot.portfolio-value.create-snapshots.lock-for}",
		lockAtLeastFor = "\${fatbot.portfolio-value.create-snapshots.lock-for}",
	)
	fun trigger() {
		logger.info("$LOCK_NAME cron started")
		portfolioValueSnapshotService.createPortfolioValueSnapshots()
		logger.info("$LOCK_NAME cron ended")
	}
}
