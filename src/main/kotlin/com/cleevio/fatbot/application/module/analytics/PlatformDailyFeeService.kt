package com.cleevio.fatbot.application.module.analytics

import com.cleevio.fatbot.application.module.analytics.port.out.AggregateDailyFees
import com.cleevio.fatbot.domain.analytics.PlatformDailyFeeCreateService
import com.cleevio.fatbot.domain.analytics.PlatformDailyFeeFinderService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class PlatformDailyFeeService(
	private val platformDailyFeeCreateService: PlatformDailyFeeCreateService,
	private val platformDailyFeeFinderService: PlatformDailyFeeFinderService,
) {

	@Transactional
	fun saveAll(dailyFees: List<AggregateDailyFees.Result>) = dailyFees.forEach { dailyFee ->
		// this could be optimized to only do one search for existing values,
		// but we only expect the saveAll(dailyFees) to be called with 2 items every midnight
		// (REVENUE and REFERRAL aggregates for previous day)
		val existingFee = platformDailyFeeFinderService.findByDateAndTypeAndChain(
			date = dailyFee.date,
			type = dailyFee.type,
			chain = dailyFee.chain,
		)

		if (existingFee != null) {
			existingFee.update(dailyFee.baseAmount)
		} else {
			platformDailyFeeCreateService.createDailyFee(
				chain = dailyFee.chain,
				date = dailyFee.date,
				type = dailyFee.type,
				baseAmount = dailyFee.baseAmount,
			)
		}
	}
}
