package com.cleevio.fatbot.application.module.market.service

import com.cleevio.fatbot.application.common.util.truncateBySeconds
import com.cleevio.fatbot.domain.market.MarketPositionSnapshotRepository
import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

private val SNAPSHOT_WINDOW_DURATION = Duration.of(15, ChronoUnit.MINUTES)
private val KEEP_SNAPSHOTS_FOR_DURATION = Duration.of(36, ChronoUnit.HOURS)

@Service
class MarketPositionSnapshotService(
	private val clock: Clock,
	private val marketPositionSnapshotRepository: MarketPositionSnapshotRepository,
) {

	private val logger = logger()

	@Transactional
	fun createNewSnapshotsOfMarketPositionTableAndDeleteOldSnapshots() {
		val now = Instant.now(clock)
		val snapshotTime = getSnapshotTime(now = now)
		val deleteOlderThan = snapshotTime.minus(KEEP_SNAPSHOTS_FOR_DURATION)
		logger.info("Working with now: $now, snapshotTime: $snapshotTime, deleteOlderThan: $deleteOlderThan")

		marketPositionSnapshotRepository.copyFromMarketPositionTable(snapshotMadeAt = snapshotTime)
		marketPositionSnapshotRepository.deleteOlderSnapshotsThan(deleteOlderThan = deleteOlderThan)
	}

	/**
	 * Gets [now] truncated to [SNAPSHOT_WINDOW_DURATION]
	 *
	 * Examples:
	 * ```
	 * 14:00 -> 14:00
	 * 14:03 -> 14:00
	 * 14:28 -> 14:15
	 * 14:59:59 -> 14:45:00
	 * ```
	 */
	private fun getSnapshotTime(now: Instant): Instant = now.truncateBySeconds(SNAPSHOT_WINDOW_DURATION.seconds)

	fun getSnapshotTimeCreated24hoursAgo(now: Instant): Instant = getSnapshotTime(now).minus(24, ChronoUnit.HOURS)
}
