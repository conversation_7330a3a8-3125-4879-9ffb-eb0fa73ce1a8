package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.file.client.FileDownloadClient
import com.cleevio.fatbot.application.module.token.command.UpdateTokenImageCommand
import com.cleevio.fatbot.application.module.token.exception.TokenImageCouldNotBeDownloadedException
import com.cleevio.fatbot.application.module.token.finder.BotTokenInfoFinderService
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.token.locks.DOWNLOAD_BOT_TOKEN_IMAGE
import com.cleevio.fatbot.application.module.token.locks.TOKEN_MODULE
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.library.lockinghandler.service.Lock
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateTokenImageCommandHandler(
	private val fileDownloadClient: FileDownloadClient,
	private val tokenInfoFinderService: EvmTokenInfoFinderService,
	private val botTokenInfoFinderService: BotTokenInfoFinderService,
) : CommandHandler<Unit, UpdateTokenImageCommand> {

	private val logger = logger()

	override val command = UpdateTokenImageCommand::class

	@Transactional
	@Lock(module = TOKEN_MODULE, lockName = DOWNLOAD_BOT_TOKEN_IMAGE)
	override fun handle(command: UpdateTokenImageCommand) {
		val imageFile = runCatching { fileDownloadClient.downloadAndSaveFile(command.imageUrl) }.onFailure {
			logger.error("Failed to download file ${command.imageUrl} for token ${command.tokenAddress}")
		}.getOrElse {
			throw TokenImageCouldNotBeDownloadedException("Failed to download image for token: $command.tokenAddress")
		}

		tokenInfoFinderService.findByTokenAddressAndChain(tokenAddress = command.tokenAddress, chain = command.chain)
			?.setImageFileId(imageFileId = imageFile.id)

		botTokenInfoFinderService.findByTokenAddressAndChain(tokenAddress = command.tokenAddress, chain = command.chain)
			?.setImageFileId(imageFileId = imageFile.id)
	}
}
