package com.cleevio.fatbot.application.module.botleaderboard.scheduled

import com.cleevio.fatbot.application.module.botleaderboard.service.BotLeaderboardSnapshotService
import com.cleevio.fatbot.infrastructure.config.logger
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class CreateBotLeaderboardSnapshotsTrigger(
	private val botLeaderboardSnapshotService: BotLeaderboardSnapshotService,
) {

	private val logger = logger()

	@Scheduled(cron = "\${fatbot.bot-leaderboard.create-snapshot.cron}")
	@SchedulerLock(
		name = "CREATE_BOT_LEADERBOARD_SNAPSHOT",
		lockAtMostFor = "\${fatbot.bot-leaderboard.create-snapshot.lock-for}",
		lockAtLeastFor = "\${fatbot.bot-leaderboard.create-snapshot.lock-for}",
	)
	fun trigger() {
		logger.info("CREATE_BOT_LEADERBOARD_SNAPSHOT cron started")
		botLeaderboardSnapshotService.createLeaderboardSnapshot()
		logger.info("CREATE_BOT_LEADERBOARD_SNAPSHOT cron ended")
	}
}
