package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.adapter.out.jooq.FindAllUserEvmWalletMarketPositionDataJooq
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator
import com.cleevio.fatbot.application.module.market.exception.InvalidChainAddressCombinationException
import com.cleevio.fatbot.application.module.market.port.out.FindAllUserEvmWalletMarketPositionData
import com.cleevio.fatbot.application.module.market.port.out.GetTokenDetailFromChain
import com.cleevio.fatbot.application.module.market.port.out.GetTokenDetailFromDexScreener
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.market.port.out.invoke
import com.cleevio.fatbot.application.module.market.query.GetTokenDetailQuery
import com.cleevio.fatbot.application.module.token.exception.TokenNotFoundException
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.tokenaudit.service.GetTokenAudit
import com.cleevio.fatbot.application.module.transaction.query.SearchUserTransactionQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.net.URI
import java.util.UUID

@Component
class GetTokenDetailQueryHandler(
	private val getTokenDetailFromDexScreenerOrNull: GetTokenDetailFromDexScreener,
	private val getTokenDetailFromChainOrNull: List<GetTokenDetailFromChain>,
	private val findAllUserEvmWalletMarketPositionData: FindAllUserEvmWalletMarketPositionData,
	private val tokenInfoFinderService: EvmTokenInfoFinderService,
	private val getTokenAudit: GetTokenAudit,
	private val fileUrlMapper: FileUrlMapper,
	private val getUsdExchangeRate: GetUsdExchangeRate,
) : QueryHandler<GetTokenDetailQuery.Result, GetTokenDetailQuery> {

	override val query = GetTokenDetailQuery::class

	@Lazy
	@Autowired
	private lateinit var queryBus: QueryBus

	@SentrySpan
	override fun handle(query: GetTokenDetailQuery): GetTokenDetailQuery.Result {
		val token = query.tokenAddress.toChainAddress(query.chain)
		if (!token.isValid()) {
			throw InvalidChainAddressCombinationException("${query.tokenAddress} can't be on ${query.chain}")
		}

		val detail = getTokenDetailFromDexScreenerOrNull(token = token)
			?: getTokenDetailFromDatabaseOrNull(token = token)
			?: getTokenDetailFromChainOrNull(token = token)
			?: throw TokenNotFoundException("Token not found")

		return when (detail) {
			is GetTokenDetailQuery.DexDetail -> detail.toResult(query)
			is GetTokenDetailQuery.NonDexDetail -> detail.toResult()
		}
	}

	private fun ChainAddress.isValid() = when (chain.type) {
		ChainType.EVM -> address.isEvm()
		ChainType.SOLANA -> address.isSolana()
	}

	private fun getTokenDetailFromDatabaseOrNull(token: ChainAddress): GetTokenDetailQuery.NonDexDetail? {
		val tokenInfo = tokenInfoFinderService.findByTokenAddressAndChain(tokenAddress = token.address, chain = token.chain)

		return tokenInfo?.let {
			GetTokenDetailQuery.NonDexDetail(
				GetTokenDetailQuery.NonDexInfo(
					name = it.name,
					symbol = it.symbol,
				),
			)
		}
	}

	private fun GetTokenDetailQuery.DexDetail.toResult(query: GetTokenDetailQuery): GetTokenDetailQuery.Result {
		val tokenAudit = if (query.chain.type == ChainType.EVM) {
			getTokenAudit(
				query.tokenAddress.toChainAddress(query.chain),
			)
		} else {
			null
		}

		if (query.isOfAnonymousUser()) {
			return GetTokenDetailQuery.Result(
				tokenInfo = this,
				userWalletMarketPositionsOnThisToken = emptyList(),
				lastUserTransactionsOnThisToken = InfiniteScrollSlice.empty(),
				tokenAudit = tokenAudit,
			)
		}

		val userWalletMarketPositions = computeUserWalletMarketPositions(
			chain = query.chain,
			userId = query.userId!!,
			tokenAddress = query.tokenAddress,
			tokenDecimals = tokenDecimals.toInt(),
			currentPricePerToken = dexInfo.priceNative.asNativeAmount(),
			currentExchangeRate = getUsdExchangeRate(cryptoCurrency = query.chain.currency),
		)

		val lastUserTransactions = queryBus(
			SearchUserTransactionQuery.ofSpecificTokenLastFive(
				chain = query.chain,
				userId = query.userId,
				tokenAddress = query.tokenAddress,
			),
		)

		return GetTokenDetailQuery.Result(
			tokenInfo = this,
			userWalletMarketPositionsOnThisToken = userWalletMarketPositions,
			lastUserTransactionsOnThisToken = lastUserTransactions,
			tokenAudit = tokenAudit,
		)
	}

	private fun GetTokenDetailQuery.isOfAnonymousUser(): Boolean {
		return this.userId == null
	}

	private fun computeUserWalletMarketPositions(
		chain: Chain,
		userId: UUID,
		tokenAddress: AddressWrapper,
		tokenDecimals: Int,
		currentPricePerToken: NativeAmount,
		currentExchangeRate: BigDecimal,
	): List<GetTokenDetailQuery.Result.WalletMarketPosition> {
		val userWalletMarketPositionData = findAllUserEvmWalletMarketPositionData(
			chain = chain,
			userId = userId,
			tokenAddress = tokenAddress,
			fileToUrlMapper = fileUrlMapper::map,
		)

		return userWalletMarketPositionData.map {
			computeWalletMarketPosition(
				marketPosition = it,
				tokenDecimals = tokenDecimals,
				currentPricePerToken = currentPricePerToken,
				currentExchangeRate = currentExchangeRate,
			)
		}
	}

	private fun computeWalletMarketPosition(
		tokenDecimals: Int,
		marketPosition: FindAllUserEvmWalletMarketPositionDataJooq.Result,
		currentPricePerToken: NativeAmount,
		currentExchangeRate: BigDecimal,
	): GetTokenDetailQuery.Result.WalletMarketPosition {
		val result = MarketPositionCalculator.calculate(
			chain = marketPosition.tokenChain,
			tokenDecimals = tokenDecimals,
			tokenAmountBought = marketPosition.totalTokenAmountBought,
			totalTokenAcquisitionCost = marketPosition.totalTokenAcquisitionCost,
			tokenAmountSold = marketPosition.totalTokenAmountSold,
			tokenDispositionCost = marketPosition.totalTokenDispositionCost,
			currentPricePerNativeToken = currentPricePerToken,
			totalTokenAcquisitionCostUsd = marketPosition.totalTokenAcquisitionCostUsd,
			currentExchangeRate = currentExchangeRate,
		)

		return GetTokenDetailQuery.Result.WalletMarketPosition(
			walletId = marketPosition.walletId,
			walletDetailUrl = URI.create(marketPosition.walletAddress.getLinkToExplorer(marketPosition.walletChain)),
			walletCustomName = marketPosition.walletCustomName,
			walletChain = marketPosition.walletChain,
			tokenName = marketPosition.tokenName,
			tokenSymbol = marketPosition.tokenSymbol,
			tokenChain = marketPosition.tokenChain,
			tokenImageUrl = marketPosition.tokenImageUrl,
			tokenAmount = result.currentUserTokenAmount.toNative(tokenDecimals),
			currentValueUsd = result.currentValueUsd,
			currentValueChangeUsd = result.currentPnlUsd,
			currentValueChangeFraction = result.currentPnlFractionUsd,
		)
	}

	// for now, we are assuming if token is not on DEX user couldn't make any tx with our platform
	private fun GetTokenDetailQuery.NonDexDetail.toResult() = GetTokenDetailQuery.Result(
		tokenInfo = this,
		userWalletMarketPositionsOnThisToken = emptyList(),
		lastUserTransactionsOnThisToken = InfiniteScrollSlice.empty(),
		tokenAudit = null,
	)
}
