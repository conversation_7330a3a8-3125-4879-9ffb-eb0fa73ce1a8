package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.market.service.PortfolioOverviewService
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.application.module.wallet.query.GetUserWalletQuery
import com.cleevio.fatbot.infrastructure.config.properties.AntiMevProperties
import org.springframework.stereotype.Component
import java.net.URI

@Component
class GetUserWalletQueryHandler(
	private val walletFinderService: WalletFinderService,
	private val antiMevProperties: AntiMevProperties,
	private val portfolioOverviewService: PortfolioOverviewService,
) : QueryHandler<GetUserWalletQuery.Result, GetUserWalletQuery> {

	override val query = GetUserWalletQuery::class

	override fun handle(query: GetUserWalletQuery): GetUserWalletQuery.Result {
		val wallet = walletFinderService.getByIdAndUserId(id = query.walletId, userId = query.userId)

		val walletPortfolioOverview = portfolioOverviewService.getForWallet(
			userId = query.userId,
			walletId = query.walletId,
		)

		val (buyAntiMev, sellAntiMev) = when (wallet.chain.type) {
			ChainType.EVM -> antiMevProperties.forceUse to antiMevProperties.forceUse
			ChainType.SOLANA -> wallet.buyAntiMevProtection to wallet.sellAntiMevProtection
		}

		return GetUserWalletQuery.Result(
			id = wallet.id,
			isDefault = wallet.isDefault,
			customName = wallet.customName,
			walletAddress = wallet.address,
			walletDetailUri = URI.create(wallet.address.getLinkToExplorer(wallet.chain)),
			walletBalance = walletPortfolioOverview.walletBalance,
			walletBalanceUsd = walletPortfolioOverview.walletBalanceUsd,
			chain = wallet.chain,
			buyAntiMevProtection = buyAntiMev,
			sellAntiMevProtection = sellAntiMev,
			currentPortfolioValueUsd = walletPortfolioOverview.totalValueAmountUsd,
			currentPortfolioValueChangeUsd = walletPortfolioOverview.totalPnlAmountUsd,
			currentPortfolioValueChangeFraction = walletPortfolioOverview.totalPnlAmountFraction,
			portfolioOneDayChangeAmountUsd = walletPortfolioOverview.oneDayChangeAmountUsd,
			portfolioOneDayChangeFraction = walletPortfolioOverview.oneDayChangeFraction,
		)
	}
}
