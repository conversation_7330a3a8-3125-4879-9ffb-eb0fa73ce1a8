package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.module.limitorder.command.CreateLimitOrderCommand
import com.cleevio.fatbot.domain.limitorder.LimitOrderCreateService
import org.springframework.stereotype.Component

@Component
class CreateLimitOrderCommandHandler(
	private val limitOrderCreateService: LimitOrderCreateService,
) : CommandHandler<Unit, CreateLimitOrderCommand> {

	override val command = CreateLimitOrderCommand::class

	override fun handle(command: CreateLimitOrderCommand) {
		limitOrderCreateService.create(
			userId = command.userId,
			walletId = command.walletId,
			tokenAddress = command.tokenAddress,
			chain = command.chain,
			limitPrice = command.limitPrice.toBase(chain = command.chain),
			initialAmount = command.initialAmount.toBase(chain = command.chain),
			type = command.type,
		)
	}
}
