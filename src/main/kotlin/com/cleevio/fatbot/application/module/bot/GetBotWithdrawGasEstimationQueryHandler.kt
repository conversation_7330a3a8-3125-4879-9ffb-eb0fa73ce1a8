package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.bot.query.GetBotWithdrawGasEstimationQuery
import com.cleevio.fatbot.application.module.botwallet.BotWalletFinderService
import com.cleevio.fatbot.application.module.market.port.out.EstimateGas
import com.cleevio.fatbot.application.module.market.port.out.estimate
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component

@Component
class GetBotWithdrawGasEstimationQueryHandler(
	private val botWalletFinderService: BotWalletFinderService,
	private val estimateGas: List<EstimateGas>,
	private val usdConverter: UsdConverter,
) : QueryHandler<GetBotWithdrawGasEstimationQuery.Result, GetBotWithdrawGasEstimationQuery> {

	override val query = GetBotWithdrawGasEstimationQuery::class

	override fun handle(query: GetBotWithdrawGasEstimationQuery): GetBotWithdrawGasEstimationQuery.Result {
		val botWallet = botWalletFinderService.getByBotId(botId = query.botId)
		val baseAmount = query.nativeAmount.asNativeAmount().toBase(botWallet.chain)

		val estimateGasResult = estimateGas.estimate(
			EstimateGas.TransferCurrencyInput(
				privateKey = botWallet.privateKey,
				chain = botWallet.chain,
				gasInfo = null,
				amount = baseAmount,
				toAddress = query.recipientAddress,
			),
		)

		return when (botWallet.chain) {
			Chain.EVM_BASE, Chain.EVM_MAINNET, Chain.EVM_ARBITRUM_ONE, Chain.EVM_BSC,
			-> handleEvmTransfer(estimateGasResult, botWallet.chain)
			Chain.SOLANA -> handleSolanaTransfer(estimateGasResult)
		}
	}

	private fun handleEvmTransfer(
		estimateGasResult: EstimateGas.Result,
		chain: Chain,
	): GetBotWithdrawGasEstimationQuery.Result {
		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultEvm) error("Estimate gas must be of EVM type")

		val usdEstimatedGasLimit = estimateGasResult.gasLimitEstimate?.let {
			val baseEstimateGasLimit = BaseAmount(it * estimateGasResult.gasInfo.maxFeePerGas)
			usdConverter.baseToUsd(baseAmount = baseEstimateGasLimit, chain = chain)
		}

		return GetBotWithdrawGasEstimationQuery.Result(usdEstimatedGasLimit)
	}

	private fun handleSolanaTransfer(estimateGasResult: EstimateGas.Result): GetBotWithdrawGasEstimationQuery.Result {
		// just for smartcast
		if (estimateGasResult !is EstimateGas.ResultSolana) error("Estimate gas must be of Solana type")
		val usdEstimatedGasLimit = usdConverter.baseToUsd(estimateGasResult.feeBaseAmount, Chain.SOLANA)
		return GetBotWithdrawGasEstimationQuery.Result(usdEstimatedGasLimit)
	}
}
