package com.cleevio.fatbot.application.module.wallet

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.wallet.command.MarkWalletAsDefaultCommand
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.application.module.wallet.locks.CREATE_OR_UPDATE_USER_WALLETS
import com.cleevio.fatbot.application.module.wallet.locks.WALLET_MODULE
import com.cleevio.fatbot.application.module.wallet.port.out.SetDefaultWalletOnChain
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class MarkWalletAsDefaultCommandHandler(
	private val walletFinderService: WalletFinderService,
	private val setDefaultWalletOnChain: SetDefaultWalletOnChain,
) : CommandHandler<Unit, MarkWalletAsDefaultCommand> {

	override val command = MarkWalletAsDefaultCommand::class

	@Transactional
	@Lock(module = WALLET_MODULE, lockName = CREATE_OR_UPDATE_USER_WALLETS)
	override fun handle(@LockFieldParameter("userId") command: MarkWalletAsDefaultCommand) {
		val newDefaultWallet = walletFinderService.getByIdAndUserId(id = command.walletId, userId = command.userId)
		if (newDefaultWallet.isDefault) return

		setDefaultWalletOnChain(
			userId = command.userId,
			newDefaultWallet = newDefaultWallet.id,
			chain = newDefaultWallet.chain,
		)
	}
}
