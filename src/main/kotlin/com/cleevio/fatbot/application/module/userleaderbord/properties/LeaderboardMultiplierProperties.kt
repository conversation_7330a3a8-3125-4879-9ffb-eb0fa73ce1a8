package com.cleevio.fatbot.application.module.userleaderbord.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.math.BigDecimal

@ConfigurationProperties(prefix = "leaderboard.multiplier.rank")
class LeaderboardMultiplierProperties(
	val thresholds: Map<String, BigDecimal> = emptyMap(),
) {

	fun getMultiplierByRank(rank: Int): BigDecimal {
		return thresholds.entries
			.firstOrNull { (threshold, _) -> rank <= threshold.toInt() }
			?.value ?: BigDecimal.ONE
	}
}
