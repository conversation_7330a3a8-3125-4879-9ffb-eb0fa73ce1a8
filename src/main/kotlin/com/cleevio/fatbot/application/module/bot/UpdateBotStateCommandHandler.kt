package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.bot.command.UpdateBotStatusCommand
import com.cleevio.fatbot.application.module.bot.exception.MaxActiveBotsExceeded
import com.cleevio.fatbot.application.module.bot.locks.BOT_MODULE
import com.cleevio.fatbot.application.module.bot.locks.UPDATE_BOT
import com.cleevio.fatbot.domain.bot.MAX_ACTIVE_BOTS_PER_USER
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import io.sentry.spring.jakarta.tracing.SentrySpan
import jakarta.transaction.Transactional
import org.springframework.stereotype.Component

@Component
class UpdateBotStateCommandHandler(
	private val botFinderService: BotFinderService,
) : CommandHandler<Unit, UpdateBotStatusCommand> {

	override val command = UpdateBotStatusCommand::class

	@SentrySpan
	@Transactional
	@Lock(module = BOT_MODULE, lockName = UPDATE_BOT)
	override fun handle(@LockFieldParameter("userId") command: UpdateBotStatusCommand) {
		val bot = botFinderService.getByIdAndUserId(command.botId, command.userId)

		if (command.active && !bot.isActive) {
			val activeBots = botFinderService.findAllByUserIdAndIsActiveTrue(command.userId)
			if (activeBots.size >= MAX_ACTIVE_BOTS_PER_USER) {
				throw MaxActiveBotsExceeded(
					"User ${command.userId} can have maximum of $MAX_ACTIVE_BOTS_PER_USER active bots",
				)
			}
		}

		// TODO: As we do not have lock acquired for the bot trading, it's possible
		//  that while this is updated, we already hold another version of bot entity in memory
		//  and Optimistic lock exception might happen. Maybe safer way here would be use Jooq directly to
		//  set the value as active = command.active
		bot.updateState(command.active)
	}
}
