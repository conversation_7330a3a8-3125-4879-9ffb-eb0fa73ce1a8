package com.cleevio.fatbot.application.module.token

import com.cleevio.fatbot.adapter.out.solana.pumpswap.GetPumpswapPool
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.port.out.VerifyPumpswapPairAddress
import com.cleevio.fatbot.application.module.matchmaking.command.BotsSellPromotedTokenCommand
import com.cleevio.fatbot.application.module.token.command.TokenPromotedToPumpswapCommand
import com.cleevio.fatbot.application.module.token.exception.FailedToVerifyPumpswapPairException
import com.cleevio.fatbot.application.module.token.finder.TokenPairInfoFinderService
import com.cleevio.fatbot.application.module.token.locks.CREATE_OR_UPDATE_TOKEN_PAIR
import com.cleevio.fatbot.application.module.token.locks.TOKEN_MODULE
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.token.TokenPairInfoCreateService
import com.cleevio.fatbot.domain.token.TokenPairInfoDeleteService
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

private val PUMPFUN_TOKEN_DECIMALS = 6.toBigInteger()

@Component
class TokenPromotedToPumpswapCommandHandler(
	private val tokenPairInfoFinderService: TokenPairInfoFinderService,
	private val tokenPairInfoCreateService: TokenPairInfoCreateService,
	private val tokenPairInfoDeleteService: TokenPairInfoDeleteService,
	private val verifyPumpswapPairAddress: VerifyPumpswapPairAddress,
	private val getPumpswapPool: GetPumpswapPool,
) : CommandHandler<Unit, TokenPromotedToPumpswapCommand> {

	override val command = TokenPromotedToPumpswapCommand::class

	@Autowired
	@Lazy
	private lateinit var self: TokenPromotedToPumpswapCommandHandler

	@Autowired
	@Lazy
	private lateinit var commandBus: CommandBus

	override fun handle(command: TokenPromotedToPumpswapCommand) {
		val isValid = verifyPumpswapPairAddress(pairAddress = command.pairAddress, tokenAddress = command.tokenAddress)

		if (!isValid) {
			throw FailedToVerifyPumpswapPairException("Failed to verify Pumpswap pair address ${command.pairAddress}")
		}

		// We already verified that the pairAddress is a valid pumpswap pool
		val pumpswapPool = getPumpswapPool(address = command.pairAddress)

		require(pumpswapPool.coinCreator == command.creator) {
			"Received wrong creator as Pumpswap pool coinCreator. Should be: ${pumpswapPool.coinCreator}, was: ${command.creator}"
		}

		handleManualTrading(
			tokenAddress = command.tokenAddress,
			pairAddress = command.pairAddress,
			coinCreator = command.creator,
		)
		handleBotTrading(
			blockSlot = command.blockSlot,
			tokenAddress = command.tokenAddress,
			pairAddress = command.pairAddress,
			creator = command.creator,
		)
	}

	private fun handleManualTrading(
		tokenAddress: AddressWrapper,
		pairAddress: AddressWrapper,
		coinCreator: AddressWrapper,
	) {
		val tokenPairs = tokenPairInfoFinderService.findAllByTokenAddressAndChain(
			tokenAddress = tokenAddress,
			chain = Chain.SOLANA,
		)

		val pumpfunPair = tokenPairs.find { it.dexType == GetDex.Dex.PUMP_FUN }

		// No reason to promote / create a new Pumpswap token pair if we never traded on this token before graduation
		if (pumpfunPair == null) return

		self.inTransactionTryLock(pairAddress = pairAddress) {
			tokenPairInfoDeleteService.deleteById(pumpfunPair.id)

			val alreadyMigrated = tokenPairs.any { it.dexType == GetDex.Dex.PUMP_SWAP }

			// Already migrated once, don't have to do it second time
			if (alreadyMigrated) return@inTransactionTryLock

			tokenPairInfoCreateService.create(
				chain = Chain.SOLANA,
				dex = GetDex.PumpSwap(pairAddress = pairAddress),
				tokenAddress = tokenAddress,
				raydiumAmmMarketData = null,
				raydiumCpmmMarketData = null,
				tokenDecimals = PUMPFUN_TOKEN_DECIMALS,
				creatorAddress = coinCreator,
			)
		}
	}

	@Transactional
	@TryLock(module = TOKEN_MODULE, lockName = CREATE_OR_UPDATE_TOKEN_PAIR)
	protected fun inTransactionTryLock(@LockArgumentParameter pairAddress: AddressWrapper, block: () -> Unit) = block()

	fun handleBotTrading(
		blockSlot: Int,
		tokenAddress: AddressWrapper,
		pairAddress: AddressWrapper,
		creator: AddressWrapper,
	) {
		commandBus(
			BotsSellPromotedTokenCommand(
				blockSlot = blockSlot,
				tokenAddress = tokenAddress,
				pairAddress = pairAddress,
				creator = creator,
			),
		)
	}
}
