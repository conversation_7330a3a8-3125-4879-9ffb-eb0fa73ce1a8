package com.cleevio.fatbot.application.module.botdraft.command

import com.cleevio.fatbot.adapter.`in`.rest.dto.PatchBotSettingsRequest
import com.cleevio.fatbot.application.common.command.Command
import io.swagger.v3.oas.annotations.media.Schema
import java.util.UUID

data class CreateBotDraftCommand(
	val userId: UUID,
) : Command<CreateBotDraftCommand.Result> {

	@Schema(name = "CreateBotDraftResult")
	data class Result(val botDraftId: UUID)
}

data class PatchBotDraftCommand(
	val userId: UUID,
	val botDraftId: UUID,
	val patchRequest: PatchBotSettingsRequest,
) : Command<Unit>

data class DeleteBotDraftCommand(
	val userId: UUID,
	val botDraftId: UUID,
) : Command<Unit>
