package com.cleevio.fatbot.application.module.portfolio

import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.domain.portfolio.PortfolioValueSnapshotCreateService
import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Clock
import java.time.LocalDate

private const val USERS_WARNING_LIMIT = 500

@Service
class PortfolioValueSnapshotService(
	private val portfolioValueSnapshotCreateService: PortfolioValueSnapshotCreateService,
	private val portfolioValueService: PortfolioValueService,
	private val userFinderService: FirebaseUserFinderService,
	private val clock: Clock,
) {

	private val logger = logger()

	@Transactional
	fun createPortfolioValueSnapshots() {
		val snapshotDate = LocalDate.now(clock).minusDays(1)
		val userToPortfolioValue = userFinderService.findAll().associate {
			it.id to portfolioValueService.byChainInUsdNow(it.id)
		}

		if (userToPortfolioValue.size > USERS_WARNING_LIMIT) {
			logger.warn(
				"High load warning: Processing ${userToPortfolioValue.size} users." +
					"Consider implementing batching or optimizing user processing.",
			)
		}

		userToPortfolioValue.forEach { (userId, portfolioValues) ->
			portfolioValues.forEach { (chain, usdAmount) ->
				portfolioValueSnapshotCreateService.createPortfolioValueSnapshot(
					userId = userId,
					chain = chain,
					date = snapshotDate,
					currentValueUsd = usdAmount,
				)
			}
		}
	}
}
