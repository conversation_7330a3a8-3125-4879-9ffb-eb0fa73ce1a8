package com.cleevio.fatbot.application.module.botmarket.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class BotMarketPositionNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.BOT_MARKET_POSITION_NOT_FOUND,
	message = message,
)

@ResponseStatus(HttpStatus.NOT_FOUND)
class BotMarketPositionTradeStateNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.BOT_MARKET_POSITION_TRADE_STATE_NOT_FOUND,
	message = message,
)
