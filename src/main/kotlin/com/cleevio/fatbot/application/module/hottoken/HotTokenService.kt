package com.cleevio.fatbot.application.module.hottoken

import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.hottoken.HotTokenCreateService
import com.cleevio.fatbot.domain.hottoken.HotTokenDeleteService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class HotTokenService(
	private val hotTokenCreateService: HotTokenCreateService,
	private val hotTokenDeleteService: HotTokenDeleteService,
) {

	@Transactional
	fun deleteAndSaveAll(tokens: List<HotTokenRefreshService.RefreshData>, chain: Chain) {
		hotTokenDeleteService.deleteAllByChain(chain)

		tokens.forEach { token ->
			hotTokenCreateService.create(
				chain = token.chain,
				tokenAddress = token.tokenAddress,
				tokenSymbol = token.tokenSymbol,
				tokenName = token.tokenName,
				tokenDecimals = token.tokenDecimals,
				tokenImageFileId = token.tokenImageFileId,
				tokenImageFileExtension = token.tokenImageFileExtension,
				volume24hUsd = token.volume24hUsd,
			)
		}
	}
}
