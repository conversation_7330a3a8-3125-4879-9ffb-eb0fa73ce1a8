package com.cleevio.fatbot.application.module.botmarket.query

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionSellReason
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.net.URI
import java.time.Instant
import java.util.UUID

data class SearchBotMarketPositionQuery(
	val userId: UUID,
	val filter: Filter,
	val infiniteScroll: InfiniteScrollDesc.UUID,
) : Query<InfiniteScrollSlice<SearchBotMarketPositionQuery.Result, UUID>> {

	data class Filter(
		val botIds: Set<UUID>?,
		val isBotMarketPositionActive: Boolean? = null,
		val searchString: String? = null,
		val timeRange: TimeRange? = null,
	)

	@Schema(name = "SearchBotMarketPositionResult")
	data class Result(
		val id: UUID,
		val state: BotMarketPositionState,
		val tokenAddress: AddressWrapper,
		val tokenDetailUrl: URI,
		val tokenName: String,
		val tokenSymbol: String,
		val tokenChain: Chain,
		val tokenImageUrl: URI?,
		val openValueUsd: BigDecimal,
		val closeValueUsd: BigDecimal?,
		val openTimeStampAt: Instant,
		val closedTimeStampAt: Instant?,
		val pnlAmountUsd: BigDecimal,
		val pnlAmountFraction: BigDecimal,
		val currentValueUsd: BigDecimal,
	)
}

data class GetBotMarketPositionQuery(
	val userId: UUID,
	val botId: UUID,
	val botMarketPositionId: UUID,
) : Query<GetBotMarketPositionQuery.Result> {

	@Schema(name = "GetBotMarketPositionResult")
	data class Result(
		val id: UUID,
		val botId: UUID,
		val tokenAddress: AddressWrapper,
		val tokenDetailUrl: URI,
		val tokenName: String,
		val tokenSymbol: String,
		val tokenChain: Chain,
		val tokenImageUrl: URI?,
		val openValueUsd: BigDecimal,
		val closeValueUsd: BigDecimal?,
		val openTimeStampAt: Instant,
		val closedTimeStampAt: Instant?,
		val pnlAmountUsd: BigDecimal,
		val pnlAmountFraction: BigDecimal,
		val currentValueUsd: BigDecimal,
		val buyTradeTransaction: BotTradeTransaction,
		val sellTradeTransaction: BotTradeTransaction?,
		val sellReason: BotMarketPositionSellReason?,
	)

	@Schema(name = "BotTradeTransaction")
	data class BotTradeTransaction(
		val txId: UUID,
		val txHash: TxHash,
		val txDetailUrl: URI,
		val txType: BotTransactionType,
		val txStatus: TransactionStatus,
		val txCreatedAt: Instant,
		val txChain: Chain,
		val botWalletId: UUID,
		val amountUsd: BigDecimal,
		val marketCapUsd: BigDecimal,
		val liquidityUsd: BigDecimal,
		val volumeUsd: BigDecimal,
		val numOfHolders: Long,
		val buyVolume: BigDecimal,
		val sellVolume: BigDecimal,
		val fractionOfSellTransactions: BigDecimal,
	)
}
