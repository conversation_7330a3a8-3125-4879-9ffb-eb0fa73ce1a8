package com.cleevio.fatbot.application.module.bottransaction.port.out

import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.module.bottransaction.query.SearchUserBotsTransactionsQuery
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.net.URI
import java.time.Instant
import java.util.UUID

interface SearchLastBotsTransaction {
	operator fun invoke(
		userId: UUID,
		botIds: Set<UUID>,
		from: Instant?,
		to: Instant?,
		txHashToURIMapper: (TxHash, Chain) -> URI,
		fileToUrlMapper: FileToUrlMapper,
	): Map<UUID, List<SearchUserBotsTransactionsQuery.Result>>
}
