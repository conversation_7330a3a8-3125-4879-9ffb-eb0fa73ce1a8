package com.cleevio.fatbot.application.module.tokenaudit.service

import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.tokenaudit.event.TokenAuditRequestedEvent
import com.cleevio.fatbot.application.module.tokenaudit.locks.SAVE_TOKEN_AUDIT
import com.cleevio.fatbot.application.module.tokenaudit.locks.TOKEN_AUDIT_MODULE
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.domain.tokenaudit.TokenAuditCreateService
import com.cleevio.fatbot.domain.tokenaudit.TokenAuditResult
import com.cleevio.fatbot.domain.tokenaudit.TokenAuditState
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Clock
import java.time.Instant
import kotlin.time.Duration.Companion.minutes
import kotlin.time.toJavaDuration

@Component
class GetTokenAudit(
	private val tokenAuditFinderService: TokenAuditFinderService,
	private val tokenAuditCreateService: TokenAuditCreateService,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val clock: Clock,
) {

	data class TokenAuditDto(
		val result: TokenAuditResult?,
		val state: TokenAuditState,
	)

	@Transactional
	@Lock(module = TOKEN_AUDIT_MODULE, lockName = SAVE_TOKEN_AUDIT)
	operator fun invoke(@LockArgumentParameter tokenAddress: ChainAddress): TokenAuditDto {
		require(tokenAddress.chain.type == ChainType.EVM) { "Token audit is not supported for SOLANA tokens" }

		val tokenAudit = tokenAuditFinderService.findByTokenAddress(tokenAddress)
			?: tokenAuditCreateService.create(tokenAddress)

		if (tokenAudit.isFinished) {
			return TokenAuditDto(
				result = tokenAudit.auditResult,
				state = tokenAudit.state,
			)
		}

		val now = Instant.now(clock)
		val shouldRequestNewAudit = tokenAudit.shouldRequestNewAudit(
			now = now,
			maxAllowedProcessingDuration = MAX_ALLOWED_PROCESSING_DURATION,
		)

		if (shouldRequestNewAudit) {
			applicationEventPublisher.publishEvent(TokenAuditRequestedEvent(tokenAddress))
			tokenAudit.markNewProcessing(now)
		}

		return TokenAuditDto(
			result = tokenAudit.auditResult,
			state = tokenAudit.state,
		)
	}

	companion object {
		val MAX_ALLOWED_PROCESSING_DURATION = 3.minutes.toJavaDuration()
	}
}
