package com.cleevio.fatbot.application.module.portfolio.query

import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.module.portfolio.constant.PieChartValueType
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class GetPortfolioValuesQuery(
	val userId: UUID,
) : Query<GetPortfolioValuesQuery.Result> {

	@Schema(name = "GetPortfolioValuesResult")
	data class Result(
		val totalValueAmountUsd: BigDecimal,
		val totalPnlAmountUsd: BigDecimal,
		val totalPnlAmountFraction: BigDecimal,
		val oneDayChangeAmountUsd: BigDecimal,
		val oneDayChangeFraction: BigDecimal,
		val pastValues: List<PortfolioPastValue>,
		val pieChartValues: List<PieChartValue>,
	)

	@Schema(name = "PortfolioPastValue")
	data class PortfolioPastValue(
		val portfolioValueUsd: BigDecimal,
		val date: LocalDate,
	)

	@Schema(name = "PieChartValue")
	data class PieChartValue(
		val chain: Chain,
		val portfolioValueUsd: BigDecimal,
		val portfolioPnlFraction: BigDecimal,
		val type: PieChartValueType,
	)
}
