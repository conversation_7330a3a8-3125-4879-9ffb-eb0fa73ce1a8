package com.cleevio.fatbot.application.module.bottransaction.query

import com.cleevio.fatbot.adapter.`in`.InfiniteScroll
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.query.Query
import com.cleevio.fatbot.application.common.validation.NullOrNotBlank
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import java.math.BigDecimal
import java.net.URI
import java.time.Instant
import java.util.UUID

data class SearchUserBotsTransactionsQuery(
	val userId: UUID,
	@field:Valid val filter: Filter,
	@field:Valid val infiniteScroll: InfiniteScroll<UUID>,
) : Query<InfiniteScrollSlice<SearchUserBotsTransactionsQuery.Result, UUID>> {

	data class Filter(
		val botId: UUID,
		@field:NullOrNotBlank val searchString: String?,
	)

	@Schema(name = "SearchUserBotsTransactionsResult")
	data class Result(
		val botId: UUID,
		val botName: String,
		val txId: UUID,
		val txHash: TxHash,
		val txDetailUrl: URI,
		val txType: BotTransactionType,
		val txStatus: TransactionStatus,
		val txCreatedAt: Instant,
		val txChain: Chain,
		val walletId: UUID,
		val tokenAddress: AddressWrapper?,
		val tokenName: String?,
		val tokenSymbol: String?,
		val nativeAmount: NativeAmount?,
		val amountUsd: BigDecimal?,
		val percentageOf: BigDecimal?,
	)
}
