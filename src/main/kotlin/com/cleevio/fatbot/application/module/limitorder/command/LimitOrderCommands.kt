package com.cleevio.fatbot.application.module.limitorder.command

import com.cleevio.fatbot.application.common.command.Command
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.limitorder.LimitOrderType
import jakarta.validation.constraints.Positive
import java.util.UUID

data class CreateLimitOrderCommand(
	val userId: UUID,
	val walletId: UUID,
	val tokenAddress: AddressWrapper,
	val chain: Chain,
	@field:Positive
	val limitPrice: NativeAmount,
	@field:Positive
	val initialAmount: NativeAmount,
	val type: LimitOrderType,
) : Command<Unit>

data class DeleteLimitOrderCommand(
	val userId: UUID,
	val limitOrderId: UUID,
) : Command<Unit>
