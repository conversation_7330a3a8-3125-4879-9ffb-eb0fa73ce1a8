package com.cleevio.fatbot.application.module.botwallet.port.out

import com.cleevio.fatbot.application.common.crypto.TxHash
import java.math.BigDecimal
import java.math.BigInteger
import java.util.UUID

interface UpsertBotDepositTransactions {

	data class Input(
		val botWalletId: UUID,
		val txHash: TxHash,
		val baseValue: BigInteger,
		val exchangeRateUsd: BigDecimal,
	)

	operator fun invoke(inputs: List<Input>)
}
