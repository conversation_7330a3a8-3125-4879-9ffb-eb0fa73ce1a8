package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.user.port.out.GetUser
import com.cleevio.fatbot.application.module.user.query.GetUserQuery
import org.springframework.stereotype.Component

@Component
class GetUserQueryHandler(
	private val getUser: GetUser,
) : QueryHandler<GetUserQuery.Result, GetUserQuery> {

	override val query = GetUserQuery::class

	override fun handle(query: GetUserQuery): GetUserQuery.Result = getUser(userId = query.userId)
}
