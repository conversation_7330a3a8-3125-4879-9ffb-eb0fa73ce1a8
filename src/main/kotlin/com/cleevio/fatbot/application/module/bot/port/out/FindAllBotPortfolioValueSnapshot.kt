package com.cleevio.fatbot.application.module.bot.port.out

import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

interface FindAllBotPortfolioValueSnapshot {
	operator fun invoke(botIds: Set<UUID>, from: Instant, to: Instant): List<Result>

	data class Result(
		val botId: UUID,
		val chain: Chain,
		val snapshotMadeAt: Instant,
		val portfolioValueUsd: BigDecimal,
		val acquisitionValueUsd: BigDecimal,
	)
}
