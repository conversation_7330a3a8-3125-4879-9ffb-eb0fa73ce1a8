package com.cleevio.fatbot.application.module.token.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.exception.TokenNotFoundException
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.token.EvmTokenInfo
import com.cleevio.fatbot.domain.token.EvmTokenInfoRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class EvmTokenInfoFinderService(private val evmTokenInfoRepository: EvmTokenInfoRepository) :
	BaseFinderService<EvmTokenInfo>(evmTokenInfoRepository) {

	override fun errorBlock(message: String) = throw TokenNotFoundException(message)

	override fun getEntityType() = EvmTokenInfo::class

	@Transactional(readOnly = true)
	fun findByTokenAddressAndChain(tokenAddress: AddressWrapper, chain: Chain) =
		evmTokenInfoRepository.findByAddressAndChain(tokenAddress, chain)

	@Transactional(readOnly = true)
	fun getByTokenAddressAndChain(tokenAddress: AddressWrapper, chain: Chain) =
		evmTokenInfoRepository.findByAddressAndChain(tokenAddress, chain)
			?: errorBlock("Unknown token $tokenAddress on chain $chain")

	@Transactional(readOnly = true)
	fun findAllByTokenAddresses(tokenAddress: Set<AddressWrapper>) =
		evmTokenInfoRepository.findAllByAddressIn(tokenAddress)
}
