package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.calculateFractionChange
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotMarketPosition
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotPortfolioValueSnapshot
import com.cleevio.fatbot.application.module.bot.port.out.GetMyBots
import com.cleevio.fatbot.application.module.bot.query.GetMyBotsQuery
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.market.service.UsdConverter
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Clock
import java.time.Instant
import java.util.UUID

@Component
class GetMyBotsQueryHandler(
	private val getMyBots: GetMyBots,
	private val findAllBotMarketPosition: FindAllBotMarketPosition,
	private val getTokenPrices: GetTokenPrices,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val findAllBotPortfolioValueSnapshot: FindAllBotPortfolioValueSnapshot,
	private val fileUrlMapper: FileUrlMapper,
	private val usdConverter: UsdConverter,
	private val clock: Clock,
) : QueryHandler<List<GetMyBotsQuery.Result>, GetMyBotsQuery> {

	override val query = GetMyBotsQuery::class

	@SentrySpan
	override fun handle(query: GetMyBotsQuery): List<GetMyBotsQuery.Result> {
		val now = Instant.now(clock)

		val myAllBots = getMyBots(
			userId = query.userId,
			timeRange = query.timeRange,
			now = now,
			fileToUrlMapper = fileUrlMapper::map,
		)

		val bots = myAllBots.filterIsInstance<GetMyBots.Result.Bot>()
		val botDrafts = myAllBots.filterIsInstance<GetMyBots.Result.BotDraft>()

		val botToPortfolioValueOverview = getPortfolioOverviews(
			userId = query.userId,
			bots = bots,
		)

		val botResults = bots.map {
			val overview = botToPortfolioValueOverview.getValue(it.id)

			GetMyBotsQuery.Result(
				id = it.id,
				isActive = it.isActive,
				numberOfActiveDays = it.numberOfActiveDays,
				avatarFileId = it.avatarFileId,
				name = it.name,
				buyCount = it.buyCount,
				sellCount = it.sellCount,
				avatarFileUrl = it.avatarFileUrl,
				draftCompleteness = null,
				botWalletAddress = it.walletAddress,
				balanceUsd = overview.totalValueUsd,
				timeRangeChangeUsd = overview.timeRangeChangeUsd,
				timeRangeChangeFraction = overview.timeRangeChangeFraction,
			)
		}

		val botDraftResults = botDrafts.map {
			GetMyBotsQuery.Result(
				id = it.id,
				isActive = it.isActive,
				numberOfActiveDays = it.numberOfActiveDays,
				avatarFileId = it.avatarFileId,
				name = it.name,
				avatarFileUrl = it.avatarFileUrl,
				draftCompleteness = it.draftCompleteness,
				buyCount = null,
				sellCount = null,
				botWalletAddress = null,
				balanceUsd = null,
				timeRangeChangeUsd = null,
				timeRangeChangeFraction = null,
			)
		}

		return botResults + botDraftResults
	}

	private data class BotPortfolioValueOverview(
		val totalValueUsd: BigDecimal,
		val timeRangeChangeUsd: BigDecimal,
		val timeRangeChangeFraction: BigDecimal,
	)

	private fun getPortfolioOverviews(
		userId: UUID,
		bots: List<GetMyBots.Result.Bot>,
	): Map<UUID, BotPortfolioValueOverview> {
		val botIds = bots.mapToSet { it.id }

		val allBotMarketPositions = findAllBotMarketPosition(
			userId = userId,
			botIds = botIds,
			isBotMarketPositionActive = null,
			searchString = null,
			fileToUrlMapper = fileUrlMapper::map,
		)

		val openPositions = allBotMarketPositions.filter { it.positionClosedAt == null }
		val openPositionTokens = openPositions.mapToSet { it.tokenAddress.toChainAddress(it.chain) }
		val tokenAddressToTokenPrice = getTokenPrices(tokens = openPositionTokens)

		val chainToExchangeRate = Chain.entries.associateWith { getUsdExchangeRate(it.currency) }

		val botIdToMarketPositionValue = BotMarketPositionCalculator.botToMarketPositionsResults(
			botMarketPositions = openPositions,
			tokenAddressToTokenPrice = tokenAddressToTokenPrice,
			chainToExchangeRate = chainToExchangeRate,
		)

		val botIdToOverview = bots.associate { bot ->
			val walletBalanceUsd = usdConverter.baseToUsd(bot.walletBalance, bot.chain, chainToExchangeRate.getValue(bot.chain))
			val marketPositionsValueUsd = botIdToMarketPositionValue[bot.id]
				?.let { it.currentValueUsd + it.rentUsd }
				?: BigDecimal.ZERO

			val totalValueNowUsd = marketPositionsValueUsd + walletBalanceUsd

			val timeRangeChangeUsd = totalValueNowUsd - bot.acquisitionValueUsd
			val timeRangeChangeFraction = calculateFractionChange(totalValueNowUsd, bot.acquisitionValueUsd)

			val overview = BotPortfolioValueOverview(
				totalValueUsd = totalValueNowUsd,
				timeRangeChangeUsd = timeRangeChangeUsd,
				timeRangeChangeFraction = timeRangeChangeFraction,
			)

			bot.id to overview
		}

		return botIdToOverview
	}
}
