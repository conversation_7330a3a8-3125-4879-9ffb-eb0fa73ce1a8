package com.cleevio.fatbot.application.module.gassnapshot.scheduled

import com.cleevio.fatbot.application.module.gassnapshot.GasSnapshotProcessingService
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentryTransaction
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class GasInfoSnapshotTrigger(private val gasSnapshotProcessingService: GasSnapshotProcessingService) {

	private val logger = logger()

	@SentryTransaction(operation = "scheduled.process-gas-snapshots")
	@Scheduled(cron = "\${fatbot.gas.snapshot-processing.cron}")
	@SchedulerLock(
		name = "PROCESS_GAS_SNAPSHOTS",
		lockAtMostFor = "\${fatbot.gas.snapshot-processing.lock-for}",
		lockAtLeastFor = "\${fatbot.gas.snapshot-processing.lock-for}",
	)
	fun trigger() {
		logger.info("PROCESS_GAS_SNAPSHOTS cron started")
		gasSnapshotProcessingService.processGasSnapshots()
		logger.info("PROCESS_GAS_SNAPSHOTS cron ended")
	}
}
