package com.cleevio.fatbot.application.module.botportfolio.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.domain.botportfolio.BotPortfolioValueSnapshot
import com.cleevio.fatbot.domain.botportfolio.BotPortfolioValueSnapshotRepository
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class BotPortfolioValueSnapshotFinderService(
	val botPortfolioValueSnapshotRepository: BotPortfolioValueSnapshotRepository,
) : BaseFinderService<BotPortfolioValueSnapshot>(
	botPortfolioValueSnapshotRepository,
) {

	override fun errorBlock(message: String) = error(message)
	override fun getEntityType() = BotPortfolioValueSnapshot::class

	fun findAllByBotIds(botIds: Set<UUID>): List<BotPortfolioValueSnapshot> {
		return botPortfolioValueSnapshotRepository.findAllByBotIdIn(botIds = botIds)
	}
}
