package com.cleevio.fatbot.application.module.file

import com.cleevio.fatbot.application.module.file.finder.FileFinderService
import com.cleevio.fatbot.domain.file.File
import com.cleevio.fatbot.domain.file.FileCreateService
import com.cleevio.fatbot.domain.file.FileDeleteService
import com.cleevio.fatbot.domain.file.FileNotDeletedException
import org.apache.tika.config.TikaConfig
import org.apache.tika.metadata.Metadata
import org.apache.tika.metadata.TikaCoreProperties
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import java.io.InputStream
import java.net.URI
import java.util.UUID

@Service
class FileService(
	private val fileStorageService: FileStorageService,
	private val fileCreateService: FileCreateService,
	private val fileDeleteService: FileDeleteService,
	private val fileFinderService: FileFinderService,
) {

	private val tikaConfig = TikaConfig.getDefaultConfig()

	@Transactional
	fun upload(multipartFile: MultipartFile) = upload(multipartFile.inputStream, multipartFile.originalFilename)

	@Transactional
	fun upload(byteArray: ByteArray, fileName: String) = upload(byteArray.inputStream(), fileName)

	private fun upload(inputStream: InputStream, fileName: String?): File {
		val fileExtension = getFileExtension(inputStream, fileName)
		val file = fileCreateService.create(fileExtension)

		fileStorageService.save(file.fileName(), inputStream)

		return file
	}

	private fun getFileExtension(inputStream: InputStream, fileName: String?): String {
		val metadata = Metadata().apply {
			set(TikaCoreProperties.RESOURCE_NAME_KEY, fileName)
		}

		val mediaType = tikaConfig.mimeRepository.detect(inputStream, metadata)
		val mimeType = tikaConfig.mimeRepository.forName(mediaType.toString())
		val extension = mimeType.extension?.removePrefix(".")

		require(!extension.isNullOrBlank())

		return extension
	}

	@Transactional
	fun delete(fileId: UUID) {
		val file = fileFinderService.getById(fileId)
		if (!fileStorageService.delete(file.fileName())) throw FileNotDeletedException()
		fileDeleteService.deleteById(file.id)
	}

	@Transactional
	fun deleteAll(fileIds: Set<UUID>) {
		val files = fileFinderService.getAllByIds(ids = fileIds)
		files.forEach { fileStorageService.delete(it.fileName()) }
		fileDeleteService.deleteAllByIds(fileIds = fileIds)
	}

	/**
	 * Duplicate file with given fileId, returning new File pointing to separate file on disk.
	 * Deleting newly created File will not affect old File in any way (nor old file on disk).
	 */
	@Transactional
	fun duplicate(fileId: UUID): File {
		val file = fileFinderService.getById(fileId)
		val newFile = fileCreateService.create(extension = file.extension)
		fileStorageService.copy(file.fileName(), newFile.fileName())
		return newFile
	}

	fun getAllByFileId(fileIds: Set<UUID>): List<java.io.File> {
		return fileFinderService
			.getAllByIds(fileIds)
			.map { getByFilename(it.fileName()) }
	}

	fun getById(fileId: UUID): java.io.File {
		val file = fileFinderService.getById(id = fileId)
		return fileStorageService.get(file.fileName())
	}

	fun getByFilename(filename: String): java.io.File {
		return fileStorageService.get(filename)
	}
}

typealias FileToUrlMapper = (fileId: UUID?, fileExtension: String?) -> URI?

@Component
class FileUrlMapper(
	@Value("\${fatbot.file-storage-url}") private val fileStorageUrl: String,
) {

	fun map(fileId: UUID?, fileExtension: String?): URI? = if (fileId != null && fileExtension != null) {
		URI.create(mapFromFileName("$fileId.$fileExtension"))
	} else {
		null
	}

	fun map(fileName: String): URI = URI.create(mapFromFileName(fileName))

	private fun mapFromFileName(fileName: String): String = "$fileStorageUrl/$fileName"
}
