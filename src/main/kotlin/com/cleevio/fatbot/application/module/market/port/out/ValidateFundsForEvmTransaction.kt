package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.adapter.out.evm.model.GasInfo
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import java.math.BigInteger

interface ValidateFundsForEvmTransaction {

	data class Result(
		val gasLimitEstimate: BigInteger,
	)

	operator fun invoke(
		walletAddress: AddressWrapper,
		tokenAddress: AddressWrapper?,
		chainId: Long,
		transactionType: TransactionType,
		transactionAmount: BaseAmount,
		gasInfo: GasInfo,
		gasLimitEstimate: BigInteger?,
	): Result
}
