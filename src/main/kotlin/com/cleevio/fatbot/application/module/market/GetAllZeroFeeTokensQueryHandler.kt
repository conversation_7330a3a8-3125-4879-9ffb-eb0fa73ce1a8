package com.cleevio.fatbot.application.module.market

import com.cleevio.fatbot.application.common.crypto.COINS_EXEMPTED_FROM_FATBOT_FEE
import com.cleevio.fatbot.application.common.crypto.stableUsdPeggedTokens
import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.mapNotNullToSet
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.file.finder.FileFinderService
import com.cleevio.fatbot.application.module.market.query.GetAllZeroFeeTokensQuery
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import org.springframework.stereotype.Component
import java.net.URI

@Component
class GetAllZeroFeeTokensQueryHandler(
	private val evmTokenInfoFinderService: EvmTokenInfoFinderService,
	private val fileFinderService: FileFinderService,
	private val fileUrlMapper: FileUrlMapper,
) : QueryHandler<List<GetAllZeroFeeTokensQuery.Result>, GetAllZeroFeeTokensQuery> {

	override val query = GetAllZeroFeeTokensQuery::class

	val zeroFeeTokens = COINS_EXEMPTED_FROM_FATBOT_FEE + stableUsdPeggedTokens

	override fun handle(query: GetAllZeroFeeTokensQuery): List<GetAllZeroFeeTokensQuery.Result> {
		val tokenAddresses = zeroFeeTokens.mapToSet { it.address }

		val tokenAddressToTokenInfo = evmTokenInfoFinderService
			.findAllByTokenAddresses(tokenAddress = tokenAddresses)
			.associateBy { it.address }

		val fileIds = tokenAddressToTokenInfo
			.values
			.mapNotNullToSet { it.imageFileId }

		val fileIdToFile = fileFinderService.getAllByIds(ids = fileIds).associateBy { it.id }

		return zeroFeeTokens.map {
			val tokenInfo = tokenAddressToTokenInfo[it.address]
			val tokenFileId = tokenInfo?.imageFileId
			GetAllZeroFeeTokensQuery.Result(
				chain = it.chain,
				imageUrl = fileUrlMapper.map(
					fileId = tokenFileId,
					fileExtension = tokenFileId?.let { fileIdToFile[it]?.extension },
				),
				tokenAddress = it.address,
				tokenName = tokenInfo?.name ?: "",
				tokenSymbol = tokenInfo?.symbol ?: "",
				tokenDetailUrl = URI.create(it.address.getLinkToExplorer(it.chain)),
			)
		}
	}
}
