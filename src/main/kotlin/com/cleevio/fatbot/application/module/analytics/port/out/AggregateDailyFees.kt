package com.cleevio.fatbot.application.module.analytics.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.time.LocalDate

interface AggregateDailyFees {

	/**
	 * Aggregates the daily revenue fees within the specified date range.
	 *
	 * The [from] date, if provided, is used as the lower bound for the aggregation (inclusive).
	 * The [to] date, if provided, is used as the upper bound for the aggregation (exclusive).
	 *
	 * The (chain, date) is unique across the whole returned list.
	 */
	operator fun invoke(from: LocalDate? = null, to: LocalDate? = null): List<Result>

	data class Result(
		val chain: Chain,
		val date: LocalDate,
		val type: DailyFeeType,
		val baseAmount: BaseAmount,
	)
}
