package com.cleevio.fatbot.application.module.userfattycard

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.fattycard.finder.FattyCardFinderService
import com.cleevio.fatbot.application.module.userfattycard.command.ClaimUserFattyCardCommand
import com.cleevio.fatbot.application.module.userfattycard.finder.UserFattyCardFinderService
import com.cleevio.fatbot.application.module.userfattycard.locks.CREATE_OR_UPDATE_USER_FATTY_CARDS
import com.cleevio.fatbot.application.module.userfattycard.locks.USER_FATTY_CARD_MODULE
import com.cleevio.fatbot.application.module.userstatistics.service.UpdateDonutsGainedUserStatisticsService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class ClaimUserFattyCardCommandHandler(
	private val fattyCardFinderService: FattyCardFinderService,
	private val userFattyCardFinderService: UserFattyCardFinderService,
	private val updateDonutsGainedUserStatisticsService: UpdateDonutsGainedUserStatisticsService,
) : CommandHandler<Unit, ClaimUserFattyCardCommand> {

	override val command = ClaimUserFattyCardCommand::class

	@Transactional
	@Lock(module = USER_FATTY_CARD_MODULE, lockName = CREATE_OR_UPDATE_USER_FATTY_CARDS)
	override fun handle(@LockFieldParameter("userId") command: ClaimUserFattyCardCommand) {
		val userFattyCard = userFattyCardFinderService.getByIdAndUserId(
			id = command.userFattyCardId,
			userId = command.userId,
		)

		val fattyCard = fattyCardFinderService.getById(id = userFattyCard.fattyCardId)

		// add donut reward to user statistics
		updateDonutsGainedUserStatisticsService(userId = command.userId, tradeUsdAmount = fattyCard.donutReward)
		userFattyCard.claim()
	}
}
