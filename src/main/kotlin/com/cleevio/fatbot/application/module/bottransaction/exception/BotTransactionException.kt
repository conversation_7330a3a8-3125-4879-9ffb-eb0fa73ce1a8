package com.cleevio.fatbot.application.module.bottransaction.exception

import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus
import java.util.UUID

class BotTransactionNotLandedException(val id: UUID) : Throwable()

class BotTransactionFailedException(val id: UUID) : Throwable()

@ResponseStatus(HttpStatus.NOT_FOUND)
class BotTransactionNotFoundException(message: String) : FatbotApiException(
	reason = ExtendedErrorReasonType.BOT_TRANSACTION_NOT_FOUND,
	message = message,
)
