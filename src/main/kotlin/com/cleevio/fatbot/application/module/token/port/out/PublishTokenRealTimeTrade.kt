package com.cleevio.fatbot.application.module.token.port.out

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.time.Instant

interface PublishTokenRealTimeTrade {
	operator fun invoke(
		tokenAddress: AddressWrapper,
		chain: Chain,
		timestamp: Instant,
		priceUsd: BigDecimal,
		amountUsd: BigDecimal,
	)
}
