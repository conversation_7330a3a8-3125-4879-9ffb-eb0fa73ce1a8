package com.cleevio.fatbot.application.module.heartbeat.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.domain.heartbeat.HeartBeat
import com.cleevio.fatbot.domain.heartbeat.HeartBeatRepository
import com.cleevio.fatbot.domain.heartbeat.HeartBeatType
import org.springframework.stereotype.Service

@Service
class HeartBeatFinderService(
	private val heartBeatRepository: HeartBeatRepository,
) : BaseFinderService<HeartBeat>(heartBeatRepository) {

	override fun errorBlock(message: String) = error(message)

	override fun getEntityType() = HeartBeat::class

	fun findByType(type: HeartBeatType) = heartBeatRepository.findByType(type = type)
}
