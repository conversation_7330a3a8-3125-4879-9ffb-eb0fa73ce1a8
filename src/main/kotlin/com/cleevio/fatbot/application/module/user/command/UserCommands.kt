package com.cleevio.fatbot.application.module.user.command

import com.cleevio.fatbot.application.common.command.Command
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import java.math.BigDecimal
import java.util.UUID

data class SignUpFirebaseUserCommand(
	@field:NotBlank val email: String,
	val referralCode: String?,
) : Command<Unit>

data class CreateNewReferralCodeCommand(
	val userId: UUID,
	val referralCode: String,
) : Command<Unit>

data class UpdateQuickBuyAmountCommand(
	val userId: UUID,
	val quickBuyAmountUsd: BigDecimal,
) : Command<Unit>

data class UpdateSelectedChainsCommand(
	val userId: UUID,
	@field:NotEmpty val selectedChains: Set<Chain>,
) : Command<Unit>
