package com.cleevio.fatbot.application.module.user

import com.cleevio.fatbot.application.common.command.CommandHandler
import com.cleevio.fatbot.application.module.user.command.UpdateSelectedChainsCommand
import com.cleevio.fatbot.application.module.user.finder.FirebaseUserFinderService
import com.cleevio.fatbot.application.module.user.locks.UPDATE_USER
import com.cleevio.fatbot.application.module.user.locks.USER_MODULE
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class UpdateSelectedChainsCommandHandler(
	private val firebaseUserFinderService: FirebaseUserFinderService,
) : CommandHandler<Unit, UpdateSelectedChainsCommand> {

	override val command = UpdateSelectedChainsCommand::class

	@Transactional
	@Lock(module = USER_MODULE, lockName = UPDATE_USER)
	override fun handle(@LockFieldParameter("userId") command: UpdateSelectedChainsCommand) {
		firebaseUserFinderService.getById(id = command.userId).apply {
			this.setSelectedChains(selectedChains = command.selectedChains)
		}
	}
}
