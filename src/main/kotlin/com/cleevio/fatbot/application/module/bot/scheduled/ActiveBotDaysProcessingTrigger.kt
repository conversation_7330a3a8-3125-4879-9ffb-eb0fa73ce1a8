package com.cleevio.fatbot.application.module.bot.scheduled

import com.cleevio.fatbot.application.module.bot.service.ActiveBotsDaysService
import com.cleevio.fatbot.infrastructure.config.logger
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class ActiveBotDaysProcessingTrigger(
	private val activeBotsDaysService: ActiveBotsDaysService,
) {

	private val logger = logger()

	@Scheduled(cron = "\${fatbot.bot.active-days.cron}")
	@SchedulerLock(
		name = "ACTIVE_BOT_DAYS",
		lockAtMostFor = "\${fatbot.bot.active-days.lock-for}",
		lockAtLeastFor = "\${fatbot.bot.active-days.lock-for}",
	)
	fun trigger() {
		logger.info("ACTIVE_BOT_DAYS cron started")
		activeBotsDaysService.processBotActiveDays()
		logger.info("ACTIVE_BOT_DAYS cron ended")
	}
}
