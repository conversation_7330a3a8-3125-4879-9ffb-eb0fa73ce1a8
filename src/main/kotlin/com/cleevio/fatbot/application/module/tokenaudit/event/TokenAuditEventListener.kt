package com.cleevio.fatbot.application.module.tokenaudit.event

import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.module.tokenaudit.command.PerformTokenAuditCommand
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class TokenAuditEventListener(
	private val commandBus: CommandBus,
) {

	@Async
	@TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
	@SentryTransaction(operation = "async.save-token-audit")
	fun onTokenAuditRequestedEvent(event: TokenAuditRequestedEvent) {
		commandBus(
			PerformTokenAuditCommand(tokenAddress = event.tokenAddress),
		)
	}
}
