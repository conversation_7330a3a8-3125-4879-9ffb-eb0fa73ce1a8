package com.cleevio.fatbot.application.module.transaction

import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.transaction.port.out.PublishUserTransactionStatusChanged
import com.cleevio.fatbot.application.module.transaction.port.out.model.TransactionStatusModel
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import org.springframework.stereotype.Service

@Service
class TransactionStatusPublishingService(
	private val walletFinderService: WalletFinderService,
	private val publishUserTransactionStatusChanged: PublishUserTransactionStatusChanged,
) {
	fun publishTransactionsToUsers(transactionStatusModels: List<TransactionStatusModel>) {
		val transactionWalletIds = transactionStatusModels.mapToSet { it.walletId }
		val userIdToWalletIds = walletFinderService.findAllByIds(ids = transactionWalletIds)
			.groupBy { it.userId }
			.mapValues { entry -> entry.value.map { it.id } }

		userIdToWalletIds.forEach { userIdToWalletIdsEntry ->
			val userWalletIds = userIdToWalletIds[userIdToWalletIdsEntry.key] ?: return@forEach
			publishUserTransactionStatusChanged(
				userId = userIdToWalletIdsEntry.key,
				transactions = transactionStatusModels.filter { it.walletId in userWalletIds }.toList(),
			)
		}
	}
}
