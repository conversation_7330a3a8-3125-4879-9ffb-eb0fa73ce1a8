package com.cleevio.fatbot.application.module.bot

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.module.bot.query.GetBotSettingsStatisticsQuery
import com.cleevio.fatbot.application.module.bot.query.GetBotSettingsStatisticsQuery.ValueBucket
import com.cleevio.fatbot.domain.bot.Bot
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.RoundingMode

private val TOP_ITEMS_DROP_SIZE_BP = BasisPoint.ofPercentage(10.0)

@Component
class GetBotSettingsStatisticsQueryHandler(
	private val botFinderService: BotFinderService,
) : QueryHandler<GetBotSettingsStatisticsQuery.Result, GetBotSettingsStatisticsQuery> {

	override val query = GetBotSettingsStatisticsQuery::class

	object NumBuckets {
		const val TRADE_AMOUNT = 10
		const val BUY_FREQUENCY = 50

		const val PROFIT_TARGET = 20
		const val STOP_LOSS = 20
		const val NUM_HOLDERS = 20
		const val BUY_VOLUME = 10
		const val SELL_VOLUME = 10
		const val SELL_TRANSACTION_FRACTION_THRESHOLD = 10

		const val MARKET_CAP = 35
		const val LIQUIDITY = 35
		const val DAILY_VOLUME = 35
	}

	// The logic here is fine for development early project run (tested on 32000 bots)
	// TODO: Refactor to remove potential complexity wall
	override fun handle(query: GetBotSettingsStatisticsQuery): GetBotSettingsStatisticsQuery.Result {
		val allBots = botFinderService.findAll()

		val tradeAmount = allBots.groupingCountByBuckets(
			numBuckets = NumBuckets.TRADE_AMOUNT,
			selector = { it.tradeAmount.scaleByPowerOfTen(2) },
		).mapWith { it.scaleByPowerOfTen(-2).stripTrailingZeros() }

		val buyFrequency = allBots.groupingCountByBuckets(
			numBuckets = NumBuckets.BUY_FREQUENCY,
			selector = { it.buyFrequency.toBigDecimal() },
		)
		val stopLoss = allBots.groupingCountByBuckets(
			selector = { it.stopLossFraction.scaleByPowerOfTen(2) },
			numBuckets = NumBuckets.STOP_LOSS,
		).mapWith { it.scaleByPowerOfTen(-2) }

		val profitTarget = allBots.groupingCountByBuckets(
			numBuckets = NumBuckets.PROFIT_TARGET,
			selector = { it.profitTargetFraction.scaleByPowerOfTen(2) },
		).mapWith { it.scaleByPowerOfTen(-2) }

		val buyVolume = allBots.groupingCountByBuckets(
			numBuckets = NumBuckets.BUY_VOLUME,
			selector = { it.buyVolume?.scaleByPowerOfTen(2) ?: BigDecimal.ZERO },
		).mapWith { it.scaleByPowerOfTen(-2).stripTrailingZeros() }

		val sellVolume = allBots.groupingCountByBuckets(
			numBuckets = NumBuckets.SELL_VOLUME,
			selector = { it.sellVolume?.scaleByPowerOfTen(2) ?: BigDecimal.ZERO },
		).mapWith { it.scaleByPowerOfTen(-2).stripTrailingZeros() }

		val sellTransactionFractionThreshold = allBots.groupingCountByBuckets(
			numBuckets = NumBuckets.SELL_TRANSACTION_FRACTION_THRESHOLD,
			selector = { it.sellTransactionFraction?.scaleByPowerOfTen(2) ?: BigDecimal.ZERO },
		).mapWith { it.scaleByPowerOfTen(-2).stripTrailingZeros() }

		val marketCap = allBots.groupingCountRangeByBuckets(
			numBuckets = NumBuckets.MARKET_CAP,
			fromSelector = { it.marketCapFromUsd ?: BigDecimal.ZERO },
			toSelector = { it.marketCapToUsd },
		)

		val dailyVolumeUsd = allBots.groupingCountRangeByBuckets(
			numBuckets = NumBuckets.DAILY_VOLUME,
			fromSelector = { it.dailyVolumeFromUsd ?: BigDecimal.ZERO },
			toSelector = { it.dailyVolumeToUsd },
		)

		val liquidity = allBots.groupingCountRangeByBuckets(
			numBuckets = NumBuckets.LIQUIDITY,
			fromSelector = { it.liquidityFromUsd ?: BigDecimal.ZERO },
			toSelector = { it.liquidityToUsd },
		)

		val numberOfHolders = allBots.groupingCountRangeByBuckets(
			numBuckets = NumBuckets.NUM_HOLDERS,
			fromSelector = { it.numberOfHoldersFrom?.toBigDecimal() ?: BigDecimal.ZERO },
			toSelector = { it.numberOfHoldersTo?.toBigDecimal() },
		)

		return GetBotSettingsStatisticsQuery.Result(
			tradeAmount = tradeAmount,
			profitTarget = profitTarget,
			stopLoss = stopLoss,
			buyVolume = buyVolume,
			sellVolume = sellVolume,
			marketCapUsd = marketCap,
			liquidityUsd = liquidity,
			numberOfHolders = numberOfHolders.mapWith { it?.toBigInteger() },
			buyFrequency = buyFrequency.mapWith(BigDecimal::toBigInteger),
			dailyVolumeUsd = dailyVolumeUsd,
			sellTransactionFractionThreshold = sellTransactionFractionThreshold,
		)
	}

	private fun List<Bot>.groupingCountByBuckets(
		numBuckets: Int,
		selector: (Bot) -> BigDecimal,
	): List<ValueBucket<BigDecimal>> {
		val data = map(selector).sorted()

		val dropCount = TOP_ITEMS_DROP_SIZE_BP.applyOn(data.size)
		val values = data.dropLast(dropCount)

		val buckets = groupToBuckets(
			values = values,
			minValue = BigDecimal.ZERO,
			maxValue = values.lastOrNull(),
			numBuckets = numBuckets,
		)

		return buckets
	}

	private fun List<Bot>.groupingCountRangeByBuckets(
		numBuckets: Int,
		fromSelector: (Bot) -> BigDecimal,
		toSelector: (Bot) -> BigDecimal?,
	): List<ValueBucket<out BigDecimal?>> {
		val fromItems = map(fromSelector)
		val toItems = mapNotNull(toSelector).sorted()

		val dropCount = TOP_ITEMS_DROP_SIZE_BP.applyOn(toItems.size)
		val values = fromItems + toItems.dropLast(dropCount)

		// The number of items removed by `mapNotNull(toSelector)`
		val nullCount = (size * 2) - (toItems.size + fromItems.size)

		val buckets = groupToBuckets(
			values = values,
			minValue = BigDecimal.ZERO,
			maxValue = toItems.lastOrNull() ?: fromItems.maxOrNull(),
			numBuckets = numBuckets,
		)

		val lastBucket = run {
			val from = buckets.maxOf { it.to }

			ValueBucket(from = from, to = null, count = nullCount)
		}

		return buckets + lastBucket
	}

	private fun groupToBuckets(
		values: List<BigDecimal>,
		minValue: BigDecimal,
		maxValue: BigDecimal?,
		numBuckets: Int,
	): List<ValueBucket<BigDecimal>> {
		require(numBuckets > 0) { "Number of buckets must be greater than 0" }

		val maxValueNormalized = maxValue
			?.setScale(0, RoundingMode.CEILING)
			?.inc()
			?.max(numBuckets.toBigDecimal()) ?: numBuckets.toBigDecimal()

		require(maxValueNormalized > minValue) { "minValue must be less than maxValue" }

		val bucketWidth = (maxValueNormalized - minValue).divide(numBuckets.toBigDecimal(), RoundingMode.CEILING)
		val bucketToCount = values.groupingBy { value ->
			when {
				value < minValue -> 0
				else -> (value - minValue).divide(bucketWidth, RoundingMode.FLOOR).toInt() + 1
			}
		}.eachCount()

		val buckets = (1..numBuckets).map { bucketNumber ->
			val from = bucketWidth * bucketNumber.toBigDecimal().dec()
			val to = from + bucketWidth
			val count = bucketToCount[bucketNumber] ?: 0

			ValueBucket(from = from, to = to, count = count)
		}

		return buckets
	}

	private fun <T, Y> List<ValueBucket<out T>>.mapWith(fn: (T) -> Y) =
		map { ValueBucket(from = fn(it.from), to = fn(it.to), count = it.count) }
}
