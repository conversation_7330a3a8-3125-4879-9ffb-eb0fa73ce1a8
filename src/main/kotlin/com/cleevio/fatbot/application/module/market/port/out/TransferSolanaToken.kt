package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper

interface TransferSolanaToken {

	operator fun invoke(
		senderTokenAccountAddress: AddressWrapper,
		senderTokenAccountBalance: BaseAmount,
		tokenAddress: AddressWrapper,
		isToken2022: <PERSON><PERSON><PERSON>,
		tokenDecimals: Int,
		destinationWalletAddress: AddressWrapper,
		privateKey: String,
		amountToTransfer: BaseAmount,
	): SignedTx
}
