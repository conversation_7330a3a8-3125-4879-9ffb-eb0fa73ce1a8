package com.cleevio.fatbot.application.module.userstatistics

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.util.atStartOfNextDay
import com.cleevio.fatbot.application.module.userstatistics.port.out.GetUserStreak
import com.cleevio.fatbot.application.module.userstatistics.properties.UserStreakMultiplierProperties
import com.cleevio.fatbot.application.module.userstatistics.query.GetUserStreakQuery
import com.cleevio.fatbot.application.module.userstatistics.query.GetUserStreakQuery.MultipliersThresholdResult
import org.springframework.stereotype.Component
import java.time.LocalDate

@Component
class GetUserStreakQueryHandler(
	private val getUserStreak: GetUserStreak,
	private val userStreakMultiplierProperties: UserStreakMultiplierProperties,
) : QueryHandler<GetUserStreakQuery.Result, GetUserStreakQuery> {

	override val query = GetUserStreakQuery::class

	override fun handle(query: GetUserStreakQuery): GetUserStreakQuery.Result {
		val streak = getUserStreak(query.userId)
		val multiplier = userStreakMultiplierProperties.getCurrentMultiplier(
			daysInStreak = streak.daysInStreak,
		)
		val nextMultiplier = userStreakMultiplierProperties.getNextMultiplierInfo(
			currentDaysInStreak = streak.daysInStreak,
		)

		val threshold = userStreakMultiplierProperties.thresholds.map {
			MultipliersThresholdResult(
				daysInStreak = it.key,
				multiplier = it.value,
				completed = streak.daysInStreak >= it.key,
			)
		}.sortedBy { it.daysInStreak }

		return GetUserStreakQuery.Result(
			userId = query.userId,
			daysInStreak = streak.daysInStreak,
			currentMultiplier = multiplier,
			daysToNextStreak = nextMultiplier?.let { it.daysNeeded - streak.daysInStreak },
			nextMultiplier = nextMultiplier?.multiplier,
			thresholds = threshold,
			streakDates = streak.streakDaysInWeek.map {
				GetUserStreakQuery.StreakDayResult(
					date = it.date,
					state = it.state,
				)
			},
			streakExpiresAt = LocalDate.now().atStartOfNextDay(),
			isThresholdDay = streak.daysInStreak in userStreakMultiplierProperties.thresholds.keys,
		)
	}
}
