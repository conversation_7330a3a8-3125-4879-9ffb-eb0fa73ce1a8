package com.cleevio.fatbot.application.module.limitorder

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.module.limitorder.port.out.GetAllUserLimitOrders
import com.cleevio.fatbot.application.module.limitorder.query.GetAllUserLimitOrdersQuery
import org.springframework.stereotype.Component

@Component
class GetAllUserLimitOrdersQueryHandler(
	private val getAllUserLimitOrders: GetAllUserLimitOrders,
) : QueryHandler<List<GetAllUserLimitOrdersQuery.Result>, GetAllUserLimitOrdersQuery> {

	override val query = GetAllUserLimitOrdersQuery::class

	override fun handle(query: GetAllUserLimitOrdersQuery): List<GetAllUserLimitOrdersQuery.Result> {
		return getAllUserLimitOrders(userId = query.userId)
	}
}
