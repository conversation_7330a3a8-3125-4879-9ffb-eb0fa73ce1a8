package com.cleevio.fatbot.application.module.gassnapshot.finder

import com.cleevio.fatbot.application.common.service.BaseFinderService
import com.cleevio.fatbot.domain.gassnapshot.GasInfoSnapshot
import com.cleevio.fatbot.domain.gassnapshot.GasInfoSnapshotRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Clock
import java.time.Duration
import java.time.Instant

@Service
class GasInfoSnapshotFinderService(
	private val gasInfoSnapshotRepository: GasInfoSnapshotRepository,
	private val clock: Clock,
) : BaseFinderService<GasInfoSnapshot>(gasInfoSnapshotRepository) {

	override fun errorBlock(message: String) = error(message)

	override fun getEntityType() = GasInfoSnapshot::class

	@Transactional(readOnly = true)
	fun findRecentByChainId(chainId: Long): GasInfoSnapshot? {
		return gasInfoSnapshotRepository.findByChainIdAndUpdatedAtAfter(
			chainId = chainId,
			after = Instant.now(clock).minusSeconds(GAS_INFO_AGE_THRESHOLD.seconds),
		)
	}
}

private val GAS_INFO_AGE_THRESHOLD = Duration.ofSeconds(18)
