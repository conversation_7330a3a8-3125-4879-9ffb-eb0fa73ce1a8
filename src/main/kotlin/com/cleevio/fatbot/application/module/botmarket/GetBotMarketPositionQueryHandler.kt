package com.cleevio.fatbot.application.module.botmarket

import com.cleevio.fatbot.application.common.query.QueryHandler
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.botmarket.calculator.BotMarketPositionCalculator
import com.cleevio.fatbot.application.module.botmarket.finder.BotMarketPositionFinderService
import com.cleevio.fatbot.application.module.botmarket.finder.BotMarketPositionTradeStateFinderService
import com.cleevio.fatbot.application.module.botmarket.port.out.FindBotMarketPositionLatestTradeTransactions
import com.cleevio.fatbot.application.module.botmarket.query.GetBotMarketPositionQuery
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.file.finder.FileFinderService
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.token.finder.BotTokenInfoFinderService
import com.cleevio.fatbot.application.module.transaction.port.out.GetTxDetailUri
import com.cleevio.fatbot.domain.botmarket.BotMarketPosition
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeState
import com.cleevio.fatbot.domain.botmarket.BotMarketPositionTradeStateType
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.MathContext
import java.net.URI

@Component
class GetBotMarketPositionQueryHandler(
	private val botMarketPositionFinderService: BotMarketPositionFinderService,
	private val botMarketPositionTradeStateFinderService: BotMarketPositionTradeStateFinderService,
	private val findBotMarketPositionLatestTradeTransactions: FindBotMarketPositionLatestTradeTransactions,
	private val botTokenInfoFinderService: BotTokenInfoFinderService,
	private val fileFinderService: FileFinderService,
	private val getUsdExchangeRate: GetUsdExchangeRate,
	private val fileUrlMapper: FileUrlMapper,
	private val getTokenPrices: GetTokenPrices,
	private val getTxDetailUri: GetTxDetailUri,
) : QueryHandler<GetBotMarketPositionQuery.Result, GetBotMarketPositionQuery> {

	override val query = GetBotMarketPositionQuery::class

	override fun handle(query: GetBotMarketPositionQuery): GetBotMarketPositionQuery.Result {
		val marketPosition = botMarketPositionFinderService.getByIdAndBotId(
			id = query.botMarketPositionId,
			botId = query.botId,
		)

		val buyTrade = botMarketPositionTradeStateFinderService.getByPositionIdAndType(
			botMarketPositionId = marketPosition.id,
			type = BotMarketPositionTradeStateType.BUY,
		)

		val sellTrade = marketPosition.positionClosedAt?.let {
			botMarketPositionTradeStateFinderService.getByPositionIdAndType(
				botMarketPositionId = marketPosition.id,
				type = BotMarketPositionTradeStateType.SELL,
			)
		}

		val tradeTransactions = findBotMarketPositionLatestTradeTransactions(
			tokenAddress = marketPosition.tokenAddress,
			botWalletId = marketPosition.botWalletId,
			txHashToURIMapper = { txHash, chain -> getTxDetailUri(txHash, chain) },
		)

		val buyTransaction = tradeTransactions.first
		val sellTransaction = tradeTransactions.second

		val tokenInfo = botTokenInfoFinderService.getByTokenAddressAndChain(
			tokenAddress = marketPosition.tokenAddress,
			chain = marketPosition.chain,
		)
		val tokenImageFile = tokenInfo.imageFileId?.let { fileFinderService.getById(id = it) }

		marketPosition.positionClosedAt?.let {
			require(sellTrade != null && sellTransaction != null) {
				"Closed bot market position must have existing successful sell trade state and transaction."
			}
		}

		val marketPositionCalculatorInput = marketPosition.toCalculatorInput(
			botId = query.botId,
			tokenName = tokenInfo.name,
			tokenSymbol = tokenInfo.symbol,
			tokenImageUrl = fileUrlMapper.map(tokenImageFile?.id, tokenImageFile?.extension),
			tokenDecimals = tokenInfo.decimals.toInt(),
		)

		val tokenChainAddress = marketPosition.tokenAddress.toChainAddress(marketPosition.chain)
		val tokenAddressToTokenPrice = marketPosition.positionClosedAt?.let {
			mapOf()
		} ?: getTokenPrices(tokens = setOf(tokenChainAddress))
		val currentExchangeRate = getUsdExchangeRate(marketPosition.chain.currency)

		val marketPositionUsdValue = BotMarketPositionCalculator.toBotMarketPositionResult(
			botMarketPosition = marketPositionCalculatorInput,
			tokenAddressToTokenPrice = tokenAddressToTokenPrice,
			currentExchangeRate = currentExchangeRate,
		)

		val buyTradeTransaction = mapTradeTransaction(
			marketPosition = marketPosition,
			amountUsd = marketPositionUsdValue.acquisitionValueUsd,
			trade = buyTrade,
			transaction = buyTransaction,
		)

		val sellTradeTransaction = marketPosition.positionClosedAt?.let {
			mapTradeTransaction(
				marketPosition = marketPosition,
				amountUsd = marketPositionUsdValue.currentValueUsd,
				trade = sellTrade!!,
				transaction = sellTransaction!!,
			)
		}

		return GetBotMarketPositionQuery.Result(
			id = marketPosition.id,
			botId = query.botId,
			tokenAddress = marketPosition.tokenAddress,
			tokenDetailUrl = marketPosition.tokenAddress.getLinkToExplorer(marketPosition.chain).let(URI::create),
			tokenName = tokenInfo.name,
			tokenSymbol = tokenInfo.symbol,
			tokenChain = tokenInfo.chain,
			tokenImageUrl = fileUrlMapper.map(tokenImageFile?.id, tokenImageFile?.extension),
			openValueUsd = marketPositionUsdValue.acquisitionValueUsd,
			closeValueUsd = marketPosition.positionClosedAt?.let { marketPositionUsdValue.currentValueUsd },
			openTimeStampAt = marketPosition.createdAt,
			closedTimeStampAt = marketPosition.positionClosedAt,
			pnlAmountUsd = marketPositionUsdValue.currentPnlUsd,
			pnlAmountFraction = marketPositionUsdValue.currentPnlFraction,
			currentValueUsd = marketPositionUsdValue.currentValueUsd,
			buyTradeTransaction = buyTradeTransaction,
			sellTradeTransaction = sellTradeTransaction,
			sellReason = marketPosition.sellReason,
		)
	}

	private fun mapTradeTransaction(
		marketPosition: BotMarketPosition,
		amountUsd: BigDecimal,
		trade: BotMarketPositionTradeState,
		transaction: FindBotMarketPositionLatestTradeTransactions.Result,
	) = GetBotMarketPositionQuery.BotTradeTransaction(
		txId = transaction.txId,
		txHash = transaction.txHash,
		txDetailUrl = getTxDetailUri(transaction.txHash, marketPosition.chain),
		txType = transaction.txType,
		txStatus = transaction.txStatus,
		txCreatedAt = transaction.txCreatedAt,
		txChain = marketPosition.chain,
		botWalletId = marketPosition.botWalletId,
		amountUsd = amountUsd,
		marketCapUsd = trade.marketCapUsd,
		liquidityUsd = trade.liquidityUsd,
		volumeUsd = trade.volumeUsd,
		numOfHolders = trade.numOfAccountHolders,
		buyVolume = trade.buyVolume,
		sellVolume = trade.sellVolume,
		fractionOfSellTransactions = trade.fractionOfSellTransactions.toPercentageOfSellTransactions(),
	)

	/*
	Derived from the fact that:

	sellTxFraction / buyTxFraction = fractionOfSellTransactions
	sellTxFraction / (1 - sellTxFraction) = fractionOfSellTransactions

	let sellTxFraction be substituted for x

	x / (1 - x) = fractionOfSellTransactions
	x = fractionOfSellTransactions * (1 - x)
	x = (1 * fractionOfSellTransactions) - (x * fractionOfSellTransactions)
	x = (fractionOfSellTransactions) - (x * fractionOfSellTransactions)
	x + (x * fractionOfSellTransactions) = fractionOfSellTransactions
	(1 + fractionOfSellTransactions) * x = fractionOfSellTransactions
	x = (fractionOfSellTransactions) / (1 + fractionOfSellTransactions)
	 */
	private fun BigDecimal.toPercentageOfSellTransactions() = (this).divide(BigDecimal.ONE + this, MathContext.DECIMAL128)
}
