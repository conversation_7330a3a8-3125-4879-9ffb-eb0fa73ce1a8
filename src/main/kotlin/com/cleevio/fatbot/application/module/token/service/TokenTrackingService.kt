package com.cleevio.fatbot.application.module.token.service

import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.finder.MarketPositionFinderService
import com.cleevio.fatbot.application.module.market.port.out.GetTokenBalancesEVM
import com.cleevio.fatbot.application.module.market.port.out.GetTokenBalancesSolana
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.cleevio.fatbot.application.module.transaction.finder.TransactionFinderService
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.finder.WalletFinderService
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Service
import java.math.BigInteger
import java.util.UUID

@Service
class TokenTrackingService(
	private val walletFinderService: WalletFinderService,
	private val marketPositionFinderService: MarketPositionFinderService,
	private val getTokenBalancesEVM: GetTokenBalancesEVM,
	private val getTokenBalancesSolana: GetTokenBalancesSolana,
	private val getTokenPrices: GetTokenPrices,
	private val tokenTrackingUpdateService: TokenTrackingUpdateService,
	private val transactionFinderService: TransactionFinderService,
	private val chainProperties: ChainProperties,
) {
	private val log = logger()

	@SentrySpan
	fun trackTokensForUser(userId: UUID) {
		val wallets = walletFinderService.findAllByUserIdAndChainIn(
			userId = userId,
			chains = chainProperties.enabledChains,
		)

		val allMarketPositions = marketPositionFinderService.findAllByWalletIds(
			walletIds = wallets.mapToSet { it.id },
		)

		val pendingTransactionTokenAddresses = transactionFinderService.findAllRealPending().mapToSet { it.tokenAddress }

		val walletToMarketPositions = allMarketPositions
			// Ignore all pending transactions to avoid potential race condition issues
			.filter { it.tokenAddress !in pendingTransactionTokenAddresses }
			.groupBy { it.walletId }
			// Max 3 wallets per user
			.mapKeys { (walletId) -> wallets.single { it.id == walletId } }

		val positionIdToDetectedBalances = walletToMarketPositions.flatMap { (wallet, marketPositions) ->
			if (marketPositions.isEmpty()) {
				log.warn("No market positions found for wallet ${wallet.id}.")
				return@flatMap emptyList()
			}

			val tokenBalances = when (wallet.chain.type) {
				ChainType.EVM -> getTokenBalancesEVM(
					chainId = wallet.chain.evmId,
					walletAddress = wallet.address,
					tokenAddresses = marketPositions.map { position -> position.tokenAddress },
				)
				ChainType.SOLANA -> getTokenBalancesSolana.ofAll(
					ownerAddress = wallet.address,
					tokenAddresses = marketPositions.map { position -> position.tokenAddress },
				)
			}

			marketPositions.mapNotNull { position ->
				val positionChainBalance = tokenBalances[position.tokenAddress] ?: BigInteger.ZERO
				val positionBalance = position.getTokenBalance()

				if (positionBalance == positionChainBalance) return@mapNotNull null

				position.id to Pair(positionBalance, positionChainBalance)
			}
		}.toMap()

		val tokens = walletToMarketPositions.values
			.flatMap { positions -> positions.filter { it.id in positionIdToDetectedBalances } }
			.mapToSet { it.tokenAddress.toChainAddress(it.chain) }

		val tokenPrices = getTokenPrices(tokens = tokens)

		tokenTrackingUpdateService.update(userId, positionIdToDetectedBalances, tokenPrices)
	}
}
