package com.cleevio.fatbot.application.module.market.port.out

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import java.util.UUID

interface ValidateFundsForSvmTransaction {
	/**
	 * Validates whether it's possible for given wallet to send amount of SOL.
	 */
	operator fun invoke(
		walletId: UUID,
		walletAddress: AddressWrapper,
		transactionAmount: BaseAmount,
		estimatedFees: BaseAmount,
	)
}
