package com.cleevio.fatbot.adapter

import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.network.okHttpClient
import com.cleevio.fatbot.infrastructure.config.logger
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Response
import org.springframework.http.HttpHeaders
import java.time.Duration

// TODO: Remove after issues with Bitquery (very slow response time) are resolved
private val TIMEOUT = Duration.ofMinutes(1)

abstract class ApolloConnector(
	graphQlEndpoint: String,
	accessToken: String,
) {
	protected val logger = logger()

	protected val apolloClient = ApolloClient.Builder()
		.serverUrl(graphQlEndpoint)
		.okHttpClient(
			OkHttpClient.Builder()
				.connectTimeout(TIMEOUT)
				.readTimeout(TIMEOUT)
				.addInterceptor(AuthorizationInterceptor(accessToken))
				.build(),
		)
		.build()

	private class AuthorizationInterceptor(val accessToken: String) : Interceptor {
		override fun intercept(chain: Interceptor.Chain): Response {
			val request = chain.request().newBuilder()
				.apply {
					addHeader(
						HttpHeaders.AUTHORIZATION,
						"Bearer $accessToken",
					)
				}
				.build()
			return chain.proceed(request)
		}
	}
}
