package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.module.analytics.port.out.AggregateDailyFees
import com.cleevio.fatbot.application.module.analytics.port.out.DailyFeeType
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.tables.references.REFERRAL_REWARD
import com.cleevio.fatbot.tables.references.TRANSACTION
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneOffset

@Component
class AggregateDailyFeesJooq(private val dslContext: DSLContext) : AggregateDailyFees {

	private val revenueDate = DSL.cast(TRANSACTION.CREATED_AT, LocalDate::class.java)
	private val revenueFeeSum = DSL.coalesce(DSL.sum(TRANSACTION.FATBOT_FEE), BigDecimal.ZERO)

	private val referralDate = DSL.cast(REFERRAL_REWARD.CREATED_AT, LocalDate::class.java)
	private val referralFeeSum = DSL.sum(REFERRAL_REWARD.BASE_AMOUNT)

	override fun invoke(from: LocalDate?, to: LocalDate?): List<AggregateDailyFees.Result> {
		val revenueFeeConditions = listOfNotNull(
			TRANSACTION.STATUS.eq(TransactionStatus.SUCCESS),
			from?.let { TRANSACTION.CREATED_AT.greaterOrEqual(from.atStartOfDay().toInstant(ZoneOffset.UTC)) },
			to?.let { TRANSACTION.CREATED_AT.lessThan(to.atStartOfDay().toInstant(ZoneOffset.UTC)) },
		)

		val revenueDailyFees = dslContext.select(revenueDate, TRANSACTION.CHAIN, revenueFeeSum)
			.from(TRANSACTION)
			.where(revenueFeeConditions)
			.groupBy(revenueDate, TRANSACTION.CHAIN)
			.fetch()
			.map {
				AggregateDailyFees.Result(
					chain = it[TRANSACTION.CHAIN]!!,
					date = it[revenueDate],
					type = DailyFeeType.REVENUE,
					baseAmount = it[revenueFeeSum].toBigInteger().asBaseAmount(),
				)
			}

		val referralFeeConditions = listOfNotNull(
			from?.let { REFERRAL_REWARD.CREATED_AT.greaterOrEqual(from.atStartOfDay().toInstant(ZoneOffset.UTC)) },
			to?.let { REFERRAL_REWARD.CREATED_AT.lessThan(to.atStartOfDay().toInstant(ZoneOffset.UTC)) },
		)

		val referralDailyFees = dslContext.select(referralDate, REFERRAL_REWARD.CHAIN, referralFeeSum)
			.from(REFERRAL_REWARD)
			.where(referralFeeConditions)
			.groupBy(referralDate, REFERRAL_REWARD.CHAIN)
			.fetch()
			.map {
				AggregateDailyFees.Result(
					chain = it[REFERRAL_REWARD.CHAIN]!!,
					date = it[referralDate],
					type = DailyFeeType.REFERRAL,
					baseAmount = it[referralFeeSum].toBigInteger().asBaseAmount(),
				)
			}

		return revenueDailyFees + referralDailyFees
	}
}
