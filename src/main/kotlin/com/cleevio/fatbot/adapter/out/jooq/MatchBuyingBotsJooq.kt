package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.adapter.out.solana.pumpfun.model.PumpFunCurveState
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.matchmaking.command.TokenStateChangesBuyingCommand
import com.cleevio.fatbot.application.module.matchmaking.port.out.MatchBuyingBots
import com.cleevio.fatbot.tables.references.BOT
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION
import com.cleevio.fatbot.tables.references.BOT_WALLET
import org.jooq.CommonTableExpression
import org.jooq.Condition
import org.jooq.DSLContext
import org.jooq.Field
import org.jooq.Record
import org.jooq.Record7
import org.jooq.SelectUnionStep
import org.jooq.impl.DSL
import org.jooq.types.YearToSecond
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.BigInteger
import java.math.MathContext
import java.math.RoundingMode
import java.util.UUID
import kotlin.time.toJavaDuration

private typealias MatchedBuyingBotRecord = Record7<UUID?, UUID?, String, BigInteger, BigInteger, String, BigDecimal>

/**
 * Query to match buying tokens.
 *
 * One common query for all token changes.
 *
 * This is achieved by providing the token address as a constant in the select of the query.
 *
 * For each token state change we're making a new query, we're building the queries together by utilizing 'unionAll'
 * for each new token state. Each token address will the get result of the matched:
 *
 * 		1. Bot id
 * 		2. Bot trade amount in SOL
 * 		3. Bot wallet private key
 * 		4. And the token address with which the query result is matched with
 *
 * Number of matched bots is limited to 3 bots per token state change. This is done to avoid a vicious cycle of
 * trading bots buying and selling and keeping reacting to each other indefinitely.
 *
 * Only active bots can be matched.
 *
 * In the future, it might be necessary to parallelize this query for performance reasons, especially if we'll need to match
 * to higher number of bots per token state change at once or if we'll be expecting to be receiving large number of
 * token state changes at once per request.
 */
@Component
class MatchBuyingBotsJooq(
	private val dslContext: DSLContext,
) : MatchBuyingBots {

	override fun invoke(states: List<TokenStateChangesBuyingCommand.NewState>): List<MatchBuyingBots.Result> {
		val activeBotConditions = listOf(
			BOT.IS_ACTIVE.isTrue,
			BOT.REMAINING_BUY_FREQUENCY.gt(0),
			BOT_WALLET.BALANCE.gt(BOT.TRADE_AMOUNT),
		)

		val cteNeededFields = listOf(
			BOT.ID,
			BOT.FOLLOWER_BOT_ID,
			BOT_WALLET.ID.`as`("bot_wallet_id"),
			BOT.MARKET_CAP_FROM_USD,
			BOT.MARKET_CAP_TO_USD,
			BOT.NUMBER_OF_HOLDERS_FROM,
			BOT.NUMBER_OF_HOLDERS_TO,
			BOT.LIQUIDITY_FROM_USD,
			BOT.LIQUIDITY_TO_USD,
			BOT.DAILY_VOLUME_FROM_USD,
			BOT.DAILY_VOLUME_TO_USD,
			BOT.SELL_VOLUME,
			BOT.SELL_TO_BUY_TRANSACTION_RATIO,
			BOT.BUY_TOKENS_ALIVE_AT_LEAST_FOR,
			BOT.CREATOR_GRADUATION_SUCCESS_RATE_FRACTION,
			BOT.TOKEN_TICKER_COPY_IS_CHECKED,
			BOT.CREATOR_HIGH_BUY_IS_CHECKED,
			BOT.BUNDLED_BUYS_DETECTED_IS_CHECKED,
			BOT.SUSPICIOUS_WALLETS_DETECTED_IS_CHECKED,
			BOT.SINGLE_HIGH_BUY_IS_CHECKED,
		)

		val activeBotsCte = DSL
			.name("active_bots")
			.`as`(
				DSL.select(cteNeededFields)
					.from(BOT)
					.join(BOT_WALLET).on(BOT_WALLET.BOT_ID.eq(BOT.ID))
					.where(activeBotConditions),
			)

		val selectQuery = states
			.mapIndexed { index, it ->
				val includeCte = index == 0
				buildSelectQuery(it, activeBotsCte, includeCte)
			}
			.reduce { accumulator, query -> accumulator.unionAll(query) }

		return dslContext
			.fetch(selectQuery)
			.map {
				MatchBuyingBots.Result(
					botId = it[BOT.ID]!!,
					followerId = it[BOT.FOLLOWER_BOT_ID],
					tokenAddress = AddressWrapper(it["token_address"]!! as String),
					curveState = PumpFunCurveState.fromVirtual(
						virtualSolReserves = BaseAmount(it["solana_reserves"]!! as BigInteger),
						virtualTokenReserves = BaseAmount(it["token_reserves"]!! as BigInteger),
						creator = AddressWrapper(it["creator"]!! as String),
					),
					tokenPrice = it["token_price"]!! as BigDecimal,
				)
			}
	}

	private fun buildSelectQuery(
		tokenStateChange: TokenStateChangesBuyingCommand.NewState,
		activeBotsCte: CommonTableExpression<Record>,
		includeCte: Boolean,
	): SelectUnionStep<MatchedBuyingBotRecord> {
		val marketCapRangeCondition = makeRangeCondition(
			fromField = activeBotsCte.field(BOT.MARKET_CAP_FROM_USD)!!,
			toField = activeBotsCte.field(BOT.MARKET_CAP_TO_USD)!!,
			value = tokenStateChange.marketCapUsd,
		)

		val numOfAccountHoldersCondition = makeRangeCondition(
			fromField = activeBotsCte.field(BOT.NUMBER_OF_HOLDERS_FROM)!!,
			toField = activeBotsCte.field(BOT.NUMBER_OF_HOLDERS_TO)!!,
			value = tokenStateChange.numOfAccountHolders,
		)

		val liquidityRangeCondition = makeRangeCondition(
			fromField = activeBotsCte.field(BOT.LIQUIDITY_FROM_USD)!!,
			toField = activeBotsCte.field(BOT.LIQUIDITY_TO_USD)!!,
			value = tokenStateChange.liquidityUsd,
		)

		val volumeRangeCondition = makeRangeCondition(
			fromField = activeBotsCte.field(BOT.DAILY_VOLUME_FROM_USD)!!,
			toField = activeBotsCte.field(BOT.DAILY_VOLUME_TO_USD)!!,
			value = tokenStateChange.volumeUsd,
		)

		val tokenBotMarketPositionDoesNotExistCondition = DSL.notExists(
			dslContext.selectOne()
				.from(BOT_MARKET_POSITION)
				.where(BOT_MARKET_POSITION.BOT_WALLET_ID.eq(activeBotsCte.field(BOT_WALLET.ID)!!.`as`("bot_wallet_id")))
				.and(BOT_MARKET_POSITION.TOKEN_ADDRESS.eq(tokenStateChange.tokenAddress)),
		)

		val buyToSellVolumeRatio = when (tokenStateChange.buyVolume) {
			BigDecimal.ZERO -> BigDecimal.ZERO
			else -> tokenStateChange.sellVolume.divide(tokenStateChange.buyVolume, MathContext.DECIMAL32)
				.setScale(2, RoundingMode.CEILING)
		}

		val buyToSellVolumeRatioCondition =
			activeBotsCte.field(BOT.SELL_VOLUME)!!.isNull
				.or(DSL.`val`(buyToSellVolumeRatio).le(activeBotsCte.field(BOT.SELL_VOLUME)!!))

		val sellTransactionThresholdCondition = activeBotsCte.field(BOT.SELL_TO_BUY_TRANSACTION_RATIO)!!.isNull.or(
			activeBotsCte.field(BOT.SELL_TO_BUY_TRANSACTION_RATIO)!!.le(tokenStateChange.fractionOfSellTransactions),
		)

		val tokenAliveForAtLeastCondition = (activeBotsCte.field(BOT.BUY_TOKENS_ALIVE_AT_LEAST_FOR)!!.isNull).or(
			activeBotsCte.field(BOT.BUY_TOKENS_ALIVE_AT_LEAST_FOR)!!
				.le(YearToSecond.valueOf(tokenStateChange.tokenAliveFor.toJavaDuration())),
		)

		// TODO: This is only experimental condition
		val creatorSuccessRateCondition =
			(activeBotsCte.field(BOT.CREATOR_GRADUATION_SUCCESS_RATE_FRACTION)!!.isNull).or(
				activeBotsCte.field(BOT.CREATOR_GRADUATION_SUCCESS_RATE_FRACTION)!!
					.le(tokenStateChange.creatorGraduationSuccessRateFraction),
			)

		// Only pass buy trades if following conditions are not checked in case any of the checked conditions are detected by token aggregator.
		val tokenTickerCopyCondition = activeBotsCte.field(BOT.TOKEN_TICKER_COPY_IS_CHECKED)!!.isFalse.takeIf {
			tokenStateChange.isRedFlagTokenTickerCopy
		}
		val creatorHighBuyCondition = activeBotsCte.field(BOT.CREATOR_HIGH_BUY_IS_CHECKED)!!.isFalse.takeIf {
			tokenStateChange.isRedFlagCreatorHighBuy
		}
		val bundledBuysDetectedCondition = activeBotsCte.field(BOT.BUNDLED_BUYS_DETECTED_IS_CHECKED)!!.isFalse.takeIf {
			tokenStateChange.isRedFlagBundledBuysDetected
		}
		val suspiciousWalletsDetected =
			activeBotsCte.field(BOT.SUSPICIOUS_WALLETS_DETECTED_IS_CHECKED)!!.isFalse.takeIf {
				tokenStateChange.isRedFlagSuspiciousWalletsDetected
			}
		val singleHighBuyCondition = activeBotsCte.field(BOT.SINGLE_HIGH_BUY_IS_CHECKED)!!.isFalse.takeIf {
			tokenStateChange.isRedFlagSingleHighBuy
		}

		val botBuyMatchingConditions = listOfNotNull(
			marketCapRangeCondition,
			liquidityRangeCondition,
			volumeRangeCondition,
			numOfAccountHoldersCondition,
			tokenBotMarketPositionDoesNotExistCondition,
			buyToSellVolumeRatioCondition,
			tokenAliveForAtLeastCondition,
			sellTransactionThresholdCondition,
			creatorSuccessRateCondition,
			tokenTickerCopyCondition,
			creatorHighBuyCondition,
			bundledBuysDetectedCondition,
			suspiciousWalletsDetected,
			singleHighBuyCondition,
		)

		val cte = if (includeCte) activeBotsCte else null

		return DSL
			.with(cte)
			.select(
				activeBotsCte.field(BOT.ID),
				activeBotsCte.field(BOT.FOLLOWER_BOT_ID),
				DSL.`val`(tokenStateChange.tokenAddress.getAddressString()).`as`("token_address"),
				DSL.`val`(tokenStateChange.curveState.virtualSolReserves.amount).`as`("solana_reserves"),
				DSL.`val`(tokenStateChange.curveState.virtualTokenReserves.amount).`as`("token_reserves"),
				DSL.`val`(tokenStateChange.curveState.creator!!.getAddressString()).`as`("creator"),
				DSL.`val`(tokenStateChange.tokenPrice).`as`("token_price"),
			)
			.from(activeBotsCte)
			.where(botBuyMatchingConditions)
	}

	private fun <T : Any?> makeRangeCondition(fromField: Field<T>, toField: Field<T>, value: T): Condition {
		return fromField.isNull.or(fromField.le(value)).and(toField.isNull.or(toField.ge(value)))
	}
}
