package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.user.exception.UserNotFoundException
import com.cleevio.fatbot.application.module.userleaderbord.port.out.GetUserLeaderboardInfo
import com.cleevio.fatbot.application.module.userleaderbord.query.GetUserLeaderboardInfoQuery
import com.cleevio.fatbot.tables.references.USER_LEADERBOARD
import com.cleevio.fatbot.tables.references.USER_STATISTICS
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.util.UUID

@Component
class GetUserLeaderboardInfoJooq(
	private val dslContext: DSLContext,
) : GetUserLeaderboardInfo {

	override operator fun invoke(userId: UUID, multiplierThresholds: Set<Int>): GetUserLeaderboardInfoQuery.Result {
		return dslContext
			.select(
				USER_STATISTICS.USER_ID,
				USER_STATISTICS.LEADERBOARD_DONUTS,
				USER_LEADERBOARD.DONUT_MULTIPLIER,
				USER_LEADERBOARD.RANK,
			)
			.from(USER_STATISTICS)
			.leftJoin(USER_LEADERBOARD).on(USER_LEADERBOARD.USER_ID.eq(USER_STATISTICS.USER_ID))
			.where(USER_STATISTICS.USER_ID.eq(userId))
			.fetchOne()
			?.let {
				val donutsGained = it[USER_STATISTICS.LEADERBOARD_DONUTS]!!
				val rank = it[USER_LEADERBOARD.RANK] ?: 1001
				val nextRankThreshold = multiplierThresholds.filter { it < rank }.maxOrNull()

				GetUserLeaderboardInfoQuery.Result(
					userId = it[USER_STATISTICS.USER_ID]!!,
					multiplier = it[USER_LEADERBOARD.DONUT_MULTIPLIER] ?: BigDecimal.ONE,
					rank = it[USER_LEADERBOARD.RANK],
					donutsGained = donutsGained,
					donutsNeededForNextThreshold = nextRankThreshold?.let {
						fetchUsersDonutsByRank(
							rank = nextRankThreshold,
							donutsGained = donutsGained,
						)
					} ?: BigDecimal.ZERO,
				)
			} ?: throw UserNotFoundException("User not found.")
	}

	private fun fetchUsersDonutsByRank(rank: Int, donutsGained: BigDecimal): BigDecimal {
		if (rank <= 1) {
			return BigDecimal.ZERO
		}

		return dslContext
			.select(USER_STATISTICS.LEADERBOARD_DONUTS)
			.from(USER_LEADERBOARD)
			.innerJoin(USER_STATISTICS).on(USER_LEADERBOARD.USER_ID.eq(USER_STATISTICS.USER_ID))
			.where(USER_LEADERBOARD.RANK.eq(rank))
			.fetchOne()
			?.let { it[USER_STATISTICS.LEADERBOARD_DONUTS]!! - donutsGained }
			?: BigDecimal.ZERO
	}
}
