package com.cleevio.fatbot.adapter.out.solana.pumpswap

import com.cleevio.fatbot.adapter.out.solana.pumpswap.model.PumpswapPool
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.VerifyPumpswapPairAddress
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.Memcmp
import org.p2p.solanaj.rpc.types.MemcmpFilter
import org.p2p.solanaj.rpc.types.config.DataSlice
import org.p2p.solanaj.rpc.types.config.ProgramAccountConfig
import org.springframework.stereotype.Component

@Component
class VerifyPumpswapPairAddressSolana(
	private val rpcClient: RpcClient,
) : VerifyPumpswapPairAddress {

	@SentrySpan
	override fun invoke(pairAddress: AddressWrapper, tokenAddress: AddressWrapper): Boolean {
		val accounts = rpcClient.api.getProgramAccounts(
			SolanaConstants.PUMPSWAP_PROGRAM,
			ProgramAccountConfig(
				listOf(
					MemcmpFilter(Memcmp(PumpswapPool.BASE_MINT_OFFSET, tokenAddress.getAddressString())),
					MemcmpFilter(Memcmp(PumpswapPool.QUOTE_MINT_OFFSET, SolanaConstants.WSOL_MINT.toBase58())),
				),
				DataSlice(0, 0),
			),
		)

		val pool = pairAddress.getSolanaPublicKey()

		return accounts.any { it.publicKey == pool }
	}
}
