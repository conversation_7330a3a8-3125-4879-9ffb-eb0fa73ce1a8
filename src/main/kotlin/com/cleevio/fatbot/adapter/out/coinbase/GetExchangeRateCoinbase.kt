package com.cleevio.fatbot.adapter.out.coinbase

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.common.util.EpochClockTicker
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.github.benmanes.caffeine.cache.Caffeine
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Clock
import java.util.concurrent.CompletableFuture
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

@Component
class GetExchangeRateCoinbase(
	private val coinbaseConnector: CoinbaseConnector,
	clock: Clock,
) : GetUsdExchangeRate {

	private val cache = Caffeine
		.newBuilder()
		.maximumSize(10)
		.refreshAfterWrite(45.seconds.toJavaDuration())
		.expireAfterWrite(60.seconds.toJavaDuration())
		.ticker(EpochClockTicker(clock))
		.buildAsync<CryptoCurrency, BigDecimal> { key, executor ->
			CompletableFuture.supplyAsync({ coinbaseConnector.getUsdExchangeRate(currency = key) }, executor)
		}

	override fun invoke(cryptoCurrency: CryptoCurrency): BigDecimal {
		return cache.get(cryptoCurrency).join()
	}

	override fun getAll(cryptoCurrencies: Set<CryptoCurrency>): Map<CryptoCurrency, BigDecimal> {
		return cache.getAll(cryptoCurrencies).join()
	}
}
