package com.cleevio.fatbot.adapter.out.bitquery.cache

import com.cleevio.fatbot.application.common.util.calculateExpiresAtInNanos
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.constant.TimeInterval
import com.cleevio.fatbot.application.module.token.query.TokenPriceTimeIntervalItem
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.github.benmanes.caffeine.cache.Expiry
import java.time.Instant

/**
 * How this cache works: Data class GetTokenPriceChartCacheKey: {"tokenAddress": "0x123", "timeRange": "DAY"} is used as
 * the cache key. For each key, custom expiration is set based on time remaining until the end of given interval. If
 * request with such query was cached today at 20:30, it will expire in 3h 30min. If there's attempt to fetch expired
 * value from cache, new value is reloaded and stored under given key.
 **/

data class GetIntervalTokenPricesCacheKey(
	val chain: Chain,
	val pairAddress: AddressWrapper,
	val intervalCount: Int,
	val timeInterval: TimeInterval,
)

data class GetIntervalTokenPricesCacheValue(
	val intervalItems: List<TokenPriceTimeIntervalItem>,
	val extraIntervalItems: List<TokenPriceTimeIntervalItem>,
	val intervalStart: Instant,
	val intervalEnd: Instant,
)

object GetIntervalTokenPricesExpiry : Expiry<GetIntervalTokenPricesCacheKey, GetIntervalTokenPricesCacheValue> {

	override fun expireAfterCreate(
		key: GetIntervalTokenPricesCacheKey,
		value: GetIntervalTokenPricesCacheValue,
		currentTime: Long,
	): Long {
		return calculateExpiresAtInNanos(
			nowInNanos = currentTime,
			timeInterval = key.timeInterval,
			intervalCount = key.intervalCount,
		)
	}

	override fun expireAfterUpdate(
		key: GetIntervalTokenPricesCacheKey?,
		value: GetIntervalTokenPricesCacheValue?,
		currentTime: Long,
		currentDuration: Long,
	): Long {
		return currentDuration
	}

	override fun expireAfterRead(
		key: GetIntervalTokenPricesCacheKey?,
		value: GetIntervalTokenPricesCacheValue?,
		currentTime: Long,
		currentDuration: Long,
	): Long {
		return currentDuration
	}
}
