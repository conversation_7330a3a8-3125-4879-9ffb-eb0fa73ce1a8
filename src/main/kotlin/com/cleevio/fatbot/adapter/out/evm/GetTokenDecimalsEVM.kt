package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.market.port.out.GetTokenDecimals
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class GetTokenDecimalsEVM(
	private val evmChainContextFactory: EvmChainContextFactory,
	private val evmTokenInfoFinderService: EvmTokenInfoFinderService,
) : GetTokenDecimals {

	override fun supports() = ChainType.EVM

	@SentrySpan
	override fun invoke(token: ChainAddress): BigInteger {
		val dbData = evmTokenInfoFinderService.findByTokenAddressAndChain(
			tokenAddress = token.address,
			chain = token.chain,
		)

		check(dbData != null || token.chain.id != null) { "getTokenDecimals not implemented for Solana!" }

		return dbData?.decimals
			?: evmChainContextFactory.ofChainId(chainId = token.chain.id!!) { client.getTokenDecimals(token.address) }
	}
}
