package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator
import com.cleevio.fatbot.application.module.market.port.out.FindAllUserMarketPosition
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.EVM_TOKEN_INFO
import com.cleevio.fatbot.tables.references.MARKET_POSITION
import com.cleevio.fatbot.tables.references.WALLET
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.jooq.impl.DSL.or
import org.jooq.impl.DSL.select
import org.jooq.kotlin.get
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.net.URI
import java.util.UUID

@Component
class FindAllUserMarketPositionJooq(private val dslContext: DSLContext) : FindAllUserMarketPosition {

	private val sumTotalTokenAmountBought = DSL.sum(MARKET_POSITION.TOTAL_TOKEN_AMOUNT_BOUGHT)
		.`as`("ttab")
	private val sumTotalTokenAcquisitionCostInWei = DSL.sum(MARKET_POSITION.TOTAL_TOKEN_ACQUISITION_COST_BASE_AMOUNT)
		.`as`("ttacib")
	private val sumTotalTokenAcquisitionCostUsd = DSL.sum(MARKET_POSITION.TOTAL_TOKEN_ACQUISITION_COST_USD)
		.`as`("ttacu")
	private val sumTotalTokenAmountSold = DSL.sum(MARKET_POSITION.TOTAL_TOKEN_AMOUNT_SOLD)
		.`as`("ttas")
	private val sumTotalTokenDispositionCostInWei = DSL.sum(MARKET_POSITION.TOTAL_TOKEN_DISPOSITION_COST_BASE_AMOUNT)
		.`as`("ttadib")

	data class Result(
		val id: UUID,
		val tokenName: String,
		val tokenSymbol: String,
		val tokenImageUrl: URI?,
		override val chain: Chain,
		override val tokenAddress: AddressWrapper,
		override val tokenDecimals: Int,
		override val tokenAmountBought: BaseAmount,
		override val tokenAcquisitionCost: BaseAmount,
		override val tokenAmountSold: BaseAmount,
		override val tokenDispositionCost: BaseAmount,
		override val tokenAcquisitionCostUsd: BigDecimal,
	) : MarketPositionCalculator.Input

	override fun invoke(
		userId: UUID,
		walletId: UUID?,
		chains: Set<Chain>,
		searchString: String?,
		fileToUrlMapper: FileToUrlMapper,
	): List<Result> {
		val allWalletIds = select(WALLET.ID)
			.from(WALLET)
			.where(WALLET.USER_ID.eq(userId))
			.and(WALLET.CHAIN.`in`(chains))

		val singleWalletId = walletId?.let { allWalletIds.and(WALLET.ID.eq(it)) }

		val groupedTokensConditions = listOf(
			MARKET_POSITION.WALLET_ID.`in`(singleWalletId ?: allWalletIds),
			MARKET_POSITION.TOKEN_ADDRESS.eqIfValidAddressOrNoCondition(potentialAddress = searchString),
		)

		val groupedTokens = select(
			MARKET_POSITION.TOKEN_ADDRESS,
			MARKET_POSITION.CHAIN,
			sumTotalTokenAmountBought,
			sumTotalTokenAcquisitionCostInWei,
			sumTotalTokenAcquisitionCostUsd,
			sumTotalTokenAmountSold,
			sumTotalTokenDispositionCostInWei,
		)
			.from(MARKET_POSITION)
			.where(groupedTokensConditions)
			.groupBy(MARKET_POSITION.TOKEN_ADDRESS, MARKET_POSITION.CHAIN)

		val conditions = listOfNotNull(
			searchString?.let {
				or(
					EVM_TOKEN_INFO.NAME.likeIgnoreCase("%$it%"),
					EVM_TOKEN_INFO.SYMBOL.likeIgnoreCase("%$it%"),
					EVM_TOKEN_INFO.ADDRESS.eqIfValidAddressOrNoCondition(potentialAddress = it),
				)
			},
		)

		return dslContext
			.select(
				EVM_TOKEN_INFO.ID,
				EVM_TOKEN_INFO.NAME,
				EVM_TOKEN_INFO.SYMBOL,
				EVM_TOKEN_INFO.DECIMALS,
				EVM_TOKEN_INFO.file.ID,
				EVM_TOKEN_INFO.file.EXTENSION,
				groupedTokens[MARKET_POSITION.CHAIN],
				groupedTokens[MARKET_POSITION.TOKEN_ADDRESS],
				groupedTokens[sumTotalTokenAmountBought],
				groupedTokens[sumTotalTokenAcquisitionCostInWei],
				groupedTokens[sumTotalTokenAcquisitionCostUsd],
				groupedTokens[sumTotalTokenAmountSold],
				groupedTokens[sumTotalTokenDispositionCostInWei],
			)
			.from(groupedTokens)
			.leftJoin(EVM_TOKEN_INFO)
			.on(groupedTokens[MARKET_POSITION.TOKEN_ADDRESS]!!.eq(EVM_TOKEN_INFO.ADDRESS))
			.and(groupedTokens[MARKET_POSITION.CHAIN]!!.eq(EVM_TOKEN_INFO.CHAIN))
			.where(conditions)
			.fetch()
			.map {
				Result(
					id = it[EVM_TOKEN_INFO.ID]!!,
					chain = it[groupedTokens[MARKET_POSITION.CHAIN]]!!,
					tokenAddress = it[groupedTokens[MARKET_POSITION.TOKEN_ADDRESS]]!!,
					tokenName = it[EVM_TOKEN_INFO.NAME]!!,
					tokenSymbol = it[EVM_TOKEN_INFO.SYMBOL]!!,
					tokenImageUrl = fileToUrlMapper(it[EVM_TOKEN_INFO.file.ID], it[EVM_TOKEN_INFO.file.EXTENSION]),
					tokenDecimals = it[EVM_TOKEN_INFO.DECIMALS]!!.toInt(),
					tokenAmountBought = it[groupedTokens[sumTotalTokenAmountBought]]!!.toBigInteger().asBaseAmount(),
					tokenAcquisitionCost = it[groupedTokens[sumTotalTokenAcquisitionCostInWei]]!!.toBigInteger().asBaseAmount(),
					tokenAmountSold = it[groupedTokens[sumTotalTokenAmountSold]]!!.toBigInteger().asBaseAmount(),
					tokenDispositionCost = it[groupedTokens[sumTotalTokenDispositionCostInWei]]!!.toBigInteger().asBaseAmount(),
					tokenAcquisitionCostUsd = it[groupedTokens[sumTotalTokenAcquisitionCostUsd]]!!,
				)
			}
	}
}
