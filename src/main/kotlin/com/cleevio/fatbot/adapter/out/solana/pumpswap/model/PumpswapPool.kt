package com.cleevio.fatbot.adapter.out.solana.pumpswap.model

import com.cleevio.fatbot.application.common.util.readAddress
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.syntifi.near.borshj.BorshBuffer

// Type comment -> [type, byteSsize]
data class PumpswapPool(
	val poolBump: Int, // u8, 8
	val index: Int, // u16, 9
	val creator: AddressWrapper, // pubkey, 11
	val baseMint: AddressWrapper, // pubkey, 43
	val quoteMint: AddressWrapper, // pubkey, 75
	val lpMint: AddressWrapper, // pubkey, 107
	val poolBaseTokenAccount: AddressWrapper, // pubkey, 139
	val poolQuoteTokenAccount: AddressWrapper, // pubkey, 171
	val lpSupply: Long, // u64, 203
	val coinCreator: AddressWrapper, // pubkey, 211
) {

	companion object {
		const val SIZE = 300L

		const val BASE_MINT_OFFSET = 43L
		const val QUOTE_MINT_OFFSET = 75L
		const val POOL_QUOTE_TOKEN_ACCOUNT_OFFSET = 171L

		fun read(bytes: ByteArray): PumpswapPool {
			val buffer = BorshBuffer.wrap(bytes)

			buffer.read(8) // Discriminator
			val poolBump = buffer.readU8().toInt()
			val index = buffer.readU16().toInt()
			val creator = buffer.readAddress()
			val baseMint = buffer.readAddress()
			val quoteMint = buffer.readAddress()
			val lpMint = buffer.readAddress()
			val poolBaseTokenAccount = buffer.readAddress()
			val poolQuoteTokenAccount = buffer.readAddress()
			val lpSupply = buffer.readU64()
			val coinCreator = buffer.readAddress()

			return PumpswapPool(
				poolBump = poolBump,
				index = index,
				creator = creator,
				baseMint = baseMint,
				quoteMint = quoteMint,
				lpMint = lpMint,
				poolBaseTokenAccount = poolBaseTokenAccount,
				poolQuoteTokenAccount = poolQuoteTokenAccount,
				lpSupply = lpSupply,
				coinCreator = coinCreator,
			)
		}
	}
}
