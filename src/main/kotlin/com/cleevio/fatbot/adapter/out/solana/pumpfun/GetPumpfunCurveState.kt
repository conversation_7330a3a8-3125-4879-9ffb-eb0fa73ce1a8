package com.cleevio.fatbot.adapter.out.solana.pumpfun

import com.cleevio.fatbot.adapter.out.solana.pumpfun.model.PumpFunCurveState
import com.cleevio.fatbot.application.common.util.decodeData
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.stereotype.Component

@Component
class GetPumpfunCurveState(
	private val rpcClient: RpcClient,
) {

	operator fun invoke(address: AddressWrapper): PumpFunCurveState {
		val curveAccountInfo = rpcClient.api.getAccountInfo(address.getSolanaPublicKey())
		val curveState = PumpFunCurveState.read(curveAccountInfo.decodedData, address)

		return curveState
	}

	fun getMany(addresses: List<AddressWrapper>): Map<AddressWrapper, PumpFunCurveState?> {
		val accounts = rpcClient.api.getMultipleAccountsOptional(addresses.map { it.getSolanaPublicKey() }, mapOf())

		return addresses.zip(accounts) { address, account ->
			val curveState = runCatching { PumpFunCurveState.read(account.get().decodeData(), address) }.getOrNull()

			address to curveState
		}.toMap()
	}
}
