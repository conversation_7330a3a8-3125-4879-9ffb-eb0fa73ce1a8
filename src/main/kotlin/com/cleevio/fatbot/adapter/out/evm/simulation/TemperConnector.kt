package com.cleevio.fatbot.adapter.out.evm.simulation

import com.cleevio.fatbot.adapter.out.BaseConnector
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import org.web3j.tx.gas.DefaultGasProvider
import java.math.BigInteger
import java.util.UUID

@Component
class TemperConnector(@Value("\${temper.base-url}") baseUrl: String) : BaseConnector(baseUrl = baseUrl) {

	data class SimulateTransactionRequest(
		val from: String,
		val to: String,
		val data: String,
		val chainId: Long,
		val value: BigInteger?,
		val gasLimit: BigInteger = DefaultGasProvider.GAS_LIMIT,
		val commitChanges: Boolean = false,
		val blockNumber: Long? = null,
		val stateOverrides: Map<AddressWrapper, StateOverride>? = null,
		val forkUrl: String? = null,
	)

	data class SimulateStatefulTransactionRequest(
		val from: String,
		val to: String,
		val data: String,
		val value: BigInteger?,
		val gasLimit: BigInteger = DefaultGasProvider.GAS_LIMIT,
		val commitChanges: Boolean = false,
		val stateOverrides: Map<AddressWrapper, StateOverride>? = null,
	)

	data class StateOverride(
		val skipRpcWithDefaults: Boolean = true,
		val balance: String? = null,
		val nonce: Long? = null,
		val code: String? = null,
		val storage: Map<BigInteger, String>? = null,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class TemperSimulationResponse(
		val success: Boolean,
		val returnData: String,
		val logs: List<Log>,
		val gasUsed: BigInteger,
	) {
		data class Log(
			val address: AddressWrapper,
			val topics: List<String>,
			val data: String,
		)
	}

	data class CreateSimulationStateRequest(
		val blockNumber: Long,
		val chainId: Long,
		val forkUrl: String? = null,
		val stateOverrides: Map<AddressWrapper, StateOverride>? = null,
	)

	data class CreateSimulationStateResponse(val statefulSimulationId: UUID)

	fun simulate(data: SimulateTransactionRequest): TemperSimulationResponse {
		return restClient.post()
			.uri("/api/v1/simulate")
			.header("Content-Type", "application/json")
			.body(data)
			.retrieveResponseWithErrorHandler<TemperSimulationResponse>()
	}

	fun simulateBundle(data: List<SimulateTransactionRequest>): List<TemperSimulationResponse> {
		return restClient.post()
			.uri("/api/v1/simulate-bundle")
			.header("Content-Type", "application/json")
			.body(data)
			.retrieveResponseWithErrorHandler<List<TemperSimulationResponse>>()
	}

	fun simulateStatefulBundle(
		stateId: UUID,
		data: List<SimulateStatefulTransactionRequest>,
	): List<TemperSimulationResponse> {
		return restClient.post()
			.uri("/api/v1/simulate-stateful/$stateId")
			.header("Content-Type", "application/json")
			.body(data)
			.retrieveResponseWithErrorHandler<List<TemperSimulationResponse>>()
	}

	fun createStatefulSimulation(request: CreateSimulationStateRequest): CreateSimulationStateResponse {
		return restClient.post()
			.uri("/api/v1/simulate-stateful")
			.header("Content-Type", "application/json")
			.body(request)
			.retrieveResponseWithErrorHandler<CreateSimulationStateResponse>()
	}

	fun deleteStatefulSimulation(stateId: UUID) {
		return restClient.delete()
			.uri("/api/v1/simulate-stateful/$stateId")
			.retrieveWithErrorHandler()
	}
}
