package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.bot.port.out.ActiveBotsDays
import com.cleevio.fatbot.tables.references.BOT
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.time.Clock

@Component
class ActiveBotsDaysJooq(
	private val dslContext: DSLContext,
	private val clock: Clock,
) : ActiveBotsDays {
	override fun invoke() {
		dslContext.update(BOT)
			.set(BOT.NUMBER_OF_ACTIVE_DAYS, BOT.NUMBER_OF_ACTIVE_DAYS.plus(1))
			.where(BOT.IS_ACTIVE.isTrue)
			.execute()
	}
}
