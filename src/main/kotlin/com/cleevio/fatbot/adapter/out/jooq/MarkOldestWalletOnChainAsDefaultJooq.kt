package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.port.out.MarkOldestWalletOnChainAsDefault
import com.cleevio.fatbot.tables.references.WALLET
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class MarkOldestWalletOnChainAsDefaultJooq(
	private val dslContext: DSLContext,
) : MarkOldestWalletOnChainAsDefault {

	override fun invoke(userId: UUID, chain: Chain) {
		val oldestWalletId = DSL.select(WALLET.ID)
			.from(WALLET)
			.where(WALLET.USER_ID.eq(userId))
			.and(WALLET.CHAIN.eq(chain))
			.orderBy(WALLET.CREATED_AT)
			.limit(1)

		dslContext
			.update(WALLET)
			.set(WALLET.IS_DEFAULT, true)
			.where(WALLET.ID.`in`(oldestWalletId))
			.execute()
	}
}
