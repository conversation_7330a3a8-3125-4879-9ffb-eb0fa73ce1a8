package com.cleevio.fatbot.adapter.out.dexscreener.response

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal

@JsonIgnoreProperties(ignoreUnknown = true)
data class GetTokenPairsInfoResponse(
	val pairs: List<Pair>?,
) {
	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Pair(
		val chainId: String,
		val dexId: String,
		val url: String,
		val pairAddress: String,
		val baseToken: Token,
		val quoteToken: Token,
		val labels: List<String>?,
		val volume: TimeIntervals,
		val priceChange: TimeIntervals,
		@JsonProperty("fdv")
		val fullyDilutedValue: BigDecimal?,
		val marketCap: BigDecimal?,
		val liquidity: Liquidity?,
		@JsonProperty("txns")
		val transactions: Transactions,
		@JsonProperty("pairCreatedAt")
		val pairCreatedAtMillis: Long,
		val info: Info?,
	) {
		data class Token(
			val address: AddressWrapper,
			val name: String,
			val symbol: String,
		)

		data class TimeIntervals(
			@JsonProperty("h24") val hours24: BigDecimal?,
			@JsonProperty("h6") val hours06: BigDecimal?,
			@JsonProperty("h1") val hours01: BigDecimal?,
			@JsonProperty("m5") val minutes05: BigDecimal?,
		)

		data class Liquidity(
			val usd: BigDecimal,
			val base: BigDecimal,
			val quote: BigDecimal,
		)

		data class Transactions(
			@JsonProperty("h24") val hours24: BuySellInfo,
			@JsonProperty("h6") val hours06: BuySellInfo,
			@JsonProperty("h1") val hours01: BuySellInfo,
			@JsonProperty("m5") val minutes05: BuySellInfo,
		) {
			data class BuySellInfo(
				val buys: Int,
				val sells: Int,
			)
		}

		data class Info(
			val imageUrl: String,
			val websites: List<Website>,
			val socials: List<Social>,
		) {
			data class Website(
				val label: String,
				val url: String,
			)

			data class Social(
				val type: String,
				val url: String,
			)
		}
	}
}
