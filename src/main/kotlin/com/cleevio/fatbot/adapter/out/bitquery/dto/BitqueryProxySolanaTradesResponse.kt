package com.cleevio.fatbot.adapter.out.bitquery.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.time.Instant

@JsonIgnoreProperties(ignoreUnknown = true)
data class BitqueryProxySolanaTradesResponse(
	val solana: Solana,
) {

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Solana(
		val dextrades: List<DexTrade>,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class DexTrade(
		val trade: Trade,
		val block: Block,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Trade(
		val buy: TradeSide,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class TradeSide(
		val currency: Currency,
		val amountInUSD: String,
		val priceInUSD: String,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Currency(
		val mintAddress: String,
	)

	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Block(
		val time: Instant,
	)
}
