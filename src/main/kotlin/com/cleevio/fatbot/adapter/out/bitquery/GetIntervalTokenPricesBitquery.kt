package com.cleevio.fatbot.adapter.out.bitquery

import com.cleevio.fatbot.adapter.out.bitquery.cache.GetIntervalTokenPricesCacheKey
import com.cleevio.fatbot.adapter.out.bitquery.cache.GetIntervalTokenPricesCacheValue
import com.cleevio.fatbot.adapter.out.bitquery.cache.GetIntervalTokenPricesExpiry
import com.cleevio.fatbot.application.common.util.EpochClockTicker
import com.cleevio.fatbot.application.common.util.calculateFractionDifference
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.token.constant.TimeInterval
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.token.port.out.GetIntervalTokenPrices
import com.cleevio.fatbot.application.module.token.query.TokenPriceTimeIntervalItem
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.github.benmanes.caffeine.cache.Caffeine
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.MathContext
import java.time.Clock
import java.time.Duration
import java.time.Instant
import kotlin.math.ceil

private val CORRUPT_THRESHOLD_FRACTION = BigDecimal("1.5") // 150%

@Component
class GetIntervalTokenPricesBitquery(
	private val bitqueryApiV2Connector: BitqueryApiV2Connector,
	private val bitqueryEapConnector: BitqueryEapConnector,
	private val clock: Clock,
) : GetIntervalTokenPrices {

	private val cache = Caffeine
		.newBuilder()
		.maximumSize(5000)
		.expireAfter(GetIntervalTokenPricesExpiry)
		.ticker(EpochClockTicker(clock))
		.build<GetIntervalTokenPricesCacheKey, GetIntervalTokenPricesCacheValue>()

	@SentrySpan
	override fun invoke(
		chain: Chain,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		dex: GetDex.Dex,
		intervalCount: Int,
		timeInterval: TimeInterval,
		limit: Int,
	): GetIntervalTokenPricesCacheValue {
		return cache.get(GetIntervalTokenPricesCacheKey(chain, pairAddress, intervalCount, timeInterval)) {
			getFreshIntervalTokenPricesFromBitquery(
				chain = chain,
				pairAddress = pairAddress,
				tokenAddress = tokenAddress,
				dex = dex,
				intervalCount = intervalCount,
				timeInterval = timeInterval,
				limit = limit,
			)
		}
	}

	@SentrySpan
	override fun invoke(
		chain: Chain,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		dex: GetDex.Dex,
		timeRange: TimeRange,
	): GetIntervalTokenPricesCacheValue = invoke(
		chain = chain,
		pairAddress = pairAddress,
		tokenAddress = tokenAddress,
		dex = dex,
		intervalCount = timeRange.intervalCount,
		timeInterval = timeRange.timeInterval,
		limit = timeRange.limit,
	)

	private fun getFreshIntervalTokenPricesFromBitquery(
		chain: Chain,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		dex: GetDex.Dex,
		intervalCount: Int,
		timeInterval: TimeInterval,
		limit: Int,
	): GetIntervalTokenPricesCacheValue {
		val now = Instant.now(clock)

		val intervalPeriod = timeInterval.unit.duration.multipliedBy(intervalCount.toLong())
		val intervalDuration = intervalPeriod.multipliedBy(limit.toLong())

		val after = now.minus(intervalDuration)

		// An extra buffer to catch a tokenPrice before the interval (to deduce the price at the start)
		// If this returns a result, we don't need to do an additional query
		val afterDurationBuffer = when (timeInterval) {
			TimeInterval.HOUR -> Duration.ofHours(3)
			TimeInterval.DAY -> Duration.ofDays(1)
			TimeInterval.MINUTE -> Duration.ofMinutes(30)
			else -> error("Unexpected timeInterval: $timeInterval")
		}
		val afterBuffered = after.minus(afterDurationBuffer)

		val tokenPriceTimeIntervals = when (chain.type) {
			ChainType.EVM -> bitqueryApiV2Connector.getEvmTokenPrice(
				chainId = chain.evmId,
				tokenAddress = tokenAddress.getAddressString(),
				pairAddress = pairAddress.getAddressString(),
				timeInterval = timeInterval,
				intervalCount = intervalCount,
				after = afterBuffered,
				before = null,
				limit = limit,
			)
			ChainType.SOLANA -> bitqueryEapConnector.getSolanaTokenPrice(
				tokenAddress = tokenAddress,
				dex = dex,
				timeInterval = timeInterval,
				intervalCount = intervalCount,
				after = afterBuffered,
				before = null,
				limit = limit,
			)
		}

		val (intervalItems, extraIntervalItems) = tokenPriceTimeIntervals
			.fixCorrupted(intervalDuration)
			.partition { it.timestamp.isAfter(after) }

		return GetIntervalTokenPricesCacheValue(
			intervalItems = intervalItems,
			extraIntervalItems = extraIntervalItems,
			intervalStart = after,
			intervalEnd = now,
		)
	}

	fun List<TokenPriceTimeIntervalItem>.fixCorrupted(intervalDuration: Duration): List<TokenPriceTimeIntervalItem> {
		if (isEmpty()) return emptyList()

		val intervalDays = intervalDuration.toDays()

		// Targets ~1 month per chunk
		val chunkSize = when {
			intervalDays <= 31 -> size.toDouble()
			intervalDays <= 366 -> size / 12.0
			else -> size / 24.0
		}.let { ceil(it).toInt() }

		val result = this
			.sortedBy { it.timestamp }
			.chunked(chunkSize)
			.flatMap { chunk -> chunk.fixCorruptedChunk() }

		return result
	}

	private fun List<TokenPriceTimeIntervalItem>.fixCorruptedChunk(): List<TokenPriceTimeIntervalItem> {
		val itemTimestampToExchangeRate =
			associate { it.timestamp to it.close.divide(it.closeNative, MathContext.DECIMAL128) }

		val medianRate = itemTimestampToExchangeRate.values.median()

		val (correctItems, corruptedItems) = this.partition { item ->
			val itemExchangeRate = itemTimestampToExchangeRate.getValue(item.timestamp)

			calculateFractionDifference(a = medianRate, b = itemExchangeRate) < CORRUPT_THRESHOLD_FRACTION
		}

		if (corruptedItems.isEmpty()) return correctItems

		val correctItemPairs = correctItems.sortedBy { item -> item.timestamp }.zipWithNext()

		val fixedItems = corruptedItems.map { item ->
			val (closestBefore, closestAfter) = correctItemPairs.first { (_, b) -> b.timestamp > item.timestamp }

			val afterTimeDiff = closestAfter.timestamp.epochSecond - item.timestamp.epochSecond
			val beforeTimeDiff = item.timestamp.epochSecond - closestBefore.timestamp.epochSecond

			val closerTimestamp = if (afterTimeDiff < beforeTimeDiff) {
				closestAfter.timestamp
			} else {
				closestBefore.timestamp
			}

			val rateToUse = itemTimestampToExchangeRate.getValue(closerTimestamp)

			item.copy(close = item.closeNative * rateToUse)
		}

		return correctItems + fixedItems
	}

	private fun Collection<BigDecimal>.median(): BigDecimal {
		if (size == 1) return first()

		val sorted = sorted()
		val mid = size / 2

		return if (size % 2 == 0) {
			sorted[mid]
		} else {
			(sorted[mid] + sorted[mid + 1]).divide(BigDecimal.TWO, MathContext.DECIMAL128)
		}
	}
}
