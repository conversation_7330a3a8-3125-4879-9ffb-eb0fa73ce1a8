package com.cleevio.fatbot.adapter.out.websocket

import com.cleevio.fatbot.adapter.out.websocket.dto.WebSocketMessage
import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.messaging.simp.SimpMessagingTemplate
import org.springframework.stereotype.Service

@Service
class WebSocketMessagingService(
	private val simpMessagingTemplate: SimpMessagingTemplate,
) {
	private val log = logger()

	init {
		simpMessagingTemplate.sendTimeout = 1000L
	}

	fun sendMessage(destination: String, message: WebSocketMessage<*>) {
		runCatching {
			simpMessagingTemplate.convertAndSend(destination, message)
		}.onFailure {
			log.error("Failed to send message $message to $destination", it)
		}
	}

	fun sendMessageToUser(user: String, destination: String, message: WebSocketMessage<*>) {
		runCatching {
			simpMessagingTemplate.convertAndSendToUser(user, destination, message)
		}.onFailure {
			log.error("Failed to send user message $message to $destination", it)
		}
	}
}
