package com.cleevio.fatbot.adapter.out

import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.http.HttpEntity
import org.springframework.http.HttpStatusCode
import org.springframework.http.client.SimpleClientHttpRequestFactory
import org.springframework.web.client.RestClient
import org.springframework.web.client.toEntity
import java.io.BufferedReader

abstract class BaseConnector(
	baseUrl: String,
	restClientCustomizer: (RestClient.Builder) -> Unit = {},
) {
	protected val logger = logger()
	protected val restClient = RestClient
		.builder()
		.baseUrl(baseUrl)
		.requestFactory(
			SimpleClientHttpRequestFactory().also {
				it.setConnectTimeout(DEFAULT_TIMEOUT_MS)
				it.setReadTimeout(DEFAULT_TIMEOUT_MS)
			},
		)
		.requestInterceptor(
			LoggingInterceptor(this.javaClass.simpleName),
		)
		.also(restClientCustomizer)
		.build()

	protected open val errorStatusPredicate: (HttpStatusCode) -> Boolean = { !it.is2xxSuccessful }

	protected fun RestClient.ResponseSpec.errorStatusHandler(
		statusPredicate: (HttpStatusCode) -> Boolean = errorStatusPredicate,
	): RestClient.ResponseSpec = onStatus(statusPredicate, errorhandler)

	protected inline fun <reified T : Any> RestClient.RequestHeadersSpec<*>.retrieveResponseWithErrorHandler(): T =
		retrieve()
			.errorStatusHandler()
			.toEntity<T>()
			.getBodyOrThrow()

	protected fun RestClient.RequestHeadersSpec<*>.retrieveWithErrorHandler() {
		retrieve()
			.errorStatusHandler()
			.toBodilessEntity()
	}

	protected fun <T> HttpEntity<T>.getBodyOrThrow(): T =
		body ?: error("${this.javaClass.simpleName} could not parse response body.")

	private val errorhandler = RestClient.ResponseSpec.ErrorHandler { request, response ->
		logger.error(
			"An error code occurred while calling ${request.uri}. " +
				"Response [code: ${response.statusCode}, " +
				"status: ${response.statusText}, " +
				"body: ${BufferedReader(response.body.reader()).readLines()}].",
		)

		error("${this.javaClass.simpleName} integration error.")
	}
}

private const val DEFAULT_TIMEOUT_MS = 30_000
