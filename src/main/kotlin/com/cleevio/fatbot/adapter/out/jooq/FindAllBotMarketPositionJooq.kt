package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotMarketPosition
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.tables.references.BOT
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION
import com.cleevio.fatbot.tables.references.BOT_TOKEN_INFO
import com.cleevio.fatbot.tables.references.BOT_WALLET
import org.jooq.DSLContext
import org.jooq.impl.DSL.or
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.util.UUID

@Component
class FindAllBotMarketPositionJooq(
	private val dslContext: DSLContext,
) : FindAllBotMarketPosition {

	override fun invoke(
		userId: UUID?,
		botIds: Set<UUID>?,
		searchString: String?,
		isBotMarketPositionActive: Boolean?,
		fileToUrlMapper: FileToUrlMapper,
	): List<FindAllBotMarketPosition.Result> {
		val botWalletConditions = listOfNotNull(
			userId?.let { BOT.USER_ID.eq(it) },
			botIds?.let { BOT_WALLET.BOT_ID.`in`(it) },
		)

		val allBotWalletIds = select(BOT_WALLET.ID)
			.from(BOT_WALLET)
			.innerJoin(BOT).on(BOT_WALLET.BOT_ID.eq(BOT.ID))
			.where(botWalletConditions)

		val botMarketPositionConditions = listOfNotNull(
			searchString?.let {
				or(
					BOT_TOKEN_INFO.NAME.likeIgnoreCase("%$it%"),
					BOT_TOKEN_INFO.SYMBOL.likeIgnoreCase("%$it%"),
					BOT_MARKET_POSITION.TOKEN_ADDRESS.eqIfValidAddressOrNoCondition(potentialAddress = it),
				)
			},
			BOT_MARKET_POSITION.BOT_WALLET_ID.`in`(allBotWalletIds),
			isBotMarketPositionActive?.let {
				val allowedStates = if (it) BotMarketPositionState.openStates else BotMarketPositionState.closedStates
				BOT_MARKET_POSITION.STATE.`in`(allowedStates)
			},
			BOT_MARKET_POSITION.STATE.notIn(
				BotMarketPositionState.openingErrorStates +
					BotMarketPositionState.closingErrorStates,
			),
		)

		return dslContext.select(
			BOT_WALLET.BOT_ID,
			BOT_MARKET_POSITION.ID,
			BOT_MARKET_POSITION.STATE,
			BOT_MARKET_POSITION.BUY_EXCHANGE_RATE,
			BOT_MARKET_POSITION.SELL_EXCHANGE_RATE,
			BOT_MARKET_POSITION.CREATED_AT,
			BOT_MARKET_POSITION.POSITION_CLOSED_AT,
			BOT_MARKET_POSITION.CHAIN,
			BOT_MARKET_POSITION.TOKEN_ADDRESS,
			BOT_MARKET_POSITION.TOTAL_TOKEN_AMOUNT_BOUGHT,
			BOT_MARKET_POSITION.TOTAL_TOKEN_ACQUISITION_COST_BASE_AMOUNT,
			BOT_MARKET_POSITION.TOTAL_TOKEN_AMOUNT_SOLD,
			BOT_MARKET_POSITION.TOTAL_TOKEN_DISPOSITION_COST_BASE_AMOUNT,
			BOT_TOKEN_INFO.NAME,
			BOT_TOKEN_INFO.SYMBOL,
			BOT_TOKEN_INFO.IMAGE_FILE_ID,
			BOT_TOKEN_INFO.DECIMALS,
			BOT_TOKEN_INFO.file.ID,
			BOT_TOKEN_INFO.file.EXTENSION,
		)
			.from(BOT_MARKET_POSITION)
			.innerJoin(BOT_WALLET).on(BOT_MARKET_POSITION.BOT_WALLET_ID.eq(BOT_WALLET.ID))
			.leftJoin(BOT_TOKEN_INFO).on(BOT_MARKET_POSITION.TOKEN_ADDRESS.eq(BOT_TOKEN_INFO.ADDRESS))
			.and(BOT_MARKET_POSITION.CHAIN.eq(BOT_TOKEN_INFO.CHAIN))
			.where(botMarketPositionConditions)
			.fetch()
			.map {
				FindAllBotMarketPosition.Result(
					id = it[BOT_MARKET_POSITION.ID]!!,
					state = it[BOT_MARKET_POSITION.STATE]!!,
					tokenName = it[BOT_TOKEN_INFO.NAME]!!,
					tokenSymbol = it[BOT_TOKEN_INFO.SYMBOL]!!,
					tokenImageUrl = fileToUrlMapper(it[BOT_TOKEN_INFO.file.ID], it[BOT_TOKEN_INFO.file.EXTENSION]),
					positionOpenedAt = it[BOT_MARKET_POSITION.CREATED_AT]!!,
					positionClosedAt = it[BOT_MARKET_POSITION.POSITION_CLOSED_AT],
					buyExchangeRate = it[BOT_MARKET_POSITION.BUY_EXCHANGE_RATE] ?: BigDecimal.ZERO,
					sellExchangeRate = it[BOT_MARKET_POSITION.SELL_EXCHANGE_RATE] ?: BigDecimal.ZERO,
					botId = it[BOT_WALLET.BOT_ID]!!,
					chain = it[BOT_MARKET_POSITION.CHAIN]!!,
					tokenAddress = it[BOT_MARKET_POSITION.TOKEN_ADDRESS]!!,
					tokenDecimals = it[BOT_TOKEN_INFO.DECIMALS]!!.toInt(),
					tokenAmountBought = it[BOT_MARKET_POSITION.TOTAL_TOKEN_AMOUNT_BOUGHT]!!.toBigInteger()
						.asBaseAmount(),
					tokenAcquisitionCost = it[BOT_MARKET_POSITION.TOTAL_TOKEN_ACQUISITION_COST_BASE_AMOUNT]!!
						.toBigInteger()
						.asBaseAmount(),
					tokenAmountSold = it[BOT_MARKET_POSITION.TOTAL_TOKEN_AMOUNT_SOLD]!!.toBigInteger().asBaseAmount(),
					tokenDispositionCost = it[BOT_MARKET_POSITION.TOTAL_TOKEN_DISPOSITION_COST_BASE_AMOUNT]!!
						.toBigInteger()
						.asBaseAmount(),
					tokenAcquisitionCostUsd = BigDecimal.ZERO,
				)
			}
	}
}
