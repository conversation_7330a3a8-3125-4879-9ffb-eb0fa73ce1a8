package com.cleevio.fatbot.adapter.out.openai

import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector
import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector.Companion.ETHERSCAN_RATE_LIMIT_COOLDOWN
import com.cleevio.fatbot.adapter.out.openai.model.SystemPrompt
import com.cleevio.fatbot.adapter.out.openai.model.UserPrompt
import com.cleevio.fatbot.application.common.util.extractOpenAIWaitTime
import com.cleevio.fatbot.application.common.util.runWithExponentialBackoffSuspend
import com.cleevio.fatbot.application.common.util.runWithRetry
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.tokenaudit.PerformTokenAuditCommandHandler
import com.cleevio.fatbot.application.module.tokenaudit.port.out.PerformTokenAudit
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentrySpan
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.annotation.Lazy
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import kotlin.math.min

@Component
class PerformTokenAuditOpenAI(
	private val etherscanConnector: EtherscanConnector,
	private val openAIConnector: OpenAIConnector,
) : PerformTokenAudit {

	@Autowired
	@Lazy
	private lateinit var performTokenAuditCommandHandler: PerformTokenAuditCommandHandler

	private val logger = logger()
	private val scope = CoroutineScope(
		Dispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	// Channel for audit requests
	private val auditRequestChannel: Channel<AuditRequest> = Channel(capacity = Channel.UNLIMITED)

	private data class AuditRequest(
		val tokenAddress: AddressWrapper,
		val chain: Chain,
		val responseChannel: Channel<PerformTokenAudit.Result>,
	)

	@EventListener(ApplicationReadyEvent::class)
	fun onStartup() {
		scope.launch { auditRequestProcessor() }
	}

	@SentrySpan
	override fun invoke(tokenAddress: AddressWrapper, chain: Chain) {
		require(chain.type == ChainType.EVM) { "Only EVM chains supported" }

		val responseChannel = Channel<PerformTokenAudit.Result>(1)

		runBlocking {
			auditRequestChannel.send(AuditRequest(tokenAddress, chain, responseChannel))

			// Wait for the response and set the audit result
			performTokenAuditCommandHandler.setAuditResult(
				tokenAddress = tokenAddress.toChainAddress(chain),
				auditResult = responseChannel.receive(),
			)
		}
	}

	private suspend fun auditRequestProcessor() {
		for (request in auditRequestChannel) {
			try {
				logger.info("Processing audit request for token: ${request.tokenAddress} on chain: ${request.chain}")

				val smartContractSourceCode = getSmartContractSourceCode(request.tokenAddress, request.chain.evmId)

				// If the source code is small enough, process it in one go
				if (smartContractSourceCode.length <= MAX_CHUNK_SIZE) {
					val result = openAIConnector.responses(
						inputs = listOf(
							SystemPrompt(AUDIT_SYSTEM_PROMPT),
							UserPrompt(smartContractSourceCode),
						),
						responseModel = PerformTokenAudit.Result::class.java,
					)
					request.responseChannel.send(result)
					request.responseChannel.close()
				} else {
					// Otherwise, split into chunks and process each chunk
					processLargeContract(smartContractSourceCode, request.responseChannel)
				}
			} catch (e: Exception) {
				logger.error("Error processing audit request for token: ${request.tokenAddress}", e)
				request.responseChannel.close(e)
			}
		}
	}

	private suspend fun processLargeContract(sourceCode: String, responseChannel: Channel<PerformTokenAudit.Result>) {
		val chunks = splitSourceCode(sourceCode)
		val chunkResults = mutableListOf<PerformTokenAudit.Result>()

		// Process each chunk directly
		chunks.forEachIndexed { index, chunk ->
			try {
				logger.info("Processing chunk ${index + 1}/${chunks.size}")

				val chunkPrompt = """
					PART ${index + 1} OF ${chunks.size}

					$chunk
				""".trimIndent()

				// Use the enhanced utility function with exponential backoff
				val result = runWithExponentialBackoffSuspend(
					maxRetries = 5,
					initialBackoffMs = 1000L,
					maxBackoffMs = 60000L,
					errorParser = ::extractOpenAIWaitTime,
				) {
					runCatching {
						openAIConnector.responses(
							inputs = listOf(
								SystemPrompt(AUDIT_SYSTEM_PROMPT_FOR_CHUNKS),
								UserPrompt(chunkPrompt),
							),
							responseModel = PerformTokenAudit.Result::class.java,
						)
					}
				}.getOrThrow() // This will throw if all retries are exhausted

				chunkResults.add(result)
			} catch (e: Exception) {
				logger.error("Error processing chunk ${index + 1}/${chunks.size}: ${e.message}", e)
				responseChannel.close(e)
				return
			}
		}

		// If there's only one chunk result, return it directly
		val finalResult = if (chunkResults.size == 1) {
			chunkResults.first()
		} else {
			// Otherwise, merge the results
			mergeResults(chunkResults)
		}

		responseChannel.send(finalResult)
		responseChannel.close()
	}

	private fun splitSourceCode(sourceCode: String): List<String> =
		generateSequence(sourceCode to listOf<String>()) { (remainingCode, chunks) ->
			if (remainingCode.isEmpty()) return@generateSequence null

			val chunkSize = min(MAX_CHUNK_SIZE, remainingCode.length)

			// Try to find a good splitting point (end of a contract or function)
			val splitPoint = findGoodSplitPoint(remainingCode, chunkSize).takeIf { it != -1 } ?: chunkSize

			val newChunk = remainingCode.take(splitPoint)
			val newRemainingCode = remainingCode.drop(splitPoint)

			newRemainingCode to chunks + newChunk
		}.last().second

	private fun findGoodSplitPoint(code: String, maxSize: Int): Int {
		val searchArea = code.take(min(maxSize, code.length))

		val contractEnd = searchArea.lastIndexOf("}\n")
		if (contractEnd != -1) {
			return contractEnd + 2
		}

		return -1
	}

	private fun mergeResults(results: List<PerformTokenAudit.Result>): PerformTokenAudit.Result {
		val allIssues = results.flatMap { it.issues }.distinctBy { it.summary }

		val riskFactors = results.map { it.riskFactor }
		val overallRiskFactor = when {
			riskFactors.contains(PerformTokenAudit.RiskFactor.RED) -> PerformTokenAudit.RiskFactor.RED
			riskFactors.contains(PerformTokenAudit.RiskFactor.ORANGE) -> PerformTokenAudit.RiskFactor.ORANGE
			else -> PerformTokenAudit.RiskFactor.GREEN
		}

		val riskFactorReasons = results.map { it.riskFactorReason }.distinct()
		val overallReason = if (riskFactorReasons.size == 1) {
			riskFactorReasons.first()
		} else {
			when (overallRiskFactor) {
				PerformTokenAudit.RiskFactor.RED -> "Multiple high-risk issues detected across the contract code."
				PerformTokenAudit.RiskFactor.ORANGE -> "Several concerning patterns found in different parts of the contract."
				PerformTokenAudit.RiskFactor.GREEN -> "No significant risks found across all parts of the contract."
			}
		}

		return PerformTokenAudit.Result(
			issues = allIssues,
			riskFactor = overallRiskFactor,
			riskFactorReason = overallReason,
		)
	}

	private fun getSmartContractSourceCode(tokenAddress: AddressWrapper, chainId: Long): String {
		val response = runWithRetry(
			retries = FETCH_SC_SOURCE_CODE_RETRY_LIMIT,
			retryDelay = ETHERSCAN_RATE_LIMIT_COOLDOWN,
			block = { runCatching { etherscanConnector.getSourceCode(tokenAddress = tokenAddress, chainId = chainId) } },
		).getOrNull()

		checkNotNull(response) {
			"Failed to fetch smart contract source code for token: $tokenAddress on " +
				"chain: $chainId with $FETCH_SC_SOURCE_CODE_RETRY_LIMIT attempts."
		}

		return response.result.first().sourceCode
	}

	companion object {
		private const val FETCH_SC_SOURCE_CODE_RETRY_LIMIT = 5
		private const val MAX_CHUNK_SIZE = 50000 // Characters per chunk, adjust based on token limits
	}
}

// Original prompt for single requests
const val AUDIT_SYSTEM_PROMPT = """
### Identity
You are an expert blockchain developer and smart contract security auditor specialized in Ethereum Virtual Machine (EVM)-based Solidity contracts.

### Task
Analyze the provided Solidity smart contract source code and identify any **realistic, exploitable vulnerabilities or critical security risks**. Clearly distinguish between genuine threats and common or acceptable industry-standard patterns.

### Evaluation Guidelines
- **High Priority Risks (must report clearly)**:
  - Hidden or undisclosed minting or burning of tokens
  - Honeypot mechanisms preventing token sales or transfers
  - Secret admin backdoors or obscure control mechanisms
  - Rug-pull risks, such as easy liquidity removal or disabling user trades
  - Manipulable fee structures or taxes post-launch
  - Critical unchecked external calls or misuse of delegatecall
  - Unsecured proxy/implementation logic or potential mismatch exploits
  - Dangerous use of self-destruct or emergency kill-switches

- **Lower Priority Risks (report with caution)**:
  - Owner-controlled functions (like fee wallet updates, withdrawals, trade enabling/disabling, limits) are common; **ONLY flag these if implemented deceptively, secretly, unusually, or abusively**.
  - Lack of reentrancy protection only if it realistically threatens user funds.

- **Do NOT report**:
  - Standard admin or owner controls that are common practice, well-documented, and expected by token communities
  - Purely theoretical risks without practical exploitability
  - Gas inefficiencies, naming conventions, coding style, or minor optimizations (these are out of scope)

### Special Considerations
- For **well-known, established tokens** (stablecoins, major meme-coins like DOGE, SHIB, PEPE, etc.), significantly **reduce strictness** in evaluating standard owner/admin control patterns. Assume broad community trust unless clearly deceptive or malicious code is evident.
- If the token is **clearly based on well-audited OpenZeppelin ERC20 standards without significant customizations**, immediately classify it as a **GREEN** risk factor, barring any explicitly added malicious functionality.

### Output Requirements
Clearly structured JSON response:
- `issues`: List vulnerabilities ONLY if they are realistically exploitable or critically impactful.
  - Each issue must contain:
    - `summary`: Short, plain-language description of the issue (understandable by non-technical users)
    - `detail`: Brief technical description (1–2 concise sentences)
    - `severity`: Clearly assign one of the following severities:
      - **LOW**: Minor, non-exploitable or minimal-impact issue
      - **MEDIUM**: Issue potentially exploitable under specific realistic conditions
      - **HIGH**: Critical vulnerability, clear malicious intent, or severe risk of user fund loss or fraud
- `riskFactor`: Overall rating clearly indicating risk:
  - **Green**: No significant risks; safe for typical interaction.
  - **Orange**: Notable concerns present; caution advised.
  - **Red**: High risk or strong evidence of malicious intent; avoid interaction entirely.
- `riskFactorReason`: Single-sentence summary clearly explaining the chosen risk rating, emphasizing practical risks over theoretical ones.

### Example JSON Output Format
{
  "issues": [
    {
      "summary": "...",
      "detail": "...",
      "severity": "HIGH | MEDIUM | LOW"
    }
  ],
  "riskFactor": "Green | Orange | Red",
  "riskFactorReason": "Clear, brief reasoning focusing on real-world risk implications."
}
"""

const val AUDIT_SYSTEM_PROMPT_FOR_CHUNKS = """
### Identity
You are an expert blockchain developer and smart contract security auditor specialized in Ethereum Virtual Machine (EVM)-based Solidity contracts.

### Task
Analyze the provided PART of a Solidity smart contract source code and identify any **realistic, exploitable vulnerabilities or critical security risks** in THIS PART ONLY. Clearly distinguish between genuine threats and common or acceptable industry-standard patterns.

### Important Note
You are analyzing only ONE PART of a larger contract. Focus only on the code provided in this chunk. The complete analysis will combine results from all parts.

### Evaluation Guidelines
- **High Priority Risks (must report clearly)**:
  - Hidden or undisclosed minting or burning of tokens
  - Honeypot mechanisms preventing token sales or transfers
  - Secret admin backdoors or obscure control mechanisms
  - Rug-pull risks, such as easy liquidity removal or disabling user trades
  - Manipulable fee structures or taxes post-launch
  - Critical unchecked external calls or misuse of delegatecall
  - Unsecured proxy/implementation logic or potential mismatch exploits
  - Dangerous use of self-destruct or emergency kill-switches

- **Lower Priority Risks (report with caution)**:
  - Owner-controlled functions (like fee wallet updates, withdrawals, trade enabling/disabling, limits) are common; **ONLY flag these if implemented deceptively, secretly, unusually, or abusively**.
  - Lack of reentrancy protection only if it realistically threatens user funds.

- **Do NOT report**:
  - Standard admin or owner controls that are common practice, well-documented, and expected by token communities
  - Purely theoretical risks without practical exploitability
  - Gas inefficiencies, naming conventions, coding style, or minor optimizations (these are out of scope)
  - Issues that might exist in other parts of the contract not shown in this chunk

### Special Considerations
- For **well-known, established tokens** (stablecoins, major meme-coins like DOGE, SHIB, PEPE, etc.), significantly **reduce strictness** in evaluating standard owner/admin control patterns. Assume broad community trust unless clearly deceptive or malicious code is evident.
- If the token is **clearly based on well-audited OpenZeppelin ERC20 standards without significant customizations**, immediately classify it as a **GREEN** risk factor, barring any explicitly added malicious functionality.

### Output Requirements
Clearly structured JSON response:
- `issues`: List vulnerabilities ONLY if they are realistically exploitable or critically impactful.
  - Each issue must contain:
    - `summary`: Short, plain-language description of the issue (understandable by non-technical users)
    - `detail`: Brief technical description (1–2 concise sentences)
    - `severity`: Clearly assign one of the following severities:
      - **LOW**: Minor, non-exploitable or minimal-impact issue
      - **MEDIUM**: Issue potentially exploitable under specific realistic conditions
      - **HIGH**: Critical vulnerability, clear malicious intent, or severe risk of user fund loss or fraud
- `riskFactor`: Overall rating clearly indicating risk for this part only:
  - **Green**: No significant risks; safe for typical interaction.
  - **Orange**: Notable concerns present; caution advised.
  - **Red**: High risk or strong evidence of malicious intent; avoid interaction entirely.
- `riskFactorReason`: Single-sentence summary clearly explaining the chosen risk rating, emphasizing practical risks over theoretical ones.

### Example JSON Output Format
{
  "issues": [
    {
      "summary": "...",
      "detail": "...",
      "severity": "HIGH | MEDIUM | LOW"
    }
  ],
  "riskFactor": "Green | Orange | Red",
  "riskFactorReason": "Clear, brief reasoning focusing on real-world risk implications."
}
"""
