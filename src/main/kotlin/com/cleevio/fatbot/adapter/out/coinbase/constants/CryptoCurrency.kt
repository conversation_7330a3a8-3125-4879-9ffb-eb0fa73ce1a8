package com.cleevio.fatbot.adapter.out.coinbase.constants

import com.cleevio.fatbot.application.module.market.exception.IllegalChainException

enum class CryptoCurrency(val decimals: Int) {
	ETH(18),
	<PERSON>OL(9),
	BNB(18),
	;

	/**
	 * Returns the Native CryptoCurrency of given chainId
	 */
	companion object {
		fun ofChainId(chainId: Long) = when (chainId) {
			1L -> ETH
			8453L -> ETH
			42161L -> ETH
			56L -> BNB
			else -> throw IllegalChainException("Cannot return CryptoCurrency by chainID for non-EVM chain types.")
		}
	}
}
