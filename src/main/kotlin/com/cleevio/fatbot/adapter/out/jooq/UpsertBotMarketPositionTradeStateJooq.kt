package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.module.matchmaking.event.TradedBotMarketPositionsEvent
import com.cleevio.fatbot.application.module.matchmaking.port.out.UpsertBotMarketPositionTradeState
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION_TRADE_STATE
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.jooq.impl.DSL.excluded
import org.springframework.stereotype.Component
import java.time.Clock
import java.time.Instant

@Component
class UpsertBotMarketPositionTradeStateJooq(
	private val dslContext: DSLContext,
	private val clock: Clock,
) : UpsertBotMarketPositionTradeState {

	override fun invoke(tradedPositions: TradedBotMarketPositionsEvent) {
		val rows = tradedPositions.positions.map {
			DSL.row(
				UUIDv7(),
				Instant.now(clock),
				Instant.now(clock),
				0,
				it.botMarketPositionId,
				it.type,
				it.marketCapUsd,
				it.liquidityUsd,
				it.volumeUsd,
				it.numOfAccountHolders,
				it.buyVolume,
				it.sellVolume,
				it.fractionOfSellTransactions,
			)
		}

		dslContext.insertInto(
			BOT_MARKET_POSITION_TRADE_STATE,
			BOT_MARKET_POSITION_TRADE_STATE.ID,
			BOT_MARKET_POSITION_TRADE_STATE.CREATED_AT,
			BOT_MARKET_POSITION_TRADE_STATE.UPDATED_AT,
			BOT_MARKET_POSITION_TRADE_STATE.VERSION,
			BOT_MARKET_POSITION_TRADE_STATE.BOT_MARKET_POSITION_ID,
			BOT_MARKET_POSITION_TRADE_STATE.TYPE,
			BOT_MARKET_POSITION_TRADE_STATE.MARKET_CAP_USD,
			BOT_MARKET_POSITION_TRADE_STATE.LIQUIDITY_USD,
			BOT_MARKET_POSITION_TRADE_STATE.VOLUME_USD,
			BOT_MARKET_POSITION_TRADE_STATE.NUM_OF_ACCOUNT_HOLDERS,
			BOT_MARKET_POSITION_TRADE_STATE.BUY_VOLUME,
			BOT_MARKET_POSITION_TRADE_STATE.SELL_VOLUME,
			BOT_MARKET_POSITION_TRADE_STATE.FRACTION_OF_SELL_TRANSACTIONS,
		)
			.valuesOfRows(rows)
			.onConflict(BOT_MARKET_POSITION_TRADE_STATE.BOT_MARKET_POSITION_ID, BOT_MARKET_POSITION_TRADE_STATE.TYPE)
			.doUpdate()
			.set(BOT_MARKET_POSITION_TRADE_STATE.UPDATED_AT, excluded(BOT_MARKET_POSITION_TRADE_STATE.UPDATED_AT))
			.set(BOT_MARKET_POSITION_TRADE_STATE.MARKET_CAP_USD, excluded(BOT_MARKET_POSITION_TRADE_STATE.MARKET_CAP_USD))
			.set(BOT_MARKET_POSITION_TRADE_STATE.LIQUIDITY_USD, excluded(BOT_MARKET_POSITION_TRADE_STATE.LIQUIDITY_USD))
			.set(BOT_MARKET_POSITION_TRADE_STATE.VOLUME_USD, excluded(BOT_MARKET_POSITION_TRADE_STATE.VOLUME_USD))
			.set(
				BOT_MARKET_POSITION_TRADE_STATE.NUM_OF_ACCOUNT_HOLDERS,
				excluded(BOT_MARKET_POSITION_TRADE_STATE.NUM_OF_ACCOUNT_HOLDERS),
			)
			.set(BOT_MARKET_POSITION_TRADE_STATE.BUY_VOLUME, excluded(BOT_MARKET_POSITION_TRADE_STATE.BUY_VOLUME))
			.set(BOT_MARKET_POSITION_TRADE_STATE.SELL_VOLUME, excluded(BOT_MARKET_POSITION_TRADE_STATE.SELL_VOLUME))
			.set(
				BOT_MARKET_POSITION_TRADE_STATE.FRACTION_OF_SELL_TRANSACTIONS,
				excluded(BOT_MARKET_POSITION_TRADE_STATE.FRACTION_OF_SELL_TRANSACTIONS),
			)
			.execute()
	}
}
