package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.etherscan.EtherscanConnector
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.port.out.GetIsTokenContractVerified
import org.springframework.stereotype.Component

@Component
class GetIsTokenContractVerifiedEVM(
	private val etherscanConnector: EtherscanConnector,
) : GetIsTokenContractVerified {

	override fun invoke(tokenAddress: AddressWrapper, chainId: Long): Boolean {
		val response = etherscanConnector.getVerifiedContractAbi(tokenAddress, chainId)

		return response.isVerified()
	}

	private fun EtherscanConnector.GetVerifiedContractAbiResponse.isVerified(): Bo<PERSON>an {
		return when {
			result == "Contract source code not verified" -> false
			message == "OK" -> true
			else -> error("Failed to get verified contract ABI. Result: $result")
		}
	}
}
