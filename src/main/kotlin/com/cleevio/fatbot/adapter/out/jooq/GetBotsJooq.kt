package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.module.bot.port.out.GetBots
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.BOT
import com.cleevio.fatbot.tables.references.BOT_DRAFT
import com.cleevio.fatbot.tables.references.BOT_WALLET
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.util.UUID

@Component
class GetBotsJooq(
	private val dslContext: DSLContext,
) : GetBots {
	override fun invoke(userId: UUID, botIds: Set<UUID>): List<GetBots.Result> {
		val botConditions = listOf(
			BOT.ID.`in`(botIds),
			BOT.USER_ID.eq(userId),
		)

		val createdBots = dslContext.select(
			BOT.ID,
			BOT.NAME,
			BOT.IS_ACTIVE,
			BOT.NUMBER_OF_ACTIVE_DAYS,
			BOT.AVATAR_FILE_ID,
			BOT.BUY_FREQUENCY,
			BOT.REMAINING_BUY_FREQUENCY,
			BOT.BUY_FREQUENCY_LAST_RESET_AT,
			BOT_WALLET.BALANCE,
			BOT_WALLET.ACQUISITION_VALUE_USD,
			BOT_WALLET.CHAIN,
		)
			.from(BOT)
			.join(BOT_WALLET).on(BOT_WALLET.BOT_ID.eq(BOT.ID))
			.where(botConditions)
			.fetch()
			.map {
				GetBots.Result(
					id = it[BOT.ID]!!,
					name = it[BOT.NAME]!!,
					isActive = it[BOT.IS_ACTIVE]!!,
					numberOfActiveDays = it[BOT.NUMBER_OF_ACTIVE_DAYS]!!,
					botAvatarFileId = it[BOT.AVATAR_FILE_ID]!!,
					buyFrequency = it[BOT.BUY_FREQUENCY]!!,
					remainingBuyFrequency = it[BOT.REMAINING_BUY_FREQUENCY]!!,
					buyFrequencyLastResetAt = it[BOT.BUY_FREQUENCY_LAST_RESET_AT]!!,
					walletBalance = it[BOT_WALLET.BALANCE]!!.toBigInteger().asBaseAmount(),
					walletAcquisitionValueUsd = it[BOT_WALLET.ACQUISITION_VALUE_USD]!!,
					walletChain = it[BOT_WALLET.CHAIN]!!,
				)
			}

		val draftBotIds = botIds - createdBots.mapToSet { it.id }

		val botDraftConditions = listOfNotNull(
			BOT_DRAFT.ID.`in`(draftBotIds),
			BOT_DRAFT.USER_ID.eq(userId),
		)

		val draftBots = dslContext.select(
			BOT_DRAFT.ID,
			BOT_DRAFT.NAME,
			BOT_DRAFT.AVATAR_FILE_ID,
			BOT_DRAFT.BUY_FREQUENCY,
			BOT_DRAFT.CREATED_AT,
		)
			.from(BOT_DRAFT)
			.where(botDraftConditions)
			.fetch()
			.map {
				GetBots.Result(
					id = it[BOT_DRAFT.ID]!!,
					name = it[BOT_DRAFT.NAME] ?: "",
					isActive = false,
					numberOfActiveDays = 0L,
					botAvatarFileId = it[BOT_DRAFT.AVATAR_FILE_ID]!!,
					buyFrequency = it[BOT_DRAFT.BUY_FREQUENCY] ?: 0,
					remainingBuyFrequency = it[BOT_DRAFT.BUY_FREQUENCY] ?: 0,
					buyFrequencyLastResetAt = it[BOT_DRAFT.CREATED_AT]!!,
					walletBalance = BaseAmount.ZERO,
					walletAcquisitionValueUsd = BigDecimal.ZERO,
					walletChain = Chain.SOLANA,
				)
			}

		return createdBots + draftBots
	}
}
