package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.adapter.out.coinbase.constants.CryptoCurrency
import com.cleevio.fatbot.application.module.exchangerate.port.out.GetLatestExchangeRateSnapshots
import com.cleevio.fatbot.tables.references.EXCHANGE_RATE_SNAPSHOT
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Instant

@Component
class GetLatestExchangeRateSnapshotsJooq(
	private val dslContext: DSLContext,
) : GetLatestExchangeRateSnapshots {

	override fun invoke(currencies: Set<CryptoCurrency>, validBefore: Instant?): Map<CryptoCurrency, BigDecimal> {
		return dslContext
			.select(EXCHANGE_RATE_SNAPSHOT.CURRENCY, EXCHANGE_RATE_SNAPSHOT.EXCHANGE_RATE_USD)
			.distinctOn(EXCHANGE_RATE_SNAPSHOT.CURRENCY)
			.from(<PERSON>XCH<PERSON><PERSON>_RATE_SNAPSHOT)
			.where(validBefore?.let { EXCHANGE_RATE_SNAPSHOT.VALID_AT.le(it) })
			.orderBy(EXCHANGE_RATE_SNAPSHOT.CURRENCY, EXCHANGE_RATE_SNAPSHOT.VALID_AT.desc())
			.fetch()
			.associate {
				it[EXCHANGE_RATE_SNAPSHOT.CURRENCY]!! to it[EXCHANGE_RATE_SNAPSHOT.EXCHANGE_RATE_USD]!!
			}
	}
}
