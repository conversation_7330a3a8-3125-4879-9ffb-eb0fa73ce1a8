package com.cleevio.fatbot.adapter.out.solana.client

import com.cleevio.fatbot.application.common.util.decodeData
import com.syntifi.near.borshj.BorshBuffer
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.Commitment
import java.math.BigInteger

private const val RPC_REQUEST_ACCOUNT_LIMIT = 100

fun RpcClient.getTokenAccountBalances(accounts: List<PublicKey>, commitment: Commitment): List<BigInteger?> {
	val accountsData = accounts.chunked(RPC_REQUEST_ACCOUNT_LIMIT).flatMap { accountsChunk ->
		api.getMultipleAccountsOptional(
			accountsChunk,
			// Slice of [amount]
			mapOf("dataSlice" to mapOf("offset" to 64, "length" to 8), "commitment" to commitment),
		)
	}

	val balances = accountsData.map {
		if (it.isEmpty) return@map null

		val data = it.get().decodeData()
		BorshBuffer.wrap(data).readU64().toBigInteger()
	}

	return balances
}

fun RpcClient.getTokenAccountBalance(account: PublicKey, commitment: Commitment): BigInteger? {
	val accountsData = api.getAccountInfo(
		account,
		// Slice of [amount]
		mapOf("dataSlice" to mapOf("offset" to 64, "length" to 8), "commitment" to commitment),
	)

	val balance = runCatching {
		BorshBuffer.wrap(accountsData.decodedData).readU64().toBigInteger()
	}.getOrNull()

	return balance
}
