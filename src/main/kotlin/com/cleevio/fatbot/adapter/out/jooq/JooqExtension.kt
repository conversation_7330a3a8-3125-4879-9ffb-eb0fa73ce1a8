package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.adapter.`in`.InfiniteScroll
import com.cleevio.fatbot.application.common.crypto.isValidEthereumAddress
import com.cleevio.fatbot.application.common.crypto.isValidSolanaAddress
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import org.jooq.Condition
import org.jooq.Field
import org.jooq.Record
import org.jooq.SelectJoinStep
import org.jooq.SelectLimitPercentStep
import org.jooq.TableField
import org.jooq.impl.DSL
import java.math.BigDecimal

fun <R : Record, K : Comparable<K>> SelectJoinStep<R>.whereWithInfiniteScroll(
	conditions: List<Condition>,
	infiniteScroll: InfiniteScroll<K>,
	idFieldSelector: TableField<Record, K?>,
): SelectLimitPercentStep<R> {
	val lastId = infiniteScroll.lastId

	val idCondition = lastId?.let {
		when (infiniteScroll.getOrder()) {
			InfiniteScroll.Order.NEWEST_FIRST -> DSL.and(idFieldSelector.lt(lastId))
			InfiniteScroll.Order.OLDEST_FIRST -> DSL.and(idFieldSelector.gt(lastId))
		}
	} ?: DSL.noCondition()

	val sortField = when (infiniteScroll.getOrder()) {
		InfiniteScroll.Order.NEWEST_FIRST -> idFieldSelector.desc()
		InfiniteScroll.Order.OLDEST_FIRST -> idFieldSelector.asc()
	}

	return this
		.where(conditions)
		.and(idCondition)
		.orderBy(sortField)
		.limit(infiniteScroll.size)
}

fun TableField<Record, AddressWrapper?>.eqIfValidAddressOrNoCondition(potentialAddress: String?): Condition {
	if (potentialAddress == null) return DSL.noCondition()
	if (!potentialAddress.isValidEthereumAddress() && !potentialAddress.isValidSolanaAddress()) return DSL.noCondition()

	return this.eq(AddressWrapper(potentialAddress))
}

fun Field<BigDecimal>.coalesceToZero() = DSL.coalesce(this, BigDecimal.ZERO)
