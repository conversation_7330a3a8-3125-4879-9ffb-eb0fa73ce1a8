package com.cleevio.fatbot.adapter.out.evm.simulation

import com.cleevio.fatbot.adapter.out.evm.FatbotRouterFunctionCallEncoder
import com.cleevio.fatbot.adapter.out.evm.constants.EVM_MAX_UINT
import com.cleevio.fatbot.adapter.out.evm.constants.SELECTOR_APPROVE
import com.cleevio.fatbot.adapter.out.evm.constants.simulationAddress
import com.cleevio.fatbot.adapter.out.evm.constants.simulationCredentials
import com.cleevio.fatbot.adapter.out.evm.context.EvmWeb3jWrapperProvider
import com.cleevio.fatbot.adapter.out.evm.contract.FatbotUtil
import com.cleevio.fatbot.application.common.crypto.fromHexStringGetBigInteger
import com.cleevio.fatbot.application.common.crypto.padToDataPayloadLength
import com.cleevio.fatbot.application.common.crypto.remove0x
import com.cleevio.fatbot.application.common.util.calculateFractionDifference
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Component
import org.web3j.protocol.core.RemoteFunctionCall
import org.web3j.tx.gas.DefaultGasProvider
import org.web3j.utils.Convert
import java.math.BigDecimal
import java.math.BigInteger
import java.math.RoundingMode

private val DEFAULT_WEI_AMOUNT_IN = Convert.toWei(BigDecimal("0.001"), Convert.Unit.ETHER).toBigInteger()

/**
 * The Wei amount for which we will buy tokens for sell simulation in case setting the tokens directly fails.
 * Note that this amount needs to account for any buy tax or other fees enforced by the token.
 * It also should not be too high as to avoid buy amount limits.
 */
private val DEFAULT_WEI_BUY_AMOUNT_IN = DEFAULT_WEI_AMOUNT_IN * BigInteger("4")

interface GetTokenTaxEVM {

	data class Result(
		val buyTax: BigDecimal,
		val buyGasUsed: BigInteger,
		val sellTax: BigDecimal,
		val sellGasUsed: BigInteger,
	)

	/**
	 * Returns Sell tax as a fraction, e.q. 0.05 (5%), 0.00 (0%) of [tokenAddress]
	 */
	operator fun invoke(tokenAddress: AddressWrapper, chainId: Long, dexPairInfo: GetDex.Evm): Result
}

@Primary
@Component
@ConditionalOnProperty("temper.base-url", havingValue = "mock")
/**
 * Takes precedence over [GetTokenTaxEVMImpl] in case the env property is specified to "mock".
 * This is useful for dev environment, where a [TemperConnector] service would need to be running.
 */
private class GetTokenTaxEVMMock : GetTokenTaxEVM {

	init {
		logger().info("GetTokenSellTaxEVM will be backed by GetTokenSellTaxEVMMock")
	}

	override fun invoke(tokenAddress: AddressWrapper, chainId: Long, dexPairInfo: GetDex.Evm): GetTokenTaxEVM.Result =
		GetTokenTaxEVM.Result(BigDecimal.ZERO, BigInteger.ZERO, BigDecimal.ZERO, BigInteger.ZERO)
}

@Component
private class GetTokenTaxEVMImpl(
	private val web3jWrapperProvider: EvmWeb3jWrapperProvider,
	private val chainProperties: ChainProperties,
	private val temperConnector: TemperConnector,
	private val getSimulationOverrideData: GetSimulationOverrideData,
) : GetTokenTaxEVM {

	override operator fun invoke(
		tokenAddress: AddressWrapper,
		chainId: Long,
		dexPairInfo: GetDex.Evm,
	): GetTokenTaxEVM.Result {
		val web3j = web3jWrapperProvider.ofChainId(chainId).getWeb3j()
		val properties = chainProperties.ofEvmChainId(chainId = chainId)

		val util = FatbotUtil.load(
			properties.fatbotUtil.getAddressString(),
			web3j,
			simulationCredentials,
			DefaultGasProvider(),
		)

		fun makeGetAmountOutCall(amount: BigInteger, isBuy: Boolean): RemoteFunctionCall<BigInteger> {
			val weth = properties.wethAddress.getAddressString()
			val token = tokenAddress.getAddressString()

			val (tokenIn, tokenOut) = if (isBuy) weth to token else token to weth

			return when (dexPairInfo) {
				is GetDex.UniswapV2, is GetDex.PancakeswapV2 -> util.getAmountOutV2(amount, token, isBuy)

				is GetDex.UniswapV3 -> util.getAmountOutV3(
					tokenIn,
					tokenOut,
					dexPairInfo.fee.feeAmount.toBigInteger(),
					amount,
				)

				is GetDex.PancakeswapV3 -> util.getAmountOutV3(
					tokenIn,
					tokenOut,
					dexPairInfo.fee.feeAmount.toBigInteger(),
					amount,
				)
			}
		}

		val tokensInRequest = makeGetAmountOutCall(DEFAULT_WEI_AMOUNT_IN, isBuy = true).sendAsync()
		val simulationOverrideData = getSimulationOverrideData(tokenAddress, dexPairInfo.pairAddress, chainId)

		val tokensIn = tokensInRequest.join()

		val result = simulateBuySell(
			properties = properties,
			blockNumber = simulationOverrideData.blockNumber,
			tokenAddress = tokenAddress,
			buyAmountIn = DEFAULT_WEI_BUY_AMOUNT_IN,
			sellAmountIn = tokensIn,
			dexPairInfo = dexPairInfo,
			encodedGetSellAmountOutCall = makeGetAmountOutCall(tokensIn, isBuy = false).encodeFunctionCall(),
			encodedGetBuyAmountOutCall = makeGetAmountOutCall(DEFAULT_WEI_BUY_AMOUNT_IN, isBuy = true).encodeFunctionCall(),
			overrides = mapOf(
				tokenAddress to simulationOverrideData.tokenStateOverride,
				dexPairInfo.pairAddress to simulationOverrideData.pairStateOverride,
			),
		)

		val buyTax = calculateFractionDifference(
			a = result.buyAmountOut.toBigDecimal(),
			b = result.expectedBuyAmountOut.toBigDecimal(),
		).setScale(3, RoundingMode.FLOOR)

		val sellTax = calculateFractionDifference(
			a = result.sellAmountOut.toBigDecimal(),
			b = result.expectedSellAmountOut.toBigDecimal(),
		).setScale(3, RoundingMode.FLOOR)

		return GetTokenTaxEVM.Result(
			buyTax = buyTax,
			buyGasUsed = result.buyGasUsed,
			sellTax = sellTax,
			sellGasUsed = result.sellGasUsed,
		)
	}

	private data class BuySellSimulationResult(
		val expectedBuyAmountOut: BigInteger,
		val buyAmountOut: BigInteger,
		val buyGasUsed: BigInteger,

		val expectedSellAmountOut: BigInteger,
		val sellAmountOut: BigInteger,
		val sellGasUsed: BigInteger,
	)

	private fun simulateBuySell(
		properties: ChainProperties.EthereumVirtualMachine,
		blockNumber: Long,
		tokenAddress: AddressWrapper,
		buyAmountIn: BigInteger,
		sellAmountIn: BigInteger,
		dexPairInfo: GetDex.Evm,
		encodedGetBuyAmountOutCall: String,
		encodedGetSellAmountOutCall: String,
		overrides: Map<AddressWrapper, TemperConnector.StateOverride>,
	): BuySellSimulationResult {
		val readAmountOutBuyParams = TemperConnector.SimulateTransactionRequest(
			from = simulationCredentials.address,
			to = properties.fatbotUtil.getAddressString(),
			data = encodedGetBuyAmountOutCall,
			value = null,
			commitChanges = false,
			forkUrl = properties.node,
			chainId = properties.chain.evmId,
			blockNumber = blockNumber,
			stateOverrides = mapOf(
				simulationAddress to TemperConnector.StateOverride(
					balance = (buyAmountIn * BigInteger.TEN).toString(),
				),
			) + overrides,
		)

		val encodedBuyFunctionCall = FatbotRouterFunctionCallEncoder.buyFunctionCall(
			amountIn = buyAmountIn,
			minAmountOut = BigInteger.ZERO,
			tokenAddress = tokenAddress,
			toAddress = simulationCredentials.address,
			dexPairInfo = dexPairInfo,
		)

		val fatbotRouterAddress = properties.fatbotRouter.getAddressString()

		val buyParams = TemperConnector.SimulateTransactionRequest(
			from = simulationCredentials.address,
			to = fatbotRouterAddress,
			data = encodedBuyFunctionCall,
			value = buyAmountIn,
			// Commit changes, so the sell transaction has Tokens to sell
			commitChanges = true,
			forkUrl = properties.node,
			chainId = properties.chain.evmId,
			blockNumber = blockNumber,
		)

		// Read the amountOut after the price is altered by the buy simulation
		val readAmountOutParams = TemperConnector.SimulateTransactionRequest(
			from = simulationCredentials.address,
			to = properties.fatbotUtil.getAddressString(),
			data = encodedGetSellAmountOutCall,
			value = null,
			commitChanges = false,
			forkUrl = properties.node,
			chainId = properties.chain.evmId,
			blockNumber = blockNumber,
		)

		val approveParams = TemperConnector.SimulateTransactionRequest(
			from = simulationCredentials.address,
			to = tokenAddress.getAddressString(),
			data = createApprovalDataPayload(fatbotRouterAddress),
			value = null,
			forkUrl = properties.node,
			chainId = properties.chain.evmId,
			commitChanges = true,
			blockNumber = blockNumber,
		)

		val encodedSellFunctionCall = FatbotRouterFunctionCallEncoder.sellFunctionCall(
			amountIn = sellAmountIn,
			minAmountOut = BigInteger.ZERO,
			tokenAddress = tokenAddress,
			toAddress = simulationCredentials.address,
			dexPairInfo = dexPairInfo,
		)

		// Sell with tokens acquired with the tokens bought
		val sellParams = TemperConnector.SimulateTransactionRequest(
			from = simulationCredentials.address,
			to = fatbotRouterAddress,
			data = encodedSellFunctionCall,
			value = null,
			forkUrl = properties.node,
			chainId = properties.chain.evmId,
			blockNumber = blockNumber,
		)

		val results = temperConnector.simulateBundle(
			listOf(readAmountOutBuyParams, buyParams, readAmountOutParams, approveParams, sellParams),
		)

		val (readBuyResponse, buyResponse, readAmountOutResponse, _, sellResponse) = results

		val expectedBuyAmountOut = readBuyResponse.returnData.fromHexStringGetBigInteger()
		val simulatedBuyAmountOut = buyResponse.extractTokensSuccessfullySwappedEvent().amountOut
		val buyGasUsed = buyResponse.gasUsed

		val expectedSellAmountOut = readAmountOutResponse.returnData.fromHexStringGetBigInteger()
		val simulatedSellAmountOut = sellResponse.extractTokensSuccessfullySwappedEvent().amountOut
		val sellGasUsed = sellResponse.gasUsed

		return BuySellSimulationResult(
			expectedBuyAmountOut = expectedBuyAmountOut,
			buyAmountOut = simulatedBuyAmountOut,
			buyGasUsed = buyGasUsed,

			expectedSellAmountOut = expectedSellAmountOut,
			sellAmountOut = simulatedSellAmountOut,
			sellGasUsed = sellGasUsed,
		)
	}

	private fun createApprovalDataPayload(contractAddress: String): String = buildString {
		append(SELECTOR_APPROVE)
		append(contractAddress.remove0x().padToDataPayloadLength())
		append(EVM_MAX_UINT.toString(16).padToDataPayloadLength())
	}
}
