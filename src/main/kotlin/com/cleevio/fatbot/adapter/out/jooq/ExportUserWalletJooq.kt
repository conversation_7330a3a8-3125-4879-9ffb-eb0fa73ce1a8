package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.converter.PrivateKeyConverter
import com.cleevio.fatbot.application.module.wallet.exception.WalletNotFoundException
import com.cleevio.fatbot.application.module.wallet.port.out.ExportUserWallet
import com.cleevio.fatbot.application.module.wallet.query.ExportUserWalletQuery
import com.cleevio.fatbot.tables.references.WALLET
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class ExportUserWalletJooq(
	private val dslContext: DSLContext,
	private val privateKeyConverter: PrivateKeyConverter,
) : ExportUserWallet {

	override fun invoke(userId: UUID, walletId: UUID): ExportUserWalletQuery.Result {
		val conditions = listOf(
			WALLET.ID.eq(walletId),
			WALLET.USER_ID.eq(userId),
		)

		return dslContext
			.select(
				WALLET.PRIVATE_KEY,
			)
			.from(WALLET)
			.where(conditions)
			.fetchOne()
			?.let {
				ExportUserWalletQuery.Result(
					privateKey = privateKeyConverter.convertToEntityAttribute(
						it[WALLET.PRIVATE_KEY]!!,
					),
				)
			} ?: throw WalletNotFoundException("Wallet for user $userId with id $walletId not found!")
	}
}
