package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.port.out.SetDefaultWalletOnChain
import com.cleevio.fatbot.tables.references.WALLET
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class SetDefaultWalletOnChainJooq(
	private val dslContext: DSLContext,
) : SetDefaultWalletOnChain {

	override fun invoke(userId: UUID, newDefaultWallet: UUID, chain: Chain) {
		val updateCase = DSL
			.`when`(WALLET.ID.eq(newDefaultWallet), true)
			.otherwise(false)

		dslContext
			.update(WALLET)
			.set(WALLET.IS_DEFAULT, updateCase)
			.where(WALLET.USER_ID.eq(userId))
			.and(WALLET.CHAIN.eq(chain))
			.execute()
	}
}
