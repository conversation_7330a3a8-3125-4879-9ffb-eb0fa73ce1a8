package com.cleevio.fatbot.adapter.out.evm.builder.titanbuilder.request

import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigInteger

data class SendPrivateEthTransactionTitanbuilderRequest(
	val id: String = "0",
	val jsonrpc: String = "2.0",
	val method: String = "titan_boostTransaction",
	val params: List<Params>,
) {
	data class Params(
		@field:JsonProperty("tx")
		val signedTx: SignedTx,
		val targetBlock: BigInteger,
		val allowBackruns: Boolean = false,
		val allowReverts: Boolean = false,
		val trustedBuilders: List<String> = listOf("beaver"),
		val tag: String = "Fatbot",
	)
}
