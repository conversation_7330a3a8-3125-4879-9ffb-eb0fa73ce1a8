package com.cleevio.fatbot.adapter.out.evm.contract;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Callable;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.DynamicArray;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.crypto.Credentials;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.RemoteFunctionCall;
import org.web3j.tuples.generated.Tuple2;
import org.web3j.tx.Contract;
import org.web3j.tx.TransactionManager;
import org.web3j.tx.gas.ContractGasProvider;

/**
 * <p>Auto generated code.
 * <p><strong>Do not modify!</strong>
 * <p>Please use the <a href="https://docs.web3j.io/command_line.html">web3j command line tools</a>,
 * or the org.web3j.codegen.SolidityFunctionWrapperGenerator in the 
 * <a href="https://github.com/hyperledger/web3j/tree/main/codegen">codegen module</a> to update.
 *
 * <p>Generated with web3j version 1.6.1.
 */
@SuppressWarnings("rawtypes")
public class FatbotUtil extends Contract {
    public static final String BINARY = "Bin file was not provided";

    public static final String FUNC_GETAMOUNTOUTV2 = "getAmountOutV2";

    public static final String FUNC_GETAMOUNTOUTV3 = "getAmountOutV3";

    public static final String FUNC_GETERC20BALANCES = "getErc20Balances";

    public static final String FUNC_GETETHBALANCES = "getEthBalances";

    public static final String FUNC_GETETHPRICEFORTOKENS = "getEthPriceForTokens";

    public static final String FUNC_GETPOOLV2 = "getPoolV2";

    public static final String FUNC_GETPOOLV3 = "getPoolV3";

    @Deprecated
    protected FatbotUtil(String contractAddress, Web3j web3j, Credentials credentials,
                             BigInteger gasPrice, BigInteger gasLimit) {
        super(BINARY, contractAddress, web3j, credentials, gasPrice, gasLimit);
    }

    protected FatbotUtil(String contractAddress, Web3j web3j, Credentials credentials,
                             ContractGasProvider contractGasProvider) {
        super(BINARY, contractAddress, web3j, credentials, contractGasProvider);
    }

    @Deprecated
    protected FatbotUtil(String contractAddress, Web3j web3j,
                             TransactionManager transactionManager, BigInteger gasPrice, BigInteger gasLimit) {
        super(BINARY, contractAddress, web3j, transactionManager, gasPrice, gasLimit);
    }

    protected FatbotUtil(String contractAddress, Web3j web3j,
                             TransactionManager transactionManager, ContractGasProvider contractGasProvider) {
        super(BINARY, contractAddress, web3j, transactionManager, contractGasProvider);
    }

    public RemoteFunctionCall<BigInteger> getAmountOutV2(BigInteger amountIn, String tokenAddress,
                                                         Boolean buy) {
        final Function function = new Function(FUNC_GETAMOUNTOUTV2,
                                               Arrays.<Type>asList(new org.web3j.abi.datatypes.generated.Uint256(amountIn),
                                                                   new org.web3j.abi.datatypes.Address(160, tokenAddress),
                                                                   new org.web3j.abi.datatypes.Bool(buy)),
                                               Arrays.<TypeReference<?>>asList(new TypeReference<Uint256>() {}));
        return executeRemoteCallSingleValueReturn(function, BigInteger.class);
    }

    public RemoteFunctionCall<BigInteger> getAmountOutV3(String tokenIn, String tokenOut,
                                                         BigInteger fee, BigInteger amountIn) {
        final Function function = new Function(FUNC_GETAMOUNTOUTV3,
                                               Arrays.<Type>asList(new org.web3j.abi.datatypes.Address(160, tokenIn),
                                                                   new org.web3j.abi.datatypes.Address(160, tokenOut),
                                                                   new org.web3j.abi.datatypes.generated.Uint24(fee),
                                                                   new org.web3j.abi.datatypes.generated.Uint256(amountIn)),
                                               Arrays.<TypeReference<?>>asList(new TypeReference<Uint256>() {}));
        return executeRemoteCallSingleValueReturn(function, BigInteger.class);
    }

    public RemoteFunctionCall<List> getErc20Balances(List<String> tokenAddresses,
                                                     String userAddress) {
        final Function function = new Function(FUNC_GETERC20BALANCES,
                                               Arrays.<Type>asList(new org.web3j.abi.datatypes.DynamicArray<org.web3j.abi.datatypes.Address>(
                                                                           org.web3j.abi.datatypes.Address.class,
                                                                           org.web3j.abi.Utils.typeMap(tokenAddresses, org.web3j.abi.datatypes.Address.class)),
                                                                   new org.web3j.abi.datatypes.Address(160, userAddress)),
                                               Arrays.<TypeReference<?>>asList(new TypeReference<DynamicArray<Uint256>>() {}));
        return new RemoteFunctionCall<List>(function,
                                            new Callable<List>() {
                                                @Override
                                                @SuppressWarnings("unchecked")
                                                public List call() throws Exception {
                                                    List<Type> result = (List<Type>) executeCallSingleValueReturn(function, List.class);
                                                    return convertToNative(result);
                                                }
                                            });
    }

    public RemoteFunctionCall<List> getEthBalances(List<String> userAddresses) {
        final Function function = new Function(FUNC_GETETHBALANCES,
                                               Arrays.<Type>asList(new org.web3j.abi.datatypes.DynamicArray<org.web3j.abi.datatypes.Address>(
                                                       org.web3j.abi.datatypes.Address.class,
                                                       org.web3j.abi.Utils.typeMap(userAddresses, org.web3j.abi.datatypes.Address.class))),
                                               Arrays.<TypeReference<?>>asList(new TypeReference<DynamicArray<Uint256>>() {}));
        return new RemoteFunctionCall<List>(function,
                                            new Callable<List>() {
                                                @Override
                                                @SuppressWarnings("unchecked")
                                                public List call() throws Exception {
                                                    List<Type> result = (List<Type>) executeCallSingleValueReturn(function, List.class);
                                                    return convertToNative(result);
                                                }
                                            });
    }

    public RemoteFunctionCall<Tuple2<List<String>, List<BigInteger>>> getEthPriceForTokens(
            List<String> tokenAddresses) {
        final Function function = new Function(FUNC_GETETHPRICEFORTOKENS,
                                               Arrays.<Type>asList(new org.web3j.abi.datatypes.DynamicArray<org.web3j.abi.datatypes.Address>(
                                                       org.web3j.abi.datatypes.Address.class,
                                                       org.web3j.abi.Utils.typeMap(tokenAddresses, org.web3j.abi.datatypes.Address.class))),
                                               Arrays.<TypeReference<?>>asList(new TypeReference<DynamicArray<Address>>() {}, new TypeReference<DynamicArray<Uint256>>() {}));
        return new RemoteFunctionCall<Tuple2<List<String>, List<BigInteger>>>(function,
                                                                              new Callable<Tuple2<List<String>, List<BigInteger>>>() {
                                                                                  @Override
                                                                                  public Tuple2<List<String>, List<BigInteger>> call() throws Exception {
                                                                                      List<Type> results = executeCallMultipleValueReturn(function);
                                                                                      return new Tuple2<List<String>, List<BigInteger>>(
                                                                                              convertToNative((List<Address>) results.get(0).getValue()),
                                                                                              convertToNative((List<Uint256>) results.get(1).getValue()));
                                                                                  }
                                                                              });
    }

    public RemoteFunctionCall<String> getPoolV2(String tokenA, String tokenB) {
        final Function function = new Function(FUNC_GETPOOLV2,
                                               Arrays.<Type>asList(new org.web3j.abi.datatypes.Address(160, tokenA),
                                                                   new org.web3j.abi.datatypes.Address(160, tokenB)),
                                               Arrays.<TypeReference<?>>asList(new TypeReference<Address>() {}));
        return executeRemoteCallSingleValueReturn(function, String.class);
    }

    public RemoteFunctionCall<String> getPoolV3(String tokenA, String tokenB, BigInteger fee) {
        final Function function = new Function(FUNC_GETPOOLV3,
                                               Arrays.<Type>asList(new org.web3j.abi.datatypes.Address(160, tokenA),
                                                                   new org.web3j.abi.datatypes.Address(160, tokenB),
                                                                   new org.web3j.abi.datatypes.generated.Uint24(fee)),
                                               Arrays.<TypeReference<?>>asList(new TypeReference<Address>() {}));
        return executeRemoteCallSingleValueReturn(function, String.class);
    }

    @Deprecated
    public static FatbotUtil load(String contractAddress, Web3j web3j, Credentials credentials,
                                      BigInteger gasPrice, BigInteger gasLimit) {
        return new FatbotUtil(contractAddress, web3j, credentials, gasPrice, gasLimit);
    }

    @Deprecated
    public static FatbotUtil load(String contractAddress, Web3j web3j,
                                      TransactionManager transactionManager, BigInteger gasPrice, BigInteger gasLimit) {
        return new FatbotUtil(contractAddress, web3j, transactionManager, gasPrice, gasLimit);
    }

    public static FatbotUtil load(String contractAddress, Web3j web3j, Credentials credentials,
                                      ContractGasProvider contractGasProvider) {
        return new FatbotUtil(contractAddress, web3j, credentials, contractGasProvider);
    }

    public static FatbotUtil load(String contractAddress, Web3j web3j,
                                      TransactionManager transactionManager, ContractGasProvider contractGasProvider) {
        return new FatbotUtil(contractAddress, web3j, transactionManager, contractGasProvider);
    }
}
