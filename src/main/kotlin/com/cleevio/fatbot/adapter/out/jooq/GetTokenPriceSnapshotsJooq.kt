package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.tokenpricesnapshot.port.out.GetTokenPriceSnapshots
import com.cleevio.fatbot.tables.references.TOKEN_PRICE_SNAPSHOT
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.jooq.kotlin.get
import org.springframework.stereotype.Component
import java.time.Instant

private const val TOKEN_ADDRESSES_SUB_TABLE_ALIAS = "token_addresses"
private const val TOKEN_ADDRESS_FIELD_NAME = "ta"
private const val CHAIN_FIELD_NAME = "chid"

private const val LATERAL_ALIAS = "lat"

@Component
class GetTokenPriceSnapshotsJooq(private val dslContext: DSLContext) : GetTokenPriceSnapshots {

	/**
	 * Converts [AddressWrapper] to Postgres `bytea` address representation
	 * e.g. on EVM `******************************************` -> `'\x000000000000000000000000000000000000000a'`
	 *
	 * Or in Solana
	 * `6AJcP7wuLwmRYLBNbi825wgguaPsWzPBEHcHndpRpump` -> `'\x4cac92d976a3333594696d584b966514f09cbdc887446b9728806e4505a8342f'`
	 */
	@OptIn(ExperimentalStdlibApi::class)
	private fun AddressWrapper.toSqlRepresentation(): String {
		val address = getAddressBytes().toHexString()
		return "'\\x$address'"
	}

	override fun invoke(tokens: Set<ChainAddress>, before: Instant): Map<ChainAddress, GetTokenPriceSnapshots.Result> {
		// creates string of this format:
		// ['\x000000000000000000000000000000000000000a', '\x000000000000000000000000000000000000000b']
		val tokenAddressesArraySqlRepresentation = tokens.map { it.address.toSqlRepresentation() }.toString()

		// creates array of this format: ['EVM_MAINNET', 'EVM_MAINNET', 'EVM_BASE', 'EVM_BSC', 'EVM_ARBITRUM_ONE']
		val chainArraySqlRepresentation = tokens.map { "'${it.chain}'" }.toString()

		// this SQL selects each item from array as a row, with column name of TOKEN_ADDRESS_FIELD_NAME
		val tokenAddressesTableSql = DSL.sql(
			"""(
				SELECT
					unnest( array $tokenAddressesArraySqlRepresentation ::bytea[] ) as $TOKEN_ADDRESS_FIELD_NAME,
					unnest( array $chainArraySqlRepresentation ::text[] ) as $CHAIN_FIELD_NAME
			) as $TOKEN_ADDRESSES_SUB_TABLE_ALIAS
			""".trimIndent(),
		)

		// With this object we can retrieve the tokenAddress in a type safe way
		val tokenAddressField = DSL
			.field("$TOKEN_ADDRESSES_SUB_TABLE_ALIAS.$TOKEN_ADDRESS_FIELD_NAME")
			.cast(TOKEN_PRICE_SNAPSHOT.TOKEN_ADDRESS.dataType)
			.`as`(TOKEN_ADDRESS_FIELD_NAME)

		val chainField = DSL
			.field("$TOKEN_ADDRESSES_SUB_TABLE_ALIAS.$CHAIN_FIELD_NAME")
			.cast(TOKEN_PRICE_SNAPSHOT.CHAIN.dataType)
			.`as`(CHAIN_FIELD_NAME)

		// Lateral join will do a select for each row, so for each tokenAddress this select inside lateral is ran
		// getting the first value that is valid or null
		// This approach is used as it's magnitudes faster because it's using index scan only
		val lateral = DSL.lateral(
			DSL
				.select(
					TOKEN_PRICE_SNAPSHOT.TOKEN_ADDRESS,
					TOKEN_PRICE_SNAPSHOT.CHAIN,
					TOKEN_PRICE_SNAPSHOT.PRICE_WEI,
					TOKEN_PRICE_SNAPSHOT.EXCHANGE_RATE_USD,
				)
				.from(TOKEN_PRICE_SNAPSHOT)
				.where(
					TOKEN_PRICE_SNAPSHOT.CHAIN.eq(chainField),
					TOKEN_PRICE_SNAPSHOT.VALID_AT.le(before),
					TOKEN_PRICE_SNAPSHOT.TOKEN_ADDRESS.eq(tokenAddressField),
				)
				.orderBy(TOKEN_PRICE_SNAPSHOT.VALID_AT.desc())
				.limit(1),
		).`as`(LATERAL_ALIAS)

		val tokenAddressToPriceWei = dslContext
			.select(
				chainField,
				tokenAddressField,
				lateral[TOKEN_PRICE_SNAPSHOT.PRICE_WEI],
				lateral[TOKEN_PRICE_SNAPSHOT.EXCHANGE_RATE_USD],
			)
			.from(tokenAddressesTableSql)
			.leftJoin(lateral)
			.on(tokenAddressField.eq(lateral[TOKEN_PRICE_SNAPSHOT.TOKEN_ADDRESS]))
			.and(chainField.eq(lateral[TOKEN_PRICE_SNAPSHOT.CHAIN]))
			.fetch()
			.filter { it[TOKEN_PRICE_SNAPSHOT.PRICE_WEI] != null } // we filter out any tokens without price
			.associate {
				val price = it[TOKEN_PRICE_SNAPSHOT.PRICE_WEI]!!.toBigInteger().asBaseAmount()
				val exchangeRateUsd = it[TOKEN_PRICE_SNAPSHOT.EXCHANGE_RATE_USD]!!
				val chainAddress = it[tokenAddressField]!!.toChainAddress(it[chainField]!!)

				chainAddress to GetTokenPriceSnapshots.Result(price = price, exchangeRateUsd = exchangeRateUsd)
			}

		return tokenAddressToPriceWei
	}

	override fun getSingleOrNull(token: ChainAddress, before: Instant): GetTokenPriceSnapshots.Result? {
		return dslContext
			.select(
				TOKEN_PRICE_SNAPSHOT.PRICE_WEI,
				TOKEN_PRICE_SNAPSHOT.EXCHANGE_RATE_USD,
			)
			.from(TOKEN_PRICE_SNAPSHOT)
			.where(
				TOKEN_PRICE_SNAPSHOT.CHAIN.eq(token.chain),
				TOKEN_PRICE_SNAPSHOT.VALID_AT.le(before),
				TOKEN_PRICE_SNAPSHOT.TOKEN_ADDRESS.eq(token.address),
			)
			.orderBy(TOKEN_PRICE_SNAPSHOT.VALID_AT.desc())
			.limit(1)
			.fetchOne()
			?.let {
				val price = it[TOKEN_PRICE_SNAPSHOT.PRICE_WEI]!!.toBigInteger().asBaseAmount()
				val exchangeRateUsd = it[TOKEN_PRICE_SNAPSHOT.EXCHANGE_RATE_USD]!!

				GetTokenPriceSnapshots.Result(price = price, exchangeRateUsd = exchangeRateUsd)
			}
	}
}
