package com.cleevio.fatbot.adapter.out.solana.raydium

import com.cleevio.fatbot.adapter.out.solana.client.getTokenAccountBalances
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.stereotype.Component
import java.math.MathContext

@Component
class GetRaydiumAMMPrice(
	private val rpcClient: RpcClient,
) {

	data class Input(
		val pairAddress: AddressWrapper,
		val tokenAddress: AddressWrapper,
		val wsolVault: AddressWrapper,
		val tokenVault: AddressWrapper,
		val tokenDecimals: Int,
	)

	fun getMany(inputs: List<Input>): Map<AddressWrapper, LiquidityPricePair?> {
		if (inputs.isEmpty()) return emptyMap()

		val accounts = inputs.flatMap {
			listOf(it.wsolVault.getSolanaPublicKey(), it.tokenVault.getSolanaPublicKey())
		}

		val balances = rpcClient.getTokenAccountBalances(accounts, Commitment.CONFIRMED).map { it?.toBigDecimal() }

		val liquidityPriceRaw = balances.chunked(2) { (wsolBalance, tokenBalance) ->
			if (wsolBalance == null || tokenBalance == null) return@chunked null

			val liquidity = wsolBalance.times(tokenBalance).sqrt(MathContext.DECIMAL128)
			val price = wsolBalance.divide(tokenBalance, MathContext.DECIMAL128)

			LiquidityPricePair(liquidity, price.asNativeAmount())
		}

		val pairAddressToLiquidityPricePair = inputs.zip(liquidityPriceRaw) { input, liquidityPrice ->
			val priceAdjustedPair = liquidityPrice?.copy(
				// 1 Token (Native unit) -> x SOL (Native)
				price = liquidityPrice.price.toBase(input.tokenDecimals).toNative(Chain.SOLANA),
			)

			input.pairAddress to priceAdjustedPair
		}.toMap()

		return pairAddressToLiquidityPricePair
	}
}
