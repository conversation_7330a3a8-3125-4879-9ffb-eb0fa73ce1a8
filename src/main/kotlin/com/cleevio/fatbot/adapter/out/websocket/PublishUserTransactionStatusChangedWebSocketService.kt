package com.cleevio.fatbot.adapter.out.websocket

import com.cleevio.fatbot.adapter.out.websocket.constant.WebSocketMessageType
import com.cleevio.fatbot.adapter.out.websocket.dto.TransactionStatusChangedWebSocketResponse
import com.cleevio.fatbot.adapter.out.websocket.dto.WebSocketMessage
import com.cleevio.fatbot.application.module.transaction.port.out.PublishUserTransactionStatusChanged
import com.cleevio.fatbot.application.module.transaction.port.out.model.TransactionStatusModel
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class PublishUserTransactionStatusChangedWebSocketService(
	private val webSocketMessagingService: WebSocketMessagingService,
) : PublishUserTransactionStatusChanged {

	override fun invoke(userId: UUID, transactions: List<TransactionStatusModel>) {
		webSocketMessagingService.sendMessageToUser(
			user = userId.toString(),
			destination = "/queue/transactions",
			message = WebSocketMessage(
				type = WebSocketMessageType.TRANSACTION_STATUS_CHANGED,
				data = transactions.map {
					TransactionStatusChangedWebSocketResponse(
						transactionId = it.transactionId,
						transactionStatus = it.transactionStatus,
						txHash = it.txHash,
					)
				},
			),
		)
	}
}
