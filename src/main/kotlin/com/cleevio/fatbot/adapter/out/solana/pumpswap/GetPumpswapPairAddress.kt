package com.cleevio.fatbot.adapter.out.solana.pumpswap

import com.cleevio.fatbot.adapter.out.solana.client.getTokenAccountBalances
import com.cleevio.fatbot.adapter.out.solana.pumpswap.model.PumpswapPool
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.toAddress
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.syntifi.near.borshj.BorshBuffer
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.DataSizeFilter
import org.p2p.solanaj.rpc.types.Memcmp
import org.p2p.solanaj.rpc.types.MemcmpFilter
import org.p2p.solanaj.rpc.types.config.Commitment
import org.p2p.solanaj.rpc.types.config.DataSlice
import org.p2p.solanaj.rpc.types.config.ProgramAccountConfig
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class GetPumpswapPairAddress(
	private val rpcClient: RpcClient,
) {

	@SentrySpan
	operator fun invoke(tokenAddress: AddressWrapper): AddressWrapper? {
		val accounts = rpcClient.api.getProgramAccounts(
			SolanaConstants.PUMPSWAP_PROGRAM,
			ProgramAccountConfig(
				listOf(
					MemcmpFilter(Memcmp(PumpswapPool.BASE_MINT_OFFSET, tokenAddress.getAddressString())),
					MemcmpFilter(Memcmp(PumpswapPool.QUOTE_MINT_OFFSET, SolanaConstants.WSOL_MINT.toBase58())),
					DataSizeFilter(PumpswapPool.SIZE),
				),
				DataSlice(PumpswapPool.POOL_QUOTE_TOKEN_ACCOUNT_OFFSET, PublicKey.PUBLIC_KEY_LENGTH.toLong()),
			),
		)

		val pairAddress = if (accounts.size > 1) {
			val wsolTokenAccounts = accounts.map {
				PublicKey(BorshBuffer.wrap(it.account.decodedData).read(PublicKey.PUBLIC_KEY_LENGTH))
			}

			val balances = rpcClient.getTokenAccountBalances(wsolTokenAccounts, Commitment.CONFIRMED)

			// Select the pair with the highest WSOL token account balance
			accounts.zip(balances).maxBy { (_, balance) -> balance ?: BigInteger.ZERO }.first.publicKey
		} else {
			accounts.singleOrNull()?.publicKey
		}

		return pairAddress?.toAddress()
	}
}
