package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.crypto.Lamport
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.port.out.DecodeSignedTransaction
import com.cleevio.fatbot.domain.transaction.Transaction
import org.p2p.solanaj.utils.TransactionSignatureParserUtil
import org.springframework.stereotype.Component
import java.math.BigInteger
import java.util.UUID

@Component
class DecodeSolanaSignedTransaction : DecodeSignedTransaction {

	override fun invoke(
		walletId: UUID,
		signedTx: SignedTx,
		transactionType: TransactionType,
		tokenAddress: AddressWrapper?,
	): Transaction {
		assert(transactionType in listOf(TransactionType.BUY, TransactionType.SELL)) {
			"Unsupported transaction type: $transactionType"
		}

		val signatures = getSignaturesFromTransaction(signedTx)
		assert(signatures.size == 1) {
			"Can't process Solana transactions with multiple signers"
		}

		return Transaction.ofSvm(
			walletId = walletId,
			type = transactionType,
			baseValue = Lamport(BigInteger.ZERO), // TODO: Check this later?
			signedTx = signedTx,
			tokenAddress = tokenAddress,
			signature = signatures.first(),
		)
	}

	private fun getSignaturesFromTransaction(signedTx: SignedTx): List<String> {
		return TransactionSignatureParserUtil.parseSignaturesFromTransaction(signedTx.signedTx)
	}
}
