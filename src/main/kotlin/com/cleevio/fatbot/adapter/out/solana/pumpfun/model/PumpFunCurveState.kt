package com.cleevio.fatbot.adapter.out.solana.pumpfun.model

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.util.readAddress
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.syntifi.near.borshj.BorshBuffer
import java.math.BigDecimal

data class PumpFunCurveState(
	val virtualTokenReserves: BaseAmount,
	val virtualSolReserves: BaseAmount,
	val realTokenReserves: BaseAmount,
	val realSolReserves: BaseAmount,
	val tokenTotalSupply: BaseAmount,
	val complete: <PERSON><PERSON><PERSON>,
	val creator: AddressWrapper?,
) {

	companion object {

		private const val BYTES_OLD_CURVE = 49
		private const val BYTES_CURVE = 81 // exact size for all fields + creator

		// Calculated as the first 8 bytes of: `sha256("account:BondingCurve")`.
		private val SIGNATURE = listOf(0x17, 0xb7, 0xf8, 0x37, 0x60, 0xd8, 0xac, 0x60).map(Int::toByte)

		const val TOKEN_DECIMALS = 6

		fun read(bytes: ByteArray, curveAddress: AddressWrapper): PumpFunCurveState {
			check(bytes.size == BYTES_OLD_CURVE || bytes.size >= BYTES_CURVE) {
				"Invalid pumpfun curve bytes size (${bytes.size}) on curve: $curveAddress"
			}

			val buffer = BorshBuffer.wrap(bytes)

			val signatureBytes = buffer.read(SIGNATURE.size)
			check(signatureBytes.contentEquals(SIGNATURE.toByteArray())) {
				"Invalid bonding curve signature bytes $signatureBytes"
			}

			val virtualTokenReserves = buffer.readU64().toBigInteger().asBaseAmount()
			val virtualSolReserves = buffer.readU64().toBigInteger().asBaseAmount()
			val realTokenReserves = buffer.readU64().toBigInteger().asBaseAmount()
			val realSolReserves = buffer.readU64().toBigInteger().asBaseAmount()
			val tokenTotalSupply = buffer.readU64().toBigInteger().asBaseAmount()
			val complete = buffer.readBoolean()
			val creator = if (bytes.size == BYTES_OLD_CURVE) null else buffer.readAddress()

			return PumpFunCurveState(
				virtualTokenReserves = virtualTokenReserves,
				virtualSolReserves = virtualSolReserves,
				realTokenReserves = realTokenReserves,
				realSolReserves = realSolReserves,
				tokenTotalSupply = tokenTotalSupply,
				complete = complete,
				creator = creator,
			)
		}

		fun fromVirtual(
			virtualSolReserves: BaseAmount,
			virtualTokenReserves: BaseAmount,
			creator: AddressWrapper,
		): PumpFunCurveState {
			return PumpFunCurveState(
				virtualTokenReserves = virtualTokenReserves,
				virtualSolReserves = virtualSolReserves,
				// TODO: We don't receive real token reserves from token aggregator - arbitrary value with less accuracy
				realTokenReserves = NativeAmount(BigDecimal(100)).toBase(chain = Chain.SOLANA),
				// TODO: We don't receive real token reserves from token aggregator - arbitrary value with less accuracy
				realSolReserves = NativeAmount(BigDecimal(100)).toBase(chain = Chain.SOLANA),
				// We don't care about those values for bot trading - setting manually
				tokenTotalSupply = NativeAmount(BigDecimal(0)).toBase(chain = Chain.SOLANA),
				// We don't care about those values for bot trading - setting manually
				complete = false,
				creator = creator,
			)
		}
	}
}
