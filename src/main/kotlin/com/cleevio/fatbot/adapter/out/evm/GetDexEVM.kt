package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.dexscreener.EVM_ALLOWED_DEX_IDS
import com.cleevio.fatbot.adapter.out.dexscreener.EVM_BSC_ALLOWED_DEX_IDS
import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component

@Component
class GetDexEVM(
	private val evmChainContextFactory: EvmChainContextFactory,
) : GetDex {

	override fun supports() = ChainType.EVM

	@SentrySpan
	override fun invoke(
		dexId: String,
		pairAddress: AddressWrapper,
		version: GetDex.Version?,
		poolType: GetDex.PoolType?,
		chain: Chain,
	): GetDex.Result {
		require(pairAddress.isEvm())
		require(chain in Chain.allEvm())
		requireNotNull(version)

		return when (chain) {
			Chain.EVM_MAINNET, Chain.EVM_BASE, Chain.EVM_ARBITRUM_ONE -> {
				require(dexId in EVM_ALLOWED_DEX_IDS)
				when (version) {
					GetDex.Version.V2 -> getUniswapV2DexPairInfo(pairAddress = pairAddress)
					GetDex.Version.V3 -> getUniswapV3DexPairInfo(pairAddress = pairAddress, chainId = chain.evmId)
				}
			}

			Chain.EVM_BSC -> {
				require(dexId in EVM_BSC_ALLOWED_DEX_IDS)
				when (version) {
					GetDex.Version.V2 -> getPancakeswapV2DexPairInfo(pairAddress = pairAddress)
					GetDex.Version.V3 -> getPancakeswapV3DexPairInfo(pairAddress = pairAddress, chainId = chain.evmId)
				}
			}

			Chain.SOLANA -> error("Not supported")
		}
	}

	// no need to do or compute anything
	private fun getUniswapV2DexPairInfo(pairAddress: AddressWrapper) = GetDex.UniswapV2(pairAddress = pairAddress)

	// no need to do or compute anything
	private fun getPancakeswapV2DexPairInfo(pairAddress: AddressWrapper) = GetDex.PancakeswapV2(pairAddress = pairAddress)

	private fun getUniswapV3DexPairInfo(pairAddress: AddressWrapper, chainId: Long) =
		evmChainContextFactory.ofChainId(chainId) {
			val fee = client.getFeeOfUniswapV3Pool(poolAddress = pairAddress)

			GetDex.UniswapV3(
				pairAddress = pairAddress,
				fee = fee,
			)
		}

	private fun getPancakeswapV3DexPairInfo(pairAddress: AddressWrapper, chainId: Long) =
		evmChainContextFactory.ofChainId(chainId) {
			val fee = client.getFeeOfUniswapV3Pool(poolAddress = pairAddress)

			GetDex.PancakeswapV3(
				pairAddress = pairAddress,
				fee = fee,
			)
		}
}
