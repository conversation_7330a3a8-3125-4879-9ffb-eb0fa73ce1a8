package com.cleevio.fatbot.adapter.out.solana.pumpswap

import com.cleevio.fatbot.adapter.out.solana.pumpswap.model.PumpswapPool
import com.cleevio.fatbot.application.common.util.decodeData
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.stereotype.Component

@Component
class GetPumpswapPool(private val rpcClient: RpcClient) {

	operator fun invoke(address: AddressWrapper): PumpswapPool {
		val poolAccountInfo = rpcClient.api.getAccountInfo(
			address.getSolanaPublicKey(),
			mapOf("commitment" to Commitment.CONFIRMED),
		)

		val pool = PumpswapPool.read(poolAccountInfo.decodedData)

		return pool
	}

	fun getMany(addresses: List<AddressWrapper>): Map<AddressWrapper, PumpswapPool?> {
		val accounts = rpcClient.api.getMultipleAccountsOptional(addresses.map { it.getSolanaPublicKey() }, mapOf())

		return addresses.zip(accounts) { address, account ->
			val poolResult = runCatching { PumpswapPool.read(account.get().decodeData()) }.getOrNull()

			address to poolResult
		}.toMap()
	}
}
