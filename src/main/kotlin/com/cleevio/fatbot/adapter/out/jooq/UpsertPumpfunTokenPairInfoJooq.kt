package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.adapter.out.solana.pumpfun.util.getPumpfunBondingCurvePDA
import com.cleevio.fatbot.application.common.crypto.toAddress
import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.matchmaking.port.out.UpsertPumpfunTokenPairInfo
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.TOKEN_PAIR_INFO
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Clock
import java.time.Instant

@Component
class UpsertPumpfunTokenPairInfoJooq(
	private val dslContext: DSLContext,
	private val clock: Clock,
) : UpsertPumpfunTokenPairInfo {

	override fun invoke(tokenAddresses: Set<AddressWrapper>) {
		val now = Instant.now(clock)
		val rows = tokenAddresses.map {
			DSL.row(
				UUIDv7(),
				Chain.SOLANA,
				GetDex.Dex.PUMP_FUN,
				it,
				getPumpfunBondingCurvePDA(it).toAddress(),
				now,
				now,
				0,
				BigDecimal.valueOf(6),
			)
		}

		dslContext
			.insertInto(
				TOKEN_PAIR_INFO,
				TOKEN_PAIR_INFO.ID,
				TOKEN_PAIR_INFO.CHAIN,
				TOKEN_PAIR_INFO.DEX_TYPE,
				TOKEN_PAIR_INFO.TOKEN_ADDRESS,
				TOKEN_PAIR_INFO.PAIR_ADDRESS,
				TOKEN_PAIR_INFO.CREATED_AT,
				TOKEN_PAIR_INFO.UPDATED_AT,
				TOKEN_PAIR_INFO.VERSION,
				TOKEN_PAIR_INFO.TOKEN_DECIMALS,
			)
			.valuesOfRows(rows)
			.onConflict(TOKEN_PAIR_INFO.PAIR_ADDRESS)
			.doNothing()
			.execute()
	}
}
