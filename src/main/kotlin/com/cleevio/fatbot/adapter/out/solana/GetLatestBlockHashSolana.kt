package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.service.AbstractLiveDataService
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentrySpan
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class GetLatestBlockHashSolana(
	private val latestBlockHashLiveData: LatestBlockHashLiveData,
) {

	private val logger = logger()

	/**
	 * Returns latest block hash.
	 */
	@SentrySpan
	operator fun invoke(): String {
		return latestBlockHashLiveData.get()
	}

	@SentryTransaction(operation = "scheduled.get-latest-block-hash")
	@Scheduled(cron = "0/5 * * * * *")
	fun trigger() {
		logger.info("GET_LATEST_BLOCK_HASH cron started")
		val result = latestBlockHashLiveData.tryRefreshAndGet()
		logger.info("GET_LATEST_BLOCK_HASH cron ended. Set result: $result")
	}
}

@Component
class LatestBlockHashLiveData(
	private val rpcClient: RpcClient,
) : AbstractLiveDataService<String>() {

	override fun computeNewValue(): String {
		return rpcClient.api.latestBlockhash.value.blockhash
	}
}
