package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionSellReason
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.matchmaking.port.out.SetBotMarketPositionState
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class SetBotMarketPositionStateJooq(
	private val dslContext: DSLContext,
) : SetBotMarketPositionState {

	override fun invoke(
		botMarketPositionId: UUID,
		state: BotMarketPositionState,
		sellReason: BotMarketPositionSellReason?,
	) {
		dslContext
			.update(BOT_MARKET_POSITION)
			.set(BOT_MARKET_POSITION.STATE, state)
			.also { sqlChain -> sellReason?.let { sqlChain.set(BOT_MARKET_POSITION.SELL_REASON, sellReason) } }
			.where(BOT_MARKET_POSITION.ID.eq(botMarketPositionId))
			.execute()
	}
}
