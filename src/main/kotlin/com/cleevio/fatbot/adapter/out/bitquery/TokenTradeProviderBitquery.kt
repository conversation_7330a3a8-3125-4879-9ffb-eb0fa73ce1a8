package com.cleevio.fatbot.adapter.out.bitquery

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.event.EVMTokenTradedEvent
import com.cleevio.fatbot.application.module.token.event.SolanaTokenTradedEvent
import com.cleevio.fatbot.application.module.token.event.TokenTradedEvent
import com.cleevio.fatbot.application.module.wallet.constant.O_X
import com.cleevio.fatbot.infrastructure.config.logger
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds

/**
 * A common interface that shares access to [TokenTradedEvent] flow
 * Each entry represents a collection of trades from single block.
 *
 * Exposes a single [getTokenTradeFlow] that grants read-only access to the trades.
 */
interface TokenTradeProvider {
	fun getTokenTradeFlow(): SharedFlow<List<TokenTradedEvent>>
}

@Component
class TokenTradeProviderBitquery(
	private val bitqueryEapConnector: BitqueryEapConnector,
	private val bitqueryApiV2Connector: BitqueryApiV2Connector,
) : TokenTradeProvider {

	private val logger = logger()
	private val scope = CoroutineScope(
		Dispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	private val processSolanaTradeChannel: Channel<List<TokenTradedEvent>> = Channel(capacity = Channel.UNLIMITED)
	private val processEVMTradeChannel: Channel<List<TokenTradedEvent>> = Channel(capacity = Channel.UNLIMITED)

	private val tokenTradesFlow = MutableSharedFlow<List<TokenTradedEvent>>(
		replay = 0,
		extraBufferCapacity = 64,
		onBufferOverflow = BufferOverflow.DROP_OLDEST,
	)

	private val tokenTradesSharedFlow: SharedFlow<List<TokenTradedEvent>> = tokenTradesFlow.asSharedFlow()

	override fun getTokenTradeFlow(): SharedFlow<List<TokenTradedEvent>> = tokenTradesSharedFlow

	// Keep the Solana and EVM jobs in separate channels so they won't block each other
	@EventListener(ApplicationReadyEvent::class)
	fun onStartup() {
		retryWithHeartBeat(heartBeatFrequency = 3.seconds) { heartBeatChannel ->
			subscribeToSolanaTrades(heartBeatChannel)
		}
		retryWithHeartBeat(heartBeatFrequency = 15.seconds) { heartBeatChannel ->
			subscribeToEVMTrades(heartBeatChannel)
		}

		scope.launch {
			processSolanaTokenTrades()
		}

		scope.launch {
			processEVMTokenTrades()
		}
	}

	private fun onSolanaTokenTradedEvent(trades: List<TokenTradedEvent>) {
		runBlocking {
			processSolanaTradeChannel.send(trades)
		}
	}

	private fun onEVMTokenTradedEvent(trades: List<TokenTradedEvent>) {
		runBlocking {
			processEVMTradeChannel.send(trades)
		}
	}

	suspend fun processSolanaTokenTrades() {
		for (trades in processSolanaTradeChannel) {
			tokenTradesFlow.emit(trades)
		}
	}

	suspend fun processEVMTokenTrades() {
		for (trades in processEVMTradeChannel) {
			tokenTradesFlow.emit(trades)
		}
	}

	private fun retryWithHeartBeat(heartBeatFrequency: Duration, block: suspend (Channel<Any>) -> Unit) {
		// This is JobScope and this will run forever/ no exception can happen in this one
		scope.launch {
			// We create new inner scope, that is only Job, so any exception will kill it immediately
			val jobScope = CoroutineScope(
				Dispatchers.IO +
					Job() +
					CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
			)
			val monitoringScope = CoroutineScope(
				Dispatchers.IO +
					Job() +
					CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
			)

			monitoringScope.launch {
				while (jobScope.isActive) {
					delay(100.milliseconds)
				}

				// At this point job scope was cancelled, so we handle relaunch of everything here
				retryWithHeartBeat(heartBeatFrequency, block)

				// and finally kill this scope to finish cleanup
				this.cancel()
			}

			// this will be the main logic
			jobScope.launch {
				// this will be the heart beat for the main job
				val heartBeat = Channel<Any>(onBufferOverflow = BufferOverflow.DROP_OLDEST)

				jobScope.launch {
					while (true) {
						delay(heartBeatFrequency)
						if (heartBeat.tryReceive().isSuccess.not()) {
							error("Bitquery subscribe timeout likely, going to reset the processing")
						}
					}
				}

				jobScope.launch {
					block(heartBeat)
				}
			}
		}
	}

	private suspend fun subscribeToSolanaTrades(heartBeatChannel: Channel<Any>) {
		logger.info("Subscribing to SOLANA token trades.")
		bitqueryEapConnector.subscribeToSolanaTokenTrades().collect { response ->
			heartBeatChannel.send(Any())
			val trades = response.solana.dextrades.map { trade ->
				SolanaTokenTradedEvent(
					tokenAddress = AddressWrapper(trade.trade.buy.currency.mintAddress),
					priceUsd = trade.trade.buy.priceInUSD.toBigDecimal(),
					amountUsd = trade.trade.buy.amountInUSD.toBigDecimal(),
					timestamp = trade.block.time,
				)
			}

			onSolanaTokenTradedEvent(trades)
		}
	}

	private suspend fun subscribeToEVMTrades(heartBeatChannel: Channel<Any>) {
		logger.info("Subscribing to EVM token trades.")
		bitqueryApiV2Connector.subscribeToEvmTokenTrades().collect { response ->
			heartBeatChannel.send(Any())
			val trades = response.evm.dextrades.flatMap { trade ->
				// Note: 0x == ETH by bitquery standards, not WETH, but it is sent to us by bitquery anyway,
				//  for now we filter it out.
				if (trade.trade.buy.currency.smartContract == O_X ||
					trade.trade.sell.currency.smartContract == O_X
				) {
					return@flatMap emptyList()
				}

				val buyTrade = EVMTokenTradedEvent(
					tokenAddress = AddressWrapper(trade.trade.buy.currency.smartContract),
					priceUsd = trade.trade.buy.priceInUSD.toBigDecimal(),
					amountUsd = trade.trade.buy.amountInUSD.toBigDecimal(),
					timestamp = trade.block.time,
					chainId = trade.chainId.toLong(),
				)

				val sellTrade = EVMTokenTradedEvent(
					tokenAddress = AddressWrapper(trade.trade.sell.currency.smartContract),
					priceUsd = trade.trade.sell.priceInUSD.toBigDecimal(),
					amountUsd = trade.trade.sell.amountInUSD.toBigDecimal(),
					timestamp = trade.block.time,
					chainId = trade.chainId.toLong(),
				)

				listOf(buyTrade, sellTrade)
			}

			onEVMTokenTradedEvent(trades)
		}
	}
}
