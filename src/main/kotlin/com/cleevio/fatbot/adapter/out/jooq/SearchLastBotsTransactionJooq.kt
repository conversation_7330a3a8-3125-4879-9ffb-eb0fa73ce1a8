package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.bottransaction.port.out.SearchLastBotsTransaction
import com.cleevio.fatbot.application.module.bottransaction.query.SearchUserBotsTransactionsQuery
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.BOT
import com.cleevio.fatbot.tables.references.BOT_TOKEN_INFO
import com.cleevio.fatbot.tables.references.BOT_TRANSACTION
import com.cleevio.fatbot.tables.references.BOT_WALLET
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.net.URI
import java.time.Instant
import java.util.UUID

@Component
class SearchLastBotsTransactionJooq(
	private val dslContext: DSLContext,
) : SearchLastBotsTransaction {

	override fun invoke(
		userId: UUID,
		botIds: Set<UUID>,
		from: Instant?,
		to: Instant?,
		txHashToURIMapper: (TxHash, Chain) -> URI,
		fileToUrlMapper: FileToUrlMapper,
	): Map<UUID, List<SearchUserBotsTransactionsQuery.Result>> {
		val botTransactionConditions = listOfNotNull(
			BOT_TRANSACTION.BOT_WALLET_ID.eq(BOT_WALLET.ID),
			BOT_TRANSACTION.TX_HASH.isNotNull,
			BOT_TRANSACTION.TYPE.`in`(BotTransactionType.userInitiatedTypes),
			takeIf { from != null && to != null }?.let { BOT_TRANSACTION.CREATED_AT.between(from, to) },
			BOT_TRANSACTION.TYPE.ne(BotTransactionType.DEPOSIT)
				.or(BOT_TRANSACTION.AMOUNT_IN.isNull)
				.or(BOT_TRANSACTION.AMOUNT_IN.ge(BigDecimal.TEN)),
		)

		val transactionMultiset = DSL.multiset(
			DSL.select(
				BOT_TRANSACTION.ID,
				BOT_TRANSACTION.TX_HASH,
				BOT_TRANSACTION.TYPE,
				BOT_TRANSACTION.STATUS,
				BOT_TRANSACTION.CREATED_AT,
				BOT_TRANSACTION.BOT_WALLET_ID,
				BOT_TRANSACTION.BASE_VALUE,
				BOT_TRANSACTION.EXCHANGE_RATE_USD,
				BOT_TRANSACTION.TOKEN_ADDRESS,
				BOT_TRANSACTION.PERCENTAGE_OF,
				BOT_TOKEN_INFO.NAME,
				BOT_TOKEN_INFO.SYMBOL,
			)
				.from(BOT_TRANSACTION)
				.leftJoin(BOT_TOKEN_INFO)
				.on(BOT_TOKEN_INFO.ADDRESS.eq(BOT_TRANSACTION.TOKEN_ADDRESS).and(BOT_TOKEN_INFO.CHAIN.eq(Chain.SOLANA)))
				.where(botTransactionConditions)
				.orderBy(BOT_TRANSACTION.ID.desc()) // Using UUIDv7 as createAt field
				.limit(1),
		)

		return dslContext
			.select(
				BOT.ID,
				BOT.NAME,
				BOT_WALLET.CHAIN,
				transactionMultiset,
			)
			.from(BOT)
			.leftJoin(BOT_WALLET).on(BOT_WALLET.BOT_ID.eq(BOT.ID))
			.where(BOT.ID.`in`(botIds))
			.fetch()
			.associate {
				it[BOT.ID]!! to it[transactionMultiset]!!.map { multiset ->
					val exchangeRate = multiset[BOT_TRANSACTION.EXCHANGE_RATE_USD]
					val chain = it[BOT_WALLET.CHAIN]!!

					val currencyBaseAmount = multiset[BOT_TRANSACTION.BASE_VALUE]!!.toBigInteger().asBaseAmount()
					val currencyNativeAmount = currencyBaseAmount.toNative(chain)

					SearchUserBotsTransactionsQuery.Result(
						botId = it[BOT.ID]!!,
						botName = it[BOT.NAME]!!,
						txId = multiset[BOT_TRANSACTION.ID]!!,
						txHash = TxHash(multiset[BOT_TRANSACTION.TX_HASH]!!),
						txDetailUrl = txHashToURIMapper(TxHash(multiset[BOT_TRANSACTION.TX_HASH]!!), chain),
						txType = multiset[BOT_TRANSACTION.TYPE]!!,
						txStatus = multiset[BOT_TRANSACTION.STATUS]!!,
						txCreatedAt = multiset[BOT_TRANSACTION.CREATED_AT]!!,
						txChain = chain,
						walletId = multiset[BOT_TRANSACTION.BOT_WALLET_ID]!!,
						tokenAddress = multiset[BOT_TRANSACTION.TOKEN_ADDRESS],
						tokenName = multiset[BOT_TOKEN_INFO.NAME],
						tokenSymbol = multiset[BOT_TOKEN_INFO.SYMBOL],
						nativeAmount = currencyNativeAmount,
						amountUsd = exchangeRate?.times(currencyNativeAmount.amount),
						percentageOf = multiset[BOT_TRANSACTION.PERCENTAGE_OF],
					)
				}
			}
	}
}
