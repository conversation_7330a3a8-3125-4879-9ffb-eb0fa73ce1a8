package com.cleevio.fatbot.adapter.out.solana.raydium.util

import com.cleevio.fatbot.application.common.crypto.SolanaConstants.RAYDIUM_CLMM_PROGRAM_ID
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.RAYDIUM_CPMM_PROGRAM_ID
import org.p2p.solanaj.core.PublicKey

fun getCLMMPoolVaultPda(poolAddress: PublicKey, mint: PublicKey): PublicKey {
	return PublicKey.findProgramAddress(
		listOf("pool_vault".toByteArray(), poolAddress.toByteArray(), mint.toByteArray()),
		RAYDIUM_CLMM_PROGRAM_ID,
	).address
}

fun getCLMMPoolBitmapExtensionAccountPda(poolAddress: PublicKey): PublicKey {
	return PublicKey.findProgramAddress(
		listOf("pool_tick_array_bitmap_extension".toByteArray(), poolAddress.toByteArray()),
		RAYDIUM_CLMM_PROGRAM_ID,
	).address
}

fun getCPMMPoolVaultPda(poolAddress: PublicKey, mint: PublicKey): PublicKey {
	return PublicKey.findProgramAddress(
		listOf("pool_vault".toByteArray(), poolAddress.toByteArray(), mint.toByteArray()),
		RAYDIUM_CPMM_PROGRAM_ID,
	).address
}

fun getCPMMObservationPda(poolAddress: PublicKey): PublicKey? {
	return PublicKey.findProgramAddress(
		listOf("observation".toByteArray(), poolAddress.toByteArray()),
		RAYDIUM_CPMM_PROGRAM_ID,
	).address
}
