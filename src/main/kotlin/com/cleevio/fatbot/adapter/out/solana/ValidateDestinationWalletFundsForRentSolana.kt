package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.MINIMUM_RENT_THRESHOLD
import com.cleevio.fatbot.application.common.crypto.compareTo
import com.cleevio.fatbot.application.common.crypto.plus
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.ValidateDestinationWalletFundsForRent
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.exception.InsufficientFundsForTransactionException
import org.springframework.stereotype.Component

@Component
class ValidateDestinationWalletFundsForRentSolana(
	private val getWalletBalanceSolana: GetWalletBalanceSolana,
) : ValidateDestinationWalletFundsForRent {

	override fun invoke(destinationAddress: AddressWrapper, toTransferBaseAmount: BaseAmount) {
		val walletBalance = getWalletBalanceSolana(address = destinationAddress)
		val onWalletAfterTransfer = walletBalance + toTransferBaseAmount

		if (onWalletAfterTransfer < MINIMUM_RENT_THRESHOLD) {
			throw InsufficientFundsForTransactionException(
				"Transaction is likely to fail due to not meeting minimal amount of SOL on" +
					"destination wallet ${destinationAddress.getAddressString()}." +
					"The minimum amount required is ${MINIMUM_RENT_THRESHOLD.toNative(Chain.SOLANA).amount} SOL " +
					"but now it would only be ${onWalletAfterTransfer.toNative(Chain.SOLANA).amount} SOL. " +
					"Increase the transfer amount.",
			)
		}
	}
}
