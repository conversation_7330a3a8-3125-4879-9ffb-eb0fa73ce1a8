package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.botwallet.port.out.UpdateBotWallets
import com.cleevio.fatbot.tables.references.BOT_WALLET
import org.jooq.DSLContext
import org.springframework.stereotype.Component

@Component
class UpdateBotWalletsJooq(private val dslContext: DSLContext) : UpdateBotWallets {

	override fun invoke(inputs: List<UpdateBotWallets.Input>) {
		inputs.forEach {
			val originalSignatureCondition = when (val txHash = it.originalSignature?.txHash) {
				null -> BOT_WALLET.LATEST_SIGNATURE.isNull
				else -> BOT_WALLET.LATEST_SIGNATURE.eq(txHash)
			}

			val conditions = listOf(
				BOT_WALLET.ID.eq(it.botWalletId),
				// We can only update the row, if in the meantime no other transaction updated the latest signature
				originalSignatureCondition,
			)

			dslContext
				.update(BOT_WALLET)
				.set(
					BOT_WALLET.BALANCE,
					BOT_WALLET.BALANCE.plus(it.balanceIncrease),
				)
				.set(
					BOT_WALLET.ACQUISITION_VALUE_USD,
					BOT_WALLET.ACQUISITION_VALUE_USD.plus(it.acquisitionValueUsdIncrease),
				)
				.set(
					BOT_WALLET.LATEST_SIGNATURE,
					it.latestSignature?.txHash,
				)
				.where(conditions)
				.execute()
		}
	}
}
