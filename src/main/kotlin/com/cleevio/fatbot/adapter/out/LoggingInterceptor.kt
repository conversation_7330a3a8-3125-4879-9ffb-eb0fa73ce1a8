package com.cleevio.fatbot.adapter.out

import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.http.HttpRequest
import org.springframework.http.client.ClientHttpRequestExecution
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.ClientHttpResponse

class LoggingInterceptor(
	private val className: String,
) : ClientHttpRequestInterceptor {
	private val logger = logger()

	override fun intercept(
		request: HttpRequest,
		body: ByteArray,
		execution: ClientHttpRequestExecution,
	): ClientHttpResponse = execution.execute(request, body).also {
		logger.info(
			"$className - ${it.statusCode}: ${request.method} ${request.uri}: ${
				body.toString(Charsets.UTF_8).ifBlank { "{}" }
			}",
		)
	}
}
