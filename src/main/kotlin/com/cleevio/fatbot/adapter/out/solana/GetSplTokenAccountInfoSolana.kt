package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.port.out.GetSplTokenAccountInfo
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.SplTokenAccountInfo
import org.springframework.stereotype.Component

@Component
class GetSplTokenAccountInfoSolana(
	private val rpcClient: RpcClient,
) : GetSplTokenAccountInfo {

	override fun invoke(address: AddressWrapper): SplTokenAccountInfo {
		val account = rpcClient.api.getSplTokenAccountInfo(address.getSolanaPublicKey())

		return account
	}
}
