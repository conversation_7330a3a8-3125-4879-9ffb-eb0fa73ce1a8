package com.cleevio.fatbot.adapter.out.solana.raydium

import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.div
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.decodeData
import com.cleevio.fatbot.application.common.util.readAddress
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.syntifi.near.borshj.BorshBuffer
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.AccountInfo
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.BigInteger
import java.math.MathContext
import kotlin.jvm.optionals.getOrNull

/**
 * TODO: Abstract into a `poolData` model
 * See for full data structure https://solscan.io/account/********************************************#anchorData
 */
const val MINT_1_OFFSET = 105

private val SHIFT_64 = BigInteger.ONE.shiftLeft(64).toBigDecimal() // 2^64

data class LiquidityPricePair(
	val liquidity: BigDecimal,
	val price: NativeAmount,
)

@Component
class GetRaydiumCLMMPrice(
	private val rpcClient: RpcClient,
) {

	operator fun invoke(pairAddress: AddressWrapper): NativeAmount {
		val pairAccount = rpcClient.api.getAccountInfo(
			pairAddress.getSolanaPublicKey(),
			// Request a data slice of:
			// [mint1, vault0, vault1, observationKey, decimals0, decimals1, tickSpacing, liquidity, sqrtPriceX64]
			mapOf("dataSlice" to mapOf("offset" to MINT_1_OFFSET, "length" to 164)),
		)

		val liquidityPricePair = pairAccount.value.extractLiquidityPricePair()
		return liquidityPricePair.price
	}

	fun getMany(pairAddresses: List<AddressWrapper>): Map<AddressWrapper, LiquidityPricePair?> {
		if (pairAddresses.isEmpty()) return emptyMap()

		val poolAccounts = rpcClient.api.getMultipleAccountsOptional(
			pairAddresses.map { it.getSolanaPublicKey() },
			// Request a data slice of:
			// [mint1, vault0, vault1, observationKey, decimals0, decimals1, tickSpacing, liquidity, sqrtPriceX64]
			mapOf("dataSlice" to mapOf("offset" to MINT_1_OFFSET, "length" to 164)),
		)

		val result = pairAddresses.zip(poolAccounts) { pairAddress, account ->
			val pair = account.getOrNull()?.extractLiquidityPricePair()

			pairAddress to pair
		}.toMap()

		return result
	}

	private fun AccountInfo.Value.extractLiquidityPricePair(): LiquidityPricePair {
		val buffer = BorshBuffer.wrap(this.decodeData())

		val mint1 = buffer.readAddress()
		buffer.read(96) // Skip [vault0, vault1, observationKey] (3 * 32 bytes)

		val mintDecimals0 = buffer.readU8().toInt()
		val mintDecimals1 = buffer.readU8().toInt()
		buffer.read(2) // Skip 2 bytes (TICK_SPACING)
		val sqrtLiquidityX64 = buffer.readU128()
		val sqrtPriceX64 = buffer.readU128()

		val liquidity = sqrtX64ToDecimal(sqrtLiquidityX64)
		// Sol -> Token price
		val price = sqrtX64ToDecimal(sqrtPriceX64)

		val decimalAdjustedPrice = price.asNativeAmount().toBase(mintDecimals0).toNative(mintDecimals1)

		// Token -> Sol price
		val tokenSolPrice = if (mint1 == SolanaConstants.WSOL_MINT_ADDRESS) {
			decimalAdjustedPrice
		} else {
			BigDecimal.ONE.asNativeAmount() / decimalAdjustedPrice
		}

		return LiquidityPricePair(liquidity = liquidity, price = tokenSolPrice)
	}

	private fun sqrtX64ToDecimal(value: BigInteger): BigDecimal =
		value.toBigDecimal().divide(SHIFT_64, MathContext.DECIMAL128).pow(2)
}
