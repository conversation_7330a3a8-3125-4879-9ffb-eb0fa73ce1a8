package com.cleevio.fatbot.adapter.out.websocket

import com.cleevio.fatbot.adapter.out.websocket.constant.WebSocketMessageType
import com.cleevio.fatbot.adapter.out.websocket.dto.TokenRealTimeTradeMessage
import com.cleevio.fatbot.adapter.out.websocket.dto.WebSocketMessage
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.port.out.PublishTokenRealTimeTrade
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.Instant

@Service
class PublishTokenRealTimeTradeWebSocketService(
	private val webSocketMessagingService: WebSocketMessagingService,
) : PublishTokenRealTimeTrade {
	override fun invoke(
		tokenAddress: AddressWrapper,
		chain: Chain,
		timestamp: Instant,
		priceUsd: BigDecimal,
		amountUsd: BigDecimal,
	) {
		webSocketMessagingService.sendMessage(
			destination = "/queue/tokens/$tokenAddress",
			message = WebSocketMessage(
				type = WebSocketMessageType.TOKEN_REAL_TIME_TRADE,
				data = TokenRealTimeTradeMessage(
					tokenAddress = tokenAddress,
					chain = chain,
					timestamp = timestamp,
					priceUsd = priceUsd,
					amountUsd = amountUsd,
				),
			),
		)
	}
}
