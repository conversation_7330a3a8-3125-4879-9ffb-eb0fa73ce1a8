package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.module.referral.port.out.GetReferralRewardsSummaryByChain
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.ReferralReward.Companion.REFERRAL_REWARD
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.TableField
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.util.UUID

@Component
class GetReferralRewardsSummaryByChainJooq(
	private val dslContext: DSLContext,
) : GetReferralRewardsSummaryByChain {

	private val sumBaseAmount = DSL.sum(REFERRAL_REWARD.BASE_AMOUNT).`as`("sum_base_amount")

	private val sumUnclaimedBaseAmount = DSL
		.sum(REFERRAL_REWARD.BASE_AMOUNT)
		.filterWhere(REFERRAL_REWARD.CLAIMED.isFalse)

	private val coalesceSumUnclaimedBaseAmount = DSL
		.coalesce(sumUnclaimedBaseAmount, BigDecimal.ZERO)
		.`as`("sum_unclaimed_base_amount")

	override fun invoke(userId: UUID): Map<Chain, GetReferralRewardsSummaryByChain.Result> {
		return dslContext
			.select(
				REFERRAL_REWARD.CHAIN,
				sumBaseAmount,
				coalesceSumUnclaimedBaseAmount,
			)
			.from(REFERRAL_REWARD)
			.where(REFERRAL_REWARD.USER_ID.eq(userId))
			.groupBy(REFERRAL_REWARD.CHAIN)
			.fetch()
			.intoMap(REFERRAL_REWARD.CHAIN.notNullable()) {
				GetReferralRewardsSummaryByChain.Result(
					totalBaseAmount = it[sumBaseAmount]!!.toBigInteger().asBaseAmount(),
					unclaimedBaseAmount = it[coalesceSumUnclaimedBaseAmount]!!.toBigInteger().asBaseAmount(),
				)
			}
	}

	private fun <T> TableField<Record, T?>.notNullable() = this.convertFrom { it!! }
}
