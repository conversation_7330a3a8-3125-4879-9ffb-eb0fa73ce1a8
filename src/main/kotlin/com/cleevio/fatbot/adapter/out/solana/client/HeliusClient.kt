package com.cleevio.fatbot.adapter.out.solana.client

import com.cleevio.fatbot.adapter.out.BaseConnector
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import org.springframework.http.MediaType
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class HeliusClient(
	chainProperties: ChainProperties,
) : BaseConnector(
	baseUrl = chainProperties.svm.solana.node,
) {
	fun getRecommendedFee(): GetRecommendedFeeResponse {
		return restClient
			.post()
			.contentType(MediaType.APPLICATION_JSON)
			.body(GetPriorityFeeEstimateRequest())
			.retrieveResponseWithErrorHandler<GetRecommendedFeeResponse>()
	}
}

data class GetPriorityFeeEstimateRequest(
	val jsonrpc: String = "2.0",
	val id: String = "1",
	val method: String = "getPriorityFeeEstimate",
	val params: List<Options> = listOf(Options(options = Recommended())),
) {
	// Change this mess if more options will be needed from this endpoint
	data class Options(val options: Recommended)
	data class Recommended(val recommended: Boolean = true)
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class GetRecommendedFeeResponse(
	val result: Result,
) {
	@JsonIgnoreProperties(ignoreUnknown = true)
	data class Result(val priorityFeeEstimate: BigDecimal)
}
