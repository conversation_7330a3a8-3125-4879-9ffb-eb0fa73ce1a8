package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.constants.EVM_GAS_LIMIT_ETH_TRANSFER_FALLBACK
import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.adapter.out.evm.model.GasInfo
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.TransferEth
import org.springframework.stereotype.Component
import org.web3j.crypto.Credentials
import org.web3j.crypto.RawTransaction
import java.math.BigInteger

@Component
class TransferEthEVM(
	private val evmChainContextFactory: EvmChainContextFactory,
) : TransferEth {

	override fun invoke(
		destinationWalletAddress: AddressWrapper,
		privateKey: String,
		chainId: Long,
		amountToTransfer: BaseAmount,
		gasInfo: GasInfo,
		gasLimit: BigInteger?,
	): SignedTx {
		return evmChainContextFactory.ofChainId(chainId = chainId) {
			val credentials = Credentials.create(privateKey)

			val gasLimit = gasLimit ?: EVM_GAS_LIMIT_ETH_TRANSFER_FALLBACK

			val rawTx = RawTransaction.createEtherTransaction(
				chainId,
				client.getNonce(credentials),
				gasLimit,
				destinationWalletAddress.getAddressString(),
				amountToTransfer.amount,
				gasInfo.maxPriorityFeePerGas,
				gasInfo.maxFeePerGas,
			)

			sendRawTransaction(signedTx = client.signTx(rawTx, credentials))
		}
	}
}
