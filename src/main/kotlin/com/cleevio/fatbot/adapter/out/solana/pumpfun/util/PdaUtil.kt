package com.cleevio.fatbot.adapter.out.solana.pumpfun.util

import com.cleevio.fatbot.application.common.crypto.SolanaConstants.PUMPFUN_PROGRAM
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.PUMPSWAP_PROGRAM
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import org.p2p.solanaj.core.PublicKey

fun getPumpfunBondingCurvePDA(mint: AddressWrapper): PublicKey {
	return PublicKey.findProgramAddress(
		listOf("bonding-curve".toByteArray(), mint.getAddressBytes()),
		PUMPFUN_PROGRAM,
	).address
}

fun getPumpfunCreatorVaultPDA(creator: AddressWrapper): PublicKey {
	return PublicKey.findProgramAddress(
		listOf("creator-vault".toByteArray(), creator.getAddressBytes()),
		PUMPFUN_PROGRAM,
	).address
}

fun getPumpswapCoinCreatorVaultAuthority(coinCreator: AddressWrapper): <PERSON>Key {
	return PublicKey.findProgramAddress(
		listOf("creator_vault".toByteArray(), coinCreator.getAddressBytes()),
		PUMPSWAP_PROGRAM,
	).address
}
