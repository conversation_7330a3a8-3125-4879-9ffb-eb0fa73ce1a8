package com.cleevio.fatbot.adapter.out.evm.simulation

import com.cleevio.fatbot.adapter.out.evm.FatbotRouterFunctionCallEncoder.buyFunctionCall
import com.cleevio.fatbot.adapter.out.evm.constants.simulationAddress
import com.cleevio.fatbot.adapter.out.evm.constants.simulationCredentials
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.stereotype.Component
import java.math.BigInteger
import java.util.UUID
import kotlin.math.pow

private const val SIMULATION_WALLET_BALANCE_HEX = "0x7E37BE2022C0914B2680000000"

// Determines the number of values requested in one call
private const val BINARY_TREE_DEPTH = 5

// Maximum number of searches to perform in total.
private const val MAX_SEARCHES = 10

// The number of 'extra' searches to perform after an answer is first found
private const val PRECISION_SEARCHES = 2

@Component
class GetTokenMaxBuy(
	private val properties: ChainProperties,
	private val temperConnector: TemperConnector,
	private val getSimulationOverrideData: GetSimulationOverrideData,
) {

	private val binaryTreeNodes = 2.0.pow(BINARY_TREE_DEPTH).toInt() - 1

	operator fun invoke(
		targetAmount: BigInteger,
		tokenAddress: AddressWrapper,
		chainId: Long,
		dexPairInfo: GetDex.Evm,
	): BigInteger? {
		val properties = properties.ofEvmChainId(chainId = chainId)

		val overrideData = getSimulationOverrideData(tokenAddress, dexPairInfo.pairAddress, chainId)

		val statefulSimulationId = temperConnector.createStatefulSimulation(
			TemperConnector.CreateSimulationStateRequest(
				blockNumber = overrideData.blockNumber,
				chainId = chainId,
				forkUrl = properties.node,
				stateOverrides = mapOf(
					tokenAddress to overrideData.tokenStateOverride,
					dexPairInfo.pairAddress to overrideData.pairStateOverride,
				),
			),
		).statefulSimulationId

		val result = searchMaxBuy(
			from = BigInteger.ONE,
			to = targetAmount,
			filterValidBuyAmounts = { amounts ->
				val amountInToAmountOut = simulateBuyBatch(
					statefulSimulationId = statefulSimulationId,
					contractAddress = properties.fatbotRouter,
					amountsIn = amounts,
					encodeFunctionCall = { amountIn ->
						buyFunctionCall(amountIn, tokenAddress, simulationCredentials.address, dexPairInfo, BigInteger.ZERO)
					},
				)

				amountInToAmountOut.mapNotNull { (amountIn, amountOut) -> amountIn.takeIf { amountOut != null } }
			},
		)

		temperConnector.deleteStatefulSimulation(statefulSimulationId)

		return result
	}

	private fun simulateBuyBatch(
		statefulSimulationId: UUID,
		contractAddress: AddressWrapper,
		amountsIn: List<BigInteger>,
		encodeFunctionCall: (amount: BigInteger) -> String,
	): List<Pair<BigInteger, BigInteger?>> {
		val inputData: List<TemperConnector.SimulateStatefulTransactionRequest> = amountsIn.map { amount ->
			TemperConnector.SimulateStatefulTransactionRequest(
				from = simulationCredentials.address,
				to = contractAddress.getAddressString(),
				data = encodeFunctionCall(amount),
				value = amount,
				stateOverrides = mapOf(
					simulationAddress to TemperConnector.StateOverride(balance = SIMULATION_WALLET_BALANCE_HEX),
				),
			)
		}

		// Possible error will be logged by the connector
		val bundleResult = runCatching { temperConnector.simulateStatefulBundle(statefulSimulationId, inputData) }
			.getOrNull() ?: emptyList()

		val amountInToAmountOutPairs = amountsIn.zip(bundleResult) { amountIn, response ->
			val transferAmountResult = runCatching {
				response.extractTokensSuccessfullySwappedEvent().amountOut
			}

			amountIn to transferAmountResult.getOrNull()
		}

		return amountInToAmountOutPairs
	}

	// Note: In the case where the number of nodes results in a perfectly balanced tree (2^x - 1),
	// this basically 'chops' the range into equal chunks, which could probably be done simpler.
	private fun makeBinaryTreeList(from: BigInteger, to: BigInteger): List<BigInteger> {
		val queue = ArrayDeque(listOf(from to to))

		val binaryTree = buildList {
			while (this.size < binaryTreeNodes) {
				val (low, high) = queue.removeFirstOrNull() ?: break

				if (low > high) continue

				val mid = (low + high) / BigInteger.TWO

				this.add(mid)

				queue.add(low to mid)
				queue.add(mid.inc() to high)
			}
		}

		return binaryTree
	}

	private fun searchMaxBuy(
		from: BigInteger,
		to: BigInteger,
		filterValidBuyAmounts: (amounts: List<BigInteger>) -> List<BigInteger>,
	): BigInteger? {
		tailrec fun search(low: BigInteger, high: BigInteger, searchesLeft: Int, max: BigInteger?): BigInteger? {
			if (searchesLeft == 0) return max

			val amounts = makeBinaryTreeList(low, high).sorted().let {
				// Adds check for the overall max, so we can stop looking if the maxBuy is higher
				if (searchesLeft == MAX_SEARCHES) it + to else it
			}

			if (amounts.isEmpty()) return max

			val validAmounts = filterValidBuyAmounts(amounts)
			val maxAmount = validAmounts.maxOrNull()

			return when (maxAmount) {
				to -> maxAmount
				null -> search(low, amounts.min(), searchesLeft - 1, max)
				else -> {
					val newHigh = amounts.firstOrNull { it > maxAmount } ?: high
					val newSearchesLeft = if (max == null) PRECISION_SEARCHES else searchesLeft - 1

					search(maxAmount.inc(), newHigh, newSearchesLeft, maxAmount)
				}
			}
		}

		return search(from, to, MAX_SEARCHES, null)
	}
}
