package com.cleevio.fatbot.adapter.out.solana.raydium

import com.cleevio.fatbot.application.common.util.readAddress
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.domain.token.RaydiumAmmMarketData
import com.syntifi.near.borshj.BorshBuffer
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.stereotype.Component

private const val BASE_VAULT_OFFSET = 336

@Component
class GetRaydiumAMMMarketData(
	private val rpcClient: RpcClient,
) {

	operator fun invoke(pairAddress: AddressWrapper): RaydiumAmmMarketData {
		val accountData = rpcClient.api.getAccountInfo(
			pairAddress.getSolanaPublicKey(),
			// Request slice of [baseVault, quoteVault, baseMint, quoteMint]
			mapOf("dataSlice" to mapOf("offset" to BASE_VAULT_OFFSET, "length" to 128)),
		)

		val buffer = BorshBuffer.wrap(accountData.decodedData)

		val baseVault = buffer.readAddress()
		val quoteVault = buffer.readAddress()
		val baseMint = buffer.readAddress()
		val quoteMint = buffer.readAddress()

		return RaydiumAmmMarketData(baseVault, quoteVault, baseMint, quoteMint)
	}
}
