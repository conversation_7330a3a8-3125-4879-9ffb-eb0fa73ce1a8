package com.cleevio.fatbot.adapter.out.evm.constants

import java.math.BigInteger

val EVM_GAS_LIMIT_ESTIMATE: BigInteger = BigInteger.valueOf(5_000_000)
val EVM_GAS_LIMIT_BUY_FALLBACK: BigInteger = BigInteger.valueOf(400_000)
val EVM_GAS_LIMIT_SELL_FALLBACK: BigInteger = BigInteger.valueOf(400_000)
val EVM_GAS_LIMIT_ETH_TRANSFER_FALLBACK: BigInteger = BigInteger.valueOf(21_000)
val EVM_GAS_LIMIT_ERC20_TRANSFER_FALLBACK: BigInteger = BigInteger.valueOf(140_000)
val EVM_GAS_LIMIT_APPROVE_FALLBACK: BigInteger = BigInteger.valueOf(80_000)
val EVM_MAX_UINT = BigInteger("115792089237316195423570985008687907853269984665640564039457584007913129639935")
val EVM_GAS_LIMIT_MULTIPLIER: BigInteger = BigInteger.valueOf(150)

const val SELECTOR_APPROVE = "0x095ea7b3"
