package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.module.market.port.out.GetTransactionReceipt
import org.springframework.stereotype.Component
import org.web3j.protocol.core.methods.response.TransactionReceipt

@Component
class GetTransactionReceiptEVM(
	private val evmChainContextFactory: EvmChainContextFactory,
) : GetTransactionReceipt {

	override fun invoke(txHash: TxHash, chainId: Long): TransactionReceipt? {
		val receipt = evmChainContextFactory.ofChainId(chainId) {
			client.getTransactionReceipt(txHash)
		}

		return receipt
	}
}
