package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.adapter.`in`.InfiniteScroll
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.adapter.`in`.toInfiniteScrollSlice
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.port.out.SearchUserTransaction
import com.cleevio.fatbot.application.module.transaction.query.SearchUserTransactionQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.EVM_TOKEN_INFO
import com.cleevio.fatbot.tables.references.TRANSACTION
import com.cleevio.fatbot.tables.references.WALLET
import org.jooq.DSLContext
import org.jooq.impl.DSL.or
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.net.URI
import java.util.UUID

@Component
class SearchUserTransactionJooq(
	private val dslContext: DSLContext,
) : SearchUserTransaction {

	override fun invoke(
		userId: UUID,
		chains: Set<Chain>,
		filter: SearchUserTransactionQuery.Filter,
		infiniteScroll: InfiniteScroll<UUID>,
		txHashToURIMapper: (TxHash, Chain) -> URI,
		fileToUrlMapper: FileToUrlMapper,
	): InfiniteScrollSlice<SearchUserTransactionQuery.Result, UUID> {
		val conditions = listOfNotNull(
			WALLET.USER_ID.eq(userId),
			TRANSACTION.CHAIN.`in`(chains),
			TRANSACTION.TX_HASH.isNotNull,
			TRANSACTION.STATUS.ne(TransactionStatus.SUCCESS).or(TRANSACTION.EXCHANGE_RATE_USD.isNotNull),
			filter.chain?.let { TRANSACTION.CHAIN.eq(it) },
			filter.walletId?.let { TRANSACTION.WALLET_ID.eq(it) },
			filter.allowedTypes.let { TRANSACTION.TYPE.`in`(it) },
			filter.allowedStatuses.let { TRANSACTION.STATUS.`in`(it) },
			filter.tokenAddress?.let { TRANSACTION.TOKEN_ADDRESS.eq(it) },
			filter.searchString?.let {
				or(
					EVM_TOKEN_INFO.NAME.likeIgnoreCase("%$it%"),
					EVM_TOKEN_INFO.SYMBOL.likeIgnoreCase("%$it%"),
					EVM_TOKEN_INFO.ADDRESS.eqIfValidAddressOrNoCondition(potentialAddress = it),
				)
			},
		)

		return dslContext
			.select(
				TRANSACTION.ID,
				TRANSACTION.TX_HASH,
				TRANSACTION.TYPE,
				TRANSACTION.STATUS,
				TRANSACTION.FAIL_REASON,
				TRANSACTION.WALLET_ID,
				TRANSACTION.wallet.CUSTOM_NAME,
				TRANSACTION.CREATED_AT,
				TRANSACTION.AMOUNT_IN,
				TRANSACTION.AMOUNT_OUT,
				TRANSACTION.EXCHANGE_RATE_USD,
				TRANSACTION.TOKEN_ADDRESS,
				TRANSACTION.CHAIN,
				EVM_TOKEN_INFO.DECIMALS,
				EVM_TOKEN_INFO.NAME,
				EVM_TOKEN_INFO.SYMBOL,
				EVM_TOKEN_INFO.file.ID,
				EVM_TOKEN_INFO.file.EXTENSION,
			)
			.from(TRANSACTION)
			.leftJoin(EVM_TOKEN_INFO)
			.on(TRANSACTION.TOKEN_ADDRESS.eq(EVM_TOKEN_INFO.ADDRESS).and(TRANSACTION.CHAIN.eq(EVM_TOKEN_INFO.CHAIN)))
			.innerJoin(WALLET)
			.on(TRANSACTION.WALLET_ID.eq(WALLET.ID))
			.whereWithInfiniteScroll(
				conditions = conditions,
				infiniteScroll = infiniteScroll,
				idFieldSelector = TRANSACTION.ID,
			)
			.fetch()
			.map {
				val txType = it[TRANSACTION.TYPE]!!
				val amountIn = it[TRANSACTION.AMOUNT_IN]
				val amountOut = it[TRANSACTION.AMOUNT_OUT]
				val exchangeRate = it[TRANSACTION.EXCHANGE_RATE_USD]
				val chain = it[TRANSACTION.CHAIN]!!

				val currencyBaseAmount = determineCoinBaseAmountOrNull(txType, amountIn = amountIn, amountOut = amountOut)
				val currencyNativeAmount = currencyBaseAmount?.toNative(chain)

				SearchUserTransactionQuery.Result(
					txId = it[TRANSACTION.ID]!!,
					txHash = TxHash(it[TRANSACTION.TX_HASH]!!),
					txDetailUrl = txHashToURIMapper(TxHash(it[TRANSACTION.TX_HASH]!!), it[TRANSACTION.CHAIN]!!),
					transactionType = it[TRANSACTION.TYPE]!!,
					transactionStatus = it[TRANSACTION.STATUS]!!,
					txFailReason = it[TRANSACTION.FAIL_REASON],
					walletId = it[TRANSACTION.WALLET_ID]!!,
					walletCustomName = it[TRANSACTION.wallet.CUSTOM_NAME],
					createdAt = it[TRANSACTION.CREATED_AT]!!,
					tokenAddress = it[TRANSACTION.TOKEN_ADDRESS],
					tokenName = it[EVM_TOKEN_INFO.NAME],
					tokenSymbol = it[EVM_TOKEN_INFO.SYMBOL],
					tokenImageUrl = fileToUrlMapper(it[EVM_TOKEN_INFO.file.ID], it[EVM_TOKEN_INFO.file.EXTENSION]),
					tokenNativeAmount = determineTokenNativeAmountOrNull(
						tokenDecimals = it[EVM_TOKEN_INFO.DECIMALS]?.toInt(),
						transactionType = txType,
						amountIn = amountIn,
						amountOut = amountOut,
					),
					currencyNativeAmount = currencyNativeAmount,
					currencyAmountUsd = currencyNativeAmount?.let { it.amount * exchangeRate!! },
					transactionChain = chain,
				)
			}
			.toInfiniteScrollSlice(infiniteScroll = infiniteScroll, idSelector = { txId })
	}

	private fun determineTokenNativeAmountOrNull(
		tokenDecimals: Int?,
		transactionType: TransactionType,
		amountIn: BigDecimal?,
		amountOut: BigDecimal?,
	): NativeAmount? {
		val amount = when (transactionType) {
			TransactionType.BUY -> amountOut
			TransactionType.SELL -> amountIn
			TransactionType.TRANSFER_CURRENCY -> null
			TransactionType.TRANSFER_TOKEN -> amountOut
			TransactionType.APPROVE -> error("Can't search approve txs.")
			TransactionType.CLAIM_REFERRAL_REWARD -> error("Can't search claim referral reward txs.")
		}
		val baseAmount = amount?.toBigInteger()?.asBaseAmount()
		return baseAmount?.toNative(tokenDecimals!!)
	}

	private fun determineCoinBaseAmountOrNull(
		transactionType: TransactionType,
		amountIn: BigDecimal?,
		amountOut: BigDecimal?,
	): BaseAmount? = when (transactionType) {
		TransactionType.BUY -> amountIn
		TransactionType.SELL -> amountOut
		TransactionType.TRANSFER_CURRENCY -> amountOut
		TransactionType.TRANSFER_TOKEN -> amountIn
		TransactionType.APPROVE -> error("Can't search approve txs.")
		TransactionType.CLAIM_REFERRAL_REWARD -> error("Can't search claim referral reward txs.")
	}?.toBigInteger()?.asBaseAmount()
}
