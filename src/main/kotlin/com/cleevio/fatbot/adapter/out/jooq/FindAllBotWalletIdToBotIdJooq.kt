package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.bottransaction.port.out.FindAllBotWalletIdToBotId
import com.cleevio.fatbot.tables.references.BOT_WALLET
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class FindAllBotWalletIdToBotIdJooq(private val dslContext: DSLContext) : FindAllBotWalletIdToBotId {

	override fun invoke(botWalletIds: Set<UUID>): Map<UUID, UUID> {
		return dslContext
			.select(
				BOT_WALLET.ID,
				BOT_WALLET.BOT_ID,
			)
			.from(BOT_WALLET)
			.where(BOT_WALLET.ID.`in`(botWalletIds))
			.fetch()
			.associate {
				it[BOT_WALLET.ID]!! to it[BOT_WALLET.BOT_ID]!!
			}
	}
}
