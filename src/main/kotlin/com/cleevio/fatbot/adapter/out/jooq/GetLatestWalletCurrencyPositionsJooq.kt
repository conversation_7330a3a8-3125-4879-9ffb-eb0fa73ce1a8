package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.port.out.GetLatestWalletCurrencyPositions
import com.cleevio.fatbot.tables.references.WALLET
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant
import java.util.UUID

@Component
class GetLatestWalletCurrencyPositionsJooq(
	private val dslContext: DSLContext,
) : GetLatestWalletCurrencyPositions {

	override fun invoke(
		userId: UUID,
		walletId: UUID?,
		chains: Set<Chain>,
		createdBefore: Instant?,
	): List<GetLatestWalletCurrencyPositions.WalletCurrencyPosition> {
		val conditions = listOfNotNull(
			WALLET.USER_ID.eq(userId),
			WALLET.CHAIN.`in`(chains),
			walletId?.let { WALLET.ID.eq(it) },
			createdBefore?.let { WALLET.walletCurrencyPosition.CREATED_AT.le(it) },
		)

		val latestPositions = dslContext
			.select(
				WALLET.ID,
				WALLET.CHAIN,
				WALLET.walletCurrencyPosition.TOTAL_BOUGHT,
				WALLET.walletCurrencyPosition.TOTAL_SOLD,
				WALLET.walletCurrencyPosition.TOTAL_ACQUISITION_COST_USD,
			)
			.distinctOn(WALLET.ID)
			.from(WALLET)
			.leftJoin(WALLET.walletCurrencyPosition).on(WALLET.walletCurrencyPosition.WALLET_ID.eq(WALLET.ID))
			.where(conditions)
			.orderBy(WALLET.ID, WALLET.walletCurrencyPosition.CREATED_AT.desc())
			.fetch()
			.map {
				GetLatestWalletCurrencyPositions.WalletCurrencyPosition(
					chain = it[WALLET.CHAIN]!!,
					// Note: These may happen if a wallet exists without any position
					// TODO: Should we throw here?
					totalBought = it[WALLET.walletCurrencyPosition.TOTAL_BOUGHT]?.toBigInteger() ?: BigInteger.ZERO,
					totalSold = it[WALLET.walletCurrencyPosition.TOTAL_SOLD]?.toBigInteger() ?: BigInteger.ZERO,
					totalAcquisitionCostUsd = it[WALLET.walletCurrencyPosition.TOTAL_ACQUISITION_COST_USD] ?: BigDecimal.ZERO,
				)
			}

		return latestPositions
	}
}
