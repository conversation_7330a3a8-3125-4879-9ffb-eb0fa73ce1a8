package com.cleevio.fatbot.adapter.out.solana.raydium

import com.cleevio.fatbot.application.common.util.readAddress
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.domain.token.RaydiumCPMMMarketData
import com.syntifi.near.borshj.BorshBuffer
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.stereotype.Component

@Component
class GetRaydiumCPMMMarketData(
	private val rpcClient: RpcClient,
) {

	operator fun invoke(pairAddress: AddressWrapper): RaydiumCPMMMarketData {
		val pairAccountData = rpcClient.api.getAccountInfo(
			pairAddress.getSolanaPublicKey(),
			// Request slice of [ammConfig]
			mapOf("dataSlice" to mapOf("offset" to 8, "length" to 32)),
		)

		val pairDataBuffer = BorshBuffer.wrap(pairAccountData.decodedData)
		val ammConfig = pairDataBuffer.readAddress()

		val ammConfigAccountData = rpcClient.api.getAccountInfo(
			ammConfig.getSolanaPublicKey(),
			// Request slice of [tradeFeeRate]
			mapOf("dataSlice" to mapOf("offset" to 12, "length" to 8)),
		)

		val ammConfigDataBuffer = BorshBuffer.wrap(ammConfigAccountData.decodedData)
		val tradeFeeRate = ammConfigDataBuffer.readU64()

		return RaydiumCPMMMarketData(
			ammConfig = ammConfig,
			tradeFeeRate = tradeFeeRate,
		)
	}
}
