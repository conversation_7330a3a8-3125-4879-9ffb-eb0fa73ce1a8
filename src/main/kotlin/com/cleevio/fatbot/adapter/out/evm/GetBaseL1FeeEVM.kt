package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetBaseL1Fee
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component
import org.web3j.crypto.RawTransaction

private val GAS_PRICE_ORACLE_ADDRESS = AddressWrapper("******************************************")

@Component
class GetBaseL1FeeEVM(
	private val evmChainContextFactory: EvmChainContextFactory,
) : GetBaseL1Fee {

	override fun invoke(transaction: RawTransaction): BaseAmount {
		val fee = evmChainContextFactory.ofChainId(Chain.EVM_BASE.evmId) {
			client.getL1Fee(transaction, GAS_PRICE_ORACLE_ADDRESS)
		}

		return fee.asBaseAmount()
	}
}
