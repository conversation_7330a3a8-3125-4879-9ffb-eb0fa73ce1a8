package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.TOKEN_2022_PROGRAM_ID
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.TOKEN_PROGRAM_ID
import com.cleevio.fatbot.application.common.crypto.compareTo
import com.cleevio.fatbot.application.common.crypto.toAddress
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.getAssociatedTokenAddress
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.ValidateTokenBalance
import com.cleevio.fatbot.application.module.wallet.exception.InsufficientFundsForTransactionException
import org.springframework.stereotype.Component

@Component
class ValidateTokenBalanceSolana(
	private val getSolanaTokenBalance: GetSolanaTokenBalancesSolana,
) : ValidateTokenBalance {

	override fun invoke(
		walletAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		isToken2022: Boolean,
		amountToSend: BaseAmount,
		tokenDecimals: Int,
	): ValidateTokenBalance.Result {
		val tokenAccountAddress = getAssociatedTokenAddress(
			mint = tokenAddress.getSolanaPublicKey(),
			owner = walletAddress.getSolanaPublicKey(),
			programId = if (isToken2022) TOKEN_2022_PROGRAM_ID else TOKEN_PROGRAM_ID,
		).toAddress()

		val tokenAccountBalance = getSolanaTokenBalance.single(tokenAccountAddress = tokenAccountAddress)

		if (tokenAccountBalance < amountToSend) {
			throw InsufficientFundsForTransactionException(
				"Wallet ${walletAddress.getAddressString()} currently holds " +
					"${tokenAccountBalance.toNative(tokenDecimals).amount} " +
					"token ${tokenAddress.getAddressString()} " +
					"however ${amountToSend.toNative(tokenDecimals).amount} was requested in transfer.",
			)
		}

		// At this point the token account must exist as it passed the check above
		return ValidateTokenBalance.Result(
			tokenAccountBalance = tokenAccountBalance,
			tokenAccountAddress = tokenAccountAddress,
		)
	}
}
