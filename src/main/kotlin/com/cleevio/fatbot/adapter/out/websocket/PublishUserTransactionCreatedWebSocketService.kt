package com.cleevio.fatbot.adapter.out.websocket

import com.cleevio.fatbot.adapter.out.websocket.constant.WebSocketMessageType
import com.cleevio.fatbot.adapter.out.websocket.dto.UserTransactionCreatedMessage
import com.cleevio.fatbot.adapter.out.websocket.dto.WebSocketMessage
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.port.out.PublishUserTransactionCreated
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class PublishUserTransactionCreatedWebSocketService(
	private val webSocketMessagingService: WebSocketMessagingService,
) : PublishUserTransactionCreated {

	override fun invoke(transactionType: TransactionType, userId: UUID, txHash: TxHash, amountOut: NativeAmount?) {
		webSocketMessagingService.sendMessageToUser(
			user = userId.toString(),
			destination = "/queue/user-transactions",
			message = WebSocketMessage(
				type = when (transactionType) {
					TransactionType.BUY -> WebSocketMessageType.USER_TRANSACTION_BUY
					TransactionType.SELL -> WebSocketMessageType.USER_TRANSACTION_SELL
					TransactionType.TRANSFER_TOKEN -> WebSocketMessageType.USER_TOKEN_TRANSACTION
					TransactionType.TRANSFER_CURRENCY -> WebSocketMessageType.USER_CURRENCY_TRANSACTION
					TransactionType.APPROVE -> WebSocketMessageType.USER_TRANSACTION
					TransactionType.CLAIM_REFERRAL_REWARD -> WebSocketMessageType.USER_CLAIM_REWARD_TRANSACTION
				},
				data = UserTransactionCreatedMessage(
					transactionType = transactionType,
					txHash = txHash,
					amountOut = amountOut,
				),
			),
		)
	}
}
