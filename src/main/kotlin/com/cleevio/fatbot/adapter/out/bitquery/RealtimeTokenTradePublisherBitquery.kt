package com.cleevio.fatbot.adapter.out.bitquery

import com.cleevio.fatbot.application.common.util.collectCatching
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.token.event.EVMTokenTradedEvent
import com.cleevio.fatbot.application.module.token.event.SolanaTokenTradedEvent
import com.cleevio.fatbot.application.module.token.event.TokenTradedEvent
import com.cleevio.fatbot.application.module.token.port.out.PublishTokenRealTimeTrade
import com.cleevio.fatbot.application.module.token.port.out.RealtimeTokenTradePublisher
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.logger
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import org.springframework.web.socket.messaging.SessionDisconnectEvent
import java.math.BigDecimal
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentMap

@Component
@ConditionalOnProperty("processing.token-trade-publishing.enabled", havingValue = "false")
class RealtimeTokenTradePublisherMock : RealtimeTokenTradePublisher {

	private val logger = logger()

	override fun connectSession(chainAddress: ChainAddress, sessionId: String) =
		logger.info("connectSession($chainAddress, $sessionId) on RealtimeTokenTradePublisherMock")
}

@Component
@ConditionalOnProperty("processing.token-trade-publishing.enabled", havingValue = "true")
class RealtimeTokenTradePublisherBitquery(
	private val publishTokenRealTimeTrade: PublishTokenRealTimeTrade,
	tokenTradeProvider: TokenTradeProvider,
) : RealtimeTokenTradePublisher {

	private val logger = logger()

	private val scope = CoroutineScope(
		Dispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	private val tradesFlow = tokenTradeProvider.getTokenTradeFlow()

	private val chainAddressToSessionIds: ConcurrentMap<ChainAddress, Set<String>> = ConcurrentHashMap()

	@EventListener(ApplicationReadyEvent::class)
	fun onStartup() {
		scope.launch { processTradeEvents() }
	}

	private suspend fun processTradeEvents() {
		tradesFlow.collectCatching(
			exceptionHandler = { logger.error("Error in processTradeEvent", it) },
			collector = { trades -> trades.forEach { processTradeEvent(it) } },
		)
	}

	private fun processTradeEvent(event: TokenTradedEvent) {
		val chainAddress = when (event) {
			is SolanaTokenTradedEvent -> event.tokenAddress.toChainAddress(Chain.SOLANA)
			is EVMTokenTradedEvent -> event.tokenAddress.toChainAddress(Chain.ofEVM(event.chainId))
		}

		// Only process trades that are currently being viewed by users
		if (!chainAddressToSessionIds.containsKey(chainAddress)) return

		// Ignore trades with 0 USD price
		if (event.priceUsd.compareTo(BigDecimal.ZERO) == 0) return

		publishTokenRealTimeTrade(
			tokenAddress = event.tokenAddress,
			chain = chainAddress.chain,
			timestamp = event.timestamp,
			priceUsd = event.priceUsd,
			amountUsd = event.amountUsd,
		)
	}

	@EventListener(SessionDisconnectEvent::class)
	fun onSessionDisconnectEvent(event: SessionDisconnectEvent) {
		processTokenRemovedFromSession(sessionId = event.sessionId)
	}

	override fun connectSession(chainAddress: ChainAddress, sessionId: String) {
		processTokenAddedToSession(chainAddress = chainAddress, sessionId = sessionId)
	}

	private fun processTokenAddedToSession(chainAddress: ChainAddress, sessionId: String) {
		chainAddressToSessionIds.compute(chainAddress) { _, existingSessionIds ->
			val newSessionIds = existingSessionIds.orEmpty() + sessionId

			if (newSessionIds.size != existingSessionIds?.size) {
				logger.info("Added session $sessionId for token chain address: $chainAddress")
			}

			newSessionIds
		}
	}

	private fun processTokenRemovedFromSession(sessionId: String) {
		val chainAddressesWithSessionId = chainAddressToSessionIds.filterValues { sessionIds -> sessionId in sessionIds }.keys

		chainAddressesWithSessionId.forEach { chainAddress ->
			chainAddressToSessionIds.compute(chainAddress) { _, existingSessionIds ->
				// Remove sessionId or the entire entry if no sessionIds are left
				existingSessionIds?.minus(sessionId)?.ifEmpty { null }
			}
		}

		if (chainAddressesWithSessionId.isNotEmpty()) {
			logger.info("Web socket session disconnected: $sessionId")
		}
	}
}
