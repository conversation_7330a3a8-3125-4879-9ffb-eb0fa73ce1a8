package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.asSignedTx
import com.cleevio.fatbot.application.module.transaction.exception.TransactionNotFoundException
import com.cleevio.fatbot.application.module.transaction.port.out.GetSignedTransactionInfo
import com.cleevio.fatbot.application.module.transaction.port.out.model.SignedTransactionInfoModel
import com.cleevio.fatbot.tables.references.TRANSACTION
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class GetSignedTransactionInfoJooq(
	private val dslContext: DSLContext,
) : GetSignedTransactionInfo {
	override fun invoke(transactionId: UUID): SignedTransactionInfoModel {
		return dslContext.select(
			TRANSACTION.SIGNED_TX,
			TRANSACTION.CHAIN,
		)
			.from(TRANSACTION)
			.where(TRANSACTION.ID.eq(transactionId))
			.fetchOne()?.let {
				SignedTransactionInfoModel(
					signedTx = it[TRANSACTION.SIGNED_TX]?.asSignedTx(),
					chain = it[TRANSACTION.CHAIN]!!,
				)
			} ?: throw TransactionNotFoundException(transactionId.toString())
	}
}
