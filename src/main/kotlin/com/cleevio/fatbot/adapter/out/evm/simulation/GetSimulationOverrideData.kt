package com.cleevio.fatbot.adapter.out.evm.simulation

import com.cleevio.fatbot.adapter.out.evm.context.EvmWeb3jWrapperProvider
import com.cleevio.fatbot.application.common.crypto.fromHexStringGetBigInteger
import com.cleevio.fatbot.application.common.util.EpochClockTicker
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.github.benmanes.caffeine.cache.Caffeine
import org.springframework.stereotype.Component
import org.web3j.protocol.core.DefaultBlockParameterName
import java.time.Clock
import java.time.Duration
import java.time.temporal.ChronoUnit
import java.util.concurrent.CompletableFuture

private const val TOKEN_STORAGE_SLOTS_TO_LOAD = 30
private const val PAIR_STORAGE_SLOTS_TO_LOAD = 10

@Component
class GetSimulationOverrideData(
	private val web3jWrapperProvider: EvmWeb3jWrapperProvider,
	clock: Clock,
) {

	data class SimulationOverrideData(
		val blockNumber: Long,
		val tokenStateOverride: TemperConnector.StateOverride,
		val pairStateOverride: TemperConnector.StateOverride,
	)

	private data class CacheKey(
		val tokenAddress: AddressWrapper,
		val pairAddress: AddressWrapper,
		val chainId: Long,
	)

	private val cache = Caffeine
		.newBuilder()
		.maximumSize(200)
		.expireAfterWrite(Duration.of(10, ChronoUnit.SECONDS))
		.ticker(EpochClockTicker(clock))
		.buildAsync<CacheKey, SimulationOverrideData> { key, executor ->
			CompletableFuture.supplyAsync(
				{
					getSimulationOverrideData(tokenAddress = key.tokenAddress, pairAddress = key.pairAddress, chainId = key.chainId)
				},
				executor,
			)
		}

	/**
	 * Retrieves the data from chain that will be used in the simulation.
	 * Temper is not able to batch load from the RPC (loads data as it encounters them in the simulation process)
	 */
	operator fun invoke(tokenAddress: AddressWrapper, pairAddress: AddressWrapper, chainId: Long): SimulationOverrideData {
		return cache.get(CacheKey(tokenAddress, pairAddress, chainId)).join()
	}

	private fun getSimulationOverrideData(
		tokenAddress: AddressWrapper,
		pairAddress: AddressWrapper,
		chainId: Long,
	): SimulationOverrideData {
		val web3j = web3jWrapperProvider.ofChainId(chainId).getWeb3j()

		val tokenAddressString = tokenAddress.getAddressString()
		val pairAddressString = pairAddress.getAddressString()

		val blockNumberRequest = web3j.ethBlockNumber()

		val tokenCodeRequest = web3j.ethGetCode(
			tokenAddressString,
			DefaultBlockParameterName.LATEST,
		)
		val pairCodeRequest = web3j.ethGetCode(
			pairAddressString,
			DefaultBlockParameterName.LATEST,
		)

		val tokenStorageRequests = List(TOKEN_STORAGE_SLOTS_TO_LOAD) {
			web3j.ethGetStorageAt(
				tokenAddressString,
				it.toBigInteger(),
				DefaultBlockParameterName.LATEST,
			)
		}

		val pairStorageRequests = List(PAIR_STORAGE_SLOTS_TO_LOAD) {
			web3j.ethGetStorageAt(
				pairAddressString,
				it.toBigInteger(),
				DefaultBlockParameterName.LATEST,
			)
		}

		val batch = web3j.newBatch()

		batch.add(blockNumberRequest)
		batch.add(tokenCodeRequest)
		batch.add(pairCodeRequest)

		tokenStorageRequests.forEach { batch.add(it) }
		pairStorageRequests.forEach { batch.add(it) }

		val batchResponse = batch.send().responses

		val blockNumber = batchResponse[0].result.toString().fromHexStringGetBigInteger()
		val tokenCode = batchResponse[1].result.toString()
		val pairCode = batchResponse[2].result.toString()

		val tokenStorage = batchResponse.drop(3).take(TOKEN_STORAGE_SLOTS_TO_LOAD).mapIndexed { index, response ->
			index.toBigInteger() to response.result.toString()
		}.toMap()

		val pairStorage = batchResponse.drop(3 + TOKEN_STORAGE_SLOTS_TO_LOAD).mapIndexed { index, response ->
			index.toBigInteger() to response.result.toString()
		}.toMap()

		return SimulationOverrideData(
			blockNumber = blockNumber.longValueExact(),
			tokenStateOverride = TemperConnector.StateOverride(code = tokenCode, storage = tokenStorage),
			pairStateOverride = TemperConnector.StateOverride(code = pairCode, storage = pairStorage),
		)
	}
}
