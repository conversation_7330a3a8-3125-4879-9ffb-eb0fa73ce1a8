package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.crypto.Lamport
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.CU_LIMIT_CURRENCY_TRANSFER
import com.cleevio.fatbot.application.common.crypto.asSignedTx
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bot.port.out.TransferSol
import org.bitcoinj.core.Base58
import org.p2p.solanaj.core.Account
import org.p2p.solanaj.core.LegacyTransaction
import org.p2p.solanaj.programs.ComputeBudgetProgram
import org.p2p.solanaj.programs.SystemProgram
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.stereotype.Component
import java.util.Base64

@Component
class TransferSolSVM(
	private val rpcClient: RpcClient,
	private val getRecommendedFeeSolana: GetRecommendedFeeSolana,
) : TransferSol {

	override fun invoke(privateKey: String, amount: Lamport, destinationWalletAddress: AddressWrapper): SignedTx {
		val account = Account(Base58.decode(privateKey))
		val toPublicKey = destinationWalletAddress.getSolanaPublicKey()

		val currentRecommendedFee = getRecommendedFeeSolana()

		val culInstruction = ComputeBudgetProgram.setComputeUnitLimit(CU_LIMIT_CURRENCY_TRANSFER)
		val cupInstruction = ComputeBudgetProgram.setComputeUnitPrice(currentRecommendedFee)

		val transferInstruction = SystemProgram.transfer(account.publicKey, toPublicKey, amount.amount.longValueExact())

		val transaction = LegacyTransaction().apply {
			addInstruction(culInstruction)
			addInstruction(cupInstruction)
			addInstruction(transferInstruction)
		}

		rpcClient.api.sendLegacyTransaction(transaction, account)

		val signedTx = Base64.getEncoder().encodeToString(transaction.serialize())
		return signedTx.asSignedTx()
	}
}
