package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.userfattycard.exceptions.UserFattyCardNotFoundException
import com.cleevio.fatbot.application.module.userfattycard.port.out.GetUserFattyCard
import com.cleevio.fatbot.application.module.userfattycard.query.GetUserFattyCardQuery
import com.cleevio.fatbot.tables.references.FATTY_CARD
import com.cleevio.fatbot.tables.references.USER_FATTY_CARD
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class GetUserFattyCardJooq(
	private val dslContext: DSLContext,
) : GetUserFattyCard {
	override operator fun invoke(userId: UUID, userFattyCardId: UUID): GetUserFattyCardQuery.Result {
		val conditions = listOfNotNull(
			USER_FATTY_CARD.USER_ID.eq(userId),
			USER_FATTY_CARD.ID.eq(userFattyCardId),
		)

		return dslContext.select(
			USER_FATTY_CARD.ID,
			FATTY_CARD.AVATAR_FILE_ID,
			FATTY_CARD.RARITY,
			FATTY_CARD.PROBABILITY,
			FATTY_CARD.DONUT_REWARD,
		)
			.from(USER_FATTY_CARD)
			.innerJoin(FATTY_CARD).on(USER_FATTY_CARD.FATTY_CARD_ID.eq(FATTY_CARD.ID))
			.where(conditions)
			.fetchOne {
				GetUserFattyCardQuery.Result(
					userFattyCardId = it[USER_FATTY_CARD.ID]!!,
					avatarFileId = it[FATTY_CARD.AVATAR_FILE_ID]!!,
					rarity = it[FATTY_CARD.RARITY]!!,
					probability = it[FATTY_CARD.PROBABILITY]!!,
					donutReward = it[FATTY_CARD.DONUT_REWARD]!!,
				)
			} ?: throw UserFattyCardNotFoundException("User Fatty card $userFattyCardId not found.")
	}
}
