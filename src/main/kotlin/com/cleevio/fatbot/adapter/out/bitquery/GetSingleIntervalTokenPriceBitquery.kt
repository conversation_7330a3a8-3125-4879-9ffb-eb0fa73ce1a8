package com.cleevio.fatbot.adapter.out.bitquery

import com.cleevio.fatbot.adapter.out.bitquery.cache.GetSingleIntervalTokenPriceExpiry
import com.cleevio.fatbot.adapter.out.bitquery.cache.GetSingleIntervalTokenPricesCacheKey
import com.cleevio.fatbot.application.common.util.EpochClockTicker
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.token.constant.TimeInterval
import com.cleevio.fatbot.application.module.token.port.out.GetSingleIntervalTokenPrice
import com.cleevio.fatbot.application.module.token.query.TokenPriceTimeIntervalItem
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.github.benmanes.caffeine.cache.Caffeine
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import java.time.Clock
import java.time.Instant
import java.util.Optional
import kotlin.jvm.optionals.getOrNull

@Component
class GetSingleIntervalTokenPriceBitquery(
	private val bitqueryApiV2Connector: BitqueryApiV2Connector,
	private val bitqueryEapConnector: BitqueryEapConnector,
	clock: Clock,
) : GetSingleIntervalTokenPrice {

	private val cache = Caffeine
		.newBuilder()
		.maximumSize(5000)
		.expireAfter(GetSingleIntervalTokenPriceExpiry)
		.ticker(EpochClockTicker(clock))
		.build<GetSingleIntervalTokenPricesCacheKey, Optional<TokenPriceTimeIntervalItem>>()

	@SentrySpan
	override fun invoke(
		chain: Chain,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		dex: GetDex.Dex,
		intervalCount: Int,
		timeInterval: TimeInterval,
		before: Instant,
	): TokenPriceTimeIntervalItem? {
		val result = cache.get(
			GetSingleIntervalTokenPricesCacheKey(chain, pairAddress, intervalCount, timeInterval, before),
		) {
			val tokenPriceData = when (chain.type) {
				ChainType.EVM -> bitqueryApiV2Connector.getEvmTokenPrice(
					chainId = chain.evmId,
					tokenAddress = tokenAddress.getAddressString(),
					pairAddress = pairAddress.getAddressString(),
					timeInterval = timeInterval,
					intervalCount = intervalCount,
					limit = 1,
					before = before,
					after = null,
				)
				ChainType.SOLANA -> bitqueryEapConnector.getSolanaTokenPrice(
					tokenAddress = tokenAddress,
					dex = dex,
					timeInterval = timeInterval,
					intervalCount = intervalCount,
					limit = 1,
					before = before,
					after = null,
				)
			}

			val intervalItem = tokenPriceData.singleOrNull()

			val optionalIntervalItem = if (intervalItem != null) Optional.of(intervalItem) else Optional.empty()

			optionalIntervalItem
		}

		return result.getOrNull()
	}
}
