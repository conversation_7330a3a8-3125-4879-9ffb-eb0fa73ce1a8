package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.referral.port.out.CountNumberOfReferrals
import com.cleevio.fatbot.tables.references.FIREBASE_USER
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class CountNumberOfReferralsJooq(private val dslContext: DSLContext) : CountNumberOfReferrals {

	override fun invoke(userId: UUID): Int {
		return dslContext
			.select(DSL.count())
			.from(FIREBASE_USER)
			.where(FIREBASE_USER.REFERRED_BY_USER_ID.eq(userId))
			.fetchOne(0, Int::class.java) ?: 0
	}
}
