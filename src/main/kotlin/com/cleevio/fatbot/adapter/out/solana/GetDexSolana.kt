package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.adapter.out.dexscreener.SOLANA_ALLOWED_DEX_IDS
import com.cleevio.fatbot.adapter.out.dexscreener.SOLANA_METEORA_DEX_ID
import com.cleevio.fatbot.adapter.out.dexscreener.SOLANA_PUMPFUN_DEX_ID
import com.cleevio.fatbot.adapter.out.dexscreener.SOLANA_PUMPSWAP_DEX_ID
import com.cleevio.fatbot.adapter.out.dexscreener.SOLANA_RAYDIUM_DEX_ID
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import org.springframework.stereotype.Component

@Component
class GetDexSolana : GetDex {

	override fun supports() = ChainType.SOLANA

	override fun invoke(
		dexId: String,
		pairAddress: AddressWrapper,
		version: GetDex.Version?,
		poolType: GetDex.PoolType?,
		chain: Chain,
	): GetDex.Result {
		require(dexId in SOLANA_ALLOWED_DEX_IDS)
		require(pairAddress.isSolana())
		require(chain == Chain.SOLANA)

		return when (dexId) {
			SOLANA_PUMPFUN_DEX_ID -> GetDex.PumpFun(pairAddress)
			SOLANA_METEORA_DEX_ID -> GetDex.Meteora(pairAddress)
			SOLANA_RAYDIUM_DEX_ID -> GetDex.Raydium(pairAddress, poolType ?: error("Missing poolType for Raydium Dex"))
			SOLANA_PUMPSWAP_DEX_ID -> GetDex.PumpSwap(pairAddress)
			else -> error("Unable to convert DEX with dexId: $dexId")
		}
	}
}
