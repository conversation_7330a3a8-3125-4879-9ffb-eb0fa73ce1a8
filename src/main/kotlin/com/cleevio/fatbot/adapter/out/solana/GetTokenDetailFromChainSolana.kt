package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.market.port.out.GetTokenDetailFromChain
import com.cleevio.fatbot.application.module.market.query.GetTokenDetailQuery
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.fasterxml.jackson.databind.ObjectMapper
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.stereotype.Component

@Component
class GetTokenDetailFromChainSolana(
	private val rpcClient: RpcClient,
	private val objectMapper: ObjectMapper,
) : GetTokenDetailFromChain {

	override fun supports(): ChainType = ChainType.SOLANA

	override fun invoke(token: ChainAddress): GetTokenDetailQuery.NonDexDetail? {
		val nonDexInfo = runCatching { getInfo(tokenAddress = token.address) }.getOrNull()

		return nonDexInfo?.let { GetTokenDetailQuery.NonDexDetail(it) }
	}

	// Note: This is a raw implementation indented to work with Helius RPC provider
	// Its only reason is to "verify" that the address is a token via returning the base info
	private fun getInfo(tokenAddress: AddressWrapper): GetTokenDetailQuery.NonDexInfo {
		val responseMap = rpcClient.call("getAsset", mapOf("id" to tokenAddress.getAddressString()), Map::class.java)
		val responseJson = objectMapper.readTree(objectMapper.writeValueAsString(responseMap))

		check(responseJson["interface"].textValue() == "FungibleToken")

		val name = responseJson["content"]["metadata"]["name"].textValue()
		val symbol = responseJson["content"]["metadata"]["symbol"].textValue()

		return GetTokenDetailQuery.NonDexInfo(name = name, symbol = symbol)
	}
}
