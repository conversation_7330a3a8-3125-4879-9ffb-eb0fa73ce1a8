package com.cleevio.fatbot.adapter.out.evm.simulation

import com.cleevio.fatbot.adapter.out.evm.constants.EVM_GAS_LIMIT_APPROVE_FALLBACK
import com.cleevio.fatbot.adapter.out.evm.constants.EVM_GAS_LIMIT_ESTIMATE
import com.cleevio.fatbot.adapter.out.evm.constants.EVM_MAX_UINT
import com.cleevio.fatbot.adapter.out.evm.constants.SELECTOR_APPROVE
import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.adapter.out.evm.model.GasInfo
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.padToDataPayloadLength
import com.cleevio.fatbot.application.common.crypto.remove0x
import com.cleevio.fatbot.application.common.crypto.toEth
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.exception.InsufficientFundsForTransactionFeesException
import com.cleevio.fatbot.infrastructure.config.logger
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Component
import org.web3j.crypto.Credentials
import org.web3j.crypto.RawTransaction
import org.web3j.crypto.TransactionEncoder
import org.web3j.protocol.core.methods.request.Transaction
import org.web3j.utils.Numeric
import java.math.BigInteger

interface SetTokensApprovalEVM {
	operator fun invoke(
		tokenAddress: AddressWrapper,
		minimumTokenAllowance: BaseAmount,
		privateKey: String,
		chainId: Long,
		gasInfoHint: GasInfo?,
	): SignedTx?
}

@Component
private class SetTokensApprovalEVMImpl(
	private val evmChainContextFactory: EvmChainContextFactory,
	private val objectMapper: ObjectMapper,
) : SetTokensApprovalEVM {

	private val logger = logger()

	override fun invoke(
		tokenAddress: AddressWrapper,
		minimumTokenAllowance: BaseAmount,
		privateKey: String,
		chainId: Long,
		gasInfoHint: GasInfo?,
	): SignedTx? {
		val credentials = Credentials.create(privateKey)
		val walletAddress = AddressWrapper(credentials.address)

		val signedTx = evmChainContextFactory.ofChainId(chainId) {
			val allowance = client.getAllowance(
				tokenAddress = tokenAddress,
				walletAddress = walletAddress,
				uniswapRouterAddress = properties.fatbotRouter,
			)

			if (allowance >= minimumTokenAllowance.amount) return@ofChainId null
			val balance = client.getWalletBalance(walletAddress)

			val estimateTransaction = createSetMaxApproveEstimateTransaction(
				tokenAddress = tokenAddress,
				spender = properties.fatbotRouter,
				credentials = credentials,
			)

			val gasLimitEstimate = client.estimateGas(estimateTransaction) ?: run {
				logger.error(
					"""
					Failed to estimate gas for approval transaction.
					Estimated transaction:
					```
					${objectMapper.writeValueAsString(estimateTransaction)}
					```
					Node: ${properties.node}

					Using the fallback of $EVM_GAS_LIMIT_APPROVE_FALLBACK
					""".trimIndent(),
				)

				EVM_GAS_LIMIT_APPROVE_FALLBACK
			}

			// Note: This design is a bit unfortunate, as we force ourselves to fetch nonce & balance twice (approval, sell)
			val nonce = client.getNonce(credentials)
			val gasInfo = gasInfoHint ?: client.getGasInfo()

			validateFundsForApproveTransaction(
				walletAddress = walletAddress,
				gasInfo = gasInfo,
				gasLimitEstimate = gasLimitEstimate,
				balance = balance,
			)

			val maxApproveSignedTx = createSetMaxApproveSignedTx(
				chainId = chainId,
				nonce = nonce,
				tokenAddress = tokenAddress,
				spender = properties.fatbotRouter,
				gasLimit = gasLimitEstimate,
				credentials = credentials,
				gasInfo = gasInfo,
			)

			sendRawTransaction(maxApproveSignedTx)
		}

		return signedTx
	}

	private fun validateFundsForApproveTransaction(
		walletAddress: AddressWrapper,
		balance: BigInteger,
		gasLimitEstimate: BigInteger,
		gasInfo: GasInfo,
	) {
		val approveGasFee = gasInfo.maxFeePerGas * gasLimitEstimate

		if (balance < approveGasFee) {
			throw InsufficientFundsForTransactionFeesException(
				"Insufficient funds on wallet $walletAddress for max approve." +
					" You need at least ${approveGasFee.toEth()} ETH for gas fees." +
					" Your balance is ${balance.toEth()} ETH.",
			)
		}
	}

	private fun createSetMaxApproveSignedTx(
		chainId: Long,
		nonce: BigInteger,
		tokenAddress: AddressWrapper,
		spender: AddressWrapper,
		gasLimit: BigInteger,
		gasInfo: GasInfo,
		credentials: Credentials,
	): SignedTx {
		val rawTx = RawTransaction.createTransaction(
			chainId,
			nonce,
			gasLimit,
			tokenAddress.getAddressString(),
			BigInteger.ZERO,
			createApprovalDataPayload(spender = spender),
			gasInfo.maxPriorityFeePerGas,
			gasInfo.maxFeePerGas,
		)

		val signedTx = Numeric.toHexString(TransactionEncoder.signMessage(rawTx, credentials))

		return SignedTx(signedTx)
	}

	private fun createSetMaxApproveEstimateTransaction(
		tokenAddress: AddressWrapper,
		spender: AddressWrapper,
		credentials: Credentials,
	): Transaction {
		val transaction = Transaction.createFunctionCallTransaction(
			credentials.address,
			BigInteger.ZERO,
			BigInteger.ZERO,
			EVM_GAS_LIMIT_ESTIMATE,
			tokenAddress.getAddressString(),
			BigInteger.ZERO,
			createApprovalDataPayload(spender = spender),
		)

		return transaction
	}

	private fun createApprovalDataPayload(spender: AddressWrapper): String = buildString {
		append(SELECTOR_APPROVE)
		append(spender.getAddressString().remove0x().padToDataPayloadLength())
		append(EVM_MAX_UINT.toString(16).padToDataPayloadLength())
	}
}
