package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.STABLE_USD_PEGGED_TOKENS
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.transaction.port.out.GetUserLifetimeTradeVolume
import com.cleevio.fatbot.application.module.transaction.query.GetUserLifetimeTradeVolumeQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.TRANSACTION
import com.cleevio.fatbot.tables.references.WALLET
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.util.UUID

@Component
class GetUserLifetimeTradeVolumeJooq(private val dslContext: DSLContext) : GetUserLifetimeTradeVolume {

	override fun invoke(userId: UUID, chains: Set<Chain>): GetUserLifetimeTradeVolumeQuery.Result {
		val allUserWalletIds = DSL
			.select(WALLET.ID)
			.from(WALLET)
			.where(WALLET.USER_ID.eq(userId).and(WALLET.CHAIN.`in`(chains)))

		val conditions = listOf(
			TRANSACTION.WALLET_ID.`in`(allUserWalletIds),
			TRANSACTION.STATUS.eq(TransactionStatus.SUCCESS),
			TRANSACTION.TYPE.`in`(TransactionType.BUY, TransactionType.SELL),
			// way to filter out tracking transactions when importing token
			TRANSACTION.SIGNED_TX.isNotNull,
			// Operations with stable coins are not considered into lifetime user volume
			// which is business decision as they have 0% fee for Fatbot
			TRANSACTION.TOKEN_ADDRESS.notIn(STABLE_USD_PEGGED_TOKENS.map { it.token.address }),
		)

		// TODO: Is there easier way to do this, when it's chaining functions?
		val currencyDecimals = DSL
			.`when`(TRANSACTION.CHAIN.eq(Chain.SOLANA), Chain.SOLANA.currency.decimals)
			.`when`(TRANSACTION.CHAIN.eq(Chain.EVM_MAINNET), Chain.EVM_MAINNET.currency.decimals)
			.`when`(TRANSACTION.CHAIN.eq(Chain.EVM_BASE), Chain.EVM_BASE.currency.decimals)
			.`when`(TRANSACTION.CHAIN.eq(Chain.EVM_BSC), Chain.EVM_BSC.currency.decimals)
			.`when`(TRANSACTION.CHAIN.eq(Chain.EVM_ARBITRUM_ONE), Chain.EVM_ARBITRUM_ONE.currency.decimals)

		val decimals = DSL.power(10, currencyDecimals)

		val transactionAmountUsd = DSL
			.`when`(
				TRANSACTION.TYPE.eq(TransactionType.BUY),
				(TRANSACTION.AMOUNT_IN / decimals) * TRANSACTION.EXCHANGE_RATE_USD,
			)
			.`when`(
				TRANSACTION.TYPE.eq(TransactionType.SELL),
				(TRANSACTION.AMOUNT_OUT / decimals) * TRANSACTION.EXCHANGE_RATE_USD,
			)

		val transactionAmountUsdSumOrNull = DSL.sum(transactionAmountUsd)
		val transactionAmountUsdSum = DSL.coalesce(transactionAmountUsdSumOrNull, BigDecimal.ZERO)

		return dslContext
			.select(transactionAmountUsdSum)
			.from(TRANSACTION)
			.where(conditions)
			.fetchSingle()
			.map {
				GetUserLifetimeTradeVolumeQuery.Result(lifetimeTradeVolumeUsd = it[transactionAmountUsdSum]!!)
			}
	}
}
