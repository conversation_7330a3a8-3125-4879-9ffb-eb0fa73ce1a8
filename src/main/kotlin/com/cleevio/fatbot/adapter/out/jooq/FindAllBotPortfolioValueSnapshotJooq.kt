package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotPortfolioValueSnapshot
import com.cleevio.fatbot.tables.references.BOT_PORTFOLIO_VALUE_SNAPSHOT
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.time.Instant
import java.util.UUID

@Component
class FindAllBotPortfolioValueSnapshotJooq(
	private val dslContext: DSLContext,
) : FindAllBotPortfolioValueSnapshot {
	override fun invoke(botIds: Set<UUID>, from: Instant, to: Instant): List<FindAllBotPortfolioValueSnapshot.Result> {
		val botPortfolioValueSnapshotConditions = listOf(
			BOT_PORTFOLIO_VALUE_SNAPSHOT.BOT_ID.`in`(botIds),
			BOT_PORTFOLIO_VALUE_SNAPSHOT.SNAPSHOT_MADE_AT.between(from, to),
		)

		return dslContext.select(
			BOT_PORTFOLIO_VALUE_SNAPSHOT.BOT_ID,
			BOT_PORTFOLIO_VALUE_SNAPSHOT.PORTFOLIO_VALUE_USD,
			BOT_PORTFOLIO_VALUE_SNAPSHOT.ACQUISITION_VALUE_USD,
			BOT_PORTFOLIO_VALUE_SNAPSHOT.SNAPSHOT_MADE_AT,
			BOT_PORTFOLIO_VALUE_SNAPSHOT.CHAIN,
		)
			.from(BOT_PORTFOLIO_VALUE_SNAPSHOT)
			.where(botPortfolioValueSnapshotConditions)
			.fetch()
			.map {
				FindAllBotPortfolioValueSnapshot.Result(
					botId = it[BOT_PORTFOLIO_VALUE_SNAPSHOT.BOT_ID]!!,
					chain = it[BOT_PORTFOLIO_VALUE_SNAPSHOT.CHAIN]!!,
					snapshotMadeAt = it[BOT_PORTFOLIO_VALUE_SNAPSHOT.SNAPSHOT_MADE_AT]!!,
					portfolioValueUsd = it[BOT_PORTFOLIO_VALUE_SNAPSHOT.PORTFOLIO_VALUE_USD]!!,
					acquisitionValueUsd = it[BOT_PORTFOLIO_VALUE_SNAPSHOT.ACQUISITION_VALUE_USD]!!,
				)
			}
	}
}
