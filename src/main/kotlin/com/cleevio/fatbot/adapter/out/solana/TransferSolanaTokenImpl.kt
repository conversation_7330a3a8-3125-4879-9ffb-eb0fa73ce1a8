package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.CU_LIMIT_TOKEN_TRANSFER
import com.cleevio.fatbot.application.common.crypto.minus
import com.cleevio.fatbot.application.common.crypto.toSignedTx
import com.cleevio.fatbot.application.common.util.getAssociatedTokenAddress
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.TransferSolanaToken
import org.bitcoinj.core.Base58
import org.p2p.solanaj.core.Account
import org.p2p.solanaj.core.LegacyTransaction
import org.p2p.solanaj.programs.AssociatedTokenProgram
import org.p2p.solanaj.programs.ComputeBudgetProgram
import org.p2p.solanaj.programs.TokenProgram
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.stereotype.Component

@Component
class TransferSolanaTokenImpl(
	private val rpcClient: RpcClient,
	private val getRecommendedFeeSolana: GetRecommendedFeeSolana,
) : TransferSolanaToken {

	override fun invoke(
		senderTokenAccountAddress: AddressWrapper,
		senderTokenAccountBalance: BaseAmount,
		tokenAddress: AddressWrapper,
		isToken2022: Boolean,
		tokenDecimals: Int,
		destinationWalletAddress: AddressWrapper,
		privateKey: String,
		amountToTransfer: BaseAmount,
	): SignedTx {
		val senderTokenAccount = senderTokenAccountAddress.getSolanaPublicKey()
		val mint = tokenAddress.getSolanaPublicKey()
		val sender = Account(Base58.decode(privateKey))
		val destinationAccount = destinationWalletAddress.getSolanaPublicKey()
		val tokenProgram = if (isToken2022) SolanaConstants.TOKEN_2022_PROGRAM_ID else SolanaConstants.TOKEN_PROGRAM_ID

		val destinationTokenAccountAddress = getAssociatedTokenAddress(
			mint = mint,
			owner = destinationAccount,
			programId = tokenProgram,
		)

		val isDestinationTokenAccountInitialized = rpcClient.api.getAccountInfo(destinationTokenAccountAddress).value != null

		val currentRecommendedFee = getRecommendedFeeSolana()

		val culInstruction = ComputeBudgetProgram.setComputeUnitLimit(CU_LIMIT_TOKEN_TRANSFER)
		val cupInstruction = ComputeBudgetProgram.setComputeUnitPrice(currentRecommendedFee)

		val createDestinationTokenAccountInstruction = AssociatedTokenProgram.create(
			sender.publicKey,
			destinationAccount,
			mint,
			tokenProgram,
		)

		val closeSenderTokenAccountInstruction = TokenProgram.closeAccount(
			senderTokenAccount,
			sender.publicKey,
			sender.publicKey,
			tokenProgram,
		)

		val tokenTransferInstruction = when (isToken2022) {
			true -> TokenProgram.transferChecked(
				senderTokenAccount,
				destinationTokenAccountAddress,
				amountToTransfer.amount.longValueExact(),
				tokenDecimals.toByte(),
				sender.publicKey,
				tokenAddress.getSolanaPublicKey(),
				tokenProgram,
			)
			false -> TokenProgram.transfer(
				senderTokenAccount,
				destinationTokenAccountAddress,
				amountToTransfer.amount.longValueExact(),
				sender.publicKey,
				tokenProgram,
			)
		}

		val senderTokenBalanceAfter = senderTokenAccountBalance - amountToTransfer

		val transaction = LegacyTransaction().apply {
			addInstruction(culInstruction)
			addInstruction(cupInstruction)
			if (!isDestinationTokenAccountInitialized) {
				addInstruction(createDestinationTokenAccountInstruction)
			}
			addInstruction(tokenTransferInstruction)
			if (senderTokenBalanceAfter == BaseAmount.ZERO) {
				addInstruction(closeSenderTokenAccountInstruction)
			}
		}

		rpcClient.api.sendLegacyTransaction(transaction, sender)

		return transaction.toSignedTx()
	}
}
