package com.cleevio.fatbot.adapter.out.dexscreener

import com.cleevio.fatbot.adapter.out.BaseConnector
import com.cleevio.fatbot.adapter.out.dexscreener.response.GetTokenPairsInfoResponse
import com.cleevio.fatbot.application.common.crypto.STABLE_USD_PEGGED_TOKENS
import com.cleevio.fatbot.application.common.crypto.StableCoin
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class DexScreenerConnector(
	@Value("\${integration.dexscreener.base-url}") baseUrl: String,
) : BaseConnector(
	baseUrl = baseUrl,
	restClientCustomizer = {},
) {

	@SentrySpan
	fun getTokenPairsInfoByAddress(token: AddressWrapper): GetTokenPairsInfoResponse {
		val result = if (STABLE_USD_PEGGED_TOKENS.any { it.token.address == token }) {
			// we handle stable coins via special api to fetch concrete and only one pair
			val stableCoin = STABLE_USD_PEGGED_TOKENS.first { it.token.address == token }

			getFromDexscreenerStableCoin(stableCoin)
		} else {
			// normal token
			getFromDexscreener(token)
		}

		return result
	}

	private fun getFromDexscreener(token: AddressWrapper): GetTokenPairsInfoResponse {
		val response = restClient
			.get()
			.uri("/latest/dex/tokens/${token.getAddressString()}")
			.retrieveResponseWithErrorHandler<GetTokenPairsInfoResponse>()

		return response
	}

	private fun getFromDexscreenerStableCoin(stableCoin: StableCoin): GetTokenPairsInfoResponse {
		val chain = stableCoin.token.chain
		val pairAddress = stableCoin.pairAddress
		val response = restClient
			.get()
			.uri("/latest/dex/pairs/${chain.toDexScreenerUrlPathPart()}/$pairAddress")
			.retrieveResponseWithErrorHandler<GetTokenPairsInfoResponse>()

		val pair = response.pairs!!.first()
		val fixedPair = when (stableCoin.shouldSwapQuoteAndBaseToken) {
			true -> pair.copy(quoteToken = pair.baseToken, baseToken = pair.quoteToken)
			false -> pair
		}

		val result = GetTokenPairsInfoResponse(listOfNotNull(fixedPair))

		return result
	}

	private fun Chain.toDexScreenerUrlPathPart() = when (this) {
		Chain.EVM_MAINNET -> "ethereum"
		Chain.EVM_BASE -> "base"
		Chain.EVM_BSC -> "bsc"
		Chain.EVM_ARBITRUM_ONE -> "arbitrum"
		Chain.SOLANA -> "solana"
	}
}
