package com.cleevio.fatbot.adapter.out.solana.pumpfun

import com.cleevio.fatbot.adapter.out.solana.pumpfun.model.PumpFunCurveState
import com.cleevio.fatbot.adapter.out.solana.pumpfun.util.getPumpfunBondingCurvePDA
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.div
import com.cleevio.fatbot.application.common.crypto.toAddress
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.decodeData
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.stereotype.Component

@Component
class GetPumpfunPrice(
	private val rpcClient: RpcClient,
) {

	operator fun invoke(mint: AddressWrapper): NativeAmount {
		val curveAddress = getPumpfunBondingCurvePDA(mint)

		val curveAccountInfo = rpcClient.api.getAccountInfo(curveAddress)
		val curveState = PumpFunCurveState.read(curveAccountInfo.decodedData, curveAddress.toAddress())

		return calculatePrice(curveState)
	}

	fun fromCurveState(curveState: PumpFunCurveState): NativeAmount {
		return calculatePrice(curveState = curveState)
	}

	fun getManyByBondingCurves(curveAddresses: Set<AddressWrapper>): Map<AddressWrapper, NativeAmount?> {
		if (curveAddresses.isEmpty()) return emptyMap()

		val accounts = rpcClient.api.getMultipleAccounts(
			curveAddresses.map { it.getSolanaPublicKey() },
			mapOf("commitment" to Commitment.PROCESSED),
		)

		val addressToPrice = curveAddresses.zip(accounts).toMap().mapValues { (curveAddress, account) ->
			val curveState = PumpFunCurveState.read(account.decodeData(), curveAddress)

			if (curveState.complete) return@mapValues null

			calculatePrice(curveState)
		}

		return addressToPrice
	}

	private fun calculatePrice(curveState: PumpFunCurveState): NativeAmount {
		val virtualSolReservesNative = curveState.virtualSolReserves.toNative(Chain.SOLANA)
		val virtualTokenReservesNative = curveState.virtualTokenReserves.toNative(PumpFunCurveState.TOKEN_DECIMALS)

		return virtualSolReservesNative / virtualTokenReservesNative
	}
}
