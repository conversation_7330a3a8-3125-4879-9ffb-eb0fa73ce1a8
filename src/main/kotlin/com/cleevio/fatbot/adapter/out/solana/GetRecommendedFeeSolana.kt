package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.adapter.out.solana.client.GetRecommendedFeeResponse
import com.cleevio.fatbot.adapter.out.solana.client.HeliusClient
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.service.AbstractLiveDataService
import com.cleevio.fatbot.infrastructure.config.logger
import io.sentry.spring.jakarta.tracing.SentrySpan
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.MathContext

@Component
class GetRecommendedFeeSolana(
	private val priorityFeeLiveData: PriorityFeeLiveData,
) {

	private val logger = logger()

	/**
	 * Returns recommended fee in micro-lamports.
	 *
	 * Recommended fee is always at least 10_000.
	 */
	@SentrySpan
	operator fun invoke(): Int {
		return priorityFeeLiveData.get().result.priorityFeeEstimate.toInt()
	}

	@SentryTransaction(operation = "scheduled.helius.get-recommended-fee")
	@Scheduled(cron = "0 * * * * *")
	fun trigger() {
		logger.info("RECOMMENDED_FEE_LIVE_DATA_REFRESH cron started")
		val result = priorityFeeLiveData.tryRefreshAndGet()
		logger.info("RECOMMENDED_FEE_LIVE_DATA_REFRESH cron ended. Set result: $result")
	}

	/**
	 * Returns lamports needed for transaction requesting `computeUnitLimit` amount of CU's
	 */
	fun forComputeUnitLimit(computeUnitLimit: Int): BaseAmount {
		val microLamportsPerCU = invoke().toBigDecimal()
		val microScale = BigDecimal.ONE.scaleByPowerOfTen(-6)
		val lamportsPerCU = microLamportsPerCU.multiply(microScale, MathContext.DECIMAL128)

		val priorityFee = lamportsPerCU * computeUnitLimit.toBigDecimal()

		return priorityFee.toBigInteger().asBaseAmount()
	}

	/**
	 * Returns recommended fee for Jito tip
	 *
	 * ```
	 * When using sendTransaction, it is recommended to use a 70/30 split between priority fee and jito tip(e.g.):
	 *
	 *     Priority Fee (70%): 0.7 SOL
	 *     +
	 *     Jito Tip (30%): 0.3 SOL
	 *     ===========================
	 *     Total Fee: 1.0 SOL
	 *
	 * So, when using sendTransaction:
	 *
	 * You would allocate 0.7 SOL as the priority fee.
	 * And 0.3 SOL as the Jito tip.
	 *
	 * ```
	 *
	 */
	fun forJitoTip(priorityFeeBaseAmount: BaseAmount): BaseAmount {
		val missingThirtyPerCent = (BigDecimal("30").divide(BigDecimal("70"), MathContext.DECIMAL32))
		val jitoTip = priorityFeeBaseAmount.amount.toBigDecimal() * missingThirtyPerCent
		return jitoTip.toBigInteger().asBaseAmount()
	}
}

@Component
class PriorityFeeLiveData(
	private val heliusClient: HeliusClient,
) : AbstractLiveDataService<GetRecommendedFeeResponse>() {

	override fun computeNewValue(): GetRecommendedFeeResponse {
		return heliusClient.getRecommendedFee()
	}
}
