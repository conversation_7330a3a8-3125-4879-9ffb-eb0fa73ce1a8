package com.cleevio.fatbot.adapter.out.solana.client

import com.cleevio.fatbot.adapter.out.BaseConnector
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class FatbotTsProxyConnector(
	@Value("\${integration.fatbot-ts-proxy.base-url}") baseUrl: String,
) : BaseConnector(
	baseUrl = baseUrl,
) {
	data class GetClmmSwapDataResponse(
		val expectedAmountOut: BigInteger,
		val remainingAccounts: List<AddressWrapper>,
		val ammConfig: AddressWrapper,
		val observationId: AddressWrapper,
	)

	fun getCLMMSwapData(poolAddress: AddressWrapper, inputTokenMint: AddressWrapper, inputAmount: BaseAmount) = restClient
		.get()
		.uri { uriBuilder ->
			uriBuilder
				.path("/clmm-swap-accounts")
				.queryParam("poolAddress", poolAddress.getAddressString())
				.queryParam("inputTokenMint", inputTokenMint.getAddressString())
				.queryParam("inputAmount", inputAmount.amount)
				.build()
		}
		.retrieveResponseWithErrorHandler<GetClmmSwapDataResponse>()
}
