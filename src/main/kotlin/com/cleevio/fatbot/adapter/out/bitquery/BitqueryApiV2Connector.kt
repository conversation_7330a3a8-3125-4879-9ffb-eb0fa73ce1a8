package com.cleevio.fatbot.adapter.out.bitquery

import com.apollographql.apollo3.api.Optional
import com.apollographql.apollo3.network.http.HttpInfo
import com.cleevio.fatbot.adapter.ApolloConnector
import com.cleevio.fatbot.adapter.out.bitquery.dto.BitqueryProxyEvmTradesResponse
import com.cleevio.fatbot.application.module.market.exception.UnsupportedChainIdException
import com.cleevio.fatbot.application.module.token.constant.TimeInterval
import com.cleevio.fatbot.application.module.token.query.TokenOHLCTimeIntervalItem
import com.cleevio.fatbot.application.module.token.query.TokenPriceTimeIntervalItem
import com.cleevio.fatbot.graphql.bitquery.v2.type.OLAP_DateTimeIntervalUnits
import com.cleevio.fatbot.graphql.bitquery.v2.type.evm_network
import com.cleevio.fatbot.graphql.bitquery.v2operation.EVMTokenHistoricalDataQuery
import com.cleevio.fatbot.graphql.bitquery.v2operation.EVMTokenPriceQuery
import com.cleevio.fatbot.infrastructure.config.properties.BitqueryProperties
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import com.cleevio.fatbot.infrastructure.config.properties.FatbotBitqueryProxyProperties
import io.sentry.spring.jakarta.tracing.SentrySpan
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.runBlocking
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.bodyToFlow
import java.time.Instant
import java.time.ZoneOffset

@Component
class BitqueryApiV2Connector(
	fatbotBitqueryProxyProperties: FatbotBitqueryProxyProperties,
	bitqueryProperties: BitqueryProperties,
	val chainProperties: ChainProperties,
) : ApolloConnector(
	graphQlEndpoint = bitqueryProperties.graphqlApiV2Endpoint,
	accessToken = bitqueryProperties.accessToken,
) {

	private val webClient = WebClient
		.builder()
		.baseUrl(fatbotBitqueryProxyProperties.baseUrl)
		.defaultHeader(HttpHeaders.AUTHORIZATION, fatbotBitqueryProxyProperties.apiKey.toString())
		.build()

	@SentrySpan
	fun getEvmTokenPrice(
		chainId: Long,
		pairAddress: String,
		tokenAddress: String,
		timeInterval: TimeInterval,
		intervalCount: Int,
		limit: Int,
		after: Instant?,
		before: Instant?,
	): List<TokenPriceTimeIntervalItem> {
		val properties = chainProperties.ofEvmChainId(chainId = chainId)

		// Bitquery suggested performance improvement (presumably they index the date field)
		val dateAfter = after?.atZone(ZoneOffset.UTC)?.toLocalDate()?.minusDays(1)?.toString()

		val query = apolloClient.query(
			EVMTokenPriceQuery(
				network = chainId.toBitqueryNetwork(),
				currencyAddress = properties.wethAddress.getAddressString(),
				tokenAddress = tokenAddress,
				pairAddress = pairAddress,
				timeInterval = timeInterval.toBitqueryInterval(),
				intervalCount = intervalCount,
				limit = limit,
				afterDate = Optional.presentIfNotNull(dateAfter),
				afterDateTime = Optional.presentIfNotNull(after),
			),
		)

		val result = runBlocking { query.execute() }

		val queryId = (result.executionContext as? HttpInfo)?.headers
			?.find { it.name == "x-bitquery-gql-query-id" }?.value

		logger.info("Fetched EVMTokenPriceQuery with queryId: $queryId")

		return result.data?.EVM?.DEXTradeByTokens?.map { it.toTimeIntervalItem() }
			?: error("Invalid Bitquery response for EVMTokenPriceQuery.")
	}

	@SentrySpan
	fun getEvmTokenHistoricalData(
		chainId: Long,
		tokenAddress: String,
		timeInterval: TimeInterval,
		intervalCount: Int,
		limit: Int,
		after: Instant?,
		before: Instant?,
	): List<TokenOHLCTimeIntervalItem> {
		val properties = chainProperties.ofEvmChainId(chainId = chainId)

		// Bitquery suggested performance improvement (presumably they index the date field)
		val beforeDate = before?.atZone(ZoneOffset.UTC)?.toLocalDate()?.plusDays(1)?.toString()
		val afterDate = after?.atZone(ZoneOffset.UTC)?.toLocalDate()?.minusDays(1)?.toString()

		val query = apolloClient.query(
			EVMTokenHistoricalDataQuery(
				network = chainId.toBitqueryNetwork(),
				currencyAddress = properties.wethAddress.getAddressString(),
				tokenAddress = tokenAddress,
				timeInterval = timeInterval.toBitqueryInterval(),
				intervalCount = intervalCount,
				limit = limit,
				after = Optional.presentIfNotNull(after),
				afterDate = Optional.presentIfNotNull(afterDate),
				before = Optional.presentIfNotNull(before),
				beforeDate = Optional.presentIfNotNull(beforeDate),
			),
		)

		val result = runBlocking { query.execute() }

		val queryId = (result.executionContext as? HttpInfo)?.headers
			?.find { it.name == "x-bitquery-gql-query-id" }?.value

		logger.info("Fetched EVMTokenHistoricalData with queryId: $queryId")

		return result.data?.EVM?.DEXTradeByTokens?.map { it.toTimeIntervalItem() }
			?: error("Invalid Bitquery response for EVMTokenHistoricalData.")
	}

	@SentrySpan
	fun subscribeToEvmTokenTrades(): Flow<BitqueryProxyEvmTradesResponse> {
		return webClient
			.get()
			.uri("/internal/stream/bitquery/evm-trades")
			.retrieve()
			.bodyToFlow()
	}

	private fun Long.toBitqueryNetwork() = when (this) {
		1L -> evm_network.eth
		8453L -> evm_network.base
		56L -> evm_network.bsc
		42161L -> evm_network.arbitrum
		else -> throw UnsupportedChainIdException("Unsupported chain id $this")
	}

	private fun TimeInterval.toBitqueryInterval(): OLAP_DateTimeIntervalUnits = when (this) {
		TimeInterval.SECOND -> OLAP_DateTimeIntervalUnits.seconds
		TimeInterval.MINUTE -> OLAP_DateTimeIntervalUnits.minutes
		TimeInterval.HOUR -> OLAP_DateTimeIntervalUnits.hours
		TimeInterval.DAY -> OLAP_DateTimeIntervalUnits.days
	}
}

fun EVMTokenPriceQuery.DEXTradeByToken.toTimeIntervalItem() = TokenPriceTimeIntervalItem(
	timestamp = Block?.Time.orError(),
	close = price?.toBigDecimal().orError(),
	closeNative = priceNative?.toBigDecimal().orError(),
	volume = volume?.toBigDecimal().orError(),
)

fun EVMTokenHistoricalDataQuery.DEXTradeByToken.toTimeIntervalItem() = TokenOHLCTimeIntervalItem(
	timestamp = Block?.Time.orError(),
	openUsd = Trade?.open?.toBigDecimal().orError(),
	openNative = Trade?.openNative?.toBigDecimal().orError(),
	highUsd = high?.toBigDecimal().orError(),
	lowUsd = low?.toBigDecimal().orError(),
	closeUsd = Trade?.close?.toBigDecimal().orError(),
	closeNative = Trade?.closeNative?.toBigDecimal().orError(),
	volumeUsd = volume?.toBigDecimal().orError(),
)

private fun <T : Any> T?.orError(message: String = "Error parsing token price data."): T = this ?: error(message)
