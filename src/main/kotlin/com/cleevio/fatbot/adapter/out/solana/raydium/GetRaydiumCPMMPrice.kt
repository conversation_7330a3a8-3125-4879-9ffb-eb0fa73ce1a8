package com.cleevio.fatbot.adapter.out.solana.raydium

import com.cleevio.fatbot.adapter.out.solana.client.getTokenAccountBalances
import com.cleevio.fatbot.adapter.out.solana.raydium.util.getCPMMPoolVaultPda
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.WSOL_MINT
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.stereotype.Component
import java.math.MathContext

@Component
class GetRaydiumCPMMPrice(
	private val rpcClient: RpcClient,
) {
	data class Input(
		val poolAddress: AddressWrapper,
		val tokenAddress: AddressWrapper,
		val tokenDecimals: Int,
	)

	fun getMany(inputs: List<Input>): Map<AddressWrapper, LiquidityPricePair?> {
		if (inputs.isEmpty()) return emptyMap()

		val accounts = inputs.flatMap { input ->
			val tokenVault = getCPMMPoolVaultPda(input.poolAddress.getSolanaPublicKey(), input.tokenAddress.getSolanaPublicKey())
			val wsolVault = getCPMMPoolVaultPda(input.poolAddress.getSolanaPublicKey(), WSOL_MINT)

			listOf(tokenVault, wsolVault)
		}

		val accountsBalances = rpcClient.getTokenAccountBalances(accounts, Commitment.CONFIRMED).map { it?.toBigDecimal() }

		val liquidityToPricePairs = accountsBalances.chunked(2) { (tokenBalance, wsolBalance) ->
			if (wsolBalance == null || tokenBalance == null) return@chunked null

			val price = wsolBalance.divide(tokenBalance, MathContext.DECIMAL128)
			val liquidity = wsolBalance.times(tokenBalance).sqrt(MathContext.DECIMAL128)

			LiquidityPricePair(liquidity, price.asNativeAmount())
		}

		val poolAddressToLiquidityPrice = inputs.zip(liquidityToPricePairs).associate { (input, liquidityPricePair) ->
			val pair = liquidityPricePair?.copy(
				price = liquidityPricePair.price.toBase(input.tokenDecimals).toNative(Chain.SOLANA),
			)

			input.poolAddress to pair
		}

		return poolAddressToLiquidityPrice
	}
}
