package com.cleevio.fatbot.adapter.out.bitquery

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.constant.TimeInterval
import com.cleevio.fatbot.application.module.token.port.out.GetTokenCandleChartHistoricalData
import com.cleevio.fatbot.application.module.token.query.TokenOHLCTimeIntervalItem
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import org.springframework.stereotype.Component
import java.time.Instant

@Component
class GetTokenCandleChartHistoricalDataBitquery(
	private val bitqueryEapConnector: BitqueryEapConnector,
	private val bitqueryApiV2Connector: BitqueryApiV2Connector,
) : GetTokenCandleChartHistoricalData {

	override fun invoke(
		chain: Chain,
		tokenAddress: AddressWrapper,
		isPumpfunExclusive: Boolean,
		intervalCount: Int,
		timeInterval: TimeInterval,
		limit: Int,
		before: Instant?,
		after: Instant?,
	): List<TokenOHLCTimeIntervalItem> {
		return when (chain.type) {
			ChainType.SOLANA -> bitqueryEapConnector.getSolanaTokenHistoricalData(
				tokenAddress = tokenAddress,
				isPumpfunExclusive = isPumpfunExclusive,
				timeInterval = timeInterval,
				intervalCount = intervalCount,
				limit = limit,
				before = before,
				after = after,
			)
			ChainType.EVM -> bitqueryApiV2Connector.getEvmTokenHistoricalData(
				chainId = chain.evmId,
				tokenAddress = tokenAddress.getAddressString(),
				timeInterval = timeInterval,
				intervalCount = intervalCount,
				before = before,
				after = after,
				limit = limit,
			)
		}
	}
}
