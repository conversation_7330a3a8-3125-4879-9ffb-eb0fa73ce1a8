package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bottransaction.port.out.FindAllBotTokenInfo
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.tables.references.BOT_TOKEN_INFO
import org.jooq.DSLContext
import org.springframework.stereotype.Component

@Component
class FindAllBotTokenInfoJooq(
	private val dslContext: DSLContext,
) : FindAllBotTokenInfo {

	override fun invoke(
		tokenAddresses: Set<AddressWrapper>,
		fileToUrlMapper: FileToUrlMapper,
	): Set<FindAllBotTokenInfo.Result> {
		if (tokenAddresses.isEmpty()) return emptySet()

		val conditions = listOf(
			BOT_TOKEN_INFO.ADDRESS.`in`(tokenAddresses),
		)

		return dslContext.select(
			BOT_TOKEN_INFO.NAME,
			BOT_TOKEN_INFO.SYMBOL,
			BOT_TOKEN_INFO.IMAGE_FILE_ID,
			BOT_TOKEN_INFO.DECIMALS,
			BOT_TOKEN_INFO.ADDRESS,
			BOT_TOKEN_INFO.file.ID,
			BOT_TOKEN_INFO.file.EXTENSION,
		)
			.from(BOT_TOKEN_INFO)
			.where(conditions)
			.fetch()
			.mapToSet {
				FindAllBotTokenInfo.Result(
					tokenName = it[BOT_TOKEN_INFO.NAME]!!,
					tokenSymbol = it[BOT_TOKEN_INFO.SYMBOL]!!,
					tokenImageUrl = fileToUrlMapper(it[BOT_TOKEN_INFO.file.ID], it[BOT_TOKEN_INFO.file.EXTENSION]),
					tokenAddress = it[BOT_TOKEN_INFO.ADDRESS]!!,
					tokenDecimals = it[BOT_TOKEN_INFO.DECIMALS]!!.toInt(),
				)
			}
	}
}
