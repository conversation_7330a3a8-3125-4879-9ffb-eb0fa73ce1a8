package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.adapter.out.evm.contract.FatbotRouter
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.util.toUUID
import com.cleevio.fatbot.application.module.transaction.constant.BuySellTransactionSuccess
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailure
import com.cleevio.fatbot.application.module.transaction.constant.TransactionFailureReason
import com.cleevio.fatbot.application.module.transaction.constant.TransactionPending
import com.cleevio.fatbot.application.module.transaction.constant.TransactionResult
import com.cleevio.fatbot.application.module.transaction.constant.TransactionSuccess
import com.cleevio.fatbot.application.module.transaction.port.out.GetTransactionResults
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import org.web3j.protocol.core.DefaultBlockParameterNumber
import org.web3j.protocol.core.methods.request.Transaction
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt
import org.web3j.protocol.core.methods.response.TransactionReceipt
import kotlin.jvm.optionals.getOrNull

@Component
class GetTransactionResultsEVM(
	private val evmChainContextFactory: EvmChainContextFactory,
) : GetTransactionResults {

	@SentrySpan
	override fun invoke(txHashes: Set<TxHash>, chainId: Long): Map<TxHash, TransactionResult> {
		return evmChainContextFactory.ofChainId(chainId = chainId) {
			val batchRequest = client.getWeb3j().newBatch()

			val txHashOrder = txHashes.withIndex().associate { (index, txHash) ->
				// add request, not triggering any network communication so far
				batchRequest.add(client.getWeb3j().ethGetTransactionReceipt(txHash.txHash))
				txHash to index
			}

			// send to RPC node in one HTTP request
			val batchResponse = runCatching { batchRequest.send() }
				.getOrElse { ex -> error("Batch request failed: ${ex.message}.") }

			txHashOrder.mapValues { (_, index) ->
				// responses are guaranteed to be in order of requests put in Batch
				val response = batchResponse.responses[index] as EthGetTransactionReceipt
				determineTransactionStatus(response.transactionReceipt.getOrNull(), chainId)
			}
		}
	}

	fun determineTransactionStatus(transactionReceipt: TransactionReceipt?, chainId: Long): TransactionResult {
		return when {
			transactionReceipt == null -> TransactionPending()
			transactionReceipt.isStatusOK -> {
				val event = FatbotRouter.getTokensSuccessfullySwappedEvents(transactionReceipt).firstOrNull()
					?: return TransactionSuccess()

				BuySellTransactionSuccess(
					amountIn = event.amountIn,
					amountOut = event.amountOut,
					fee = event.fee,
					referralFee = event.referralFee,
					referralRewardRecipient = event.uuid.toUUID(),

					// For now this value is not used at all on EVM side and is here
					// only for need of BotTransactions
					balanceChange = BaseAmount.ZERO,
				)
			}

			else -> {
				val isFailureSlippage = determineTransactionFailureIsSlippage(
					txHash = transactionReceipt.transactionHash,
					chainId = chainId,
				)

				TransactionFailure(
					// For now this value is not used at all on EVM side and is here
					// only for need of BotTransactions
					balanceChange = BaseAmount.ZERO,
					isNoTokensOwnedErrorHint = null,
					failReason = when (isFailureSlippage) {
						true -> TransactionFailureReason.SLIPPAGE
						false -> TransactionFailureReason.UNDEFINED
					},
				)
			}
		}
	}

	private fun determineTransactionFailureIsSlippage(txHash: String, chainId: Long): Boolean {
		val resultMessage = evmChainContextFactory.ofChainId(chainId = chainId) {
			val web3j = client.getWeb3j()

			val ethTransaction = web3j.ethGetTransactionByHash(txHash).send()
			val transaction = ethTransaction.transaction.get()

			val callTransaction = Transaction.createEthCallTransaction(
				transaction.from,
				transaction.to,
				transaction.input,
			)
			val blockParameter = DefaultBlockParameterNumber(transaction.blockNumber)
			val ethCall = web3j.ethCall(callTransaction, blockParameter).send()
			ethCall.error.message
		}

		return SLIPPAGE_ERROR_MESSAGES.any { it in resultMessage }
	}
}

private val SLIPPAGE_ERROR_MESSAGES =
	listOf("INSUFFICIENT_OUTPUT_AMOUNT", "Too little received", "Price slippage check")
