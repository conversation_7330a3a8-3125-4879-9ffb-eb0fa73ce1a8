package com.cleevio.fatbot.adapter.out.solana.pumpfun

import com.cleevio.fatbot.adapter.out.solana.pumpfun.model.PumpFunCurveState
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutPumpfun
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.stereotype.Component

@Component
class GetAmountOutPumpfunSolana(
	private val rpcClient: RpcClient,
) : GetAmountOutPumpfun {

	override operator fun invoke(amount: BaseAmount, pairAddress: AddressWrapper, isBuy: Boolean): BaseAmount {
		val curveAccountInfo = rpcClient.api.getAccountInfo(pairAddress.getSolanaPublicKey())
		val curveState = PumpFunCurveState.read(curveAccountInfo.decodedData, pairAddress)

		val amountOut = fromCurveState(amount, curveState, isBuy)
		return amountOut
	}

	fun fromCurveState(amount: BaseAmount, curveState: PumpFunCurveState, isBuy: Boolean): BaseAmount {
		// Zero input or migrated bonding curve
		if (amount == BaseAmount.ZERO || curveState.virtualTokenReserves == BaseAmount.ZERO) return BaseAmount.ZERO

		return if (isBuy) {
			calculateBuyAmount(amount = amount, curveState = curveState)
		} else {
			calculateSellAmount(amount = amount, curveState = curveState)
		}
	}

	private fun calculateBuyAmount(amount: BaseAmount, curveState: PumpFunCurveState): BaseAmount {
		val maxBps = BasisPoint.MAX_VALUE.toBigInteger()
		val pumpfunTotalFeeBps = SolanaConstants.PUMPFUN_GLOBAL_TOTAL_FEE_BP.value.toBigInteger()

		val inputAmount = (amount.amount * maxBps) / (pumpfunTotalFeeBps + maxBps)

		val virtualSolReserves = curveState.virtualSolReserves.amount
		val virtualTokenReserves = curveState.virtualTokenReserves.amount

		val tokenAmount = (inputAmount * virtualTokenReserves) / (virtualSolReserves + inputAmount)

		val amountOut = minOf(tokenAmount, curveState.realTokenReserves.amount)

		return amountOut.asBaseAmount()
	}

	private fun calculateSellAmount(amount: BaseAmount, curveState: PumpFunCurveState): BaseAmount {
		val virtualSolReserves = curveState.virtualSolReserves.amount
		val virtualTokenReserves = curveState.virtualTokenReserves.amount

		// Calculate the proportional amount of virtual sol reserves to be received
		val solAmount = (amount.amount * virtualSolReserves) / (virtualTokenReserves + amount.amount)

		val pumpfunFee = SolanaConstants.PUMPFUN_GLOBAL_FEE_BP.applyOnCeilDiv(solAmount)
		val creatorFee = SolanaConstants.PUMPSWAP_CREATOR_FEE_BP.applyOnCeilDiv(solAmount)

		val solAmountOut = solAmount - pumpfunFee - creatorFee

		return solAmountOut.asBaseAmount()
	}
}
