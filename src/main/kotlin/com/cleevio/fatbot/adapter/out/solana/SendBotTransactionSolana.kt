package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.adapter.out.solana.pumpfun.util.getPumpfunBondingCurvePDA
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.toAddress
import com.cleevio.fatbot.application.common.util.getAssociatedTokenAddress
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.matchmaking.event.BotBuyTransactionRequest
import com.cleevio.fatbot.application.module.matchmaking.event.BotForceSellTokenRequest
import com.cleevio.fatbot.application.module.matchmaking.event.BotHighPriorityTransactionSentEvent
import com.cleevio.fatbot.application.module.matchmaking.event.BotLowPriorityTransactionSentEvent
import com.cleevio.fatbot.application.module.matchmaking.event.BotRequestedTransactionEvent
import com.cleevio.fatbot.application.module.matchmaking.event.BotSellPromotedTokenRequest
import com.cleevio.fatbot.application.module.matchmaking.event.BotSellRedFlaggedTokenRequest
import com.cleevio.fatbot.application.module.matchmaking.event.BotSellStaleTokenRequest
import com.cleevio.fatbot.application.module.matchmaking.event.BotSellTransactionRequest
import com.cleevio.fatbot.application.module.matchmaking.event.HighPriorityBotTransaction
import com.cleevio.fatbot.application.module.matchmaking.event.LowPriorityBotTransaction
import com.cleevio.fatbot.application.module.matchmaking.port.out.SendBotTransaction
import com.cleevio.fatbot.application.module.referral.ReferralRewardService.Companion.ZERO_UUID
import com.cleevio.fatbot.application.module.token.finder.BotTokenInfoFinderService
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import io.sentry.Sentry
import io.sentry.SpanStatus
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.bitcoinj.core.Base58
import org.p2p.solanaj.core.Account
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.RpcSendTransactionConfig
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import kotlin.math.absoluteValue
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds

@Component
class SendBotTransactionSolana(
	private val chainProperties: ChainProperties,
	@Qualifier("stakedRpcClient") private val rcpClient: RpcClient,
	@Qualifier("alchemyRpcClient") private val repeaterRpcClient: RpcClient,
	private val buyTokenSolana: BuyTokenSolana,
	private val sellTokenSolana: SellTokenSolana,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val botTokenInfoFinderService: BotTokenInfoFinderService,
) : SendBotTransaction {

	private val logger = logger()
	private val scope = CoroutineScope(
		Dispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)

	private val mainChannel: Channel<BotRequestedTransactionEvent> = Channel(capacity = Channel.UNLIMITED)

	private val fanOutFactor = 16
	private val fanOutChannels: List<Channel<HighPriorityBotTransaction>> =
		List(fanOutFactor) { Channel(capacity = Channel.UNLIMITED) }

	private val alchemyRepeaterChannels: List<Channel<SignedTx>> =
		List(fanOutFactor) { Channel(capacity = Channel.UNLIMITED) }

	private val lowPriorityChannelNumber = -1
	private val lowPriorityChannel: Channel<LowPriorityBotTransaction> = Channel(capacity = Channel.UNLIMITED)
	private val alchemyRepeaterLowPriorityChannel: Channel<SignedTx> = Channel(capacity = Channel.UNLIMITED)

	@EventListener(ApplicationReadyEvent::class)
	fun onStartup() {
		if (chainProperties.svm.solana.warmUpHeliusStakedNodeCache) {
			scope.launch { heliusCacheWarming() }
		}

		fanOutChannels.forEachIndexed { number, channel ->
			val repeaterChannel = alchemyRepeaterChannels[number]
			scope.launch {
				highPriorityChannelProcessor(
					channel = channel,
					repeater = repeaterChannel,
					channelNumber = number,
				)
			}
			scope.launch {
				alchemyRepeaterChannelProcessor(
					channel = repeaterChannel,
					channelNumber = number,
				)
			}
		}

		scope.launch { lowPriorityChannelProcessor(lowPriorityChannel, lowPriorityChannelNumber) }
		scope.launch {
			alchemyRepeaterChannelProcessor(
				channel = alchemyRepeaterLowPriorityChannel,
				channelNumber = lowPriorityChannelNumber,
			)
		}
		scope.launch { mainChanelProcessor() }
	}

	// Based on: https://docs.helius.dev/guides/sending-transactions-on-solana#recommended-optimizations-for-traders
	suspend fun heliusCacheWarming() {
		while (true) {
			runCatching { rcpClient.api.health }
			delay(1.seconds)
		}
	}

	override fun invoke(tx: BotRequestedTransactionEvent) {
		// This will never block as it's UNLIMITED size channel
		runBlocking { mainChannel.send(tx) }
	}

	suspend fun mainChanelProcessor() {
		for (event in mainChannel) {
			when (event) {
				// This type of event is highly time-sensitive, so it will go to fanOut service
				// and must be sent ASAP
				is HighPriorityBotTransaction -> {
					val channelIndex = determineChannelIndex(tokenAddress = event.tokenAddress)
					// This will never block as it's UNLIMITED size channel
					fanOutChannels[channelIndex].send(event)
				}

				// These are not so much time-sensitive events, so they will go to separate sequential channel
				is LowPriorityBotTransaction -> {
					// This will never block as it's UNLIMITED size channel
					lowPriorityChannel.send(event)
				}
			}
		}
	}

	// Deterministic mapping AddressWrapper -> Int
	// This way all transactions on same token will be sent in received order
	private fun determineChannelIndex(tokenAddress: AddressWrapper): Int {
		// TODO: There is definitely a better way to do this than remainder of first byte...
		return tokenAddress.getAddressBytes().first().toInt().absoluteValue % fanOutChannels.size
	}

	suspend fun highPriorityChannelProcessor(
		channel: Channel<HighPriorityBotTransaction>,
		repeater: Channel<SignedTx>,
		channelNumber: Int,
	) {
		for (event in channel) {
			val signedTx = inSentryTransaction(
				sentryTxName = "SendBotTransactionSolana.highPriorityChannelProcessor()",
				sentryOperationName = "async.send-high-priority-bot-transaction",
				channelNumber = channelNumber,
				blockSlot = event.blockSlot,
			) {
				when (event) {
					is BotBuyTransactionRequest -> sendBuyOrder(event, channelNumber)
					is BotSellTransactionRequest -> sendSellOrder(event, channelNumber)
					is BotSellPromotedTokenRequest -> sendSellPromotedTokenOrder(event, channelNumber)
					is BotSellRedFlaggedTokenRequest -> sendSellRedFlagOrder(event, channelNumber)
				}
			}

			signedTx?.let { repeater.send(signedTx) }
		}
	}

	suspend fun lowPriorityChannelProcessor(channel: Channel<LowPriorityBotTransaction>, channelNumber: Int) {
		for (event in channel) {
			val signedTx = inSentryTransaction(
				sentryTxName = "SendBotTransactionSolana.lowPriorityChannelProcessor()",
				sentryOperationName = "async.send-low-priority-bot-transaction",
				channelNumber = channelNumber,
				blockSlot = null,
			) {
				when (event) {
					is BotSellStaleTokenRequest -> sendStaleTokenSellOrder(data = event, channelNumber = channelNumber)
					is BotForceSellTokenRequest -> sendForceSellTokenSellOrder(
						data = event,
						channelNumber = channelNumber,
					)
				}
			}

			signedTx?.let { alchemyRepeaterLowPriorityChannel.send(signedTx) }

			// We delay the low priority channel a bit here (cca one block), in order not to overwhelm Helius when
			// a lot of stale tokens are found to be sold at the same time
			// TODO: Think of something more efficient?
			delay(500.milliseconds)
		}
	}

	suspend fun alchemyRepeaterChannelProcessor(channel: Channel<SignedTx>, channelNumber: Int) {
		for (signedTx in channel) {
			inSentryTransaction(
				sentryTxName = "SendBotTransactionSolana.alchemyRepeaterChannelProcessor()",
				sentryOperationName = "async.send-repeater-bot-transaction",
				channelNumber = channelNumber,
				blockSlot = null,
			) {
				repeaterRpcClient.api.sendRawTransaction(signedTx.signedTx, RpcSendTransactionConfig())
				signedTx
			}
		}
	}

	private fun inSentryTransaction(
		sentryTxName: String,
		sentryOperationName: String,
		channelNumber: Int,
		blockSlot: Int?,
		block: () -> SignedTx,
	): SignedTx? {
		val sentryTransaction = Sentry.startTransaction(
			sentryTxName,
			sentryOperationName,
		)
		sentryTransaction.spanContext.setTag("channelNumber", channelNumber.toString())
		sentryTransaction.spanContext.setTag("block", blockSlot.toString())

		return runCatching { block() }
			.onFailure { e ->
				logger.error("Block[$blockSlot] Channel #[$channelNumber] threw error:", e)

				sentryTransaction.throwable = e
				sentryTransaction.status = SpanStatus.INTERNAL_ERROR
				sentryTransaction.finish()
			}
			.onSuccess {
				sentryTransaction.finish()
			}
			.getOrNull()
	}

	fun sendBuyOrder(data: BotBuyTransactionRequest, channelNumber: Int): SignedTx {
		logger.info(
			"Block[${data.blockSlot}] " +
				"Channel #[$channelNumber] received request to send BUY transaction on token ${data.tokenAddress} " +
				"by botWalletId: ${data.botWalletId}",
		)
		val userAccount = Account(Base58.decode(data.privateKey))
		val pairAddress = getPumpfunBondingCurvePDA(mint = data.tokenAddress).toAddress()
		val associatedTokenAccount = getAssociatedTokenAddress(
			mint = data.tokenAddress.getSolanaPublicKey(),
			owner = userAccount.publicKey,
			programId = SolanaConstants.TOKEN_PROGRAM_ID,
		)

		val signedTx = buyTokenSolana.buyPumpfun(
			userAccount = Account(Base58.decode(data.privateKey)),
			bondingCurve = pairAddress,
			creatorAddress = data.creator,
			tokenAddress = data.tokenAddress,
			associatedTokenAccount = associatedTokenAccount,
			amount = data.tradeAmountInBase,
			feeBp = BasisPoint.ZERO,
			referralFeeBp = BasisPoint.ZERO,
			referralUUID = ZERO_UUID,
			useStakedEndpoint = true,
			useMevProtection = false,
		)

		applicationEventPublisher.publishEvent(
			BotHighPriorityTransactionSentEvent(
				type = BotTransactionType.BUY,
				blockSlot = data.blockSlot,
				botWalletId = data.botWalletId,
				tradeAmount = data.tradeAmountInBase.amount,
				signedTx = signedTx,
				tokenAddress = data.tokenAddress,
				chain = data.chain,
			),
		)

		return signedTx
	}

	fun sendSellOrder(data: BotSellTransactionRequest, channelNumber: Int): SignedTx {
		logger.info(
			"Block[${data.blockSlot}] " +
				"Channel #[$channelNumber] received request to send SELL transaction on token ${data.tokenAddress} " +
				"by botWalletId: ${data.botWalletId}",
		)
		val pairAddress = getPumpfunBondingCurvePDA(mint = data.tokenAddress).toAddress()
		val signedTx = sellTokenSolana.sellPumpfunAll(
			privateKey = data.privateKey,
			pairAddress = pairAddress,
			tokenAddress = data.tokenAddress,
			creatorAddress = data.creator,
			userId = data.userId,
			useStakedEndpoint = true,
		)

		applicationEventPublisher.publishEvent(
			BotHighPriorityTransactionSentEvent(
				type = BotTransactionType.SELL,
				botWalletId = data.botWalletId,
				tradeAmount = data.amountToSell,
				signedTx = signedTx,
				tokenAddress = data.tokenAddress,
				chain = data.chain,
				blockSlot = data.blockSlot,
			),
		)

		return signedTx
	}

	fun sendStaleTokenSellOrder(data: BotSellStaleTokenRequest, channelNumber: Int): SignedTx {
		logger.info(
			"Time[${data.requestedAt}] " +
				"Channel #[$channelNumber] received request to send SELL_STALE transaction on token ${data.tokenAddress} " +
				"by botWalletId: ${data.botWalletId}",
		)

		val botTokenInfo = botTokenInfoFinderService.getByTokenAddressAndChain(
			tokenAddress = data.tokenAddress,
			chain = data.chain,
		)
		val signedTx = if (botTokenInfo.isGraduated) {
			logger.info("Time[${data.requestedAt}] Channel #[$channelNumber] SELL_STALE sent to PUMP_SWAP")
			sellTokenSolana.sellPumpswapAll(
				privateKey = data.privateKey,
				pairAddress = botTokenInfo.graduatedToPairAddress!!,
				tokenAddress = data.tokenAddress,
				coinCreatorAddress = data.creator,
				targetTokenProgramId = SolanaConstants.TOKEN_PROGRAM_ID,
				userId = data.userId,
				useStakedEndpoint = true,
			)
		} else {
			logger.info("Time[${data.requestedAt}] Channel #[$channelNumber] SELL_STALE sent to PUMP_FUN")
			sellTokenSolana.sellPumpfunAll(
				privateKey = data.privateKey,
				pairAddress = AddressWrapper(getPumpfunBondingCurvePDA(mint = data.tokenAddress).toByteArray()),
				creatorAddress = data.creator,
				tokenAddress = data.tokenAddress,
				userId = data.userId,
				useStakedEndpoint = true,
			)
		}

		applicationEventPublisher.publishEvent(
			BotLowPriorityTransactionSentEvent(
				type = BotTransactionType.SELL,
				botWalletId = data.botWalletId,
				tradeAmount = data.amountToSell,
				signedTx = signedTx,
				tokenAddress = data.tokenAddress,
				chain = data.chain,
				requestedAt = data.requestedAt,
			),
		)

		return signedTx
	}

	fun sendForceSellTokenSellOrder(data: BotForceSellTokenRequest, channelNumber: Int): SignedTx {
		logger.info(
			"Time[${data.requestedAt}] " +
				"Channel #[$channelNumber] received request to send FORCE_SELL transaction on token ${data.tokenAddress} " +
				"by botWalletId: ${data.botWalletId}",
		)

		val botTokenInfo = botTokenInfoFinderService.getByTokenAddressAndChain(
			tokenAddress = data.tokenAddress,
			chain = data.chain,
		)
		val signedTx = if (botTokenInfo.isGraduated) {
			logger.info("Time[${data.requestedAt}] Channel #[$channelNumber] FORCE_SELL sent to PUMP_SWAP")
			sellTokenSolana.sellPumpswapAll(
				privateKey = data.privateKey,
				pairAddress = botTokenInfo.graduatedToPairAddress!!,
				tokenAddress = data.tokenAddress,
				coinCreatorAddress = data.creator,
				targetTokenProgramId = SolanaConstants.TOKEN_PROGRAM_ID,
				userId = data.userId,
				useStakedEndpoint = true,
			)
		} else {
			logger.info("Time[${data.requestedAt}] Channel #[$channelNumber] FORCE_SELL sent to PUMP_FUN")
			sellTokenSolana.sellPumpfunAll(
				privateKey = data.privateKey,
				pairAddress = AddressWrapper(getPumpfunBondingCurvePDA(mint = data.tokenAddress).toByteArray()),
				tokenAddress = data.tokenAddress,
				creatorAddress = data.creator,
				userId = data.userId,
				useStakedEndpoint = true,
			)
		}

		applicationEventPublisher.publishEvent(
			BotLowPriorityTransactionSentEvent(
				type = BotTransactionType.SELL,
				botWalletId = data.botWalletId,
				tradeAmount = data.amountToSell,
				signedTx = signedTx,
				tokenAddress = data.tokenAddress,
				chain = data.chain,
				requestedAt = data.requestedAt,
			),
		)

		return signedTx
	}

	fun sendSellPromotedTokenOrder(data: BotSellPromotedTokenRequest, channelNumber: Int): SignedTx {
		logger.info(
			"Block[${data.blockSlot}] " +
				"Channel #[$channelNumber] received request to send SELL_PROMOTED token ${data.tokenAddress} " +
				"by botWalletId: ${data.botWalletId}",
		)

		val signedTx = sellTokenSolana.sellPumpswapAll(
			privateKey = data.privateKey,
			pairAddress = data.pairAddress,
			tokenAddress = data.tokenAddress,
			coinCreatorAddress = data.creator,
			targetTokenProgramId = SolanaConstants.TOKEN_PROGRAM_ID,
			userId = data.userId,
			useStakedEndpoint = true,
		)

		applicationEventPublisher.publishEvent(
			BotHighPriorityTransactionSentEvent(
				type = BotTransactionType.SELL,
				blockSlot = data.blockSlot,
				botWalletId = data.botWalletId,
				tradeAmount = data.amountToSell.amount,
				signedTx = signedTx,
				tokenAddress = data.tokenAddress,
				chain = data.chain,
			),
		)

		return signedTx
	}

	fun sendSellRedFlagOrder(data: BotSellRedFlaggedTokenRequest, channelNumber: Int): SignedTx {
		logger.info(
			"Block[${data.blockSlot}] " +
				"Channel #[$channelNumber] received request to send SELL_RED_FLAGGED token ${data.tokenAddress} " +
				"by botWalletId: ${data.botWalletId}",
		)

		val pairAddress = getPumpfunBondingCurvePDA(mint = data.tokenAddress).toAddress()
		val signedTx = sellTokenSolana.sellPumpfunAll(
			privateKey = data.privateKey,
			pairAddress = pairAddress,
			tokenAddress = data.tokenAddress,
			creatorAddress = data.creator,
			userId = data.userId,
			useStakedEndpoint = true,
		)

		applicationEventPublisher.publishEvent(
			BotHighPriorityTransactionSentEvent(
				type = BotTransactionType.SELL,
				blockSlot = data.blockSlot,
				botWalletId = data.botWalletId,
				tradeAmount = data.amountToSell,
				signedTx = signedTx,
				tokenAddress = data.tokenAddress,
				chain = data.chain,
			),
		)

		return signedTx
	}
}
