package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.module.bot.port.out.FindAllBotWallet
import com.cleevio.fatbot.tables.references.BOT_WALLET
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class FindAllBotWalletJooq(
	private val dslContext: DSLContext,
) : FindAllBotWallet {
	override fun invoke(botIds: Set<UUID>): List<FindAllBotWallet.Result> {
		return dslContext.select(
			BOT_WALLET.ID,
			BOT_WALLET.BOT_ID,
			BOT_WALLET.BALANCE,
			BOT_WALLET.CHAIN,
		)
			.from(BOT_WALLET)
			.where(BOT_WALLET.BOT_ID.`in`(botIds))
			.fetch()
			.map {
				FindAllBotWallet.Result(
					botId = it[BOT_WALLET.BOT_ID]!!,
					botWalletId = it[BOT_WALLET.ID]!!,
					balance = it[BOT_WALLET.BALANCE]!!.toBigInteger().asBaseAmount(),
					chain = it[BOT_WALLET.CHAIN]!!,
				)
			}
	}
}
