package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.cache.AsyncTokenPriceCacheLoader
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.util.EpochClockTicker
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.market.port.out.GetTokenPrices
import com.github.benmanes.caffeine.cache.Caffeine
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.stereotype.Component
import java.time.Clock
import java.util.concurrent.CompletableFuture
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

@Component
class GetTokenPricesEVM(
	private val asyncTokenPriceCacheLoader: AsyncTokenPriceCacheLoader,
	clock: Clock,
) : GetTokenPrices {

	/**
	 * When key is added to the cache at time T, following will happen when fetching at different time intervals:
	 *
	 * ```
	 * (T..T+50s] -> the value is retrieved from cache directly, nothing else happens
	 *
	 * (T+50s..T+90s] -> the old value from cache will be returned and async process to update the stale keys will trigger
	 *
	 * (T+90..end of time) -> first the fetch of data will happen AND ONLY AFTER THAT the data will be returned
	 * ```
	 *
	 * Note: This pattern is also known as: stale-while-revalidate
	 */
	private val cache = Caffeine
		.newBuilder()
		.maximumSize(5000)
		.refreshAfterWrite(50.seconds.toJavaDuration())
		.expireAfterWrite(90.seconds.toJavaDuration())
		.ticker(EpochClockTicker(clock))
		.buildAsync(asyncTokenPriceCacheLoader)

	@SentrySpan
	override fun invoke(tokens: Set<ChainAddress>): Map<ChainAddress, BaseAmount> {
		// TODO: We should move / refactor this as Solana prices are now fetched in .evm port out
		return cache
			.getAll(tokens)
			.join()
	}

	@SentrySpan
	override fun getSingle(token: ChainAddress): BaseAmount {
		return cache.get(token).join()
	}

	override fun getSingleWithoutCoalescing(token: ChainAddress): BaseAmount {
		val existingEntry = cache.getIfPresent(token)

		if (existingEntry != null) {
			return existingEntry.join()
		}

		val result = asyncTokenPriceCacheLoader.getResults(setOf(token)).values.single()

		cache.put(token, CompletableFuture.completedFuture(result))

		return result
	}
}
