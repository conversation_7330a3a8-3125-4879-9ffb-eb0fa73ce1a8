package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.adapter.out.evm.model.GasInfo
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.minusBasisPoints
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.port.out.BuyToken
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.referral.ReferralRewardService.Companion.ZERO_UUID
import com.cleevio.fatbot.application.module.transaction.fee.FeeDetailsProvider
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component
import org.web3j.crypto.Credentials
import org.web3j.crypto.RawTransaction
import java.math.BigInteger
import java.util.UUID

@Component
class BuyTokenEVM(
	private val evmChainContextFactory: EvmChainContextFactory,
	private val feeDetailsProvider: FeeDetailsProvider,
) : BuyToken<BuyTokenEVM.EvmInput> {

	data class EvmInput(
		val userId: UUID,
		val tokenAddress: AddressWrapper,
		val privateKey: String,
		val tokenChainId: Long,
		val buyForBaseAmount: BaseAmount,
		val expectedAmountOut: BaseAmount,
		val gasInfoHint: GasInfo?,
		val dexPairInfo: GetDex.Evm,
		val gasLimit: BigInteger,
	)

	override fun invoke(input: EvmInput): SignedTx = evmChainContextFactory.ofChainId(chainId = input.tokenChainId) {
		val credentials = Credentials.create(input.privateKey)
		val nonce = client.getNonce(credentials)

		val functionCall = getEncodedFunctionCall(
			userId = input.userId,
			tokenAddress = input.tokenAddress,
			tokenChainId = input.tokenChainId,
			buyForBaseAmount = input.buyForBaseAmount,
			expectedAmountOut = input.expectedAmountOut,
			slippageBp = properties.buySellSlippageBp,
			credentials = credentials,
			dexPairInfo = input.dexPairInfo,
		)
		val gasInfo = input.gasInfoHint ?: client.getGasInfo()

		val rawTx = RawTransaction.createTransaction(
			input.tokenChainId,
			nonce,
			input.gasLimit,
			properties.fatbotRouter.getAddressString(),
			input.buyForBaseAmount.amount,
			functionCall,
			gasInfo.maxPriorityFeePerGas,
			gasInfo.maxFeePerGas,
		)

		sendTransaction(signedTx = client.signTx(rawTx, credentials))
	}

	fun getEncodedFunctionCall(
		userId: UUID,
		tokenAddress: AddressWrapper,
		tokenChainId: Long,
		buyForBaseAmount: BaseAmount,
		expectedAmountOut: BaseAmount,
		slippageBp: BasisPoint,
		credentials: Credentials,
		dexPairInfo: GetDex.Evm,
	): String {
		val feeDetails = feeDetailsProvider.getFeeDetails(
			transactingUserId = userId,
			token = tokenAddress.toChainAddress(Chain.ofEVM(tokenChainId)),
		)

		// The contract first subtract the platformFees and then performs swap
		val minAmountOut = expectedAmountOut.minusBasisPoints(feeDetails.platformFeeBps).minusBasisPoints(slippageBp)

		return FatbotRouterFunctionCallEncoder.buyFunctionCall(
			amountIn = buyForBaseAmount.amount,
			minAmountOut = minAmountOut.amount,
			tokenAddress = tokenAddress,
			toAddress = credentials.address,
			dexPairInfo = dexPairInfo,
			referralUUID = feeDetails.referrerId ?: ZERO_UUID,
			platformFeePercentage = feeDetails.platformFeeBps.toBigInteger(),
			referralFeePercentage = feeDetails.referrerFeeBps.toBigInteger(),
		)
	}
}
