package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.stereotype.Component
import kotlin.jvm.optionals.getOrNull

@Component
class GetWalletBalanceSolana(
	private val rpcClient: RpcClient,
) {

	@SentrySpan
	operator fun invoke(address: AddressWrapper): BaseAmount {
		val balance = rpcClient.api.getBalance(address.getSolanaPublicKey())

		return balance.toBigInteger().asBaseAmount()
	}

	@SentrySpan
	fun getMany(addresses: List<AddressWrapper>): Map<AddressWrapper, BaseAmount> {
		val accounts = rpcClient.api.getMultipleAccountsOptional(
			addresses.map { it.getSolanaPublicKey() },
			// We don't need any data from the account
			mapOf("dataSlice" to mapOf("offset" to 0, "length" to 0)),
		)

		val addressToBalance = addresses.zip(accounts) { address, account ->
			val balance = account.getOrNull()?.lamports?.toLong() ?: 0L

			address to balance.toBigInteger().asBaseAmount()
		}.toMap()

		return addressToBalance
	}
}
