package com.cleevio.fatbot.adapter.out.messagingproxy

import com.cleevio.fatbot.infrastructure.config.properties.FatbotApiProperties
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class FatbotMessagingProxyConnector(
	fatbotApiProperties: FatbotApiProperties,
	@Value("\${integration.fatbot-messaging-proxy.base-url}") private val baseUrl: String,
) {

// 	val webClient = WebClient
// 		.builder()
// 		.baseUrl(baseUrl)
// 		.defaultHeader(HttpHeaders.AUTHORIZATION, fatbotApiProperties.apiKey.toString())
// 		.build()
//
// 	suspend fun broadcast(message: BotTransactionCreatedMessage): ResponseEntity<Void> = webClient
// 		.post()
// 		.uri("/internal/messages/bot-transactions/broadcast")
// 		.bodyValue(message)
// 		.retrieve()
// 		.onStatus({ !it.is2xxSuccessful }) { error("Error while trying to broadcast bot transaction") }
// 		.awaitBodilessEntity()
//
// 	fun subscribeToBotTransactionMessages() = webClient
// 		.get()
// 		.uri("/internal/messages/bot-transactions")
// 		.retrieve()
// 		.bodyToFlow<BotTransactionCreatedMessage>()
}
