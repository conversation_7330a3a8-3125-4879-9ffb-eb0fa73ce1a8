package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.adapter.out.solana.client.getTokenAccountBalances
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.AMOUNT_OF_LAMPORTS_PER_SIGNATURE
import com.cleevio.fatbot.application.common.crypto.SolanaConstants.WSOL_MINT
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.plus
import com.cleevio.fatbot.application.common.util.EpochClockTicker
import com.cleevio.fatbot.application.common.util.getAssociatedTokenAddress
import com.cleevio.fatbot.application.module.market.port.out.EstimateGas
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.application.module.wallet.util.getAddressFromPrivateKey
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import com.github.benmanes.caffeine.cache.Caffeine
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.p2p.solanaj.core.PublicKey
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import java.time.Clock
import java.time.Duration
import java.time.temporal.ChronoUnit

private const val TOKEN_ACCOUNT_SIZE = 165L

@Component
class EstimateGasSolana(
	val chainProperties: ChainProperties,
	val rpcClient: RpcClient,
	val getRecommendedFeeSolana: GetRecommendedFeeSolana,
	clock: Clock,
) : EstimateGas {

	@Lazy
	@Autowired
	private lateinit var self: EstimateGasSolana // for SentrySpans to work

	// Cannot be private, otherwise throws error when called from `self` scope
	protected val rentExemptionCache = Caffeine
		.newBuilder()
		.refreshAfterWrite(Duration.of(1, ChronoUnit.DAYS))
		.expireAfterWrite(Duration.of(1, ChronoUnit.DAYS))
		.ticker(EpochClockTicker(clock))
		.build<Long, BaseAmount> { accountSize -> getMinimumBalanceForRentExemption(accountSize) }

	override fun supports(): ChainType = ChainType.SOLANA

	// For now, we are assuming that every transaction created by Fatbot can have
	// exactly one signature
	private val feeConstantBaseAmount
		// get() because direct assignment produces BaseAmount(null) for some reason
		get() = (1L * AMOUNT_OF_LAMPORTS_PER_SIGNATURE)
			.toBigInteger()
			.asBaseAmount()

	override fun invoke(input: EstimateGas.Input): EstimateGas.ResultSolana {
		return when (input) {
			is EstimateGas.BuyInput -> self.handleBuy(input)
			is EstimateGas.SellInput -> self.handleSell(input)
			is EstimateGas.TransferCurrencyInput -> self.handleTransferCurrency(input)
			is EstimateGas.TransferTokenInput -> self.handleTransferToken(input)
			is EstimateGas.ApproveInput -> throw UnsupportedOperationException()
		}
	}

	@SentrySpan
	protected fun handleBuy(input: EstimateGas.BuyInput): EstimateGas.ResultSolana {
		val computeUnitLimit = when (input.dexPairInfo as GetDex.Solana) {
			is GetDex.Meteora -> error("Meteora not yet supported")
			is GetDex.PumpFun -> SolanaConstants.CU_LIMIT_BUY_PUMPFUN
			is GetDex.Raydium -> SolanaConstants.CU_LIMIT_BUY_RAYDIUM
			is GetDex.PumpSwap -> SolanaConstants.CU_LIMIT_BUY_PUMPSWAP
		}
		val priorityFeeBaseAmount = getRecommendedFeeSolana.forComputeUnitLimit(computeUnitLimit = computeUnitLimit)
		val jitoTipBaseAmount = if (input.useAntiMevProtection) SolanaConstants.JITO_MIN_TIP else BaseAmount.ZERO

		val rent = input.getRent()

		return EstimateGas.ResultSolana(
			feeBaseAmount = feeConstantBaseAmount + rent + priorityFeeBaseAmount + jitoTipBaseAmount,
		)
	}

	@SentrySpan
	protected fun handleSell(input: EstimateGas.SellInput): EstimateGas.ResultSolana {
		val computeUnitLimit = when (input.dexPairInfo as GetDex.Solana) {
			is GetDex.Meteora -> error("Meteora not yet supported")
			is GetDex.PumpFun -> SolanaConstants.CU_LIMIT_SELL_PUMPFUN
			is GetDex.Raydium -> SolanaConstants.CU_LIMIT_SELL_RAYDIUM
			is GetDex.PumpSwap -> SolanaConstants.CU_LIMIT_SELL_PUMPSWAP
		}
		val priorityFeeBaseAmount = getRecommendedFeeSolana.forComputeUnitLimit(computeUnitLimit = computeUnitLimit)
		val jitoTipBaseAmount = if (input.useAntiMevProtection) SolanaConstants.JITO_MIN_TIP else BaseAmount.ZERO

		val rent = input.getRent()

		return EstimateGas.ResultSolana(
			feeBaseAmount = feeConstantBaseAmount + rent + priorityFeeBaseAmount + jitoTipBaseAmount,
		)
	}

	@SentrySpan
	protected fun handleTransferCurrency(input: EstimateGas.TransferCurrencyInput): EstimateGas.ResultSolana {
		val priorityFeeBaseAmount = getRecommendedFeeSolana.forComputeUnitLimit(
			computeUnitLimit = SolanaConstants.CU_LIMIT_CURRENCY_TRANSFER,
		)

		val rent = input.getRent()

		return EstimateGas.ResultSolana(feeBaseAmount = feeConstantBaseAmount + priorityFeeBaseAmount + rent)
	}

	@SentrySpan
	protected fun handleTransferToken(input: EstimateGas.TransferTokenInput): EstimateGas.ResultSolana {
		val rent = input.getRent()

		val priorityFeeBaseAmount = getRecommendedFeeSolana.forComputeUnitLimit(
			computeUnitLimit = SolanaConstants.CU_LIMIT_TOKEN_TRANSFER,
		)

		return EstimateGas.ResultSolana(feeBaseAmount = feeConstantBaseAmount + rent + priorityFeeBaseAmount)
	}

	private fun EstimateGas.Input.getRent(): BaseAmount {
		val rentAccounts = this.getRentAccounts()

		return getRent(rentAccounts)
	}

	private fun EstimateGas.Input.getRentAccounts(): List<PublicKey> = when (this) {
		// Buy needs token accounts for tokens & temporarily wrapped solana
		is EstimateGas.BuyInput -> {
			val walletAddress = getAddressFromPrivateKey(privateKey, ChainType.SOLANA)

			val tokenAccount = getAssociatedTokenAddress(
				mint = tokenAddress.getSolanaPublicKey(),
				owner = walletAddress.getSolanaPublicKey(),
				programId = if (isToken2022) SolanaConstants.TOKEN_2022_PROGRAM_ID else SolanaConstants.TOKEN_PROGRAM_ID,
			)

			val wsolAccount = when (this.dexPairInfo as GetDex.Solana) {
				is GetDex.PumpFun -> null
				is GetDex.Meteora, is GetDex.PumpSwap, is GetDex.Raydium -> {
					getAssociatedTokenAddress(
						mint = WSOL_MINT,
						owner = walletAddress.getSolanaPublicKey(),
					)
				}
			}

			listOfNotNull(tokenAccount, wsolAccount)
		}
		// Sell needs token account for temporarily wrapped solana
		is EstimateGas.SellInput -> {
			val walletAddress = getAddressFromPrivateKey(privateKey, ChainType.SOLANA)

			val wsolAccount = when (dexPairInfo as GetDex.Solana) {
				is GetDex.PumpFun -> null
				is GetDex.Meteora, is GetDex.PumpSwap, is GetDex.Raydium -> {
					getAssociatedTokenAddress(
						mint = WSOL_MINT,
						owner = walletAddress.getSolanaPublicKey(),
					)
				}
			}

			listOfNotNull(wsolAccount)
		}
		is EstimateGas.TransferCurrencyInput -> emptyList()
		// TransferToken needs token account for destination token account
		is EstimateGas.TransferTokenInput -> {
			// check if recipient of tokens has token account, if not we will be paying for the creation
			val tokenAccount = getAssociatedTokenAddress(
				mint = tokenAddress.getSolanaPublicKey(),
				owner = toAddress.getSolanaPublicKey(),
				programId = if (isToken2022) SolanaConstants.TOKEN_2022_PROGRAM_ID else SolanaConstants.TOKEN_PROGRAM_ID,
			)

			listOf(tokenAccount)
		}
		is EstimateGas.ApproveInput -> emptyList()
	}

	private fun getRent(tokenAccounts: List<PublicKey>): BaseAmount {
		val tokenAccountRent = rentExemptionCache.get(TOKEN_ACCOUNT_SIZE)

		val accountsNeeded = rpcClient.getTokenAccountBalances(tokenAccounts, Commitment.CONFIRMED).count { it == null }
		val totalRent = tokenAccountRent.amount * accountsNeeded.toBigInteger()

		return totalRent.asBaseAmount()
	}

	private fun getMinimumBalanceForRentExemption(dataLength: Long): BaseAmount {
		return rpcClient.api.getMinimumBalanceForRentExemption(dataLength).toBigInteger().asBaseAmount()
	}
}
