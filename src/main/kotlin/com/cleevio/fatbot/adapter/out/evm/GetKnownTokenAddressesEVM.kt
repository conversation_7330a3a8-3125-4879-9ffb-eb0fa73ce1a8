package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.tokenpricesnapshot.port.out.GetKnownTokenAddresses
import org.springframework.stereotype.Component

@Component
class GetKnownTokenAddressesEVM(private val evmTokenInfoFinder: EvmTokenInfoFinderService) : GetKnownTokenAddresses {

	/**
	 * To optimize performance, the tokens are ordered by chainId.
	 * This increases the likelihood that when the tokens are chunked for price retrieval,
	 * a single call will be sufficient to fetch their prices.
	 */
	override fun invoke(): Set<ChainAddress> =
		evmTokenInfoFinder.findAll().sortedBy { it.chain.id }.mapToSet { it.address.toChainAddress(it.chain) }
}
