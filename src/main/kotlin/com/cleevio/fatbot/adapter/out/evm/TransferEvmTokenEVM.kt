package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.constants.EVM_GAS_LIMIT_ERC20_TRANSFER_FALLBACK
import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.adapter.out.evm.model.GasInfo
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.padToDataPayloadLength
import com.cleevio.fatbot.application.common.crypto.remove0x
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.TransferEvmToken
import com.cleevio.fatbot.application.module.transaction.constant.FunctionSelector
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component
import org.web3j.crypto.Credentials
import org.web3j.crypto.RawTransaction
import org.web3j.utils.Numeric
import java.math.BigInteger

@Component
class TransferEvmTokenEVM(
	private val evmChainContextFactory: EvmChainContextFactory,
) : TransferEvmToken {

	override fun invoke(
		tokenAddress: AddressWrapper,
		destinationWalletAddress: AddressWrapper,
		privateKey: String,
		chainId: Long,
		amountToTransfer: BaseAmount,
		gasInfoHint: GasInfo?,
		gasLimitEstimate: BigInteger,
	): SignedTx {
		return evmChainContextFactory.ofChainId(chainId = chainId) {
			val credentials = Credentials.create(privateKey)
			val nonce = client.getNonce(credentials)
			val data = createTokenTransferDataPayload(
				destinationWalletAddress = destinationWalletAddress,
				amount = amountToTransfer,
			)
			// TODO: run estimate against temper, estimate is returning gas limit without taxes accounted for
			val gasLimit = getGasLimit(Chain.ofEVM(chainId))

			val gasInfo = gasInfoHint ?: client.getGasInfo()

			val rawTx = RawTransaction.createTransaction(
				chainId,
				nonce,
				gasLimit,
				tokenAddress.getAddressString(),
				BigInteger.ZERO,
				data,
				gasInfo.maxPriorityFeePerGas,
				gasInfo.maxFeePerGas,
			)

			sendRawTransaction(signedTx = client.signTx(rawTx, credentials))
		}
	}

	private fun getGasLimit(chain: Chain) = when (chain) {
		Chain.EVM_MAINNET, Chain.EVM_BSC -> EVM_GAS_LIMIT_ERC20_TRANSFER_FALLBACK
		Chain.EVM_ARBITRUM_ONE, Chain.EVM_BASE -> 500_000.toBigInteger()
		Chain.SOLANA -> error("Cannot get gas limit in TransferEVMToken for solana...")
	}

	private fun createTokenTransferDataPayload(destinationWalletAddress: AddressWrapper, amount: BaseAmount): String {
		return buildString {
			append(FunctionSelector.TRANSFER)
			append(destinationWalletAddress.getAddressString().remove0x().padToDataPayloadLength())
			append(Numeric.toHexStringNoPrefixZeroPadded(amount.amount, 64))
		}
	}
}
