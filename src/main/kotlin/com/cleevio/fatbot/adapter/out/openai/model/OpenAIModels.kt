package com.cleevio.fatbot.adapter.out.openai.model

import com.fasterxml.jackson.databind.JsonNode

// Request
data class OpenAIResponsesApiRequest(
	/**
	 * Defines the OpenAI model
	 */
	val model: String,

	/**
	 * A list of input texts from different roles
	 */
	val input: List<InputPrompt>,

	/**
	 * Defines the expected response structure from the model
	 */
	val text: Text,

	val temperature: Double = 1.0,
) {
	data class Text(
		val format: Format,
	) {
		data class Format(
			/**
			 * Name of the response format
			 */
			val name: String,

			/**
			 * JSON Schema representing the expected output format
			 */
			val schema: JsonNode,
		) {
			val type: String = "json_schema"
		}
	}
}

// Response
data class OpenAIResponsesApiResponse(
	val id: String,
	val output: List<Output>,
) {

	data class Output(
		val id: String,
		val status: String,
		val type: String,
		val content: List<Content>,
	) {
		data class Content(
			val type: String,
			val text: String,
		)
	}
}

// Prompts
sealed interface InputPrompt {
	val role: String
	val content: String
}

data class SystemPrompt(
	override val content: String,
) : InputPrompt {
	override val role = "system"
}

data class UserPrompt(
	override val content: String,
) : InputPrompt {
	override val role = "user"
}
