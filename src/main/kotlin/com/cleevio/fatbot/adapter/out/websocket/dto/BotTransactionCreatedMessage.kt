package com.cleevio.fatbot.adapter.out.websocket.dto

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.math.BigDecimal
import java.net.URI
import java.time.Instant
import java.util.UUID

data class BotTransactionCreatedMessage(
	val botId: UUID,
	val type: BotTransactionType,
	val marketPositionId: UUID,
	val tokenAddress: AddressWrapper,
	val tokenDetailUrl: URI,
	val tokenName: String,
	val tokenSymbol: String,
	val tokenChain: Chain,
	val tokenImageUrl: URI?,
	val openValueUsd: BigDecimal,
	val closeValueUsd: BigDecimal?,
	val openTimeStampAt: Instant,
	val closedTimeStampAt: Instant?,
	val pnlAmountUsd: BigDecimal,
	val pnlAmountFraction: BigDecimal,
	val currentValueUsd: BigDecimal,
)
