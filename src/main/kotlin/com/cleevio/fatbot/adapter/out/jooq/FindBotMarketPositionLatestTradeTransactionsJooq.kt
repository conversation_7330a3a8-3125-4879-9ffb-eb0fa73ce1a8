package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.botmarket.port.out.BuyTransaction
import com.cleevio.fatbot.application.module.botmarket.port.out.FindBotMarketPositionLatestTradeTransactions
import com.cleevio.fatbot.application.module.botmarket.port.out.SellTransaction
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.bottransaction.exception.BotTransactionNotFoundException
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.BOT_TRANSACTION
import com.cleevio.fatbot.tables.references.BOT_WALLET
import org.jooq.DSLContext
import org.jooq.impl.DSL.field
import org.jooq.impl.DSL.partitionBy
import org.jooq.impl.DSL.rank
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Component
import java.net.URI
import java.util.UUID

@Component
class FindBotMarketPositionLatestTradeTransactionsJooq(
	private val dslContext: DSLContext,
) : FindBotMarketPositionLatestTradeTransactions {
	override fun invoke(
		tokenAddress: AddressWrapper,
		botWalletId: UUID,
		txHashToURIMapper: (TxHash, Chain) -> URI,
	): Pair<BuyTransaction, SellTransaction?> {
		val botTransactionConditions = listOf(
			BOT_TRANSACTION.TOKEN_ADDRESS.eq(tokenAddress),
			BOT_TRANSACTION.BOT_WALLET_ID.eq(botWalletId),
			BOT_TRANSACTION.STATUS.eq(TransactionStatus.SUCCESS),
			BOT_TRANSACTION.TX_HASH.isNotNull,
			BOT_TRANSACTION.TYPE.`in`(BotTransactionType.BUY, BotTransactionType.SELL),
		)

		val botTradeTransactionsRank = select(
			rank().over(partitionBy(BOT_TRANSACTION.TYPE).orderBy(BOT_TRANSACTION.CREATED_AT.desc())).`as`("rank"),
			BOT_TRANSACTION.ID,
		)
			.from(BOT_TRANSACTION)
			.where(botTransactionConditions)
			.asTable("BOT_TRANSACTION_RANK")

		val tradeTransactions = dslContext.select(
			BOT_TRANSACTION.ID,
			BOT_TRANSACTION.CREATED_AT,
			BOT_TRANSACTION.TX_HASH,
			BOT_TRANSACTION.TYPE,
			BOT_TRANSACTION.STATUS,
			BOT_WALLET.CHAIN,
		)
			.from(botTradeTransactionsRank)
			.innerJoin(BOT_TRANSACTION).on(botTradeTransactionsRank.field(BOT_TRANSACTION.ID)!!.eq(BOT_TRANSACTION.ID))
			.innerJoin(BOT_WALLET).on(BOT_TRANSACTION.BOT_WALLET_ID.eq(BOT_WALLET.ID))
			.where(field("rank").eq(1))
			.fetch()
			.map {
				FindBotMarketPositionLatestTradeTransactions.Result(
					txId = it[BOT_TRANSACTION.ID]!!,
					txCreatedAt = it[BOT_TRANSACTION.CREATED_AT]!!,
					txHash = TxHash(it[BOT_TRANSACTION.TX_HASH]!!),
					txDetailUrl = txHashToURIMapper(TxHash(it[BOT_TRANSACTION.TX_HASH]!!), it[BOT_WALLET.CHAIN]!!),
					txType = it[BOT_TRANSACTION.TYPE]!!,
					txStatus = it[BOT_TRANSACTION.STATUS]!!,
				)
			}

		val buyTransaction = tradeTransactions.firstOrNull { it.txType == BotTransactionType.BUY }
			?: throw BotTransactionNotFoundException("Bot trade buy transaction for token $tokenAddress not found.")
		val sellTransaction = tradeTransactions.firstOrNull { it.txType == BotTransactionType.SELL }

		return buyTransaction to sellTransaction
	}
}
