package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.adapter.`in`.InfiniteScroll
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.adapter.`in`.toInfiniteScrollSlice
import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.bottransaction.port.out.SearchBotTransaction
import com.cleevio.fatbot.application.module.bottransaction.query.SearchUserBotsTransactionsQuery
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.BOT
import com.cleevio.fatbot.tables.references.BOT_TOKEN_INFO
import com.cleevio.fatbot.tables.references.BOT_TRANSACTION
import com.cleevio.fatbot.tables.references.BOT_WALLET
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.jooq.impl.DSL.or
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.net.URI
import java.time.Instant
import java.util.UUID

private val TRANSACTION_AMOUNT_USD_MINIMUM = BigDecimal("0.5")

@Component
class SearchBotTransactionJooq(
	private val dslContext: DSLContext,
) : SearchBotTransaction {

	override fun invoke(
		userId: UUID,
		botIds: Set<UUID>?,
		searchString: String?,
		from: Instant?,
		to: Instant?,
		infiniteScroll: InfiniteScroll<UUID>,
		txHashToURIMapper: (TxHash, Chain) -> URI,
		fileToUrlMapper: FileToUrlMapper,
	): InfiniteScrollSlice<SearchUserBotsTransactionsQuery.Result, UUID> {
		val botTransactionConditions = listOfNotNull(
			botIds?.let { BOT.ID.`in`(it) },
			BOT.USER_ID.eq(userId),
			BOT_TRANSACTION.TX_HASH.isNotNull,
			BOT_TRANSACTION.TYPE.`in`(BotTransactionType.userInitiatedTypes),
			searchString?.let {
				// Quality of life so we match enum correctly (i.e. 'portfolio wit' would not match otherwise)
				val typeSearchString = it.replace(" ", "_")

				or(
					BOT_TOKEN_INFO.NAME.likeIgnoreCase("%$it%"),
					BOT_TOKEN_INFO.SYMBOL.likeIgnoreCase("%$it%"),
					BOT_TOKEN_INFO.ADDRESS.eqIfValidAddressOrNoCondition(potentialAddress = it),
					/*
					If we don't perform a manual cast on BOT_TRANSACTION.TYPE, Jooq will interpret this as
					`cast("public"."bot_transaction"."type" as varchar) ilike cast(null as varchar)`

					Whereas with manual cast:
					cast("public"."bot_transaction"."type" as varchar) ilike '%?%'

					Suggesting a bug in the interpretation of the `likeIgnoreCase` on forcedType
					(we apply forcedType with enumConverter on BOT_TRANSACTION.TYPE).
					 */
					BOT_TRANSACTION.TYPE.cast(String::class.java).likeIgnoreCase("%$typeSearchString%"),
					BOT_TRANSACTION.BASE_VALUE
						.div(DSL.power(10, Chain.SOLANA.currency.decimals)) // To native currency
						.times(DSL.coalesce(BOT_TRANSACTION.EXCHANGE_RATE_USD, DSL.`val`(0)))
						.likeIgnoreCase("%$it%"),
				)
			},
			takeIf { from != null && to != null }?.let { BOT_TRANSACTION.CREATED_AT.between(from, to) },
			BOT_TRANSACTION.TYPE.ne(BotTransactionType.DEPOSIT)
				.or(BOT_TRANSACTION.AMOUNT_IN.isNull)
				.or(BOT_TRANSACTION.AMOUNT_IN.ge(BigDecimal.TEN)),
		)

		return dslContext.select(
			BOT.ID,
			BOT.NAME,
			BOT_TRANSACTION.ID,
			BOT_TRANSACTION.TX_HASH,
			BOT_TRANSACTION.TYPE,
			BOT_TRANSACTION.STATUS,
			BOT_TRANSACTION.CREATED_AT,
			BOT_TRANSACTION.BOT_WALLET_ID,
			BOT_TRANSACTION.BASE_VALUE,
			BOT_TRANSACTION.EXCHANGE_RATE_USD,
			BOT_TRANSACTION.TOKEN_ADDRESS,
			BOT_TRANSACTION.PERCENTAGE_OF,
			BOT_WALLET.CHAIN,
			BOT_TOKEN_INFO.NAME,
			BOT_TOKEN_INFO.SYMBOL,
		)
			.from(BOT_TRANSACTION)
			.innerJoin(BOT_WALLET).on(BOT_TRANSACTION.BOT_WALLET_ID.eq(BOT_WALLET.ID))
			.innerJoin(BOT).on(BOT_WALLET.BOT_ID.eq(BOT.ID))
			.leftJoin(
				BOT_TOKEN_INFO,
			).on(BOT_TOKEN_INFO.ADDRESS.eq(BOT_TRANSACTION.TOKEN_ADDRESS).and(BOT_TOKEN_INFO.CHAIN.eq(BOT_WALLET.CHAIN)))
			.whereWithInfiniteScroll(
				conditions = botTransactionConditions,
				infiniteScroll = infiniteScroll,
				idFieldSelector = BOT_TRANSACTION.ID,
			)
			.fetch()
			.map {
				val exchangeRate = it[BOT_TRANSACTION.EXCHANGE_RATE_USD]
				val chain = it[BOT_WALLET.CHAIN]!!

				val currencyBaseAmount = it[BOT_TRANSACTION.BASE_VALUE]!!.toBigInteger().asBaseAmount()
				val currencyNativeAmount = currencyBaseAmount.toNative(chain)

				SearchUserBotsTransactionsQuery.Result(
					botId = it[BOT.ID]!!,
					botName = it[BOT.NAME]!!,
					txId = it[BOT_TRANSACTION.ID]!!,
					txHash = TxHash(it[BOT_TRANSACTION.TX_HASH]!!),
					txDetailUrl = txHashToURIMapper(TxHash(it[BOT_TRANSACTION.TX_HASH]!!), Chain.SOLANA),
					txType = it[BOT_TRANSACTION.TYPE]!!,
					txStatus = it[BOT_TRANSACTION.STATUS]!!,
					txCreatedAt = it[BOT_TRANSACTION.CREATED_AT]!!,
					txChain = it[BOT_WALLET.CHAIN]!!,
					walletId = it[BOT_TRANSACTION.BOT_WALLET_ID]!!,
					tokenAddress = it[BOT_TRANSACTION.TOKEN_ADDRESS],
					tokenName = it[BOT_TOKEN_INFO.NAME],
					tokenSymbol = it[BOT_TOKEN_INFO.SYMBOL],
					nativeAmount = currencyNativeAmount,
					amountUsd = exchangeRate?.times(currencyNativeAmount.amount),
					percentageOf = it[BOT_TRANSACTION.PERCENTAGE_OF],
				)
			}
			.filter { it.amountUsd == null || it.amountUsd >= TRANSACTION_AMOUNT_USD_MINIMUM }
			.toInfiniteScrollSlice(infiniteScroll = infiniteScroll, idSelector = { txId })
	}
}
