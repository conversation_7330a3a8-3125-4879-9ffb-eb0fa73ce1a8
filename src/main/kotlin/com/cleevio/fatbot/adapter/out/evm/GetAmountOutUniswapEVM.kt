package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContext
import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.adapter.out.evm.context.UniswapV3Fee
import com.cleevio.fatbot.adapter.out.evm.contract.FatbotUtil
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutUniswap
import org.springframework.stereotype.Component
import org.web3j.tx.gas.DefaultGasProvider

@Component
class GetAmountOutUniswapEVM(
	private val evmChainContextFactory: EvmChainContextFactory,
) : GetAmountOutUniswap {

	private fun EvmChainContext.loadFatbotUtil() = FatbotUtil.load(
		properties.fatbotUtil.getAddressString(),
		client.getWeb3j(),
		client.web3jWrapper.getReadOnlyTransactionManager(),
		DefaultGasProvider(),
	)

	override fun getAmountOutV2(
		chainId: Long,
		tokenAddress: AddressWrapper,
		amountIn: BaseAmount,
		isBuy: Boolean,
	): BaseAmount {
		return evmChainContextFactory.ofChainId(chainId) {
			val fatbotUtil = loadFatbotUtil()

			val amountOut = fatbotUtil.getAmountOutV2(amountIn.amount, tokenAddress.getAddressString(), isBuy).send()
			amountOut.asBaseAmount()
		}
	}

	override fun getAmountOutV3(
		chainId: Long,
		tokenAddress: AddressWrapper,
		amountIn: BaseAmount,
		fee: UniswapV3Fee,
		isBuy: Boolean,
	): BaseAmount {
		return evmChainContextFactory.ofChainId(chainId) {
			val fatbotUtil = loadFatbotUtil()

			val weth = properties.wethAddress.getAddressString()
			val token = tokenAddress.getAddressString()

			/*
			 Buy: tokenIn = WETH, tokenOut = tokenAddress
			 Sell: tokenIn = tokenAddress, tokenOut = WETH
			 */
			val (tokenIn, tokenOut) = if (isBuy) weth to token else token to weth

			val amountOut = fatbotUtil.getAmountOutV3(
				tokenIn,
				tokenOut,
				fee.feeAmount.toBigInteger(),
				amountIn.amount,
			).send()

			amountOut.asBaseAmount()
		}
	}
}
