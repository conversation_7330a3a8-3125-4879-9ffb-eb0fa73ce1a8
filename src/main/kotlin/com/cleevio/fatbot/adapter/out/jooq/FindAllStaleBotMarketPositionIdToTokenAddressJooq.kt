package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.matchmaking.port.out.FindAllStaleBotMarketPositionIdToTokenAddress
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

private val POSITION_CONSIDERED_STALE_AFTER_DURATION = Duration.of(5, ChronoUnit.MINUTES)

@Component
class FindAllStaleBotMarketPositionIdToTokenAddressJooq(
	private val dslContext: DSLContext,
) : FindAllStaleBotMarketPositionIdToTokenAddress {

	override fun invoke(now: Instant): Set<Pair<UUID, AddressWrapper>> {
		val positionConsideredStaleAt = now.minus(POSITION_CONSIDERED_STALE_AFTER_DURATION)
		val conditions = listOf(
			BOT_MARKET_POSITION.STATE.eq(BotMarketPositionState.OPENED),
			BOT_MARKET_POSITION.CREATED_AT.le(positionConsideredStaleAt),
			BOT_MARKET_POSITION.botWallet.bot.SHOULD_AUTO_SELL_AFTER_HOLD_TIME.isTrue,
		)

		return dslContext
			.select(
				BOT_MARKET_POSITION.ID,
				BOT_MARKET_POSITION.TOKEN_ADDRESS,
			)
			.from(BOT_MARKET_POSITION)
			.where(conditions)
			.fetch()
			.mapToSet {
				it[BOT_MARKET_POSITION.ID]!! to it[BOT_MARKET_POSITION.TOKEN_ADDRESS]!!
			}
	}
}
