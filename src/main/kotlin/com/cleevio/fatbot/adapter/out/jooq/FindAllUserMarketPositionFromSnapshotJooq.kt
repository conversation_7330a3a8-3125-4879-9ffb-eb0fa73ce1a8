package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator
import com.cleevio.fatbot.application.module.market.port.out.FindAllUserMarketPositionFromSnapshot
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.EVM_TOKEN_INFO
import com.cleevio.fatbot.tables.references.MARKET_POSITION_SNAPSHOT
import com.cleevio.fatbot.tables.references.WALLET
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.jooq.impl.DSL.select
import org.jooq.kotlin.get
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Instant
import java.util.UUID

@Component
class FindAllUserMarketPositionFromSnapshotJooq(
	private val dslContext: DSLContext,
) : FindAllUserMarketPositionFromSnapshot {

	private val sumTotalTokenAmountBought = DSL.sum(MARKET_POSITION_SNAPSHOT.TOTAL_TOKEN_AMOUNT_BOUGHT)
		.`as`("ttab")
	private val sumTotalTokenAcquisitionCostBaseAmount = DSL.sum(
		MARKET_POSITION_SNAPSHOT.TOTAL_TOKEN_ACQUISITION_COST_BASE_AMOUNT,
	)
		.`as`("ttacba")
	private val sumTotalTokenAcquisitionCostUsd = DSL.sum(
		MARKET_POSITION_SNAPSHOT.TOTAL_TOKEN_ACQUISITION_COST_USD,
	)
		.`as`("ttacu")
	private val sumTotalTokenAmountSold = DSL.sum(MARKET_POSITION_SNAPSHOT.TOTAL_TOKEN_AMOUNT_SOLD)
		.`as`("ttas")
	private val sumTotalTokenDispositionCostBaseAmount = DSL.sum(
		MARKET_POSITION_SNAPSHOT.TOTAL_TOKEN_DISPOSITION_COST_BASE_AMOUNT,
	)
		.`as`("ttdcba")

	data class Result(
		override val chain: Chain,
		override val tokenAddress: AddressWrapper,
		override val tokenDecimals: Int,
		override val tokenAmountBought: BaseAmount,
		override val tokenAcquisitionCost: BaseAmount,
		override val tokenDispositionCost: BaseAmount,
		override val tokenAmountSold: BaseAmount,
		override val tokenAcquisitionCostUsd: BigDecimal,
	) : MarketPositionCalculator.Input

	override fun invoke(userId: UUID, chains: Set<Chain>?, walletId: UUID?, snapshotMadeAt: Instant): List<Result> {
		val allWalletIds = select(WALLET.ID)
			.from(WALLET)
			.where(WALLET.USER_ID.eq(userId))
			.and(chains?.let { WALLET.CHAIN.`in`(it) })

		val singleWalletId = walletId?.let { allWalletIds.and(WALLET.ID.eq(it)) }

		val groupedTokens = DSL
			.select(
				MARKET_POSITION_SNAPSHOT.CHAIN,
				MARKET_POSITION_SNAPSHOT.TOKEN_ADDRESS,
				sumTotalTokenAmountBought,
				sumTotalTokenAcquisitionCostBaseAmount,
				sumTotalTokenAcquisitionCostUsd,
				sumTotalTokenAmountSold,
				sumTotalTokenDispositionCostBaseAmount,
			)
			.from(MARKET_POSITION_SNAPSHOT)
			.where(
				MARKET_POSITION_SNAPSHOT.SNAPSHOT_MADE_AT.eq(snapshotMadeAt),
				MARKET_POSITION_SNAPSHOT.WALLET_ID.`in`(singleWalletId ?: allWalletIds),
			)
			.groupBy(MARKET_POSITION_SNAPSHOT.TOKEN_ADDRESS, MARKET_POSITION_SNAPSHOT.CHAIN)

		return dslContext
			.select(
				EVM_TOKEN_INFO.DECIMALS,
				groupedTokens[MARKET_POSITION_SNAPSHOT.CHAIN],
				groupedTokens[MARKET_POSITION_SNAPSHOT.TOKEN_ADDRESS],
				groupedTokens[sumTotalTokenAmountBought],
				groupedTokens[sumTotalTokenAcquisitionCostBaseAmount],
				groupedTokens[sumTotalTokenAcquisitionCostUsd],
				groupedTokens[sumTotalTokenAmountSold],
				groupedTokens[sumTotalTokenDispositionCostBaseAmount],
			)
			.from(groupedTokens)
			.leftJoin(EVM_TOKEN_INFO)
			.on(groupedTokens[MARKET_POSITION_SNAPSHOT.TOKEN_ADDRESS]!!.eq(EVM_TOKEN_INFO.ADDRESS))
			.and(groupedTokens[MARKET_POSITION_SNAPSHOT.CHAIN]!!.eq(EVM_TOKEN_INFO.CHAIN))
			.fetch()
			.map {
				Result(
					chain = it[groupedTokens[MARKET_POSITION_SNAPSHOT.CHAIN]]!!,
					tokenAddress = it[groupedTokens[MARKET_POSITION_SNAPSHOT.TOKEN_ADDRESS]]!!,
					tokenDecimals = it[EVM_TOKEN_INFO.DECIMALS]!!.toInt(),
					tokenAmountBought = it[groupedTokens[sumTotalTokenAmountBought]]!!.toBigInteger().asBaseAmount(),
					tokenAcquisitionCost = it[groupedTokens[sumTotalTokenAcquisitionCostBaseAmount]]!!.toBigInteger().asBaseAmount(),
					tokenAmountSold = it[groupedTokens[sumTotalTokenAmountSold]]!!.toBigInteger().asBaseAmount(),
					tokenDispositionCost = it[groupedTokens[sumTotalTokenDispositionCostBaseAmount]]!!.toBigInteger().asBaseAmount(),
					tokenAcquisitionCostUsd = it[groupedTokens[sumTotalTokenAcquisitionCostUsd]]!!,
				)
			}
	}
}
