package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.file.FileToUrlMapper
import com.cleevio.fatbot.application.module.market.port.out.FindAllUserEvmWalletMarketPositionData
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.EVM_TOKEN_INFO
import com.cleevio.fatbot.tables.references.MARKET_POSITION
import com.cleevio.fatbot.tables.references.WALLET
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.net.URI
import java.util.UUID

@Component
class FindAllUserEvmWalletMarketPositionDataJooq(
	private val dslContext: DSLContext,
) : FindAllUserEvmWalletMarketPositionData {

	data class Result(
		val walletId: UUID,
		val walletAddress: AddressWrapper,
		val walletCustomName: String?,
		val walletChain: Chain,
		val tokenName: String,
		val tokenSymbol: String,
		val tokenChain: Chain,
		val tokenImageUrl: URI?,
		val totalTokenAmountBought: BaseAmount,
		val totalTokenAcquisitionCost: BaseAmount,
		val totalTokenAmountSold: BaseAmount,
		val totalTokenDispositionCost: BaseAmount,
		val totalTokenAcquisitionCostUsd: BigDecimal,
	)

	@Transactional(readOnly = true)
	override fun invoke(
		chain: Chain,
		userId: UUID,
		tokenAddress: AddressWrapper,
		fileToUrlMapper: FileToUrlMapper,
	): List<Result> {
		val userEvmWalletIds = DSL
			.select(WALLET.ID)
			.from(WALLET)
			.where(WALLET.USER_ID.eq(userId).and(WALLET.CHAIN.eq(chain)))

		val conditions = listOf(
			MARKET_POSITION.WALLET_ID.`in`(userEvmWalletIds),
			MARKET_POSITION.CHAIN.eq(chain),
			MARKET_POSITION.TOKEN_ADDRESS.eq(tokenAddress),
		)

		return dslContext
			.select(
				MARKET_POSITION.wallet.ID,
				MARKET_POSITION.wallet.ADDRESS,
				MARKET_POSITION.wallet.CUSTOM_NAME,
				MARKET_POSITION.wallet.CHAIN,
				MARKET_POSITION.TOTAL_TOKEN_AMOUNT_BOUGHT,
				MARKET_POSITION.TOTAL_TOKEN_ACQUISITION_COST_BASE_AMOUNT,
				MARKET_POSITION.TOTAL_TOKEN_AMOUNT_SOLD,
				MARKET_POSITION.TOTAL_TOKEN_DISPOSITION_COST_BASE_AMOUNT,
				MARKET_POSITION.TOTAL_TOKEN_ACQUISITION_COST_USD,
				EVM_TOKEN_INFO.NAME,
				EVM_TOKEN_INFO.SYMBOL,
				EVM_TOKEN_INFO.CHAIN,
				EVM_TOKEN_INFO.file.ID,
				EVM_TOKEN_INFO.file.EXTENSION,
			)
			.from(MARKET_POSITION)
			.leftJoin(EVM_TOKEN_INFO).on(
				EVM_TOKEN_INFO.ADDRESS.eq(MARKET_POSITION.TOKEN_ADDRESS)
					.and(EVM_TOKEN_INFO.CHAIN.eq(chain)),
			)
			.where(conditions)
			.fetch()
			.map {
				Result(
					walletId = it[MARKET_POSITION.wallet.ID]!!,
					walletAddress = it[MARKET_POSITION.wallet.ADDRESS]!!,
					walletCustomName = it[MARKET_POSITION.wallet.CUSTOM_NAME],
					walletChain = it[MARKET_POSITION.wallet.CHAIN]!!,
					totalTokenAmountBought =
					it[MARKET_POSITION.TOTAL_TOKEN_AMOUNT_BOUGHT]!!.toBigInteger().asBaseAmount(),
					totalTokenAcquisitionCost =
					it[MARKET_POSITION.TOTAL_TOKEN_ACQUISITION_COST_BASE_AMOUNT]!!.toBigInteger().asBaseAmount(),
					totalTokenAmountSold =
					it[MARKET_POSITION.TOTAL_TOKEN_AMOUNT_SOLD]!!.toBigInteger().asBaseAmount(),
					totalTokenDispositionCost =
					it[MARKET_POSITION.TOTAL_TOKEN_DISPOSITION_COST_BASE_AMOUNT]!!.toBigInteger().asBaseAmount(),
					totalTokenAcquisitionCostUsd = it[MARKET_POSITION.TOTAL_TOKEN_ACQUISITION_COST_USD]!!,
					tokenName = it[EVM_TOKEN_INFO.NAME]!!,
					tokenSymbol = it[EVM_TOKEN_INFO.SYMBOL]!!,
					tokenChain = it[EVM_TOKEN_INFO.CHAIN]!!,
					tokenImageUrl = fileToUrlMapper(it[EVM_TOKEN_INFO.file.ID], it[EVM_TOKEN_INFO.file.EXTENSION]),
				)
			}
	}
}
