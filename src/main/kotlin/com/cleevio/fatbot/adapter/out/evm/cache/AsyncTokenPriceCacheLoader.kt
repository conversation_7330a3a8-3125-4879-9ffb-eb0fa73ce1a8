package com.cleevio.fatbot.adapter.out.evm.cache

import com.cleevio.fatbot.adapter.out.evm.context.EvmWeb3jWrapperProvider
import com.cleevio.fatbot.adapter.out.evm.contract.FatbotUtil
import com.cleevio.fatbot.adapter.out.jooq.FindAllTokenPairInfoJooq
import com.cleevio.fatbot.adapter.out.solana.pumpfun.GetPumpfunPrice
import com.cleevio.fatbot.adapter.out.solana.pumpfun.model.PumpFunCurveState
import com.cleevio.fatbot.adapter.out.solana.pumpswap.GetPumpswapPairAddress
import com.cleevio.fatbot.adapter.out.solana.pumpswap.GetPumpswapPrice
import com.cleevio.fatbot.adapter.out.solana.raydium.GetRaydiumAMMPrice
import com.cleevio.fatbot.adapter.out.solana.raydium.GetRaydiumCLMMPrice
import com.cleevio.fatbot.adapter.out.solana.raydium.GetRaydiumCPMMPrice
import com.cleevio.fatbot.adapter.out.solana.raydium.LiquidityPricePair
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.div
import com.cleevio.fatbot.application.common.crypto.isStableCoin
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.util.mapToSet
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.common.wrapper.assertAllChainsSupported
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.port.out.GetUsdExchangeRate
import com.cleevio.fatbot.application.module.token.event.TokenPairFoundEvent
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.logger
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import com.cleevio.fatbot.infrastructure.coroutines.createJobScope
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.web3j.tx.gas.DefaultGasProvider
import java.math.BigDecimal
import java.time.Duration
import java.time.temporal.ChronoUnit

@Component
class AsyncTokenPriceCacheLoader(
	private val chainProperties: ChainProperties,
	private val evmWeb3jWrapperProvider: EvmWeb3jWrapperProvider,
	private val getRaydiumCLMMPrice: GetRaydiumCLMMPrice,
	private val getRaydiumAMMPrice: GetRaydiumAMMPrice,
	private val getRaydiumCPMMPrice: GetRaydiumCPMMPrice,
	private val getPumpfunPrice: GetPumpfunPrice,
	private val getPumpswapPrice: GetPumpswapPrice,
	private val getPumpswapPairAddress: GetPumpswapPairAddress,
	private val findAllTokenPairInfoJooq: FindAllTokenPairInfoJooq,
	private val applicationEventPublisher: ApplicationEventPublisher,
	private val getUsdExchangeRate: GetUsdExchangeRate,
) : CoalescingBulkLoader<ChainAddress, BaseAmount>(
	maxSize = 100,
	maxTime = Duration.of(50, ChronoUnit.MILLIS),
	parallelism = 5,
) {

	private val logger = logger()

	override fun getResults(keys: Set<ChainAddress>): Map<ChainAddress, BaseAmount> {
		keys.assertAllChainsSupported(chainProperties.enabledChains)

		val scope = createJobScope()
		return runBlocking {
			val chainIdToDeferredResults = keys
				.groupBy(keySelector = { it.chain }, valueTransform = { it.address })
				.mapValues { (chain, addresses) ->
					when (chain) {
						Chain.EVM_MAINNET -> scope.async { getResultsOnChain(chain.evmId, addresses.toSet()) }
						Chain.EVM_BASE -> scope.async { getResultsOnChain(chain.evmId, addresses.toSet()) }
						Chain.EVM_BSC -> scope.async { getResultsOnChain(chain.evmId, addresses.toSet()) }
						Chain.EVM_ARBITRUM_ONE -> scope.async { getResultsOnChain(chain.evmId, addresses.toSet()) }
						Chain.SOLANA -> scope.async { getResultsOnSolanaChain(addresses.toSet()) }
					}
				}

			chainIdToDeferredResults
				.map { (chain, results) -> results.await().mapKeys { (address) -> address.toChainAddress(chain) } }
				.reduce { acc, map -> acc + map }
		}
	}

	private fun getResultsOnChain(chainId: Long, tokenAddresses: Set<AddressWrapper>): Map<AddressWrapper, BaseAmount> {
		val web3jWrapper = evmWeb3jWrapperProvider.ofChainId(chainId = chainId)
		val properties = chainProperties.ofEvmChainId(chainId = chainId)

		val contract = FatbotUtil.load(
			properties.fatbotUtil.getAddressString(),
			web3jWrapper.getWeb3j(),
			web3jWrapper.getReadOnlyTransactionManager(),
			DefaultGasProvider(),
		)

		logger.info("Going to fetch getEthPriceForTokens for ${tokenAddresses.size} tokens on chainId $chainId")

		val chain = Chain.ofEVM(chainId)
		val (stableTokens, otherTokenAddresses) = tokenAddresses.partition { it.toChainAddress(chain).isStableCoin() }

		// TODO: Fix this hack in the smart contract (probably decimal shifts are wrong)
		val stableResults = stableTokens.associateWith {
			val exchangeRate = getUsdExchangeRate(chain.currency)
			NativeAmount.ONE.div(exchangeRate).toBase(chain)
		}

		val (addresses, tokenPrices) = runCatching {
			contract.getEthPriceForTokens(otherTokenAddresses.map { it.getAddressString() }).send()
		}.getOrElse { contractError ->
			logger.warn(
				"""
				Failed to fetch tokenPrices in from FatbotUtil::getEthPriceForTokens.
				Token addresses:
				```
				$tokenAddresses
				```
				""".trimIndent(),
			)

			error("FatbotUtil::getEthPriceForTokens returned error: $contractError.")
		}

		val result = addresses
			.zip(tokenPrices)
			.associate { (tokenAddress, tokenPrice) -> AddressWrapper(tokenAddress) to tokenPrice.asBaseAmount() }

		return tokenAddresses.associateWith { result[it] ?: stableResults.getValue(it) }
	}

	private fun getRaydiumPrices(pairs: List<FindAllTokenPairInfoJooq.Result>): Map<AddressWrapper, LiquidityPricePair?> {
		val poolToPairs = pairs.groupBy {
			it.poolType ?: error("Found Raydium token pair without poolType ${it.pairAddress}")
		}

		val ammPairs = poolToPairs[GetDex.PoolType.AMM] ?: emptyList()
		val clmmPairs = poolToPairs[GetDex.PoolType.CLMM] ?: emptyList()
		val cpmmPairs = poolToPairs[GetDex.PoolType.CPMM] ?: emptyList()

		val ammPairInputs = ammPairs.map {
			val wsolVault = it.raydiumAmmMarketData!!.wsolVault()
			val tokenVault = it.raydiumAmmMarketData.tokenVault()

			GetRaydiumAMMPrice.Input(it.pairAddress, it.tokenAddress, wsolVault, tokenVault, it.tokenDecimals)
		}
		val ammPairToLiquidityPrice = getRaydiumAMMPrice.getMany(ammPairInputs)

		val clmmPairAddresses = clmmPairs.map { it.pairAddress }
		val clmmPairToLiquidityPrice = getRaydiumCLMMPrice.getMany(clmmPairAddresses)

		val cpmmPairInputs =
			cpmmPairs.map { GetRaydiumCPMMPrice.Input(it.pairAddress, it.tokenAddress, it.tokenDecimals) }
		val cpmmPairToLiquidityPrice = getRaydiumCPMMPrice.getMany(cpmmPairInputs)

		val tokenToPricePair = pairs.groupBy { it.tokenAddress }.mapValues { (_, tokenPairInfos) ->
			val amm = tokenPairInfos.mapNotNull { ammPairToLiquidityPrice[it.pairAddress] }
			val clmm = tokenPairInfos.mapNotNull { clmmPairToLiquidityPrice[it.pairAddress] }
			val cpmm = tokenPairInfos.mapNotNull { cpmmPairToLiquidityPrice[it.pairAddress] }

			(amm + clmm + cpmm).maxByOrNull { it.liquidity }
		}

		return tokenToPricePair
	}

	private fun getPumpfunPrices(pairs: List<FindAllTokenPairInfoJooq.Result>): Map<AddressWrapper, NativeAmount?> {
		val pairToPrice = getPumpfunPrice.getManyByBondingCurves(pairs.mapToSet { it.pairAddress })

		return pairs.associate { it.tokenAddress to pairToPrice[it.pairAddress] }
	}

	private fun getPumpswapPrices(pairs: List<FindAllTokenPairInfoJooq.Result>): Map<AddressWrapper, LiquidityPricePair?> {
		val inputs = pairs.map {
			GetPumpswapPrice.Input(
				pairAddress = it.pairAddress,
				tokenAddress = it.tokenAddress,
				tokenDecimals = it.tokenDecimals,
			)
		}

		val pairToPrice = getPumpswapPrice.getMany(inputs)

		// One token can have multiple Pumpswap pairs
		val tokenToLiquidityPricePairs = inputs.groupBy({ it.tokenAddress }, { pairToPrice.getValue(it.pairAddress) })

		val tokenToBestLiquidityPricePair = tokenToLiquidityPricePairs.mapValues { (_, pairs) ->
			pairs.maxByOrNull { it?.liquidity ?: BigDecimal.ZERO }
		}

		return tokenToBestLiquidityPricePair
	}

	private fun getPumpswapPriceFromTokenAddress(tokenAddress: AddressWrapper): NativeAmount? {
		val pairAddress = getPumpswapPairAddress(tokenAddress) ?: return null
		val tokenDecimals = PumpFunCurveState.TOKEN_DECIMALS

		applicationEventPublisher.publishEvent(
			TokenPairFoundEvent(
				chain = Chain.SOLANA,
				dex = GetDex.PumpSwap(pairAddress),
				tokenAddress = tokenAddress,
				tokenDecimals = tokenDecimals.toBigInteger(),
			),
		)

		return getPumpswapPrice(pairAddress = pairAddress, tokenAddress = tokenAddress, tokenDecimals = tokenDecimals)
	}

	private suspend fun getResultsOnSolanaChain(tokenAddresses: Set<AddressWrapper>): Map<AddressWrapper, BaseAmount> {
		val pairs = findAllTokenPairInfoJooq(tokenAddresses = tokenAddresses, chain = Chain.SOLANA)

		val raydiumPairs = pairs.filter { it.dex == GetDex.Dex.RAYDIUM }
		val pumpfunPairs = pairs.filter { it.dex == GetDex.Dex.PUMP_FUN }
		val pumpswapPairs = pairs.filter { it.dex == GetDex.Dex.PUMP_SWAP }

		val scope = createJobScope()
		val tokenToRaydiumLiquidityPriceDeferred = scope.async { getRaydiumPrices(raydiumPairs) }
		val tokenToPumpswapLiquidityPriceDeferred = scope.async { getPumpswapPrices(pumpswapPairs) }

		val tokenToPumpfunPrice = getPumpfunPrices(pumpfunPairs)
		val tokenToRaydiumLiquidityPrice = tokenToRaydiumLiquidityPriceDeferred.await()
		val tokenToPumpswapLiquidityPrice = tokenToPumpswapLiquidityPriceDeferred.await()

		return tokenAddresses.associateWith { token ->
			val pumpfunPrice = tokenToPumpfunPrice[token]

			// Early return on Pumpfun price, as if it exists, the token cannot be on any other pool
			if (pumpfunPrice != null) {
				return@associateWith pumpfunPrice.toBase(Chain.SOLANA)
			}

			val pumpswapPrice = tokenToPumpswapLiquidityPrice[token]
			val raydiumPrice = tokenToRaydiumLiquidityPrice[token]

			val bestLiquidityPricePair = listOfNotNull(pumpswapPrice, raydiumPrice).maxByOrNull { it.liquidity }
			val price = bestLiquidityPricePair?.price ?: run {
				// Only allow backup Pumpswap price if the token is from Pumpfun (most likely was not promoted)
				if (token !in tokenToPumpfunPrice) return@run null

				val backupPrice = getPumpswapPriceFromTokenAddress(token)

				if (backupPrice == null) {
					logger.error(
						"""
							|Failed to get price for token $token.
							|Token is labeled as Pumpfun pair, but its price could not be retrieved (most probably completed bonding curve).
							|Pumpswap was tried as a backup, but no pair could be found for this token address
						""".trimIndent(),
					)
				}

				backupPrice
			}

			if (price == null) {
				error("Failed to get price for $token")
			}

			price.toBase(Chain.SOLANA)
		}
	}
}
