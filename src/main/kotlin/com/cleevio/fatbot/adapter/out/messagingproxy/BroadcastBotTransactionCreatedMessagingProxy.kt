package com.cleevio.fatbot.adapter.out.messagingproxy

import com.cleevio.fatbot.adapter.out.websocket.dto.BotTransactionCreatedMessage
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.bottransaction.port.out.BroadcastBotTransactionCreated
import com.cleevio.fatbot.application.module.bottransaction.port.out.FindAllBotTokenInfo
import com.cleevio.fatbot.application.module.market.calculator.MarketPositionCalculator
import com.cleevio.fatbot.domain.botmarket.BotMarketPosition
import com.cleevio.fatbot.infrastructure.config.logger
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.runBlocking
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import java.net.URI
import java.util.UUID

@Component
class BroadcastBotTransactionCreatedMessagingProxy(
	private val fatbotMessagingProxyConnector: FatbotMessagingProxyConnector,
) : BroadcastBotTransactionCreated {

	private val logger = logger()
	private val scope = CoroutineScope(
		Dispatchers.IO +
			SupervisorJob() +
			CoroutineExceptionHandler { _, throwable -> logger.error("error", throwable) },
	)
	private val messageChannel = Channel<BotTransactionCreatedMessage>(capacity = Channel.UNLIMITED)

	@EventListener(ApplicationReadyEvent::class)
	fun onStartup() {
		scope.launch { messageChannelProcessor() }
	}

	override fun invoke(
		transactionType: BotTransactionType,
		botId: UUID,
		botMarketPosition: BotMarketPosition,
		botTokenInfo: FindAllBotTokenInfo.Result,
		positionUsdValue: MarketPositionCalculator.UsdResult,
	) {
		runBlocking {
			messageChannel.send(
				BotTransactionCreatedMessage(
					botId = botId,
					type = transactionType,
					marketPositionId = botMarketPosition.id,
					tokenAddress = botMarketPosition.tokenAddress,
					tokenDetailUrl = botMarketPosition.tokenAddress.getLinkToExplorer(botMarketPosition.chain)
						.let { URI.create(it) },
					tokenImageUrl = botTokenInfo.tokenImageUrl,
					tokenName = botTokenInfo.tokenName,
					tokenSymbol = botTokenInfo.tokenSymbol,
					tokenChain = botMarketPosition.chain,
					openValueUsd = positionUsdValue.acquisitionValueUsd,
					closeValueUsd = botMarketPosition.positionClosedAt?.let { positionUsdValue.currentValueUsd },
					openTimeStampAt = botMarketPosition.createdAt,
					closedTimeStampAt = botMarketPosition.positionClosedAt,
					pnlAmountUsd = positionUsdValue.currentPnlUsd,
					pnlAmountFraction = positionUsdValue.currentPnlFraction,
					currentValueUsd = positionUsdValue.currentValueUsd,
				),
			)
		}
	}

	suspend fun messageChannelProcessor() {
		for (message in messageChannel) {
			runCatching { fatbotMessagingProxyConnector.broadcast(message = message) }
				.onFailure { logger.error("Failed to broadcast bot transaction created message", it) }
		}
	}
}
