package com.cleevio.fatbot.adapter.out.evm.simulation

import com.cleevio.fatbot.adapter.out.evm.contract.FatbotRouter
import org.web3j.protocol.core.methods.response.Log

private const val TOKENS_SUCCESSFULLY_SWAPPED_EVENT_TOPIC_HASH =
	"0x30eae65fb8f17127e6038a896dee2b34add4ba7e5a7f57a740d708a16c14ae37"

fun TemperConnector.TemperSimulationResponse.extractTokensSuccessfullySwappedEvent():
	FatbotRouter.TokensSuccessfullySwappedEventResponse {
	val successfullySwappedLog = logs.single {
		it.topics.first() == TOKENS_SUCCESSFULLY_SWAPPED_EVENT_TOPIC_HASH
	}

	val event = FatbotRouter.getTokensSuccessfullySwappedEventFromLog(
		Log(false, "", "", "", "", "", "", successfullySwappedLog.data, "", successfullySwappedLog.topics),
	)

	return event
}
