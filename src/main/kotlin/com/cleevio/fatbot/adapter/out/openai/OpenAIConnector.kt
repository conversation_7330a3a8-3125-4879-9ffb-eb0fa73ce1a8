package com.cleevio.fatbot.adapter.out.openai

import com.cleevio.fatbot.adapter.out.BaseConnector
import com.cleevio.fatbot.adapter.out.openai.model.InputPrompt
import com.cleevio.fatbot.adapter.out.openai.model.OpenAIResponsesApiRequest
import com.cleevio.fatbot.adapter.out.openai.model.OpenAIResponsesApiResponse
import com.cleevio.fatbot.infrastructure.config.KotlinJacksonModule
import com.cleevio.fatbot.infrastructure.config.properties.OpenAIProperties
import com.fasterxml.jackson.databind.ObjectMapper
import com.github.victools.jsonschema.generator.Option
import com.github.victools.jsonschema.generator.OptionPreset
import com.github.victools.jsonschema.generator.SchemaGenerator
import com.github.victools.jsonschema.generator.SchemaGeneratorConfigBuilder
import com.github.victools.jsonschema.generator.SchemaVersion
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.stereotype.Component
import org.springframework.web.client.toEntity

@Component
class OpenAIConnector(
	private val openAIProperties: OpenAIProperties,
	private val objectMapper: ObjectMapper,
) : BaseConnector(
	baseUrl = openAIProperties.baseUrl,
	restClientCustomizer = {
		it.defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer ${openAIProperties.apiKey}")
	},
) {

	private val schemaGenerator = SchemaGeneratorConfigBuilder(
		SchemaVersion.DRAFT_2020_12,
		OptionPreset.PLAIN_JSON,
	).also { configBuilder ->
		configBuilder.with(KotlinJacksonModule())
		configBuilder.with(Option.FORBIDDEN_ADDITIONAL_PROPERTIES_BY_DEFAULT)
	}.build().let { SchemaGenerator(it) }

	/**
	 * Call to the Responses API, which returns structured outputs based on the provided JSON schema
	 */
	@SentrySpan
	fun <T> responses(inputs: List<InputPrompt>, responseModel: Class<T>): T {
		val schema = schemaGenerator.generateSchema(responseModel)

		val request = OpenAIResponsesApiRequest(
			model = openAIProperties.model,
			input = inputs,
			text = OpenAIResponsesApiRequest.Text(
				format = OpenAIResponsesApiRequest.Text.Format(
					name = responseModel.simpleName,
					schema = schema,
				),
			),
			temperature = 0.1,
		)

		val response = restClient.post()
			.uri {
				it.path("/v1/responses").build()
			}.header(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
			.body(request)
			.retrieve()
			.errorStatusHandler()
			.toEntity<OpenAIResponsesApiResponse>()
			.getBodyOrThrow()

		val responseText = response.output.first().content.first().text

		return objectMapper.readValue(responseText, responseModel)
	}
}
