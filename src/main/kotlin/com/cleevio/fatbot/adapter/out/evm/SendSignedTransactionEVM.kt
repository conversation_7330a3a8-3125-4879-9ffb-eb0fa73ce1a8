package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.application.module.transaction.port.out.SendSignedTransaction
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.RpcSendTransactionConfig
import org.springframework.stereotype.Component

@Component
class SendSignedTransactionEVM(
	private val evmChainContextFactory: EvmChainContextFactory,
	private val rpcClient: RpcClient,
) : SendSignedTransaction {
	override fun invoke(input: SendSignedTransaction.Input) {
		when (input.chain.type) {
			ChainType.EVM -> evmChainContextFactory.ofChainId(chainId = input.chain.evmId) {
				sendTransaction(input.signedTx)
			}
			ChainType.SOLANA -> rpcClient.api.sendRawTransaction(
				input.signedTx.signedTx,
				RpcSendTransactionConfig(),
			)
		}
	}
}
