package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.bottransaction.port.out.FindAllPendingBotTransactions
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.tables.references.BOT_TRANSACTION
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class FindAllPendingBotTransactionsJooq(private val dslContext: DSLContext) : FindAllPendingBotTransactions {

	override fun invoke(greaterThanId: UUID?): List<UUID> {
		val conditions = listOf(
			BOT_TRANSACTION.STATUS.eq(TransactionStatus.PENDING),
			greaterThanId?.let { BOT_TRANSACTION.ID.gt(greaterThanId) },
		)

		return dslContext
			.select(BOT_TRANSACTION.ID)
			.from(BOT_TRANSACTION)
			.where(conditions)
			.orderBy(BOT_TRANSACTION.ID)
			.map { it[BOT_TRANSACTION.ID]!! }
	}
}
