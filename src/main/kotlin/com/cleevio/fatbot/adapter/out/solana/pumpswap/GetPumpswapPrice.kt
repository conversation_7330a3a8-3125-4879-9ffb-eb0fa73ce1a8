package com.cleevio.fatbot.adapter.out.solana.pumpswap

import com.cleevio.fatbot.adapter.out.solana.client.getTokenAccountBalances
import com.cleevio.fatbot.adapter.out.solana.raydium.LiquidityPricePair
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.util.getAssociatedTokenAddress
import com.cleevio.fatbot.application.common.util.getAssociatedTokenAddressPair
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.stereotype.Component
import java.math.MathContext

@Component
class GetPumpswapPrice(
	private val rpcClient: RpcClient,
) {

	data class Input(
		val pairAddress: AddressWrapper,
		val tokenAddress: AddressWrapper,
		val tokenDecimals: Int,
	)

	fun getMany(inputs: List<Input>): Map<AddressWrapper, LiquidityPricePair?> {
		val accounts = inputs.flatMap {
			val pool = it.pairAddress.getSolanaPublicKey()

			val tokenVaults = getAssociatedTokenAddressPair(
				mint = it.tokenAddress.getSolanaPublicKey(),
				owner = pool,
			)

			val wsolVault = getAssociatedTokenAddress(
				mint = SolanaConstants.WSOL_MINT,
				owner = pool,
			)

			listOf(tokenVaults.tokenAddress, tokenVaults.token2022Address, wsolVault)
		}

		val accountsBalances = rpcClient.getTokenAccountBalances(accounts, Commitment.CONFIRMED).map { it?.toBigDecimal() }

		val liquidityToPricePairs = accountsBalances.chunked(3) { (tokenBalance, token2022Balance, wsolBalance) ->
			val tokenAccountBalance = tokenBalance ?: token2022Balance

			if (wsolBalance == null || tokenAccountBalance == null) return@chunked null

			val price = wsolBalance.divide(tokenAccountBalance, MathContext.DECIMAL128)
			val liquidity = wsolBalance.times(tokenAccountBalance).sqrt(MathContext.DECIMAL128)

			LiquidityPricePair(liquidity, price.asNativeAmount())
		}

		val poolAddressToLiquidityPrice = inputs.zip(liquidityToPricePairs) { input, liquidityPricePair ->
			val pair = liquidityPricePair?.copy(
				price = liquidityPricePair.price.toBase(input.tokenDecimals).toNative(Chain.SOLANA),
			)

			input.pairAddress to pair
		}.toMap()

		return poolAddressToLiquidityPrice
	}

	operator fun invoke(pairAddress: AddressWrapper, tokenAddress: AddressWrapper, tokenDecimals: Int): NativeAmount? {
		val pool = pairAddress.getSolanaPublicKey()

		val tokenVaults = getAssociatedTokenAddressPair(
			mint = tokenAddress.getSolanaPublicKey(),
			owner = pool,
		)

		val wsolVault = getAssociatedTokenAddress(
			mint = SolanaConstants.WSOL_MINT,
			owner = pool,
		)

		val accounts = listOf(tokenVaults.tokenAddress, tokenVaults.token2022Address, wsolVault)
		val (tokenBalance, token2022Balance, wsolReserves) = rpcClient.getTokenAccountBalances(accounts, Commitment.CONFIRMED)
		val tokenReserves = tokenBalance ?: token2022Balance

		if (wsolReserves == null || tokenReserves == null) return null

		val price = wsolReserves.toBigDecimal().divide(tokenReserves.toBigDecimal(), MathContext.DECIMAL128).asNativeAmount()
		val priceAdjusted = price.toBase(tokenDecimals).toNative(Chain.SOLANA)

		return priceAdjusted
	}
}
