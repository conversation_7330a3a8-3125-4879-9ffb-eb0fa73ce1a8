package com.cleevio.fatbot.adapter.out.evm.builder.titanbuilder

import com.cleevio.fatbot.adapter.out.evm.SendPrivateEthTransaction
import com.cleevio.fatbot.application.common.crypto.SignedTx
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class SendPrivateEthTransactionTitanbuilder(
	private val titanbuilderConnector: TitanbuilderConnector,
) : SendPrivateEthTransaction {

	override fun invoke(signedTx: SignedTx, maxAllowedBlockNumberToIncludeHint: BigInteger) {
		titanbuilderConnector.sendPrivateEthTransaction(signedTx, maxAllowedBlockNumberToIncludeHint)
	}
}
