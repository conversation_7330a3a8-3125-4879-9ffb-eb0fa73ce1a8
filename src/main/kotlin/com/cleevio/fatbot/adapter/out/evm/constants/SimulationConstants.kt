package com.cleevio.fatbot.adapter.out.evm.constants

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import org.web3j.crypto.Credentials

/**
 * This is just an arbitrary PK that is used in simulations and is not a sensitive information
 */
const val SIMULATION_WALLET_PK = "fbd981c590cb767e574fb359416f05c0688eb63c5d376f493fafeb22b54ab53e"

val simulationCredentials: Credentials = Credentials.create(SIMULATION_WALLET_PK)
val simulationAddress: AddressWrapper = AddressWrapper(simulationCredentials.address)
