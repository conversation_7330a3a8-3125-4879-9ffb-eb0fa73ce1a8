package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.module.bottransaction.constant.BotTransactionType
import com.cleevio.fatbot.application.module.botwallet.port.out.UpsertBotDepositTransactions
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.tables.references.BOT_TRANSACTION
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.time.Clock
import java.time.Instant

@Component
class UpsertBotDepositTransactionsJooq(
	private val dslContext: DSLContext,
	private val clock: Clock,
) : UpsertBotDepositTransactions {

	override fun invoke(inputs: List<UpsertBotDepositTransactions.Input>) {
		val now = Instant.now(clock)
		val rows = inputs.map {
			DSL.row(
				UUIDv7(),
				it.botWalletId,
				BotTransactionType.DEPOSIT,
				it.txHash.txHash,
				TransactionStatus.SUCCESS,
				0,
				it.exchangeRateUsd,
				it.baseValue.toBigDecimal(),
				it.baseValue.toBigDecimal(),
				now,
				now,
				0,
			)
		}

		dslContext
			.insertInto(
				BOT_TRANSACTION,
				BOT_TRANSACTION.ID,
				BOT_TRANSACTION.BOT_WALLET_ID,
				BOT_TRANSACTION.TYPE,
				BOT_TRANSACTION.TX_HASH,
				BOT_TRANSACTION.STATUS,
				BOT_TRANSACTION.VERIFICATION_COUNT,
				BOT_TRANSACTION.EXCHANGE_RATE_USD,
				BOT_TRANSACTION.BASE_VALUE,
				BOT_TRANSACTION.AMOUNT_IN,
				BOT_TRANSACTION.CREATED_AT,
				BOT_TRANSACTION.UPDATED_AT,
				BOT_TRANSACTION.VERSION,
			)
			.valuesOfRows(rows)
			.onConflict(BOT_TRANSACTION.TX_HASH)
			.doNothing()
			.execute()
	}
}
