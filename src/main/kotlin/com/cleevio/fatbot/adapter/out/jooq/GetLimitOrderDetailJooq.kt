package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.module.limitorder.exception.LimitOrderExceptionNotFoundException
import com.cleevio.fatbot.application.module.limitorder.port.out.GetLimitOrderDetail
import com.cleevio.fatbot.application.module.limitorder.query.GetLimitOrderDetailQuery
import com.cleevio.fatbot.tables.references.LIMIT_ORDER
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class GetLimitOrderDetailJooq(
	private val dslContext: DSLContext,
) : GetLimitOrderDetail {
	override fun invoke(userId: UUID, limitOrderId: UUID): GetLimitOrderDetailQuery.Result {
		val limitOrderConditions = listOf(
			LIMIT_ORDER.USER_ID.eq(userId),
			LIMIT_ORDER.ID.eq(limitOrderId),
		)

		return dslContext.select(
			LIMIT_ORDER.ID,
			LIMIT_ORDER.WALLET_ID,
			LIMIT_ORDER.TYPE,
			LIMIT_ORDER.TOKEN_ADDRESS,
			LIMIT_ORDER.CHAIN,
			LIMIT_ORDER.LIMIT_PRICE,
			LIMIT_ORDER.REMAINING_AMOUNT,
			LIMIT_ORDER.FILLED_AMOUNT,
			LIMIT_ORDER.IS_LOCKED,
		)
			.from(LIMIT_ORDER)
			.where(limitOrderConditions)
			.fetchOne()
			?.let {
				GetLimitOrderDetailQuery.Result(
					walletId = it[LIMIT_ORDER.WALLET_ID]!!,
					limitOrderId = it[LIMIT_ORDER.ID]!!,
					type = it[LIMIT_ORDER.TYPE]!!,
					tokenAddress = it[LIMIT_ORDER.TOKEN_ADDRESS]!!,
					chain = it[LIMIT_ORDER.CHAIN]!!,
					limitPrice = it[LIMIT_ORDER.LIMIT_PRICE]!!.asNativeAmount(),
					remainingAmount = it[LIMIT_ORDER.REMAINING_AMOUNT]!!.asNativeAmount(),
					filledAmount = it[LIMIT_ORDER.FILLED_AMOUNT]!!.asNativeAmount(),
					isLocked = it[LIMIT_ORDER.IS_LOCKED]!!,
				)
			} ?: throw LimitOrderExceptionNotFoundException("Limit order with id: $limitOrderId not found.")
	}
}
