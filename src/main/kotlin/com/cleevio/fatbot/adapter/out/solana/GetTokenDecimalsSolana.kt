package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.market.port.out.GetTokenDecimals
import com.cleevio.fatbot.application.module.token.finder.EvmTokenInfoFinderService
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class GetTokenDecimalsSolana(
	private val rpcClient: RpcClient,
	private val evmTokenInfoFinderService: EvmTokenInfoFinderService,
) : GetTokenDecimals {

	override fun supports() = ChainType.SOLANA

	override fun invoke(token: ChainAddress): BigInteger {
		val dbData = evmTokenInfoFinderService.findByTokenAddressAndChain(
			tokenAddress = token.address,
			chain = token.chain,
		)

		if (dbData != null) {
			return dbData.decimals
		}

		val tokenAccountInfo = rpcClient.api.getSplTokenAccountInfo(token.address.getSolanaPublicKey())
		return tokenAccountInfo.value.data.parsed.info.decimals.toBigInteger()
	}
}
