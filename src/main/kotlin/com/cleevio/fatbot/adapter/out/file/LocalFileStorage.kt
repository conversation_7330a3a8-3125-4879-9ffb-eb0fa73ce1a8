package com.cleevio.fatbot.adapter.out.file

import com.cleevio.fatbot.application.module.file.FileStorageService
import com.cleevio.fatbot.infrastructure.config.logger
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.io.File
import java.io.InputStream

@Service
class LocalFileStorage(
	@Value("\${fatbot.storage.local-path}") private val storagePath: String,
) : FileStorageService {

	private val logger = logger()

	override fun save(fileName: String, file: InputStream) {
		storagePath.ensureDirectoryExistsOrThrow()

		File(storagePath, fileName)
			.outputStream()
			.use { output -> file.use { it.copyTo(output) } }
	}

	override fun delete(fileName: String) = runCatching { File(storagePath, fileName).delete() }.getOrElse {
		logger.error("Unable to delete $fileName", it)
		false
	}

	override fun copy(originalFileName: String, newFileName: String) = runCatching {
		File(storagePath, originalFileName).copyTo(File(storagePath, newFileName))
	}
		.getOrElse {
			logger.error("Unable to copy $originalFileName as $newFileName", it)
			throw it
		}
		.run { if (!this.exists()) error("Copied file does not exist. Original: $originalFileName New: $newFileName") }

	override fun get(fileName: String): File {
		return File(storagePath, fileName).also { assert(it.isFile) }
	}

	private fun String.ensureDirectoryExistsOrThrow() = File(this).also {
		if (!it.exists() && !it.mkdirs()) throw Exception("The $this directory does not exist")
	}
}
