package com.cleevio.fatbot.adapter.out.bitquery.cache

import com.cleevio.fatbot.application.common.util.calculateExpiresAtInNanos
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.token.constant.TimeInterval
import com.cleevio.fatbot.application.module.token.query.TokenPriceTimeIntervalItem
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.github.benmanes.caffeine.cache.Expiry
import java.time.Instant
import java.util.Optional

/**
 * Identical to [GetIntervalTokenPricesCacheKey] (to ensure expiration at the same time) with the addition of [before],
 * which identifies it with requests made at different time
 */
data class GetSingleIntervalTokenPricesCacheKey(
	val chain: Chain,
	val pairAddress: AddressWrapper,
	val intervalCount: Int,
	val timeInterval: TimeInterval,
	val before: Instant,
)

object GetSingleIntervalTokenPriceExpiry :
	Expiry<GetSingleIntervalTokenPricesCacheKey, Optional<TokenPriceTimeIntervalItem>> {

	override fun expireAfterCreate(
		key: GetSingleIntervalTokenPricesCacheKey,
		value: Optional<TokenPriceTimeIntervalItem>,
		currentTime: Long,
	): Long = calculateExpiresAtInNanos(
		nowInNanos = currentTime,
		timeInterval = key.timeInterval,
		intervalCount = key.intervalCount,
	)

	override fun expireAfterUpdate(
		key: GetSingleIntervalTokenPricesCacheKey?,
		value: Optional<TokenPriceTimeIntervalItem>?,
		currentTime: Long,
		currentDuration: Long,
	): Long = currentDuration

	override fun expireAfterRead(
		key: GetSingleIntervalTokenPricesCacheKey?,
		value: Optional<TokenPriceTimeIntervalItem>?,
		currentTime: Long,
		currentDuration: Long,
	): Long = currentDuration
}
