package com.cleevio.fatbot.adapter.out.solana.meteora.model

// We can add deserialization for the fields
class LbPair {

	companion object {
		const val BYTES: Int = 904

		const val PARAMETERS_OFFSET: Int = 8
		const val V_PARAMETERS_OFFSET: Int = 40
		const val BUMP_SEED_OFFSET: Int = 72
		const val BIN_STEP_SEED_OFFSET: Int = 73
		const val PAIR_TYPE_OFFSET: Int = 75
		const val ACTIVE_ID_OFFSET: Int = 76
		const val BIN_STEP_OFFSET: Int = 80
		const val STATUS_OFFSET: Int = 82
		const val REQUIRE_BASE_FACTOR_SEED_OFFSET: Int = 83
		const val BASE_FACTOR_SEED_OFFSET: Int = 84
		const val ACTIVATION_TYPE_OFFSET: Int = 86
		const val PADDING0_OFFSET: Int = 87
		const val TOKEN_X_MINT_OFFSET: Int = 88
		const val TOKEN_Y_MINT_OFFSET: Int = 120
		const val RESERVE_X_OFFSET: Int = 152
		const val RESERVE_Y_OFFSET: Int = 184
		const val PROTOCOL_FEE_OFFSET: Int = 216
		const val PADDING1_OFFSET: Int = 232
		const val REWARD_INFOS_OFFSET: Int = 264
		const val ORACLE_OFFSET: Int = 552
		const val BIN_ARRAY_BITMAP_OFFSET: Int = 584
		const val LAST_UPDATED_AT_OFFSET: Int = 712
		const val PADDING2_OFFSET: Int = 720
		const val PRE_ACTIVATION_SWAP_ADDRESS_OFFSET: Int = 752
		const val BASE_KEY_OFFSET: Int = 784
		const val ACTIVATION_POINT_OFFSET: Int = 816
		const val PRE_ACTIVATION_DURATION_OFFSET: Int = 824
		const val PADDING3_OFFSET: Int = 832
		const val PADDING4_OFFSET: Int = 840
		const val CREATOR_OFFSET: Int = 848
		const val RESERVED_OFFSET: Int = 880
	}
}
