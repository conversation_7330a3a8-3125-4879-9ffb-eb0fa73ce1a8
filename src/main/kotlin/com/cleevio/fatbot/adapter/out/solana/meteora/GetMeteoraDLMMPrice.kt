package com.cleevio.fatbot.adapter.out.solana.meteora

import com.cleevio.fatbot.adapter.out.solana.meteora.model.LbPair
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.crypto.asNativeAmount
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.syntifi.near.borshj.BorshBuffer
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.math.MathContext
import java.util.Base64

private val BASIS_POINT_MAX = 10000.toBigDecimal()

@Component
class GetMeteoraDLMMPrice(
	private val rpcClient: RpcClient,
) {

	data class Input(
		val lpPair: AddressWrapper,
		val tokenDecimalsX: Int,
		val tokenDecimalsY: Int,
	)

	fun invoke(input: Input): NativeAmount {
		val dataSliceOffset = LbPair.ACTIVE_ID_OFFSET
		val lbPairAccount = rpcClient.api.getAccountInfo(
			input.lpPair.getSolanaPublicKey(),
			// Request a data slice of [LbPair.ACTIVE_ID_OFFSET, LbPair.BIN_STEP_OFFSET]
			mapOf("dataSlice" to mapOf("offset" to dataSliceOffset, "length" to 6)),
		)
		val lbPairData = BorshBuffer.wrap(lbPairAccount.decodedData)

		val activeId = lbPairData.readU32()
		val binStep = lbPairData.readU16()

		val price = calculatePrice(
			binStep = binStep.toInt().toBigDecimal(),
			activeId = activeId,
			tokenDecimalsX = input.tokenDecimalsX,
			tokenDecimalsY = input.tokenDecimalsY,
		)

		return price
	}

	fun getMany(inputs: List<Input>): Map<AddressWrapper, NativeAmount> {
		val dataSliceOffset = LbPair.ACTIVE_ID_OFFSET
		val accounts = rpcClient.api.getMultipleAccounts(
			inputs.map { it.lpPair.getSolanaPublicKey() },
			// Request a data slice of [LbPair.ACTIVE_ID_OFFSET, LbPair.BIN_STEP_OFFSET]
			mapOf("dataSlice" to mapOf("offset" to dataSliceOffset, "length" to 6)),
		)

		val addressToPrice = inputs.zip(accounts).associate { (input, account) ->
			val decodedData = Base64.getDecoder().decode(account.data.first().toByteArray())
			val lbPairData = BorshBuffer.wrap(decodedData)

			val activeId = lbPairData.readU32()
			val binStep = lbPairData.readU16()

			val price = calculatePrice(
				binStep = binStep.toInt().toBigDecimal(),
				activeId = activeId,
				tokenDecimalsX = input.tokenDecimalsX,
				tokenDecimalsY = input.tokenDecimalsY,
			)

			input.lpPair to price
		}

		return addressToPrice
	}

	private fun calculatePrice(
		binStep: BigDecimal,
		activeId: Int,
		tokenDecimalsX: Int,
		tokenDecimalsY: Int,
	): NativeAmount {
		val basisPoints = binStep.divide(BASIS_POINT_MAX, MathContext.DECIMAL128)
		val base = BigDecimal.ONE + basisPoints

		val price = base.pow(activeId, MathContext.DECIMAL128).asNativeAmount()
		val priceAdjusted = price.toBase(tokenDecimalsX).toNative(tokenDecimalsY)

		return priceAdjusted
	}
}
