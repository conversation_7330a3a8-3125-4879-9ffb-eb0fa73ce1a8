package com.cleevio.fatbot.adapter.out.aggregator

import com.cleevio.fatbot.adapter.out.aggregator.response.GetTokenStateResponse
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.matchmaking.port.out.GetTokenState
import org.springframework.stereotype.Component

@Component
class GetTokenStateAggregator(
	private val fatbotAggregatorConnector: FatbotAggregatorConnector,
) : GetTokenState {

	override fun ofSingleToken(tokenAddress: AddressWrapper): GetTokenStateResponse {
		return fatbotAggregatorConnector.getTokenState(tokenAddress)
	}

	override fun ofManyTokens(tokenAddresses: Set<AddressWrapper>): Map<AddressWrapper, GetTokenStateResponse> {
		return fatbotAggregatorConnector
			.searchTokenStates(tokenAddresses)
			.associateBy { it.tokenAddress }
	}
}
