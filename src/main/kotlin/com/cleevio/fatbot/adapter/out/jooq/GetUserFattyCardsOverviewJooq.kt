package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.userfattycard.port.out.GetUserFattyCardsOverview
import com.cleevio.fatbot.application.module.userfattycard.query.GetUserFattyCardsOverviewQuery
import com.cleevio.fatbot.application.module.userstatistics.exception.UserStatisticsNotFoundException
import com.cleevio.fatbot.tables.references.USER_FATTY_CARD
import com.cleevio.fatbot.tables.references.USER_STATISTICS
import org.jooq.DSLContext
import org.jooq.impl.DSL.count
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

@Component
class GetUserFattyCardsOverviewJooq(
	private val dslContext: DSLContext,
) : GetUserFattyCardsOverview {
	override operator fun invoke(
		userId: UUID,
		maxCardsEarnedInOneDay: Int,
		tradeAmountForFattyCard: BigDecimal,
	): GetUserFattyCardsOverviewQuery.Result {
		val userFattyCardsEarnedByDate = select(count())
			.from(USER_FATTY_CARD)
			.where(
				USER_FATTY_CARD.CREATED_AT.cast(LocalDate::class.java).eq(LocalDate.now()),
				USER_FATTY_CARD.USER_ID.eq(USER_STATISTICS.USER_ID),
			).asField<Int>()

		return dslContext.select(
			USER_STATISTICS.USER_ID,
			USER_STATISTICS.TRADED_AMOUNT_IN_DAY,
			userFattyCardsEarnedByDate,
		).from(USER_STATISTICS)
			.where(USER_STATISTICS.USER_ID.eq(userId))
			.fetchOne()
			?.map {
				val amountOfFattyCardsEarnedToday = it[userFattyCardsEarnedByDate]!!
				val amountNeededToNextFattyCard = if (amountOfFattyCardsEarnedToday >= maxCardsEarnedInOneDay) {
					BigDecimal.ZERO
				} else {
					tradeAmountForFattyCard.minus(
						it[USER_STATISTICS.TRADED_AMOUNT_IN_DAY]!! % tradeAmountForFattyCard,
					)
				}

				GetUserFattyCardsOverviewQuery.Result(
					userId = it[USER_STATISTICS.USER_ID]!!,
					amountOfFattyCardsEarnedToday = amountOfFattyCardsEarnedToday,
					amountNeededToNextFattyCard = amountNeededToNextFattyCard,
				)
			} ?: throw UserStatisticsNotFoundException("User statistics for user $userId not found.")
	}
}
