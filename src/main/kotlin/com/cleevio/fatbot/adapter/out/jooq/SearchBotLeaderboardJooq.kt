package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fatbot.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fatbot.adapter.`in`.toInfiniteScrollSlice
import com.cleevio.fatbot.application.module.bot.constant.BotSortParameter
import com.cleevio.fatbot.application.module.botleaderboard.port.out.SearchBotLeaderboard
import com.cleevio.fatbot.application.module.botleaderboard.query.BotsLeaderboardSearchQuery
import com.cleevio.fatbot.tables.references.BOT
import com.cleevio.fatbot.tables.references.BOT_LEADERBOARD_SNAPSHOT
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION
import com.cleevio.fatbot.tables.references.BOT_WALLET
import org.jooq.DSLContext
import org.jooq.impl.DSL.field
import org.jooq.impl.DSL.partitionBy
import org.jooq.impl.DSL.rank
import org.jooq.impl.DSL.select
import org.jooq.impl.DSL.selectCount
import org.jooq.kotlin.`as`
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.UUID

@Component
class SearchBotLeaderboardJooq(
	private val dslContext: DSLContext,
) : SearchBotLeaderboard {
	override fun invoke(
		userId: UUID?,
		from: Instant,
		to: Instant,
		sortParameter: BotSortParameter,
		infiniteScroll: InfiniteScrollDesc<BigDecimal>,
		searchString: String?,
	): InfiniteScrollSlice<BotsLeaderboardSearchQuery.Result, BigDecimal> {
		val botConditions = listOfNotNull(
			userId?.let { BOT.USER_ID.eq(it) },
			searchString?.let { BOT.NAME.likeIgnoreCase("%$it%") },
			field("rank").eq(1),
		)

		val botLeaderboardSnapshotConditions = listOf(
			BOT_LEADERBOARD_SNAPSHOT.SNAPSHOT_MADE_AT.between(from, to),
		)

		val sortField = when (sortParameter) {
			BotSortParameter.PROFIT_USD -> BOT_LEADERBOARD_SNAPSHOT.PROFIT_VALUE_USD
			BotSortParameter.PROFIT_PERCENTAGE -> BOT_LEADERBOARD_SNAPSHOT.PROFIT_VALUE_FRACTION
			BotSortParameter.WALLET_BALANCE -> BOT_LEADERBOARD_SNAPSHOT.BALANCE_USD
		}

		val buyTransactionsCount = selectCount()
			.from(BOT_MARKET_POSITION)
			.where(BOT_MARKET_POSITION.BOT_WALLET_ID.eq(BOT_WALLET.ID))
			.and(BOT_MARKET_POSITION.CREATED_AT.between(from, to)).`as`("buy_transactions_count")

		val sellTransactionsCount = selectCount()
			.from(BOT_MARKET_POSITION)
			.where(BOT_MARKET_POSITION.BOT_WALLET_ID.eq(BOT_WALLET.ID))
			.and(BOT_MARKET_POSITION.TOTAL_TOKEN_AMOUNT_SOLD.ne(BigDecimal.ZERO))
			.and(BOT_MARKET_POSITION.CREATED_AT.between(from, to)).`as`("sell_transactions_count")

		val botLeaderboardRank = select(
			rank().over(
				partitionBy(BOT_LEADERBOARD_SNAPSHOT.BOT_ID).orderBy(BOT_LEADERBOARD_SNAPSHOT.SNAPSHOT_MADE_AT.desc()),
			).`as`("rank"),
			BOT_LEADERBOARD_SNAPSHOT.ID,
			BOT_LEADERBOARD_SNAPSHOT.BOT_ID,
		)
			.from(BOT_LEADERBOARD_SNAPSHOT)
			.where(botLeaderboardSnapshotConditions)
			.asTable("BOT_LEADERBOARD_RANK")

		return dslContext.select(
			BOT.ID,
			BOT.CREATED_AT,
			BOT.NAME,
			BOT.AVATAR_FILE_ID,
			BOT_LEADERBOARD_SNAPSHOT.PROFIT_VALUE_USD,
			BOT_LEADERBOARD_SNAPSHOT.PROFIT_VALUE_FRACTION,
			BOT_LEADERBOARD_SNAPSHOT.BALANCE_USD,
			buyTransactionsCount,
			sellTransactionsCount,
		)
			.from(botLeaderboardRank)
			.innerJoin(BOT).on(botLeaderboardRank.field(BOT_LEADERBOARD_SNAPSHOT.BOT_ID)!!.eq(BOT.ID))
			.innerJoin(BOT_LEADERBOARD_SNAPSHOT)
			.on(botLeaderboardRank.field(BOT_LEADERBOARD_SNAPSHOT.ID)!!.eq(BOT_LEADERBOARD_SNAPSHOT.ID))
			.innerJoin(BOT_WALLET).on(BOT.ID.eq(BOT_WALLET.BOT_ID))
			.whereWithInfiniteScroll(
				conditions = botConditions,
				infiniteScroll = infiniteScroll,
				idFieldSelector = sortField,
			)
			.fetch()
			.map {
				BotsLeaderboardSearchQuery.Result(
					botId = it[BOT.ID]!!,
					botName = it[BOT.NAME]!!,
					botAvatarFileId = it[BOT.AVATAR_FILE_ID]!!,
					profitUsd = it[BOT_LEADERBOARD_SNAPSHOT.PROFIT_VALUE_USD]!!,
					profitPercentage = it[BOT_LEADERBOARD_SNAPSHOT.PROFIT_VALUE_FRACTION]!!,
					walletBalance = it[BOT_LEADERBOARD_SNAPSHOT.BALANCE_USD]!!,
					daysActive = ChronoUnit.DAYS.between(it[BOT.CREATED_AT]!!, to).toInt(),
					buyTransactionsCount = it[buyTransactionsCount]!!,
					sellTransactionsCount = it[sellTransactionsCount]!!,
				)
			}
			.toInfiniteScrollSlice(infiniteScroll = infiniteScroll) {
				when (sortParameter) {
					BotSortParameter.PROFIT_USD -> profitUsd
					BotSortParameter.PROFIT_PERCENTAGE -> profitPercentage
					BotSortParameter.WALLET_BALANCE -> walletBalance
				}
			}
	}
}
