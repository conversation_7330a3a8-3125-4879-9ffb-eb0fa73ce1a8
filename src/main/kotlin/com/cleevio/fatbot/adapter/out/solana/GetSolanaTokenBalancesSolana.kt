package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.adapter.out.solana.client.getTokenAccountBalance
import com.cleevio.fatbot.adapter.out.solana.client.getTokenAccountBalances
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.util.getAssociatedTokenAddressPair
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetTokenBalancesSolana
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class GetSolanaTokenBalancesSolana(
	private val rpcClient: RpcClient,
) : GetTokenBalancesSolana {

	override fun single(ownerAddress: AddressWrapper, tokenAddress: AddressWrapper): BaseAmount {
		val balance = fetchTokenBalances(listOf(tokenAddress), ownerAddress).singleOrNull()
		val resultBalance = balance ?: BigInteger.ZERO

		return resultBalance.asBaseAmount()
	}

	override fun single(tokenAccountAddress: AddressWrapper): BaseAmount {
		val balance = rpcClient.getTokenAccountBalance(
			tokenAccountAddress.getSolanaPublicKey(),
			Commitment.CONFIRMED,
		) ?: BigInteger.ZERO

		return balance.asBaseAmount()
	}

	override fun ofAll(
		ownerAddress: AddressWrapper,
		tokenAddresses: List<AddressWrapper>,
	): Map<AddressWrapper, BigInteger> {
		val tokenBalances = fetchTokenBalances(tokenAddresses, ownerAddress)

		val tokenAddressToBalance = tokenAddresses.zip(tokenBalances) { tokenAddress, tokenBalance ->
			tokenAddress to (tokenBalance ?: BigInteger.ZERO)
		}.toMap()

		return tokenAddressToBalance
	}

	private fun fetchTokenBalances(
		tokenAddresses: List<AddressWrapper>,
		ownerAddress: AddressWrapper,
	): List<BigInteger?> {
		val owner = ownerAddress.getSolanaPublicKey()

		val tokenAccountAddresses = tokenAddresses.flatMap {
			val pair = getAssociatedTokenAddressPair(mint = it.getSolanaPublicKey(), owner)

			listOf(pair.tokenAddress, pair.token2022Address)
		}

		val accountBalances = rpcClient.getTokenAccountBalances(tokenAccountAddresses, Commitment.CONFIRMED)
		return accountBalances.chunked(2) { (tokenBalance, token2022Balance) -> tokenBalance ?: token2022Balance }
	}
}
