package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.bot.port.out.DeleteAllBotMarketPositionTradeState
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION_TRADE_STATE
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class DeleteAllBotMarketPositionTradeStateJooq(
	private val dslContext: DSLContext,
) : DeleteAllBotMarketPositionTradeState {

	override fun invoke(botWalletId: UUID) {
		val allMarketPositionIdsOfBotWallet = DSL
			.select(BOT_MARKET_POSITION.ID)
			.from(BOT_MARKET_POSITION)
			.where(BOT_MARKET_POSITION.BOT_WALLET_ID.eq(botWalletId))

		dslContext
			.deleteFrom(BOT_MARKET_POSITION_TRADE_STATE)
			.where(BOT_MARKET_POSITION_TRADE_STATE.BOT_MARKET_POSITION_ID.`in`(allMarketPositionIdsOfBotWallet))
			.execute()
	}
}
