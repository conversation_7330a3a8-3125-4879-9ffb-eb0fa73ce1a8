package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.botmarket.constant.BotMarketPositionState
import com.cleevio.fatbot.application.module.matchmaking.port.out.FindAllRedFlaggedBotMarketPositionIds
import com.cleevio.fatbot.application.module.token.constant.SolanaTokenRedFlag
import com.cleevio.fatbot.tables.references.BOT
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION
import com.cleevio.fatbot.tables.references.BOT_WALLET
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class FindAllRedFlaggedBotMarketPositionIdsJooq(
	private val dslContext: DSLContext,
) : FindAllRedFlaggedBotMarketPositionIds {

	override fun invoke(tokenAddress: AddressWrapper, type: SolanaTokenRedFlag): List<UUID> {
		val redFlagCondition = when (type) {
			SolanaTokenRedFlag.TICKER_COPY -> BOT.TOKEN_TICKER_COPY_IS_CHECKED.isTrue
			SolanaTokenRedFlag.CREATOR_HIGH_BUY -> BOT.CREATOR_HIGH_BUY_IS_CHECKED.isTrue
			SolanaTokenRedFlag.BUNDLE_DETECTED -> BOT.BUNDLED_BUYS_DETECTED_IS_CHECKED.isTrue
			SolanaTokenRedFlag.SUSPICIOUS_WALLETS_DETECTED -> BOT.SUSPICIOUS_WALLETS_DETECTED_IS_CHECKED.isTrue
			SolanaTokenRedFlag.SINGLE_HIGH_BUY -> BOT.SINGLE_HIGH_BUY_IS_CHECKED.isTrue
		}

		val conditions = listOf(
			redFlagCondition,
			BOT_MARKET_POSITION.STATE.eq(BotMarketPositionState.OPENED),
			BOT_MARKET_POSITION.TOKEN_ADDRESS.eq(tokenAddress),
		)

		return dslContext
			.select(BOT_MARKET_POSITION.ID)
			.from(BOT_MARKET_POSITION)
			.innerJoin(BOT_WALLET).on(BOT_MARKET_POSITION.BOT_WALLET_ID.eq(BOT_WALLET.ID))
			.innerJoin(BOT).on(BOT_WALLET.BOT_ID.eq(BOT.ID))
			.where(conditions)
			.fetch()
			.map {
				it[BOT_MARKET_POSITION.ID]!!
			}
	}
}
