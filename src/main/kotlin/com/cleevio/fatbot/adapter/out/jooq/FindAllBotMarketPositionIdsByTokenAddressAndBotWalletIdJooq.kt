package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.bottransaction.port.out.FindAllBotMarketPositionIdsByTokenAddressAndBotWalletId
import com.cleevio.fatbot.tables.references.BOT_MARKET_POSITION
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class FindAllBotMarketPositionIdsByTokenAddressAndBotWalletIdJooq(private val dslContext: DSLContext) :
	FindAllBotMarketPositionIdsByTokenAddressAndBotWalletId {

	override fun invoke(input: List<FindAllBotMarketPositionIdsByTokenAddressAndBotWalletId.Input>): List<UUID> {
		val singleMarketPositionConditions = input.map {
			DSL.condition(BOT_MARKET_POSITION.BOT_WALLET_ID.eq(it.botWalletId))
				.and(BOT_MARKET_POSITION.TOKEN_ADDRESS.eq(it.tokenAddress))
		}

		val conditions = DSL.or(singleMarketPositionConditions)

		return dslContext
			.select(BOT_MARKET_POSITION.ID)
			.from(BOT_MARKET_POSITION)
			.where(conditions)
			.fetch()
			.map { it[BOT_MARKET_POSITION.ID]!! }
	}
}
