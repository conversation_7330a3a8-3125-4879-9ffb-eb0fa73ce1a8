package com.cleevio.fatbot.adapter.out.evm.builder.titanbuilder

import com.cleevio.fatbot.adapter.out.BaseConnector
import com.cleevio.fatbot.adapter.out.evm.builder.titanbuilder.request.SendPrivateEthTransactionTitanbuilderRequest
import com.cleevio.fatbot.adapter.out.evm.builder.titanbuilder.response.SendPrivateEthTransactionTitanbuilderResponse
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.infrastructure.config.properties.TitanbuilderProperties
import com.cleevio.fatbot.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fatbot.infrastructure.exception.FatbotApiException
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.stereotype.Component
import org.springframework.web.bind.annotation.ResponseStatus
import java.math.BigInteger

@Component
class TitanbuilderConnector(
	titanbuilderProperties: TitanbuilderProperties,
) : BaseConnector(
	baseUrl = titanbuilderProperties.baseUrl,
	restClientCustomizer = { it.defaultHeader(X_API_KEY_HEADER, titanbuilderProperties.apiKey) },
) {

	fun sendPrivateEthTransaction(signedTx: SignedTx, maxAllowedBlockNumberToInclude: BigInteger) {
		val body = SendPrivateEthTransactionTitanbuilderRequest(
			params = listOf(
				SendPrivateEthTransactionTitanbuilderRequest.Params(
					signedTx = signedTx,
					targetBlock = maxAllowedBlockNumberToInclude,
				),
			),
		)

		restClient
			.post()
			.body(body)
			.contentType(MediaType.APPLICATION_JSON)
			.retrieveResponseWithErrorHandler<SendPrivateEthTransactionTitanbuilderResponse>()
			.also {
				if (it.error != null) {
					logger.error(it.toString())
					throw TitanbuilderException()
				}
			}
	}
}

@ResponseStatus(HttpStatus.BAD_GATEWAY)
class TitanbuilderException : FatbotApiException(
	reason = ExtendedErrorReasonType.TITAN_BUILDER_API_EXCEPTION,
	message = "Titanbuilder integration exception",
)

private const val X_API_KEY_HEADER: String = "X-API-Key"
