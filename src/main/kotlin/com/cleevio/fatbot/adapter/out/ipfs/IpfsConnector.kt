package com.cleevio.fatbot.adapter.out.ipfs

import com.cleevio.fatbot.adapter.out.BaseConnector
import com.cleevio.fatbot.adapter.out.ipfs.response.IpfsTokenInfoResponse
import io.sentry.spring.jakarta.tracing.SentrySpan
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class IpfsConnector(
	@Value("\${integration.ipfs.base-url}") private val baseUrl: String,
) : BaseConnector(
	baseUrl = baseUrl,
	restClientCustomizer = {},
) {
	@SentrySpan
	fun downloadTokenInfo(infoUrl: String): IpfsTokenInfoResponse {
		return restClient.get()
			.uri(infoUrl.substringAfter(baseUrl))
			.retrieveResponseWithErrorHandler<IpfsTokenInfoResponse>()
	}
}
