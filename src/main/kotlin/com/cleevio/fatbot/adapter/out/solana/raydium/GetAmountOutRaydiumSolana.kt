package com.cleevio.fatbot.adapter.out.solana.raydium

import com.cleevio.fatbot.adapter.out.solana.client.getTokenAccountBalances
import com.cleevio.fatbot.adapter.out.solana.raydium.util.getCPMMPoolVaultPda
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SolanaConstants
import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.div
import com.cleevio.fatbot.application.common.crypto.times
import com.cleevio.fatbot.application.common.crypto.toBase
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.module.market.port.out.GetAmountOutRaydium
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.token.RaydiumAmmMarketData
import com.cleevio.fatbot.domain.token.RaydiumCPMMMarketData
import com.cleevio.fatbot.domain.token.getTradeFeeBp
import org.p2p.solanaj.rpc.RpcClient
import org.p2p.solanaj.rpc.types.config.Commitment
import org.springframework.stereotype.Component
import java.math.BigInteger

@Component
class GetAmountOutRaydiumSolana(
	private val rpcClient: RpcClient,
	private val getRaydiumCLMMPrice: GetRaydiumCLMMPrice,
) : GetAmountOutRaydium {

	override fun getAmountOutCLMM(
		amount: BaseAmount,
		pairAddress: AddressWrapper,
		tokenDecimals: BigInteger,
		isBuy: Boolean,
	): BaseAmount {
		val price = getRaydiumCLMMPrice(pairAddress)

		// TODO: Temporary naive solution
		val amountOut = if (isBuy) {
			amount.toNative(Chain.SOLANA).div(price).toBase(tokenDecimals.toInt())
		} else {
			amount.toNative(tokenDecimals.toInt()).times(price).toBase(Chain.SOLANA)
		}

		return amountOut
	}

	override fun getAmountOutCPMM(
		amount: BaseAmount,
		pairAddress: AddressWrapper,
		tokenAddress: AddressWrapper,
		marketData: RaydiumCPMMMarketData,
		isBuy: Boolean,
	): BaseAmount {
		val tokenVault = getCPMMPoolVaultPda(pairAddress.getSolanaPublicKey(), tokenAddress.getSolanaPublicKey())
		val wsolVault = getCPMMPoolVaultPda(pairAddress.getSolanaPublicKey(), SolanaConstants.WSOL_MINT)

		val (tokenReserves, wsolReserves) = rpcClient.getTokenAccountBalances(
			listOf(tokenVault, wsolVault),
			Commitment.CONFIRMED,
		)

		val amountOut = if (isBuy) {
			calculateAmountOutCPMM(
				amount = amount.amount,
				reserveIn = wsolReserves!!,
				reserveOut = tokenReserves!!,
				tradeFeeBp = marketData.getTradeFeeBp(),
			)
		} else {
			calculateAmountOutCPMM(
				amount = amount.amount,
				reserveIn = tokenReserves!!,
				reserveOut = wsolReserves!!,
				tradeFeeBp = marketData.getTradeFeeBp(),
			)
		}

		return amountOut.asBaseAmount()
	}

	private fun calculateAmountOutCPMM(
		amount: BigInteger,
		reserveIn: BigInteger,
		reserveOut: BigInteger,
		tradeFeeBp: BasisPoint,
	): BigInteger {
		val fee = tradeFeeBp.applyOnCeilDiv(amount)
		val amountInAfterFee = amount - fee

		val invariant = reserveIn * reserveOut
		val newReserveIn = reserveIn + amountInAfterFee

		val newReserveOut = invariant.divideAndRemainder(newReserveIn).let { (divResult, remainder) ->
			if (remainder > BigInteger.ZERO) divResult.inc() else divResult
		}

		val amountOut = reserveOut - newReserveOut

		return amountOut
	}

	override fun getAmountOutAMM(
		amount: BaseAmount,
		pairAddress: AddressWrapper,
		marketData: RaydiumAmmMarketData,
		isBuy: Boolean,
	): BaseAmount {
		val (wsolReserves, tokenReserves) = getMarketReserves(marketData)

		val amountOut = if (isBuy) {
			calculateAmountOutAMM(amount = amount, reserveIn = wsolReserves, reserveOut = tokenReserves)
		} else {
			calculateAmountOutAMM(amount = amount, reserveIn = tokenReserves, reserveOut = wsolReserves)
		}

		return amountOut
	}

	private fun calculateAmountOutAMM(amount: BaseAmount, reserveIn: BaseAmount, reserveOut: BaseAmount): BaseAmount {
		val fee = SolanaConstants.RAYDIUM_AMM_LP_FEE_BP.applyOnCeilDiv(amount.amount)
		val amountInAfterFee = amount.amount - fee

		val amountOut = (reserveOut.amount * amountInAfterFee) / (reserveIn.amount + amountInAfterFee)

		return amountOut.asBaseAmount()
	}

	private data class MarketReserves(
		val wsolReserves: BaseAmount,
		val tokenReserves: BaseAmount,
	)

	private fun getMarketReserves(marketData: RaydiumAmmMarketData): MarketReserves {
		val wsolVault = marketData.wsolVault().getSolanaPublicKey()
		val tokenVault = marketData.tokenVault().getSolanaPublicKey()

		val (wsolReserves, tokenReserves) = rpcClient.getTokenAccountBalances(
			listOf(wsolVault, tokenVault),
			Commitment.CONFIRMED,
		)

		requireNotNull(tokenReserves) { "Received null token reserves for tokenVault: $tokenVault" }
		requireNotNull(wsolReserves) { "Received null wsol reserves for wsolVault: $wsolVault" }
		require(tokenReserves != BigInteger.ZERO) { "Received 0 token reserves for tokenVault: $tokenVault" }
		require(wsolReserves != BigInteger.ZERO) { "Received 0 wsol reserves for wsolVault: $wsolVault" }

		return MarketReserves(wsolReserves.asBaseAmount(), tokenReserves.asBaseAmount())
	}
}
