package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.application.common.wrapper.ChainAddress
import com.cleevio.fatbot.application.module.market.port.out.GetTokenDetailFromChain
import com.cleevio.fatbot.application.module.market.query.GetTokenDetailQuery
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.infrastructure.coroutines.createJobScope
import io.sentry.spring.jakarta.tracing.SentrySpan
import kotlinx.coroutines.async
import org.springframework.stereotype.Component

@Component
class GetTokenDetailFromChainEVM(
	private val evmChainContextFactory: EvmChainContextFactory,
) : GetTokenDetailFromChain {

	override fun supports(): ChainType = ChainType.EVM

	@SentrySpan
	override fun invoke(token: ChainAddress): GetTokenDetailQuery.NonDexDetail? {
		// for now, we can only search on main net
		return evmChainContextFactory.ofChainIdRunBlocking(chainId = token.chain.evmId) {
			runCatching {
				val scope = createJobScope()

				val name = scope.async { client.getTokenName(token.address) }
				val symbol = scope.async { client.getTokenSymbol(token.address) }

				GetTokenDetailQuery.NonDexDetail(
					nonDexInfo = GetTokenDetailQuery.NonDexInfo(
						name = name.await(),
						symbol = symbol.await(),
					),
				)
			}.getOrNull()
		}
	}
}
