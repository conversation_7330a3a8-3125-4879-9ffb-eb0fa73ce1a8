package com.cleevio.fatbot.adapter.out.aggregator.response

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.math.BigDecimal

@JsonIgnoreProperties(ignoreUnknown = true)
data class GetTokenStateResponse(
	val tokenAddress: AddressWrapper,
	val marketCapUsd: BigDecimal,
	val liquidityUsd: BigDecimal,
	val volumeUsd: BigDecimal,
	val numOfAccountHolders: Long,
	val buyVolume: BigDecimal,
	val sellVolume: BigDecimal,
	val fractionOfSellTransactions: BigDecimal,
)
