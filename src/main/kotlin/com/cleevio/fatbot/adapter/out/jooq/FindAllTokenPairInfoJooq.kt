package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.token.RaydiumAmmMarketData
import com.cleevio.fatbot.tables.references.TOKEN_PAIR_INFO
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.jooq.DSLContext
import org.springframework.stereotype.Component

@Component
class FindAllTokenPairInfoJooq(
	private val dsl: DSLContext,
	private val objectMapper: ObjectMapper,
) {

	data class Result(
		val tokenAddress: AddressWrapper,
		val tokenDecimals: Int,
		val pairAddress: AddressWrapper,
		val dex: GetDex.Dex,
		val poolType: GetDex.PoolType?,
		val raydiumAmmMarketData: RaydiumAmmMarketData?,
	)

	operator fun invoke(tokenAddresses: Set<AddressWrapper>, chain: Chain): List<Result> {
		return dsl
			.select(
				TOKEN_PAIR_INFO.TOKEN_ADDRESS,
				TOKEN_PAIR_INFO.PAIR_ADDRESS,
				TOKEN_PAIR_INFO.DEX_TYPE,
				TOKEN_PAIR_INFO.TOKEN_DECIMALS,
				TOKEN_PAIR_INFO.RAYDIUM_AMM_MARKET_DATA,
				TOKEN_PAIR_INFO.RAYDIUM_POOL_TYPE,
			)
			.from(TOKEN_PAIR_INFO)
			// TODO: Optimize condition
			.where(TOKEN_PAIR_INFO.TOKEN_ADDRESS.`in`(tokenAddresses))
			.and(TOKEN_PAIR_INFO.CHAIN.eq(chain))
			.fetch()
			.map {
				// TODO: Jooq convert
				val marketDataJson = it[TOKEN_PAIR_INFO.RAYDIUM_AMM_MARKET_DATA]?.data()
				val marketData = marketDataJson?.let { objectMapper.readValue<RaydiumAmmMarketData>(marketDataJson) }

				Result(
					tokenAddress = it[TOKEN_PAIR_INFO.TOKEN_ADDRESS]!!,
					tokenDecimals = it[TOKEN_PAIR_INFO.TOKEN_DECIMALS]!!.toInt(),
					pairAddress = it[TOKEN_PAIR_INFO.PAIR_ADDRESS]!!,
					dex = it[TOKEN_PAIR_INFO.DEX_TYPE]!!,
					poolType = it[TOKEN_PAIR_INFO.RAYDIUM_POOL_TYPE],
					raydiumAmmMarketData = marketData,
				)
			}
	}
}
