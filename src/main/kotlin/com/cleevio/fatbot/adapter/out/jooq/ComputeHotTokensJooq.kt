package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.common.crypto.asBaseAmount
import com.cleevio.fatbot.application.common.crypto.toNative
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.hottoken.port.out.ComputeHotTokens
import com.cleevio.fatbot.application.module.transaction.constant.TransactionStatus
import com.cleevio.fatbot.application.module.transaction.constant.TransactionType
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.tables.references.EVM_TOKEN_INFO
import com.cleevio.fatbot.tables.references.TRANSACTION
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.jooq.kotlin.get
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.math.BigInteger
import java.time.Instant
import java.util.UUID

@Component
class ComputeHotTokensJooq(private val dslContext: DSLContext) : ComputeHotTokens {

	data class Result(
		val tokenAddress: AddressWrapper,
		val tokenName: String,
		val tokenSymbol: String,
		val tokenImageFileId: UUID?,
		val tokenImageFileExtension: String?,
		val tokenDecimals: BigInteger,
		val volumeUsd: BigDecimal,
	)

	@Transactional(readOnly = true)
	override fun invoke(chain: Chain, size: Int, from: Instant, to: Instant): List<Result> {
		val conditions = listOf(
			TRANSACTION.TYPE.`in`(listOf(TransactionType.BUY, TransactionType.SELL)),
			TRANSACTION.STATUS.eq(TransactionStatus.SUCCESS),
			TRANSACTION.UPDATED_AT.between(from, to),
			TRANSACTION.CHAIN.eq(chain),
			// TODO: fix condition when a flag of tracking transactions is added
			TRANSACTION.EXCHANGE_RATE_USD.isNotNull,
		)

		val amountField =
			DSL.if_(TRANSACTION.TYPE.eq(TransactionType.SELL), TRANSACTION.AMOUNT_OUT, TRANSACTION.AMOUNT_IN)
		val usdVolumeBaseAmount = DSL.sum(amountField * TRANSACTION.EXCHANGE_RATE_USD)

		val groupedTokens = DSL
			.select(TRANSACTION.TOKEN_ADDRESS, usdVolumeBaseAmount)
			.from(TRANSACTION)
			.where(conditions)
			.groupBy(TRANSACTION.TOKEN_ADDRESS)

		val results = dslContext
			.select(
				groupedTokens[TRANSACTION.TOKEN_ADDRESS],
				groupedTokens[usdVolumeBaseAmount],
				EVM_TOKEN_INFO.NAME,
				EVM_TOKEN_INFO.SYMBOL,
				EVM_TOKEN_INFO.DECIMALS,
				EVM_TOKEN_INFO.file.ID,
				EVM_TOKEN_INFO.file.EXTENSION,
			)
			.from(groupedTokens)
			.leftJoin(EVM_TOKEN_INFO)
			.on(EVM_TOKEN_INFO.ADDRESS.eq(groupedTokens[TRANSACTION.TOKEN_ADDRESS]))
			.and(EVM_TOKEN_INFO.CHAIN.eq(chain))
			.orderBy(groupedTokens[usdVolumeBaseAmount]!!.desc())
			.limit(size)
			.fetch()
			.map {
				Result(
					tokenAddress = it[TRANSACTION.TOKEN_ADDRESS]!!,
					tokenName = it[EVM_TOKEN_INFO.NAME]!!,
					tokenSymbol = it[EVM_TOKEN_INFO.SYMBOL]!!,
					tokenDecimals = it[EVM_TOKEN_INFO.DECIMALS]!!.toBigInteger(),
					tokenImageFileId = it[EVM_TOKEN_INFO.file.ID],
					tokenImageFileExtension = it[EVM_TOKEN_INFO.file.EXTENSION],
					volumeUsd = it[groupedTokens[usdVolumeBaseAmount]]
						.toBigInteger()
						.asBaseAmount()
						.toNative(chain)
						.amount,
				)
			}

		return results
	}
}
