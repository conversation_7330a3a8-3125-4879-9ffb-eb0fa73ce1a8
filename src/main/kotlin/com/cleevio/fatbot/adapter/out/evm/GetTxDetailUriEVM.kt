package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.application.common.crypto.TxHash
import com.cleevio.fatbot.application.module.transaction.port.out.GetTxDetailUri
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.stereotype.Component
import java.net.URI

@Component
class GetTxDetailUriEVM(private val chainProperties: ChainProperties) : GetTxDetailUri {
	override fun invoke(txHash: TxHash, chain: Chain): URI {
		val chainTxExplorerUrl = when (chain) {
			Chain.EVM_MAINNET, Chain.EVM_BASE, Chain.EVM_BSC, Chain.EVM_ARBITRUM_ONE,
			-> chainProperties.ofEvmChainId(chain.evmId).txExplorerUrl
			Chain.SOLANA -> chainProperties.svm.solana.txExplorerUrl
		}

		return URI.create(chainTxExplorerUrl + txHash.txHash)
	}
}
