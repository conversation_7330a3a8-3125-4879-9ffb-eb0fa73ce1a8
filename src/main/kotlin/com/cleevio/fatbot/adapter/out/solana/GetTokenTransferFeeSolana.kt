package com.cleevio.fatbot.adapter.out.solana

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.module.token.port.out.GetTokenTransferFee
import org.p2p.solanaj.rpc.RpcClient
import org.springframework.stereotype.Component

private const val TRANSFER_FEE_EXTENSION_TYPE = "transferFeeConfig"

@Component
class GetTokenTransferFeeSolana(
	private val rpcClient: RpcClient,
) : GetTokenTransferFee {

	override fun invoke(tokenAddress: AddressWrapper): BasisPoint {
		val accountInfo = rpcClient.api.getSplTokenAccountInfo(tokenAddress.getSolanaPublicKey())

		val result = runCatching {
			accountInfo.getExtension(TRANSFER_FEE_EXTENSION_TYPE).get().state.newerTransferFee.transferFeeBasisPoints
		}

		return result.fold(onSuccess = { BasisPoint.of(it.toBigDecimal()) }, onFailure = { BasisPoint.ZERO })
	}
}
