package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.userleaderbord.port.out.GetTopUsersByDonutGained
import com.cleevio.fatbot.tables.references.USER_STATISTICS
import org.jooq.DSLContext
import org.springframework.stereotype.Component

@Component
class GetTopUsersByDonutGainedJooq(
	private val dslContext: DSLContext,
) : GetTopUsersByDonutGained {

	override operator fun invoke(limit: Long): List<GetTopUsersByDonutGained.Result> {
		return dslContext.select(
			USER_STATISTICS.USER_ID,
			USER_STATISTICS.LEADERBOARD_DONUTS,
		).from(USER_STATISTICS)
			.orderBy(
				USER_STATISTICS.LEADERBOARD_DONUTS.desc(),
				USER_STATISTICS.CREATED_AT.asc(),
			)
			.limit(limit)
			.fetch()
			.map {
				GetTopUsersByDonutGained.Result(
					userId = it[USER_STATISTICS.USER_ID]!!,
					donutGainedAmount = it[USER_STATISTICS.LEADERBOARD_DONUTS]!!,
				)
			}
	}
}
