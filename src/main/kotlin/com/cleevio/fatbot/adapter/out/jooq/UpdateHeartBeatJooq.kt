package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.bottransaction.port.out.UpdateHeartBeat
import com.cleevio.fatbot.domain.heartbeat.HeartBeatType
import com.cleevio.fatbot.tables.references.HEART_BEAT
import org.jooq.DSLContext
import org.springframework.stereotype.Component
import java.time.Instant

@Component
class UpdateHeartBeatJooq(private val dslContext: DSLContext) : UpdateHeartBeat {

	override fun invoke(now: Instant, type: HeartBeatType) {
		dslContext
			.update(HEART_BEAT)
			.set(HEART_BEAT.LAST_ACTIVE_AT, now)
			.where(HEART_BEAT.TYPE.eq(type))
			.execute()
	}
}
