package com.cleevio.fatbot.adapter.out.firebase

import com.cleevio.fatbot.adapter.out.BaseConnector
import com.cleevio.fatbot.application.common.exception.InvalidPasswordException
import com.cleevio.fatbot.application.common.exception.TooManyAttemptsException
import com.cleevio.fatbot.application.common.port.out.FirebaseAuth
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.web3j.crypto.StructuredDataEncoder.mapper
import java.io.InputStream

@Service
class FirebaseAuthService(
	@Value("\${firebase.api-key}") private val firebaseApiKey: String,
	@Value("\${firebase.base-url}") private val firebaseBaseUrl: String,
) : FirebaseAuth, BaseConnector(
	baseUrl = "$firebaseBaseUrl?key=$firebaseApiKey",
	restClientCustomizer = {},
) {

	override fun verifyPassword(email: String, password: String) {
		val request = VerifyPasswordRequest(
			email = email,
			password = password,
			returnSecureToken = true,
		)

		val response = restClient.post()
			.body(request)
			.retrieve()
			.onStatus(
				errorStatusPredicate,
			) { _, response -> firebaseHandleErrorResponse(response.body) }
			.body(FirebaseAuthResponse::class.java)

		if (response == null || !response.registered) {
			throw InvalidPasswordException()
		}
	}
}

data class VerifyPasswordRequest(
	val email: String,
	val password: String,
	val returnSecureToken: Boolean,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class FirebaseAuthResponse(
	val registered: Boolean,
	val email: String?,
)

data class ErrorResponse(
	val error: ErrorDetails? = null,
)

data class ErrorDetails(
	val code: Int = 0,
	val message: String = "",
	val errors: List<ErrorItem>? = null,
)

data class ErrorItem(
	val message: String = "",
	val domain: String = "",
	val reason: String = "",
)

private fun parseCompleteErrorResponse(jsonString: InputStream): String {
	val mapper = ObjectMapper().registerModule(KotlinModule.Builder().build())
	val response = mapper.readValue<ErrorResponse>(jsonString)
	return response.error?.message ?: ""
}

fun firebaseHandleErrorResponse(responseBody: InputStream) {
	val errorMessage = parseCompleteErrorResponse(responseBody)

	if (errorMessage == "TOO_MANY_ATTEMPTS_TRY_LATER") {
		throw TooManyAttemptsException()
	}

	// For all other cases, throw general exception
	throw InvalidPasswordException()
}
