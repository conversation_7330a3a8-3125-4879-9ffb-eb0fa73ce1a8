package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContext
import com.cleevio.fatbot.adapter.out.evm.context.EvmChainContextFactory
import com.cleevio.fatbot.adapter.out.evm.model.GasInfo
import com.cleevio.fatbot.application.common.crypto.BaseAmount
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.common.crypto.minusBasisPoints
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.common.wrapper.BasisPoint
import com.cleevio.fatbot.application.common.wrapper.toChainAddress
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.port.out.SellToken
import com.cleevio.fatbot.application.module.referral.ReferralRewardService.Companion.ZERO_UUID
import com.cleevio.fatbot.application.module.transaction.fee.FeeDetailsProvider
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Component
import org.web3j.crypto.Credentials
import org.web3j.crypto.RawTransaction
import java.math.BigInteger
import java.util.UUID

@Component
class SellTokenEVM(
	private val evmChainContextFactory: EvmChainContextFactory,
	private val feeDetailsProvider: FeeDetailsProvider,
) : SellToken<SellTokenEVM.EvmInput> {

	data class EvmInput(
		val userId: UUID,
		val tokenAddress: AddressWrapper,
		val privateKey: String,
		val tokenChainId: Long,
		val amountToSell: BaseAmount,
		val expectedAmountOut: BaseAmount,
		val gasInfoHint: GasInfo?,
		val dexPairInfo: GetDex.Evm,
		val gasLimit: BigInteger,
	)

	override fun invoke(input: EvmInput): SignedTx = evmChainContextFactory.ofChainId(chainId = input.tokenChainId) {
		val credentials = Credentials.create(input.privateKey)
		val nonce = client.getNonce(credentials)

		val sellSignedTx = sendSellTokenTx(
			userId = input.userId,
			nonce = nonce,
			credentials = credentials,
			gasInfo = input.gasInfoHint ?: client.getGasInfo(),
			amountToSell = input.amountToSell,
			expectedAmountOut = input.expectedAmountOut,
			tokenAddress = input.tokenAddress,
			dexPairInfo = input.dexPairInfo,
			gasLimit = input.gasLimit,
		)

		sellSignedTx
	}

	private fun EvmChainContext.sendSellTokenTx(
		userId: UUID,
		nonce: BigInteger,
		credentials: Credentials,
		gasInfo: GasInfo,
		amountToSell: BaseAmount,
		expectedAmountOut: BaseAmount,
		tokenAddress: AddressWrapper,
		dexPairInfo: GetDex.Evm,
		gasLimit: BigInteger,
	): SignedTx {
		val functionCall = getEncodedFunctionCall(
			userId = userId,
			tokenAddress = tokenAddress,
			tokenChainId = properties.chain.evmId,
			amountToSell = amountToSell,
			expectedAmountOut = expectedAmountOut,
			slippageBp = properties.buySellSlippageBp,
			credentials = credentials,
			dexPairInfo = dexPairInfo,
		)

		val rawTx = RawTransaction.createTransaction(
			properties.chain.evmId,
			nonce,
			gasLimit,
			properties.fatbotRouter.getAddressString(),
			BigInteger.ZERO,
			functionCall,
			gasInfo.maxPriorityFeePerGas,
			gasInfo.maxFeePerGas,
		)

		return sendTransaction(signedTx = client.signTx(rawTx, credentials))
	}

	fun getEncodedFunctionCall(
		userId: UUID,
		tokenAddress: AddressWrapper,
		tokenChainId: Long,
		amountToSell: BaseAmount,
		expectedAmountOut: BaseAmount,
		slippageBp: BasisPoint,
		credentials: Credentials,
		dexPairInfo: GetDex.Evm,
	): String {
		val feeDetails = feeDetailsProvider.getFeeDetails(
			transactingUserId = userId,
			token = tokenAddress.toChainAddress(Chain.ofEVM(tokenChainId)),
		)

		val minAmountOut = expectedAmountOut.minusBasisPoints(slippageBp)

		return FatbotRouterFunctionCallEncoder.sellFunctionCall(
			amountIn = amountToSell.amount,
			minAmountOut = minAmountOut.amount,
			tokenAddress = tokenAddress,
			toAddress = credentials.address,
			dexPairInfo = dexPairInfo,
			referralUUID = feeDetails.referrerId ?: ZERO_UUID,
			platformFeePercentage = feeDetails.platformFeeBps.toBigInteger(),
			referralFeePercentage = feeDetails.referrerFeeBps.toBigInteger(),
		)
	}
}
