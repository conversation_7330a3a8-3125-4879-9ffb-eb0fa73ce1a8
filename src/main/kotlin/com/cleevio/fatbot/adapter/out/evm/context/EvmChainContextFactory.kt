package com.cleevio.fatbot.adapter.out.evm.context

import com.cleevio.fatbot.adapter.out.evm.SendPrivateEthTransaction
import com.cleevio.fatbot.application.common.crypto.SignedTx
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.infrastructure.config.properties.AntiMevProperties
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import io.sentry.spring.jakarta.tracing.SentrySpanAdvice
import kotlinx.coroutines.runBlocking
import org.springframework.aop.framework.ProxyFactory
import org.springframework.stereotype.Service
import java.math.BigInteger

@Service
class EvmChainContextFactory(
	private val chainProperties: ChainProperties,
	private val evmWeb3JWrapperProvider: EvmWeb3jWrapperProvider,
	private val sendPrivateEthTransaction: List<SendPrivateEthTransaction>,
	private val antiMevProperties: AntiMevProperties,
) {

	/**
	 *  This factory runs block of code with given EVM chain context.
	 *
	 *  This context consists of EvmClient to make calls to blockchain and chain properties from yaml.
	 *
	 *  Example:
	 *  ```
	 *  evmChainContextFactory.ofChainId(chainId = 1) {
	 *    // gets gas Data on Ethereum main net
	 *    client.getGasData()
	 *  }
	 *
	 *  evmChainContextFactory.ofChainId(chainId = 11155111) {
	 *    // gets gas Data on Sepolia test net
	 *    client.getGasData()
	 *  }
	 *  ```
	 *  Note that the code is exactly the same and the chain it will be executed is determined solely by
	 *  chainId parameter of factory method.
	 */
	fun <T> ofChainId(chainId: Long, block: EvmChainContext.() -> T): T {
		val properties = chainProperties.ofEvmChainId(chainId = chainId)
		val web3jWrapper = evmWeb3JWrapperProvider.ofChainId(chainId = chainId)

		val client = EvmClient(properties = properties, web3jWrapper = web3jWrapper).proxiedWithSentrySpan()

		val context = EvmChainContext(
			client = client,
			properties = properties,
			sendPrivateEthTransaction = sendPrivateEthTransaction,
			antiMevProperties = antiMevProperties,
		)
		return context.block()
	}

	fun <T> ofChainIdRunBlocking(chainId: Long, block: suspend EvmChainContext.() -> T): T = runBlocking {
		val properties = chainProperties.ofEvmChainId(chainId = chainId)
		val web3jWrapper = evmWeb3JWrapperProvider.ofChainId(chainId = chainId)

		val client = EvmClient(properties = properties, web3jWrapper = web3jWrapper).proxiedWithSentrySpan()

		val context = EvmChainContext(
			client = client,
			properties = properties,
			sendPrivateEthTransaction = sendPrivateEthTransaction,
			antiMevProperties = antiMevProperties,
		)
		return@runBlocking context.block()
	}

	private fun EvmClient.proxiedWithSentrySpan() = ProxyFactory(this).apply {
		addAdvice(SentrySpanAdvice())
		isProxyTargetClass = true
	}.proxy as EvmClient
}

data class EvmChainContext(
	val client: EvmClient,
	val properties: ChainProperties.EthereumVirtualMachine,
	val antiMevProperties: AntiMevProperties,
	private val sendPrivateEthTransaction: List<SendPrivateEthTransaction>,
) {
	fun sendTransaction(signedTx: SignedTx): SignedTx {
		// Anti-mev is only applicable to EVM mainnet
		if (properties.chain == Chain.EVM_MAINNET && antiMevProperties.forceUse) {
			val maxAllowedBlockNumberToIncludeHint = client.getPendingBlockNumber().plus(BigInteger.ONE)
			sendPrivateEthTransaction.forEach { invokeFunction ->
				invokeFunction(
					signedTx = signedTx,
					maxAllowedBlockNumberToIncludeHint = maxAllowedBlockNumberToIncludeHint,
				)
			}
		} else {
			client.sendSignedTx(signedTx)
		}
		return signedTx
	}

	fun sendRawTransaction(signedTx: SignedTx): SignedTx {
		client.sendSignedTx(signedTx)
		return signedTx
	}
}
