package com.cleevio.fatbot.adapter.out.jooq

import com.cleevio.fatbot.application.module.bot.port.out.ResetRemainingBotBuyFrequency
import com.cleevio.fatbot.tables.references.BOT
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

private val REMAINING_BUY_FREQUENCY_RESET_FREQUENCY = Duration.of(24, ChronoUnit.HOURS)

@Component
class ResetRemainingBotBuyFrequencyJooq(
	private val dslContext: DSLContext,
	private val clock: Clock,
) : ResetRemainingBotBuyFrequency {

	override fun invoke() {
		val now = Instant.now(clock)
		dslContext.update(BOT)
			.set(BOT.REMAINING_BUY_FREQUENCY, BOT.BUY_FREQUENCY)
			.set(BOT.BUY_FREQUENCY_LAST_RESET_AT, now)
			.set(BOT.VERSION, BOT.VERSION.plus(1))
			.where(BOT.REMAINING_BUY_FREQUENCY.lt(BOT.BUY_FREQUENCY))
			.and(BOT.IS_ACTIVE.isTrue)
			.and(BOT.BUY_FREQUENCY_LAST_RESET_AT.le(DSL.`val`(now.minus(REMAINING_BUY_FREQUENCY_RESET_FREQUENCY))))
			.execute()
	}
}
