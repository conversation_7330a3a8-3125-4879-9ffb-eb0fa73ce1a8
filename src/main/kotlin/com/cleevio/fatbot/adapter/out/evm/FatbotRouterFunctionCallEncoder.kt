package com.cleevio.fatbot.adapter.out.evm

import com.cleevio.fatbot.adapter.out.evm.contract.FatbotRouter
import com.cleevio.fatbot.application.common.crypto.WalletGenerator
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.referral.ReferralRewardService.Companion.ZERO_UUID
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import org.web3j.crypto.Credentials
import org.web3j.protocol.Web3j
import org.web3j.protocol.http.HttpService
import org.web3j.tx.gas.DefaultGasProvider
import java.math.BigInteger
import java.util.UUID

object FatbotRouterFunctionCallEncoder {

	// We only use the contract to construct function calls, so dumb params are fine
	private val router = FatbotRouter.load(
		AddressWrapper.EVM_NULL_ADDRESS.getAddressString(),
		Web3j.build(HttpService()),
		Credentials.create(WalletGenerator.createWallet(ChainType.EVM).privateKey),
		DefaultGasProvider(),
	)

	fun sellFunctionCall(
		amountIn: BigInteger,
		tokenAddress: AddressWrapper,
		toAddress: String,
		dexPairInfo: GetDex.Evm,
		minAmountOut: BigInteger,
		referralUUID: UUID = ZERO_UUID,
		platformFeePercentage: BigInteger = BigInteger.ZERO,
		referralFeePercentage: BigInteger = BigInteger.ZERO,
	): String {
		val functionCall = when (dexPairInfo) {
			is GetDex.UniswapV2, is GetDex.PancakeswapV2 -> router.sellV2(
				amountIn,
				minAmountOut,
				tokenAddress.getAddressString(),
				toAddress,
				referralUUID.toString(),
				platformFeePercentage,
				referralFeePercentage,
			)

			is GetDex.UniswapV3 -> router.sellV3(
				amountIn,
				minAmountOut,
				tokenAddress.getAddressString(),
				toAddress,
				dexPairInfo.fee.feeAmount.toBigInteger(),
				referralUUID.toString(),
				platformFeePercentage,
				referralFeePercentage,
				BigInteger.ZERO,
			)

			is GetDex.PancakeswapV3 -> router.sellV3(
				amountIn,
				minAmountOut,
				tokenAddress.getAddressString(),
				toAddress,
				dexPairInfo.fee.feeAmount.toBigInteger(),
				referralUUID.toString(),
				platformFeePercentage,
				referralFeePercentage,
				BigInteger.ZERO,
			)
		}

		return functionCall.encodeFunctionCall()
	}

	fun buyFunctionCall(
		amountIn: BigInteger,
		tokenAddress: AddressWrapper,
		toAddress: String,
		dexPairInfo: GetDex.Evm,
		minAmountOut: BigInteger,
		referralUUID: UUID = ZERO_UUID,
		platformFeePercentage: BigInteger = BigInteger.ZERO,
		referralFeePercentage: BigInteger = BigInteger.ZERO,
	): String {
		val functionCall = when (dexPairInfo) {
			is GetDex.UniswapV2, is GetDex.PancakeswapV2 -> router.buyV2(
				amountIn,
				minAmountOut,
				tokenAddress.getAddressString(),
				toAddress,
				referralUUID.toString(),
				platformFeePercentage,
				referralFeePercentage,
				BigInteger.ZERO,
			)

			is GetDex.UniswapV3 -> router.buyV3(
				amountIn,
				minAmountOut,
				tokenAddress.getAddressString(),
				toAddress,
				dexPairInfo.fee.feeAmount.toBigInteger(),
				referralUUID.toString(),
				platformFeePercentage,
				referralFeePercentage,
				BigInteger.ZERO,
			)

			is GetDex.PancakeswapV3 -> router.buyV3(
				amountIn,
				minAmountOut,
				tokenAddress.getAddressString(),
				toAddress,
				dexPairInfo.fee.feeAmount.toBigInteger(),
				referralUUID.toString(),
				platformFeePercentage,
				referralFeePercentage,
				BigInteger.ZERO,
			)
		}

		return functionCall.encodeFunctionCall()
	}
}
