package com.cleevio.fatbot.adapter.out.dexscreener

import com.cleevio.fatbot.adapter.out.dexscreener.response.GetTokenPairsInfoResponse
import com.cleevio.fatbot.application.common.crypto.isValidEthereumAddress
import com.cleevio.fatbot.application.common.crypto.isValidSolanaAddress
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.file.FileUrlMapper
import com.cleevio.fatbot.application.module.market.port.out.GetTokenImageUrl
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.application.module.wallet.constant.ChainType
import com.cleevio.fatbot.infrastructure.config.properties.ChainProperties
import org.springframework.stereotype.Component
import java.net.URI

@Component
class DexScreenerPairInfoParser(
	private val chainProperties: ChainProperties,
	private val getTokenImageUrl: GetTokenImageUrl,
	private val fileUrlMapper: FileUrlMapper,
) {

	fun extractTokenPairs(
		tokenAddress: AddressWrapper,
		response: GetTokenPairsInfoResponse,
	): Map<Chain, GetTokenPairsInfoResponse.Pair> {
		val fallbackInfo = findFallbackPairInfoOrNull(response)

		val pairs = chainProperties.enabledChains.mapNotNull { chain ->
			val tokenPair = extractChainTokenPair(response = response, chain = chain) ?: return@mapNotNull null
			val completedPair = tokenPair.completePair(
				tokenImageUrl = getTokenImageUrl(tokenAddress, chain, fileUrlMapper::map),
				fallbackInfo = fallbackInfo,
			)

			chain to completedPair
		}

		val chainToPair = pairs.toMap()

		return chainToPair
	}

	private fun extractChainTokenPair(response: GetTokenPairsInfoResponse, chain: Chain): GetTokenPairsInfoResponse.Pair? {
		val filteredPairs = when (chain.type) {
			ChainType.EVM -> filterResponseOfEvmToken(response, chain)
			ChainType.SOLANA -> filterResponseOfSolanaToken(response)
		}.toList()

		// No need to sort if there is only one pair (this also fixes pump.fun missing liquidity because pump.fun)
		if (filteredPairs.size == 1) return filteredPairs.first()

		val selectedPair = filteredPairs
			.filter { it.liquidity != null }
			.maxByOrNull { it.liquidity!!.usd }

		return selectedPair
	}

	private fun GetTokenPairsInfoResponse.Pair.completePair(
		tokenImageUrl: URI?,
		fallbackInfo: GetTokenPairsInfoResponse.Pair.Info?,
	): GetTokenPairsInfoResponse.Pair {
		val info = this.info ?: fallbackInfo // Replace for fallback info if missing

		val resultPairInfo = when (tokenImageUrl) {
			null -> info
			else -> info?.copy(imageUrl = tokenImageUrl.toString()) ?: emptyPairInfoWithImageUrl(tokenImageUrl)
		}

		return this.copy(info = resultPairInfo)
	}

	private fun emptyPairInfoWithImageUrl(tokenImageUrl: URI) = GetTokenPairsInfoResponse.Pair.Info(
		imageUrl = tokenImageUrl.toString(),
		websites = emptyList(),
		socials = emptyList(),
	)

	private fun filterResponseOfEvmToken(
		response: GetTokenPairsInfoResponse,
		chain: Chain,
	): Sequence<GetTokenPairsInfoResponse.Pair> {
		if (response.pairs.isNullOrEmpty()) return emptySequence()

		val properties = chainProperties.ofEvmChain(chain = chain)
		return response.pairs
			.asSequence()
			.filter { it.chainId == chain.dexscreenerChainId() }
			.filter { it.dexId in chain.allowedDexIds() }
			.filter { it.labels in chain.allowedLabels() }
			.filter { it.quoteToken.address == properties.wethAddress }
			.filter { it.liquidity != null }
			.filter { it.pairAddress.isValidEthereumAddress() }
	}

	private fun Chain.dexscreenerChainId() = when (this) {
		Chain.EVM_MAINNET -> "ethereum"
		Chain.EVM_BASE -> "base"
		Chain.EVM_BSC -> "bsc"
		Chain.EVM_ARBITRUM_ONE -> "arbitrum"
		Chain.SOLANA -> "solana"
	}

	private fun Chain.allowedDexIds() = when (this) {
		Chain.EVM_MAINNET, Chain.EVM_BASE, Chain.EVM_ARBITRUM_ONE -> listOf(EVM_UNISWAP_DEX_ID)
		Chain.EVM_BSC -> listOf(EVM_PANCAKESWAP_DEX_ID)
		Chain.SOLANA -> SOLANA_ALLOWED_DEX_IDS
	}

	private fun Chain.allowedLabels() = when (this) {
		Chain.EVM_MAINNET, Chain.EVM_BASE, Chain.EVM_BSC -> listOf(V2_LABEL, V3_LABEL)
		// EVM_ARBITRUM_ONE uses `null` in place of `V3_LABEL`
		Chain.EVM_ARBITRUM_ONE -> listOf(V2_LABEL, null)
		Chain.SOLANA -> SOLANA_ALLOWED_LABELS
	}

	private fun filterResponseOfSolanaToken(
		response: GetTokenPairsInfoResponse,
	): Sequence<GetTokenPairsInfoResponse.Pair> {
		if (response.pairs.isNullOrEmpty()) return emptySequence()

		return response.pairs
			.asSequence()
			.filter { it.chainId == SOLANA_ALLOWED_CHAIN_ID }
			.filter { it.dexId in SOLANA_ALLOWED_DEX_IDS }
			.filter { it.quoteToken.address == SOLANA_QUOTE_TOKEN_ADDRESS }
			.filter { it.labels in SOLANA_ALLOWED_LABELS }
			.filter {
				// DexScreener does not return liquidity for pump.fun
				it.liquidity != null || it.dexId == SOLANA_PUMPFUN_DEX_ID
			}
			.filter { it.pairAddress.isValidSolanaAddress() }
	}

	private fun findFallbackPairInfoOrNull(response: GetTokenPairsInfoResponse) = response.pairs
		?.filter { it.chainId in EVM_ALLOWED_CHAIN_IDS || it.chainId == SOLANA_ALLOWED_CHAIN_ID }
		?.firstNotNullOfOrNull { it.info }
}

private val EVM_ALLOWED_CHAIN_IDS = listOf("ethereum", "base", "bsc", "arbitrum")
private val V2_LABEL = listOf("v2")
private val V3_LABEL = listOf("v3")

private const val EVM_UNISWAP_DEX_ID = "uniswap"
private const val EVM_PANCAKESWAP_DEX_ID = "pancakeswap"

val EVM_ALLOWED_DEX_IDS = listOf("uniswap")
val EVM_BSC_ALLOWED_DEX_IDS = listOf("pancakeswap")

private const val SOLANA_ALLOWED_CHAIN_ID = "solana"

const val SOLANA_RAYDIUM_DEX_ID = "raydium"
const val SOLANA_PUMPFUN_DEX_ID = "pumpfun"
const val SOLANA_PUMPSWAP_DEX_ID = "pumpswap"
const val SOLANA_METEORA_DEX_ID = "meteora"

// TODO: Meteora temporarily disabled until implemented properly
val SOLANA_ALLOWED_DEX_IDS = listOf(SOLANA_RAYDIUM_DEX_ID, SOLANA_PUMPFUN_DEX_ID, SOLANA_PUMPSWAP_DEX_ID)

// Note: `null` stands for AMM pool
private val SOLANA_ALLOWED_LABELS = listOf(null, listOf("CLMM"), listOf("CPMM"))
private val SOLANA_QUOTE_TOKEN_ADDRESS = AddressWrapper("So11111111111111111111111111111111111111112")
