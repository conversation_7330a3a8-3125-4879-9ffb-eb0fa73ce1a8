package com.cleevio.fatbot.adapter.`in`.rest

import com.cleevio.fatbot.adapter.`in`.ApiVersion
import com.cleevio.fatbot.adapter.`in`.SwaggerBearerToken
import com.cleevio.fatbot.adapter.`in`.rest.dto.CreateNewReferralCodeRequest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.referral.command.ClaimReferralRewardsCommand
import com.cleevio.fatbot.application.module.referral.query.GetReferralRewardsSummaryQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "User Referrals")
@RestController
@SwaggerBearerToken
@RequestMapping("/users/me/referrals")
class UserReferralController(
	private val commandBus: CommandBus,
	private val queryBus: QueryBus,
) {

	@PostMapping(produces = [ApiVersion.VERSION_1_JSON])
	fun createReferralCode(
		@AuthenticationPrincipal userId: UUID,
		@RequestBody request: CreateNewReferralCodeRequest,
	): Unit = commandBus(request.toCommand(userId))

	@GetMapping(produces = [ApiVersion.VERSION_1_JSON])
	fun getReferralRewardsSummary(@AuthenticationPrincipal userId: UUID) = queryBus(GetReferralRewardsSummaryQuery(userId))

	@Operation(
		description = """
		Tries to claim everything on chain from referral program into user default wallet on that chain
	""",
	)
	@PostMapping("/{chain}/claim-referral-reward", produces = [ApiVersion.VERSION_1_JSON])
	fun claimRewards(@AuthenticationPrincipal userId: UUID, @PathVariable chain: Chain) =
		commandBus(ClaimReferralRewardsCommand(userId = userId, chain = chain))
}
