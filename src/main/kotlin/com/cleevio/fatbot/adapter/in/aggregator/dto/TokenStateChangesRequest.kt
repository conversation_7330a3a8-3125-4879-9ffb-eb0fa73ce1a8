package com.cleevio.fatbot.adapter.`in`.aggregator.dto

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.math.BigDecimal

@JsonIgnoreProperties(ignoreUnknown = true)
data class TokenStateChangesRequest(
	val blockSlot: Int,
	val states: List<NewState>,
) {
	@JsonIgnoreProperties(ignoreUnknown = true)
	data class NewState(
		val tgeBlockSlot: Int,
		val tokenAddress: AddressWrapper,
		val creator: AddressWrapper,
		val name: String,
		val symbol: String,
		val infoUrl: String?,
		val marketCapUsd: BigDecimal,
		val liquidityUsd: BigDecimal,
		val volumeUsd: BigDecimal,
		val numOfAccountHolders: Long,
		val buyVolume: BigDecimal,
		val sellVolume: BigDecimal,
		val tokenReserves: BigDecimal,
		val solanaReserves: BigDecimal,
		val fractionOfSellTransactions: BigDecimal,
		val creatorGraduationSuccessRateFraction: BigDecimal,
		val isRedFlagTokenTickerCopy: Boolean,
		val isRedFlagCreatorHighBuy: Boolean,
		val isRedFlagBundledBuysDetected: Boolean,
		val isRedFlagSuspiciousWalletsDetected: Boolean,
		val isRedFlagSingleHighBuy: Boolean,
	)
}
