package com.cleevio.fatbot.adapter.`in`.rest

import com.cleevio.fatbot.adapter.`in`.ApiVersion
import com.cleevio.fatbot.adapter.`in`.SwaggerBearerToken
import com.cleevio.fatbot.adapter.`in`.rest.dto.CreateLimitOrderRequest
import com.cleevio.fatbot.application.common.command.CommandBus
import com.cleevio.fatbot.application.common.query.QueryBus
import com.cleevio.fatbot.application.module.limitorder.command.DeleteLimitOrderCommand
import com.cleevio.fatbot.application.module.limitorder.query.GetAllUserLimitOrdersQuery
import com.cleevio.fatbot.application.module.limitorder.query.GetLimitOrderDetailQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "User Limit Order")
@RestController
@SwaggerBearerToken
@RequestMapping("/users/me/limit-orders")
class UserLimitOrderController(
	private val commandBus: CommandBus,
	private val queryBus: QueryBus,
) {
	@GetMapping(produces = [ApiVersion.VERSION_1_JSON])
	fun getAllUserLimitOrders(@AuthenticationPrincipal userId: UUID) =
		queryBus(GetAllUserLimitOrdersQuery(userId = userId))

	@GetMapping("/{limitOrderId}", produces = [ApiVersion.VERSION_1_JSON])
	fun getLimitOrderDetail(@AuthenticationPrincipal userId: UUID, @PathVariable limitOrderId: UUID) =
		queryBus(GetLimitOrderDetailQuery(userId = userId, limitOrderId = limitOrderId))

	@PostMapping(produces = [ApiVersion.VERSION_1_JSON])
	fun createLimitOrder(@AuthenticationPrincipal userId: UUID, @RequestBody request: CreateLimitOrderRequest) =
		commandBus(request.toCommand(userId = userId))

	@DeleteMapping("/{limitOrderId}", produces = [ApiVersion.VERSION_1_JSON])
	fun deleteLimitOrder(@AuthenticationPrincipal userId: UUID, @PathVariable limitOrderId: UUID) =
		commandBus(DeleteLimitOrderCommand(userId = userId, limitOrderId = limitOrderId))
}
