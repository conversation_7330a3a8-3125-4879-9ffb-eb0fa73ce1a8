package com.cleevio.fatbot.adapter.`in`.rest.dto

import com.cleevio.fatbot.adapter.out.evm.context.UniswapV3Fee
import com.cleevio.fatbot.application.common.crypto.NativeAmount
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.market.port.out.GetDex
import com.cleevio.fatbot.application.module.market.port.out.toDexPairInfo
import com.cleevio.fatbot.application.module.market.query.GetPredictedNativeAmountOnSellQuery
import com.cleevio.fatbot.application.module.market.query.GetPredictedTokenAmountOnBuyQuery
import com.cleevio.fatbot.application.module.token.command.UpdateTokenImageCommand
import com.cleevio.fatbot.application.module.token.constant.CandleChartTimeRange
import com.cleevio.fatbot.application.module.token.constant.TimeRange
import com.cleevio.fatbot.application.module.token.query.GetContinuousTokenPriceChartQuery
import com.cleevio.fatbot.application.module.token.query.GetTokenHistoricalCandleChartQuery
import com.cleevio.fatbot.application.module.token.query.GetTokenPriceChartQuery
import com.cleevio.fatbot.application.module.token.query.GetTokenTaxQuery
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import java.time.Instant

data class GetTokenPriceChartRequestV2(
	val timeRange: TimeRange,
	val chain: Chain,
) {
	fun toContinuousPriceChartQuery(pairAddress: AddressWrapper) = GetContinuousTokenPriceChartQuery.withNowItem(
		chain = chain,
		pairAddress = pairAddress,
		timeRange = timeRange,
	)

	fun toPriceChartQuery(pairAddress: AddressWrapper) = GetTokenPriceChartQuery.withNowItem(
		chain = chain,
		pairAddress = pairAddress,
		timeRange = timeRange,
	)
}

data class GetPredictedTokenAmountOnBuyRequestV2(
	val chain: Chain,
	val buyForCurrencyNativeAmount: NativeAmount,
	val dexPairInfo: DexPairInfoInputV2,
) {
	fun toQuery(tokenAddress: AddressWrapper) = GetPredictedTokenAmountOnBuyQuery(
		tokenAddress = tokenAddress,
		buyForCurrencyNativeAmount = buyForCurrencyNativeAmount,
		chain = chain,
		dexPairInfo = dexPairInfo.toDexPairInfo(),
	)
}

data class GetPredictedEthAmountOnSellRequestV2(
	val toSellTokenNativeAmount: NativeAmount,
	val chain: Chain,
	val dexPairInfo: DexPairInfoInputV2,
) {
	fun toQuery(tokenAddress: AddressWrapper) = GetPredictedNativeAmountOnSellQuery(
		tokenAddress = tokenAddress,
		toSellTokenNativeAmount = toSellTokenNativeAmount,
		chain = chain,
		dexPairInfo = dexPairInfo.toDexPairInfo(),
	)
}

data class GetTokenTaxRequestV2(
	val chain: Chain,
	val dexPairInfo: DexPairInfoInputV2,
) {
	fun toQuery(tokenAddress: AddressWrapper) = GetTokenTaxQuery(
		tokenAddress = tokenAddress,
		chain = chain,
		dexPairInfo = dexPairInfo,
	)
}

data class DexPairInfoInputV2(
	val pairAddress: AddressWrapper,
	val dex: GetDex.Dex,
	val fee: UniswapV3Fee? = null,
	val poolType: GetDex.PoolType? = null,
)

data class GetTokenHistoricalCandleChartRequest(
	val timeRange: CandleChartTimeRange,
	val chain: Chain,
	val before: Instant?,
	val after: Instant?,
) {
	fun toQuery(tokenAddress: AddressWrapper) = GetTokenHistoricalCandleChartQuery(
		tokenAddress = tokenAddress,
		timeRange = timeRange,
		chain = chain,
		before = before,
		after = after,
	)
}

data class UpdateTokenImageRequest(
	val imageUrl: String,
	val chain: Chain,
) {
	fun toCommand(tokenAddress: AddressWrapper) = UpdateTokenImageCommand(
		tokenAddress = tokenAddress,
		chain = chain,
		imageUrl = imageUrl,
	)
}
