package com.cleevio.fatbot.domain.transaction

import com.cleevio.fatbot.application.module.transaction.locks.CREATE_TRANSACTIONS
import com.cleevio.fatbot.application.module.transaction.locks.TRANSACTION_MODULE
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockArgumentParameter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class TransactionCreateService(
	private val transactionRepository: TransactionRepository,
) {

	@Transactional
	@Lock(TRANSACTION_MODULE, CREATE_TRANSACTIONS)
	fun create(@LockArgumentParameter walletId: UUID, transactions: List<Transaction>) {
		transactionRepository.saveAll(transactions)
	}
}
