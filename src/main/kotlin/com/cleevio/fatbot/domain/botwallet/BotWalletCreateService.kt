package com.cleevio.fatbot.domain.botwallet

import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class BotWalletCreateService(private val botWalletRepository: BotWalletRepository) {

	fun create(botId: UUID, address: AddressWrapper, privateKey: String, chain: Chain) = botWalletRepository.save(
		BotWallet(
			botId = botId,
			address = address,
			privateKey = privateKey,
			chain = chain,
		),
	)
}
