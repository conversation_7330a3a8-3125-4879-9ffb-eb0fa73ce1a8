package com.cleevio.fatbot.domain.token

import com.cleevio.fatbot.application.common.converter.AddressWrapperHibernateConverter
import com.cleevio.fatbot.application.common.util.UUIDv7
import com.cleevio.fatbot.application.common.wrapper.AddressWrapper
import com.cleevio.fatbot.application.module.wallet.constant.Chain
import com.cleevio.fatbot.domain.BaseDomainEntity
import jakarta.persistence.Convert
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.math.BigInteger
import java.util.UUID

/**
 * An entity that stores info about bot tokens and cache it for further use.
 */
@Entity
class BotTokenInfo(
	id: UUID = UUIDv7(),
	@Convert(converter = AddressWrapperHibernateConverter::class)
	val address: AddressWrapper,
	@Convert(converter = AddressWrapperHibernateConverter::class)
	val creatorAddress: AddressWrapper,

	@Enumerated(EnumType.STRING)
	val chain: Chain,
	val decimals: BigInteger,
	val name: String,
	val symbol: String,

	/**
	 * Applicable for [Chain.SOLANA]
	 */
	val isToken2022: Boolean,
) : BaseDomainEntity(id) {

	var imageFileId: UUID? = null
		private set

	/**
	 * If this token is graduated (i.e. Pumpfun -> Pumpswap), its new pair address is set here
	 */
	@Convert(converter = AddressWrapperHibernateConverter::class)
	var graduatedToPairAddress: AddressWrapper? = null
		private set

	val isGraduated
		get() = graduatedToPairAddress != null

	fun setImageFileId(imageFileId: UUID) {
		this.imageFileId = imageFileId
	}

	fun graduate(pairAddress: AddressWrapper) {
		graduatedToPairAddress = pairAddress
	}
}

@Repository
interface BotTokenInfoRepository : JpaRepository<BotTokenInfo, UUID> {
	fun findByAddressAndChain(tokenAddress: AddressWrapper, chain: Chain): BotTokenInfo?

	fun findAllByAddressInAndChain(tokenAddresses: Set<AddressWrapper>, chain: Chain): List<BotTokenInfo>
}
